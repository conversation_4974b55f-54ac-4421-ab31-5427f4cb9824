// import Routing from "./utils/Routes/Routing";

import "./App.css";
// import { AuthProvider } from "./AuthProvider";
import Loader from "../src/assets/loader/suryaconLogo.gif";
import Routing from "./utills/Routes/Routing";
import { useCallback, useEffect, useState } from "react";
// import Toast from "./commoncomponents/MessageToasts/SubComponents/MessageToasts";
import { useSelector } from "react-redux";
import { RootState } from "./redux/store";
import { useDispatch } from "react-redux";
import {
  clearToast,
  setToast,
} from "./redux/features/Modules/Reusble/ToastSlice";

import { useLocation, useNavigate } from "react-router-dom";
import Backup from "./Backup";
import Toast from "./components/Common/MessageToasts/SubComponents/MessageToasts";
import { useAuth } from "./AuthProvider";

import {
  initializeBreadcrumb,
  resetTaskName,
} from "./redux/features/Modules/Reusble/navigationSlice";
import {
  currentActiveSubRouteIndex,
  setLabel,
} from "./redux/features/Modules/Reusble/sidebarSlice";

import BackupProgress from "./Backup/BackupProgress";
import { useSocket } from "./SocketProvider";
import { syncTableWithProgress } from "./Backup/BackupIntial";
import {
  clearCategoryPopup,
  clearPopups,
} from "./redux/features/Modules/Reusble/popupSlice";
import {
  setIsEditTask,
  setNavigateToTask,
  setNavigateToTaskView,
  setSearchKey,
  setTypeSearchKey,
} from "./redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import { Departments, Designations } from "./Backup/MastersBackup";
import { downloadZipFile } from "./functions/functions";
import { Root } from "react-dom/client";
import styled from "styled-components";

const App: React.FC = () => {

  const toast = useSelector((state: RootState) => state.toasthandel);
  const unAuth = useSelector((state: RootState) => state.auth.isAuthenticated);
  const navigateArray = useSelector(
    (state: RootState) => state.navigateData.navigateArray
  );
  const navigateFlag = useSelector(
    (state: RootState) => state.taskForm.navigateToTask
  );
  const navigateFlagTaskView = useSelector(
    (state: RootState) => state.taskForm.navigateToTaskView
  );

  const { isAuthenticated } = useAuth();
  const { pathname } = useLocation();
  const [backupTable, setBackupTable] = useState<string[]>([]);
  const [backupTableForLocal, setBackupTableForLocal] = useState<string[]>();
  const { socket, isConnected } = useSocket();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  useEffect(() => {
    const timer = setTimeout(() => {
      dispatch(
        setToast({
          isOpen: false,
          messageContent: "",
          type: "success",
        })
      );
    }, 4000);

    return () => clearTimeout(timer);
  }, [toast]);

  useEffect(() => {
    dispatch(clearPopups());
    dispatch(setIsEditTask(false));
  }, [navigate]);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/auth");
    } else if (backupTable.length <= 0) {
      downloadZipFile();

      navigate("/tools-master");
      dispatch(
        initializeBreadcrumb({
          route: "/tools-master",
          title: "Category",
        })
      );
      dispatch(setLabel("Tools"));
      dispatch(resetTaskName({ title: "Tools" }));
    }
  }, [isAuthenticated, backupTable]);

  useEffect(() => {
    if (!unAuth) navigate("/auth");
  }, [unAuth, navigate]);

  useEffect(() => {
    dispatch(setTypeSearchKey(""));
    dispatch(setSearchKey(""));
  }, [pathname]);

  useEffect(() => {
    if (navigateFlag && navigateArray.length > 0) {
      // Find the latest route that has 'task' but not 'subtask'
      const taskRoute = [...navigateArray]
        .reverse()
        .find(
          (item) =>
            item.route.includes("/task/") && !item.route.includes("/subtask/")
        );

      if (taskRoute?.route) {
        navigate(taskRoute.route);
        console.log("taskRoute.route", taskRoute.route);
        dispatch(setNavigateToTask(false));
      }
    }
  }, [navigateFlag]);

  useEffect(() => {
    if (navigateFlagTaskView && navigateArray.length > 0) {
      // Find the latest route that has 'task' but not 'subtask'
      const taskRoute = [...navigateArray]
        .reverse()
        .find(
          (item) =>
            item.route.includes("/category/") && !item.route.includes("/task/")
        );

      if (taskRoute?.route) {
        navigate(taskRoute.route);
        console.log("taskRoute.route", taskRoute.route);
        dispatch(setNavigateToTaskView(false));
      }
    }
  }, [navigateFlagTaskView]);

  // const fetchDesignationAndDepartments = async () => {
  //   await Departments(dispatch);
  //   await Designations(dispatch);
  // };
  // useEffect(() => {
  //   const CallFetchDesignationsAndDepartmants = async () => {
  //     await fetchDesignationAndDepartments();
  //   };
  //   CallFetchDesignationsAndDepartmants();
  // }, []);

  const getpath = async () => {
    const data = await window.electron.getImagePath();
  };
  getpath();

  const getBackuptable = () => {
    const table = JSON.parse(
      localStorage.getItem("backuptable") || "[]"
    ) as string[];
    console.log("check for table bro here >>", table);
    setBackupTable(table);
  };

  useEffect(() => {
    window.addEventListener("backuptable", getBackuptable);
    return () => window.removeEventListener("backuptable", getBackuptable);
  }, []);
  interface AppWrapperProps {
    scale: number;
  }

  useEffect(() => {
    //backup intially
    if (socket && isConnected) {
      socket.on("initialbackup", (data) => {
        if (data.Allparentsdata && data.Allparentsdata.length > 0) {
          const tables = [
            "Departmentmaster",
            "Designationmaster",
            "ToolCategory",
            "ToolDesignation",
            "MaterialCategory",
            "MaterialDesignation",
            "Manpowercategory",
            "Manpowerdesignation",
            "MachinaryCategory",
            "MachinaryDesignation",
            "TaskCategory",
            "Taskmaster",
            "Taskmaster_Form",
            "Subtaskmaster",
            "Departmentmaster",
            "Designationmaster",
            "project",
            "Towerlocations",
            "TowerlocationsDetails_Routes",
            "TowerlocationsDetails_Tasks",
            "TowerlocationsDetails_Subtask",
            "SubtasklocDetail",
          ];

          // Update state
          setBackupTableForLocal(tables);

          // Save to localStorage
          localStorage.setItem("backuptable", JSON.stringify(tables));

          // Dispatch backup event
          const newEvent = new Event("backuptable");
          dispatchEvent(newEvent);
        }
      });
    }
  }, [socket, isConnected]);

  useEffect(() => {
    if (backupTable.length > 0) {
      syncTableWithProgress(dispatch);
    }
  }, [backupTableForLocal]);

  return (
    <div className="main_container">
      {toast.isOpen && (
        <Toast type={toast?.type} messageContent={toast.messageContent} />
      )}
      <>
        {backupTable.length >= 1 && isAuthenticated ? (
          <BackupProgress />
        ) : (
          <>
            <Backup />
            <Routing />
          </>
        )}
      </>
    </div>
  );
};

export default App;
