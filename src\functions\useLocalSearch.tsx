import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { AppPath, initializeDatabase, TableName } from "./functions";
import {
  useAppDispatch,
  useAppSelector,
} from "../redux/hooks/Modules/Reduxhooks/ReduxHooks";

type SearchHandlerOptions = {
  pathTableMap: any;
  searchKey: string;
  setData: any;
  isDeleted?: boolean;
  setPage?: React.Dispatch<React.SetStateAction<number>>;
  detectChanges?: boolean;
  key?: string;
};

export const usePouchSearch = ({
  pathTableMap,
  searchKey,
  setData,
  isDeleted = false,
  setPage,
  key = "name",
}: SearchHandlerOptions) => {
  const location = useLocation();
  const currentPath = location.pathname;
  const dispatch = useAppDispatch();
  // const detectChanges = useAppSelector((state) => state.backupSlice.isOpen);

  let firstPart = "";
  if (currentPath && currentPath.split("/").length > 1) {
    firstPart = "/" + currentPath.split("/")[1]; // safe split
  } else {
    firstPart = currentPath;
  }

  const path = pathTableMap[firstPart as any];

  const handleSearch = async () => {
    const dbname = await initializeDatabase(path);
    const safePattern = searchKey?.replace(/[-\/\\^$*+?.()|[\]{}]/g, "\\$&");

    if (!safePattern.trim()) {
      dispatch(setData([]));
      setPage?.(1);
      return;
    }

    const res = await window.electron.searchData({
      path: dbname,
      key,
      deleted: isDeleted,
      value: safePattern,
    });

    console.log(dbname, "check for db name here>>", res);

    dispatch(setData(res));
  };

  useEffect(() => {
    handleSearch();
  }, [searchKey, isDeleted]);
};
