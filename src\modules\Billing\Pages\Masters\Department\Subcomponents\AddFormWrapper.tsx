import { ReactNode, useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import { CloseIcon } from "../../../../../../assets/icons";
import Button from "../../../../../../components/Reusble/Global/Button";
import { RootState } from "../../../../../../redux/store";
import styles from "../Styles/Department.module.css";

export interface AddFormState {
  isOpen: boolean; // whether the form modal is open.
  label: string;
  key: string; //  unique key for the form instance.
  isEdited?: boolean; // Optional flag indicating if the form is in edit mode.
  isDeleted?: boolean; //  Optional flag indicating if the form is marked as deleted.
  headingLabel: string; // The heading label displayed on the form.
  isSummary: boolean; //  Indicates if the summary section is shown.
  setSummary: (value: boolean) => void; // State setter for toggling the summary section.
  isDiscard: boolean; // indicating if the form is in discard state.
  setDiscard: (value: boolean) => void; // State setter for toggling the discard state.
  onClose: () => void; // Callback function to close the form.
  children?: ReactNode; // Optional React children to be rendered inside the form.
  validateIsEmpty?: () => Boolean; //  Optional function to validate if the form is empty.
  isRightButtonVisible?: boolean; // Optional flag to show/hide the right button (defaults to false).
  btn1Label?: string; // Optional label for the first button.
  btn1Handler?: () => void; //  Optional callback for the first button action.
  btn2Label?: string; // Optional label for the second button.
  btn2Handler?: () => void; // Optional callback for the second button action.
  validateFormUpdated?: () => Boolean; // Optional function to validate if the form has been updated.
  status?: "update" | "delete" | "default"; // Optional status of the form, can be "update", "delete", or "default".
  validateError?: () => boolean; // Optional function to handle validation errors.
  inputError?: boolean; //
  isSecondFormOpen?: boolean;
  variant?: "primary" | "secondary"; // Optional variant of the form, can be "primary" or "secondary".
  summaryContent?: string;
}

/**
 * AddForm is a reusable modal form component for adding, editing, deleting, or summarizing items.
 *
 * @description
 * AddForm provides a flexible modal dialog for CRUD operations with built-in validation, discard, and summary dialogs.
 * It is designed to be highly customizable and can be used for various forms throughout the application.
 */

const AddFormWrapper = ({
  label = "",
  isOpen = false,
  isSummary = false,
  isDiscard = false,
  isDeleted = false,
  isEdited = false,
  setDiscard,
  setSummary,
  headingLabel,
  isRightButtonVisible = false,
  children: Children,
  onClose,
  btn1Label = "Cancel",
  btn1Handler = () => {},
  btn2Label = "Add",
  btn2Handler = () => {},
  validateIsEmpty,
  validateFormUpdated,
  status = "default",
  validateError,
}: AddFormState) => {
  // ⚠️ Note: Using useRef instead of useState to persist `backToSummary`
  // This flag controls logical flow between summary ↔ discard modes.
  // useRef is used because:
  // - It persists across renders
  // - It does NOT trigger a re-render when changed
  // - This flag is internal logic only, not UI state
  const backToSummaryRef = useRef(false);
  // formRef is used to reference the form modal DOM element.
  // This allows us to detect clicks outside the modal (for closing the modal when clicking outside)
  const formRef = useRef<HTMLDivElement>(null);
  const formMode = useSelector((state: RootState) => state.masterForm.formMode);
  // Cancel: Close if not changed or empty, else show discard dialog
  const cancelHandler = () => {
    if (formMode === "Reason") {
      onClose();
    }
    if (
      (isEdited && validateFormUpdated && !validateFormUpdated()) ||
      isDeleted ||
      (validateIsEmpty && validateIsEmpty())
    ) {
      onClose();
    } else {
      setDiscard(true);
    }
  };

  // Handles clicks outside the form modal to trigger onClose if certain conditions are met
  const outSideClickHandler = (event: MouseEvent) => {
    event.stopPropagation();
    if (formRef.current && !formRef.current.contains(event.target as Node)) {
      // Maintains focus on form when clicking outside
      if (formRef.current) {
        event.preventDefault();
        formRef.current.focus();
      }

      // Only close if not in discard/summary mode and form is empty
      if (
        isDeleted ||
        formMode === "Reason" ||
        (!isDiscard && !isSummary && validateIsEmpty && validateIsEmpty()) ||
        (isEdited && validateFormUpdated && !validateFormUpdated())
      ) {
        onClose();
      }
    }
  };

  // Sets up and cleans up the outside click event listener when the modal is open
  useEffect(() => {
    if (!isOpen) return;

    document.addEventListener("mousedown", outSideClickHandler);
    return () => {
      document.removeEventListener("mousedown", outSideClickHandler);
    };
  }, [isOpen, outSideClickHandler]);

  // Add: Show alert if empty or unchanged, else show summary
  const addHandler = () => {
    console.log("Add Handler");
    if (validateError && validateError()) {
      return;
    } else {
      setSummary(true);
    }
  };

  // Back: Hide discard or summary dialog
  const backHandler = () => {
    if (isDiscard) {
      setDiscard(false);
    } else if (isSummary) {
      setSummary(false);
    }
  };

  // Using backToSummaryRef.current to remember whether we should return to summary view.
  // This avoids unnecessary state updates and rerenders since it's just a control flag.
  const rightButtonHandler = () => {
    if (formMode === "Reason") onClose();
    const isFormUnchanged = validateFormUpdated && !validateFormUpdated();

    // Case 1: Close if deleted or edit mode with no changes
    if (isDeleted || (isEdited && isFormUnchanged)) {
      onClose();
      return;
    }

    // Case 2: If currently in summary mode
    if (isSummary) {
      if (isEdited && isFormUnchanged) {
        onClose(); // Edited summary but nothing changed
        return;
      }

      // Move from summary to discard
      backToSummaryRef.current = true;
      setSummary(false);
      setDiscard(true);
      return;
    }

    // Case 3: If currently in discard mode
    if (isDiscard) {
      if (backToSummaryRef.current) {
        setDiscard(false);
        setSummary(true);
      } else {
        setDiscard(false);
      }
      backToSummaryRef.current = false;
      return;
    }

    // Case 4: Default behavior — close or go to discard
    if (validateIsEmpty?.()) {
      onClose();
    } else {
      setDiscard(true);
    }

    backToSummaryRef.current = false;
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (
      (e.key === "Enter" && e.shiftKey) ||
      (formMode === "Reason" && e.key === "Enter")
    )
      return;

    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      if (isSummary) {
        btn2Handler();
      }
      if (!isSummary && !isDiscard) {
        addHandler();
      }
      if (isDiscard) onClose();
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      if (formMode === "Reason") {
        onClose();
        return;
      }

      if (!isSummary && !isDiscard) {
        cancelHandler();
      }

      if (isSummary) {
        backHandler();
      }

      if (isDiscard) {
        rightButtonHandler();
      }
    }
  };

  useEffect(() => {
    if (
      formRef.current &&
      (isSummary || isDiscard || isDeleted || formMode === "Reason")
    ) {
      formRef.current.focus(); // <-- Add this line to set focus
    }
  }, [isSummary, isDiscard]);

  return (
    <>
      {/* Render a semi-transparent overlay behind the modal when it is open */}
      {isOpen && <div className={styles.addForm_overlay} />}
      <div
        className={`${styles.addForm_box} ${isOpen ? "" : styles.isClosing}`}
        ref={formRef}
        tabIndex={-1}
        onKeyDown={handleKeyDown}
      >
        <div className={`${styles.addForm_head}`}>
          <div
            className={`
            ${styles.addForm_head_content}
            ${
              isDeleted || isDiscard || formMode === "Reason"
                ? styles.addForm_head_content_discard
                : ""
            }
            `}
            // ${isEdited && !isSummary ? styles.addForm_head_content_updated : ""}
          >
            <h3>
              {formMode === "Reason"
                ? "Reason"
                : // formMode==="Edit"? `Edit ${label}`:
                isDeleted
                ? `Are you sure you want to delete this ${label} ?`
                : isDiscard
                ? `Are you sure you want to discard this ${label}?`
                : isSummary
                ? `Are you sure you want to ${
                    formMode === "Add" ? "add" : "update"
                  } this ${label}?`
                : headingLabel
                ? headingLabel
                : null}
            </h3>
          </div>

          {isRightButtonVisible && (
            <button
              className={`${styles.addForm_head_button}`}
              onClick={rightButtonHandler}
            >
              <CloseIcon />
            </button>
          )}
        </div>

        <div className={styles.addForm_content}>{Children}</div>

        {formMode !== "Reason" && (
          <div className={`${styles.addForm_footer}`}>
            <Button
              Content={isDiscard ? "No" : isSummary ? "Back" : btn1Label}
              Callback={() => {
                if (!isDiscard && !isSummary) cancelHandler();
                else if (isDiscard || isSummary) backHandler();
                else btn1Handler();
              }}
              key={"addForm-btn1"}
              type="Cancel"
            />

            <Button
              Content={
                isDiscard
                  ? "Yes"
                  : isSummary
                  ? "Submit"
                  : isEdited
                  ? "Update"
                  : isDeleted
                  ? "Delete"
                  : btn2Label
              }
              Callback={() => {
                if (!isSummary && !isDiscard && !isDeleted) addHandler();
                else if (isDiscard) onClose();
                else btn2Handler();
              }}
              key={"addForm-btn2"}
              type="Accept"
            />
          </div>
        )}
      </div>
    </>
  );
};

export default AddFormWrapper;
