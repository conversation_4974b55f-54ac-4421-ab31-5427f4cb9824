import React, { useEffect, useLayoutEffect, useState } from "react";
import LocationCardView from "./Subcomponents/LocationCardView";
import LocationHeader from "./Subcomponents/LocationHeader";

import TableViewLocation from "./Subcomponents/TableViewLocation";
import styles from "./Styles/Location.module.css";
import Sidebar from "../../../../../components/Common/Sidebar";
import {
  setCategoryIdLocation,
  setSelectedtowerType,
  setTowerLocations,
} from "../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/ProjectSlice";
import { useDispatch, useSelector } from "react-redux";
import { useLazyGetTowerlocationByProjectIdQuery } from "../../../../../redux/api/Modules/Billing/Billingapi";
import { useParams } from "react-router-dom";
import {
  initializeDatabase,
  pathTableMap,
} from "../../../../../functions/functions";
import { RootState } from "../../../../../redux/store";
import { SiD } from "react-icons/si";
import { useAppSelector } from "../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { usePouchSearch } from "../../../../../functions/useLocalSearch";
import { setSearchData } from "../../../../../redux/features/Modules/Masters";
import { useNestedPouchSearch } from "../../../../../functions/useNestedLocalSearch";
const Location: React.FC = () => {
  const [isTableView, setIsTableView] = useState(true); // Managing the state
  const dispatch = useDispatch();
  const { projectId } = useParams();
  
  const [getLocationsByProjectid] = useLazyGetTowerlocationByProjectIdQuery();
  const detecteChanges = useSelector(
    (state: RootState) => state?.backupSlice.isOpen
  );
  const isTowerLocationDeleted = useSelector(
    (state: RootState) => state.projectLocalDb.isTowerLocationDeleted
  );

  useLayoutEffect(() => {
    dispatch(setSelectedtowerType("Tower"));
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      if (projectId) {
        console.log(projectId,"rendering many times>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
        const dbname = await initializeDatabase("Towerlocations");
        console.log(dbname, "this is db name in location page");
        const response = await window.electron.getDocumentByParentId({
          dbName: dbname,
          catId: projectId,
          page: page,
          categoryId: "project_id",
          needSorting: true,
          isDeletedNext: isTowerLocationDeleted,
        });
        console.log(response, "this is response from electroasdfn");
        if (response) {
          dispatch(setTowerLocations(Array.isArray(response) ? response : []));
          dispatch(setCategoryIdLocation(projectId as string));
        }
      }
    };
    fetchData();
  }, [
    projectId,
    // detecteChanges,
    // isTowerLocationDeleted,
    // getLocationsByProjectid,
  ]);

  
  const [page, setPage] = useState(1);
  const navRef = React.useRef<HTMLDivElement>(null);
  const sidebarRef = React.useRef<HTMLDivElement>(null);
  const mainRef = React.useRef<HTMLDivElement>(null);
  const [width, setWidth] = useState<null | number>(null);
  const clientFunction = () => {
    const cardViewcontainer = document.querySelector(
      `.${styles.location_main_container}`
    ) as HTMLDivElement;
    console.log(
      "details of card view container in useEffect div",
      cardViewcontainer
    );
    const details = cardViewcontainer?.getBoundingClientRect();
    const maincontwidth = mainRef.current?.getBoundingClientRect()?.width;
    console.log("details of card view container in useEffect details", details);
    setWidth(details?.width);
    console.log("inner width");
    // if (window.innerWidth < 1200) {
    const sidebarWidth = sidebarRef.current?.getBoundingClientRect().width;
    navRef.current?.style.setProperty(
      "width",
      `${(maincontwidth || 0) + (sidebarWidth || 0) + 32}px`
    ); // }
  };
  useEffect(() => {
    clientFunction();
    const handleResize = () => clientFunction();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [window.innerWidth]);

  const containerRef = React.useRef<HTMLDivElement>(null);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    if (scrollTop + clientHeight >= scrollHeight - 200) {
      setPage((prev) => prev + 1);
    }
  };
  const [searchLocalKey, setSearchLocalKey] = useState<string>("");
  const searchKey = useAppSelector((state) => state.taskMaster.searchKey);
  useEffect(() => {
    if (searchKey) {
      setSearchLocalKey(searchKey);
    } else {
      setSearchLocalKey("");
    }
  }, [searchKey]);

  useNestedPouchSearch({
    pathRecord: "Towerlocations",
    searchKey: searchKey,
    setData: setSearchData,
    setPage,
    extraSearchParams: {
      catId: projectId,
      categoryId: "project_id",
      isDeletedNext: false,
    },
  });
  console.log(searchKey, "this is searched data");
  return (
    <div
      ref={containerRef}
      className={`${styles.location_container}`}
      onScroll={handleScroll}
    >
      {/* <LocationHeader /> */}
      <div className={`${styles.location_content_container}`}>
        <div ref={mainRef} className={`${styles.location_main_container}`}>
          {isTableView ? <LocationCardView /> : <TableViewLocation />}
        </div>
      </div>
    </div>
  );
};

export default Location;
