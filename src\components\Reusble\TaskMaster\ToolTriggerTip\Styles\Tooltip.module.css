.tooltip {
  min-height: 2.6rem;
  height: 2.6rem;
  min-width: 5rem;
  cursor: pointer;
  backdrop-filter: blur(40px);
  border-radius: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.6rem 1.5rem;
  box-shadow: 0px 0px 4px 0px #91a1a180;
  border: 1px solid var(--primary_color);
  backdrop-filter: blur(40px);
  /* word-break: break-all; */
}

.tooltip p {
  color: #000000;
  line-height: 1.1rem;
  color: var(--text-black-60);
  /* white-space: nowrap; */
}

.tooltip.time_interval {
  display: flex;
  overflow: hidden;
  align-items: normal !important;
  padding: 0 !important;
  height: auto !important;
}

.tooltip.time_interval div:first-child {
  flex: 2 1 66.67%;
  padding: 0.6rem 1.5rem;
  display: flex;
  justify-content: center;
}

.tooltip.time_interval div:last-child {
  flex: 1 1 33.33%;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--primary_color);
  color: white !important;
}

.time_interval_p {
  color: white !important;
  margin-top: 0.5rem;
}

.tooltip_main_card {
  min-width: 8rem;
  height: 6rem;
}

/* new styling */
.tooltip_bottom {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding: 0.5rem;
  gap: 0.5rem;
  border-radius: 0.75rem;
  box-shadow: var(--extra-shdow-second);
  height: 4rem;
  color: var(--primary_color);
}

.tooltip_top {
  position: relative;
  background-color: var(--main_background);
  z-index: 3;
  border-radius: 0.75rem;
  min-height: 2.8rem;
  min-width: 5rem;
  cursor: pointer;
  backdrop-filter: blur(40px);
  border-radius: 25px;
  padding: 0.3rem 1rem;
  box-shadow: 0px 0px 4px 0px #91a1a180;
  border: 1px solid var(--primary_color);
  backdrop-filter: blur(40px);
}

.tooltip_main_card div:not(:first-child) p {
  align-self: end;
}

.tooltip_bottom_1 {
  position: relative;
  top: -2rem;
  z-index: 1;
  background-color: var(--primary_background);
}

.tooltip_bottom_calculated {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding: 0.5rem;
  gap: 0.5rem;
  border-radius: 0.75rem;
  box-shadow: var(--extra-shdow-second);
  height: 4rem;
  color: var(--secondary_color);
  background-color: var(--secondary_background);
}

.tooltip_bottom_2 {
  position: relative;
  top: -2rem;
  z-index: 1;
  color: var(--secondary_color);
  background-color: var(--secondary_background);
}
