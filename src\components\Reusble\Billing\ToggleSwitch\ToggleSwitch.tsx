import { useEffect, useState } from "react";
import styles from "./ToggleSwtich.module.css";

interface ToggleSwitchProps {
  leftLabel?: React.ReactNode;
  rightLabel?: React.ReactNode;
  onToggle?: (isLeft: boolean) => void;
  width?: string;
  countLeft?: number;
  countRight?: number;
  id: string;
}

export function ToggleSwitch({
  leftLabel = 0,
  rightLabel = 0,
  countLeft,
  countRight,
  onToggle,
  width,
}: ToggleSwitchProps) {
  const [isLeft, setIsLeft] = useState(true);

  const handleToggle = (moveLeft: boolean) => {
    if (moveLeft !== isLeft) {
      setIsLeft(moveLeft);
    }
  };

  useEffect(() => {
    onToggle && onToggle(isLeft);
  }, [isLeft]);

  return (
    <div className={styles.toggle_main} style={{ width }}>
      <div className={styles.toggleContainer}>
        <div
          className={`${styles.toggleLabel} ${isLeft ? styles.active : ""}`}
          onClick={() => handleToggle(true)}
        >
          <div className={styles.left_right_Label}>{leftLabel}</div>
          {(countLeft ?? 0) >= 0 ? (
            <div
              className={styles.numeric_values}
              style={{
                color: !isLeft ? "#00000047" : "",
                backgroundColor: !isLeft ? "#F0F6F6" : "",
              }}
            >
              {countLeft ?? "0"}
            </div>
          ) : (
            ""
          )}
        </div>
        <div
          className={`${styles.toggleLabel} ${!isLeft ? styles.active : ""}`}
          onClick={() => handleToggle(false)}
        >
          <div className={styles.left_right_Label}>{rightLabel}</div>
          <div>
            {(countRight ?? 0) >= 0 ? (
              <div
                className={styles.numeric_values}
                style={{
                  color: isLeft ? "#00000047" : "",
                  backgroundColor: isLeft ? "#F0F6F6" : "",
                }}
              >
                {countRight ?? "0"}
              </div>
            ) : (
              ""
            )}
          </div>
        </div>
        <div
          className={`${styles.toggleSlider} ${
            isLeft ? styles.left : styles.right
          }`}
        />
      </div>
    </div>
  );
}
