/*  AUTHOR NAME : CHARVI */
.tasknav_conatiner {
  display: flex;
  margin-top: 1.7rem !important;
  height: 2.6rem;
  justify-content: space-between;
  align-items: center;
  padding-inline: 1rem;
}

.CategoryView_Navlinks {
  display: flex;
  width: fit-content;
  gap: 0.3rem;
  align-items: center;
}

.CategoryView_DesignArrow_Rotate {
  transform: rotate(-90deg);
  display: flex;
  align-items: center;
  width: fit-content;
}

.summaryDivData {
  display: flex;
  align-items: center;
}

.summaryDataContent {
  display: flex;
  flex-direction: column;
  background: #ffffff99;
  border-radius: 0.75rem;
  width: 30.8rem;
  /* max-width: 28.5rem; */
  min-height: 3.188rem;
  padding: 1rem;
  white-space: normal;
  margin: 0.6rem;
  gap: 0.2rem;
  line-height: 1.363rem;
  text-align: left;
}

.leftbtn {
  border: 1px solid var(--primary_color);
  width: 5.1rem;
  height: 2rem;
  margin: 0.7rem;
  color: var(--primary_color);
  border-radius: 3.125rem;
  background-color: white;
}

.tasknav_rightbtns {
  display: flex;
  gap: 0.938rem;
  position: relative;
}

.taskdltbtn,
.taskdexportbtn {
  /* width: 7.1rem; */
  height: 2.6rem;
  padding: 1rem 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  border-radius: 6.25rem;
  cursor: pointer;
  background-color: var(--main_background);
  box-shadow: 0px 0px 4px 0px #00000066;
  border: none;
  color: var(--text-black-60);
}

.taskaddcategorybtn {
  min-width: 7.1rem;
  height: 2.6rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  border-radius: 6.25rem;
  cursor: pointer;
  background-color: var(--primary_color);
  color: var(--text-white-100);
  border: none;

  padding: 1rem;
}

.locationbtncorner {
  position: fixed;
  right: -3.5rem;
  width: 6.563rem;
  height: 3.25rem;
  text-align: left;
  padding-left: 1rem;
  background-color: var(--main_background);
  box-shadow: 0px 0px 4px 0px #00000066;
  border: none;
  border-radius: 1.75rem;
  cursor: pointer;
  z-index: 2;
}

.task_header {
  position: sticky;
  top: 0;
  margin-top: -0.7rem;

  z-index: 1;
  margin-right:0.625rem;
}

.tasknav_left {
  display: flex;
  gap: 1rem;
}
