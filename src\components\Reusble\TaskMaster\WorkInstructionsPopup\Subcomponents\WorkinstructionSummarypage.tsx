import React from "react";
import styles from "../Styles/WorkInstructions.module.css";
import { AudioIcon, ImageIcon, VideoIcon } from "../../../../../assets/icons";
import { getFileName } from "../../../../../functions/functions";

interface FileData {
  name: string;
  type: string;
}

interface PhotoSection {
  id: number;
  photo: string | null;
  fileName?: string;
  referenceDetail: string;
}

interface WorkinstructionSummarypageProps {
  initialdata?: {
    id?: string;
    description: string;
    file: { name: string; type: string } | null;
    category: string;
    photoDetails: PhotoSection[];
  };
  description: string;
  summary?: boolean;
  file: FileData | null;
  selectedAction: string;
  photoDetails: PhotoSection[];
  onBack: () => void;
  onSubmit: () => void;
  discard?: boolean;
  height?: number;
  startWithPhotoCheckboxPage?: boolean;
}

const getIconForFileType = (type: string) => {
  switch (type) {
    case "jpg":
    case "avif":
    case "jpeg":
      return <ImageIcon />;
    case "png":
      return <ImageIcon />;
    case "mp4":
      return <VideoIcon />;
    case "mp3":
      return <AudioIcon />;
    default:
      return null;
  }
};

const WorkinstructionSummarypage: React.FC<WorkinstructionSummarypageProps> = ({
  initialdata,
  description,
  file,
  summary,
  selectedAction,
  photoDetails,
  startWithPhotoCheckboxPage,
  discard,
  height,
}) => {
  const isChanged = (initialValue: any, currentValue: any) => {
    // if (
    //   initialValue === undefined ||
    //   initialValue === null ||
    //   initialValue === "" ||
    //   (Array.isArray(initialValue) && initialValue.length === 0)
    // ) {
    //   return false;
    // }

    // Handle undefined cases
    if (initialValue === undefined && currentValue === undefined) return false;
    if (initialValue === undefined || currentValue === undefined) return true;
    return initialValue !== currentValue;
  };

  // console.log('isdiscard>>', discard)
  // console.log("photooo>>", photoDetails);
  // console.log("file??insumpage", file);
  // console.log("file??ischanged", summary &&
  //   initialdata &&
  //   !discard &&
  //   // initialdata?.file &&
  //   isChanged(initialdata?.file?.name, file?.name));
  console.log("file??initialdata>>", initialdata, "photooo>>", photoDetails);
  /*
  section.id != initialdata?.photoDetails.some(
    (item) => item.id == section.id)
    console.log('heiight',height)
    */

  console.log("height>in summary page", height);
  const condition = window.innerWidth > 1800;
  return (
    <div
      className={styles.summaryPage}
      style={{
        height: height ? `calc(100% - ${height}px - 4.5rem)` : undefined,
      }}
    >
      {!startWithPhotoCheckboxPage && (
        <>
          {(description! || file!) && (
            <div className={styles.summaryDivData}>
              <div className={styles.summaryDataContent}>
                {(description! || file!) && (
                  <>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Description
                    </p>
                    <h4
                      style={{
                        color:
                          summary &&
                          initialdata &&
                          isChanged(initialdata.description, description)
                            ? "var(--secondary_color)"
                            : "var(--text-black-87)",
                      }}
                    >
                      {description ?? "No description provided"}
                    </h4>
                  </>
                )}

                {file! && (
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    {file! && getIconForFileType(file.type)}
                    {file! && (
                      <h4
                        style={{
                          marginLeft: "0.5rem",

                          color:
                            summary &&
                            initialdata &&
                            // !discard &&
                            // initialdata.file &&
                            isChanged(initialdata?.file?.name, file?.name)
                              ? "var(--secondary_color)"
                              : "var(--primary_color)",
                        }}
                      >
                        {file.name ?? ""}
                      </h4>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {selectedAction && (
            <div className={styles.summaryDivData}>
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Action
                </p>
                <h4
                  style={{
                    textTransform: "capitalize",
                    color:
                      summary &&
                      initialdata &&
                      isChanged(initialdata.category, selectedAction)
                        ? "var(--secondary_color)"
                        : "var(--text-black-87)",
                  }}
                >
                  {selectedAction || "No action selected"}
                </h4>
              </div>
            </div>
          )}
        </>
      )}

      {selectedAction === "photo" &&
        photoDetails?.some(
          (e) => e?.referenceDetail !== "" || e?.photo !== null
        ) && (
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              {photoDetails?.map((section, index) => (
                <div key={section.id} style={{ marginBottom: "1rem" }}>
                  <h4
                    style={{
                      color:
                        (summary &&
                          initialdata &&
                          !initialdata?.photoDetails?.some(
                            (item) => item.photo === section.photo
                          )) ||
                        (isChanged(initialdata?.category, selectedAction) &&
                          photoDetails?.length > 1 &&
                          !discard &&
                          initialdata?.category == "photo" &&
                          !initialdata?.photoDetails?.some(
                            (item) => item.id == section.id
                          )) ||
                        (initialdata &&
                          !discard &&
                          !initialdata?.photoDetails?.some(
                            (item) => item.id === section.id
                          ))
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {section?.fileName || getFileName(section?.photo) || "N/A"}
                  </h4>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Reference Detail
                  </p>
                  <h4
                    style={{
                      color:
                        (summary &&
                          initialdata &&
                          !initialdata?.photoDetails?.some(
                            (item) =>
                              item.referenceDetail === section.referenceDetail
                          )) ||
                        (isChanged(initialdata?.category, selectedAction) &&
                          photoDetails?.length > 1 &&
                          !discard &&
                          initialdata?.category == "photo" &&
                          !initialdata?.photoDetails?.some(
                            (item) => item.id == section.id
                          )) ||
                        (initialdata &&
                          !discard &&
                          !initialdata?.photoDetails?.some(
                            (item) => item.id === section.id
                          ))
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {section.referenceDetail || "N/A"}
                  </h4>
                </div>
              ))}
            </div>
          </div>
        )}
    </div>
  );
};

export default WorkinstructionSummarypage;
