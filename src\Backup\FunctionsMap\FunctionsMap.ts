import {
  project,
  SubtasklocDetail,
  Towerlocations,
  TowerlocationsDetails,
  TowerRoutes,
} from "../BillingBackup";
import {
  Designationmaster,
  MachinaryCategory,
  MachinaryDesignation,
  Manpowercategory,
  Manpowerdesignation,
  MaterialCategory,
  MaterialDesignation,
  ToolCategory,
  ToolDesignation,
  Departmentmaster,
} from "../MastersBackup";
import { TaskCategory, Taskmaster, Subtaskmaster } from "../TaskMasterBackup";

export const FuncionMap: { [key: string]: (...args: any[]) => void } = {
  TaskCategory,
  Taskmaster,
  Subtaskmaster,
  project,
  Towerlocations,
  ToolDesignation,
  ToolCategory,
  MaterialCategory,
  TowerlocationsDetails,
  MaterialDesignation,
  Manpowercategory,
  Manpowerdesignation,
  MachinaryCategory,
  MachinaryDesignation,
  SubtasklocDetail,

  Designationmaster,
  Departmentmaster,
};

export default FuncionMap;
