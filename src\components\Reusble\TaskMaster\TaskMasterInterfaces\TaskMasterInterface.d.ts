// interface for add subtask popup
interface AddSubTasksPopupProps {
  onClose: () => void;
  isEdit: boolean;
  onSubmit: (subtaskData: {
    name: string;
    Description: string;
    Unit: string;
    subtaskWeighatages: number | null;
    Tracking: string;
  }) => void;
  data?: {
    _id?: string;
    name: string;
    Description: string;
    Unit: string;
    subtaskWeighatages: number | null;
    Tracking: string;
  };
}

export interface SubtaskSummaryProps {
  height?: number;
  inputValues: Record<string, string>;
  initialState?: {
    _id?: string;
    name: string;
    Unit: string;
    subtaskWeighatages: string;
    Tracking: string;
    Description: string;
  };
  isEdit?: boolean;
  tracking?: string;
}

// interface for card
export interface CardProps {
  taskId?: string;
  path: string;
  category: TaskInterface;
  Callback?: () => void;
  isTask?: boolean;
  isApprove: boolean;
  id?: string;
}
// interface for carditems
export interface CardItemsProps {
  title: string;
  name: string | number;
}
export interface ControlPlanDetails {
  _id: string;
  description: string;
}

// interface for controlplanpopup
export interface ControlPlanPopupProps {
  onSubmit: (data: ControlPlanDetails) => void;
  onCancel: () => void;
  isEdit?: boolean;
  initialData?: ControlPlanDetails;
}
// interface for fmeapopup
interface FmeaPopupProps {
  onCancel: () => void;
  onSubmit: (data: {
    _id: number | string;
    Description: string;
    solution: string;
    severity: string;
  }) => void;
  initialData?: {
    _id: number | string;
    Description: string;
    solution: string;
    severity: number | string;
  };
}

// interface for reporting level

export interface ReportingLevelProps {
  key: any;
  isEdit: boolean;
  setReporterAdded: React.Dispatch<React.SetStateAction<boolean>>;
  reportedData: reportedDataProp;
  onAddLevelData?: (id: any) => void;
  onAddLevel?: () => void;
  onDeleteRoleName?: (
    levelIndex: any,
    designationId: any,
    deparmentId: any
  ) => void;
  onDeleteReportedData?: (index: any) => void;
  closeDialogBox?: () => void;
}
type reportedDataProp = {
  _id?: string;
  Level: number;
  designationId: { _id?: string; itemtype?: string; name: string }[];
}[];
// interface for tooltriggertip

interface TooltipProps {
  content: string;
  activeTip?: string;
  handleClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  isTimeInterval?: boolean;
}
// interface for triggertip
export interface TriggerToolTipProps {
  label: string;
  onClick?: (event: any) => void;
  handleDelete?: (item: any) => void;
  style?: React.CSSProperties;
  className?: string;
  className2?: string;
  isEdit?: boolean;
  activeTip?: string;
  data?: requiredthings[];
  icon?: React.ReactNode;
  isPllaning?: boolean;
  isActive?: boolean;
}
// interface for workinstructionpopup
export interface WIopupProps {
  onCancel: () => void;
  onSubmit: (data: {
    id?: string;
    description: string;
    file: { name: string; type: string } | null;
    category: string;
    photoDetails: PhotoSection[];
  }) => void;
  startWithPhotoCheckboxPage?: boolean;
  isEdit?: boolean;
  initialData?: {
    id?: string;
    description: string;
    file: { name: string; type: string } | null;
    category: string;
    photoDetails: PhotoSection[];
  };
}

export interface PhotoSection {
  id: number;
  photo: string | null;
  referenceDetail: string;
}
