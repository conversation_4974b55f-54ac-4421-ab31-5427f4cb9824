import styles from "../Styles/TowerDetailCard.module.css";

interface TowerCardItemsProps {
  icon?: JSX.Element;
  title: string;
  num: string | number;
}

export function TowerCardItems({ icon, title, num, titleColor }: TowerCardItemsProps & { titleColor?: string }) {
  return (
    <div className={styles.towercarditem_container}>
      <div className={styles.towercarditem_content}>
        <div className={styles.towercarditem_details}>
          {icon &&<div className={styles.towercarditem_icon}>{icon}</div>}
          <p  className={`${styles.towercarditem_head} small_text_p`} style={{ color: titleColor }}>{title}</p>
          <p className={styles.towercarditem_num}>{num}</p>
        </div>
      </div>
    </div>
  );
}
