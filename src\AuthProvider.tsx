import { createContext, useContext, ReactNode, useEffect } from "react";

import { RootState } from "./redux/store";
import { useAppDispatch, useAppSelector } from "./redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { useCheckAuthQuery, useLoginMutation } from "./redux/api/Modules/Auth/Authapi";
import { setCredentials } from "./redux/features/Modules/Auth/authSlice";
import { DiDatabase } from "react-icons/di";


interface Department {
  _id: string;
  name: string;
}

interface Designation {
  _id: string;
  name: string;
  roleId : string;
}

interface User {
  employeeId: string;
  name: string;
  departmentId: Department;
  designationId: Designation;
  phoneNumber: string;
  email: string;
}

interface AuthContextValue {
  login?: (employeeId: string, password: string) => Promise<void>;
  user: User | null;
  isAuthenticated: boolean;
  isFetching:boolean;
}

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const dispatch=useAppDispatch();
const {data,error,isFetching}=useCheckAuthQuery();
  const [loginTrigger] = useLoginMutation();
  const user = useAppSelector((state: RootState) => state.auth.user);
  const isAuthenticated = useAppSelector(
    (state: RootState) => state.auth.isAuthenticated
  );

  const login = async (employeeId: string, password: string) => {
    try {
      const response = await loginTrigger({ employeeId, password }).unwrap();
      return response;
   
    } catch (error) {
      console.error("Login failed:", error);
      return error;
    }
  };

  useEffect(()=>{
    

      if ((data && data.data) ) {
   
        const user:any = data.data; 
        dispatch(setCredentials({ user, isAuthenticated: true }));

        const event = new Event('login');
        window.dispatchEvent(event); 
      } else {
        console.error("No user data found");
      }
  },[isFetching])

  useEffect(()=>{
    if(!navigator.onLine){
      dispatch(setCredentials({user:"user",  isAuthenticated: true }));
    }
  },[])


  return (
    <AuthContext.Provider value={{ login, user, isAuthenticated,isFetching }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
