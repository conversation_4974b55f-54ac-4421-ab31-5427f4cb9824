import { Outlet } from "react-router-dom";
import styles from "./Styles/Category.module.css";
import CategoryView from "../TaskMasterNavbar/TaskMasterNav";
import { useDispatch } from "react-redux";
import { useRef } from "react";

const Category = () => {
  const dispatch = useDispatch();
  const outletRef=useRef<HTMLDivElement>(null);

  return (
    <div className={styles.taskContainer}>
      <div className={styles.taskcreation_header}>
        <CategoryView />
      </div>
      <Outlet />
    </div>
  );
};

export default Category;
