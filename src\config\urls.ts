// export const url = "http://*************/testing/api/v1";
export const env_type = import.meta.env.VITE_ENV_TYPE as
  | "development"
  | "main"
  | "testing"
  | "training"
  | "local";

const PORT = import.meta.env.VITE_PORT;
export const ip_address = import.meta.env.VITE_IP_ADDRESS as string;

export const url =
  env_type === "main"
    ? "https://www.ayrusnoc.com/api/v1"
    : env_type === "local"
    ? `http://192.168.1.${ip_address}:3000/api/v1`
    : `https://www.suryacon.net/${env_type}/api/v1`;

export const image_url = `http://localhost:${PORT}`;

// export const url='http://localhost:3000/training/api/v1'
export const taskmaster = "taskmaster";
export const billing = "billingmaster";
