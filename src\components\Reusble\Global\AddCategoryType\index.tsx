import { FC, useCallback, useEffect, useRef, useState } from "react";
import Button from "../Button";
import { CloseIcon, SuccessIcon } from "../../../../assets/icons";
import SearchBar from "../SearchBar";
import TargetBadge from "../TargetBadge/TargetBadge";
import styles from "./Styles/addcategorytype.module.css";
import {
  AddTaskPropType,
  TaskDataType,
} from "../GlobalInterfaces/GlobalInterface";
import { RootState } from "../../../../redux/store";
import { useSelector } from "react-redux";
import { initializeDatabase } from "../../../../functions/functions";
import { setTypeSearchKey } from "../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import { useAppDispatch } from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { useParams } from "react-router-dom";
import { set } from "lodash";
const AddCategoryType: FC<AddTaskPropType> = ({
  modelname = "",
  isStepForm = false,
  data,
  title = "",
  isUnit = false,
  isColorChange = false,
  label = " ",
  placeholder = "Search",
  onSelect,
  onClose,
  initialSelected = [],
  singleSelected = false,
  brandType = false,
  grade = false,
  primaryLabel = "",
  primaryLabel2 = "",
  textWidth = "",
}) => {
  console.log(label, "modelname");
  console.log(initialSelected, data, "these are initialselected subtasks");
  console.log(initialSelected, "these are initialselected>>");
  const [isClosing, setIsClosing] = useState<boolean>(false);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [secondselectedCategoryes, setsecondselectedCategoryes] = useState<
    string[]
  >([]);
  // const [searchTerm, setSearchTerm] = useState<string>("");
  const [showSummary, setShowSummary] = useState<boolean>(false);
  const [showDiscard, setShowDiscard] = useState<boolean>(false);
  const [showBrand, setShowBrand] = useState<boolean>(false);
  const [showGrade, setShowGrade] = useState<boolean>(false);
  const [shake, setShake] = useState(false);
  const [apiData, setApiData] = useState([]);
  const [openedCategory, setOpenedCategory] = useState<any>("");
  const searchKey = useSelector(
    (state: RootState) => state.taskMaster.typeSearchKey
  );
  // const { departmentId } = useParams();
  const dispatch = useAppDispatch();
  const [currentstep, setCurrentStep] = useState(1);
  const [SelectedSecondCategoriesFull, setSelectedSecondCategoriesFull] =
    useState<any>([]);
  const [StepfomData, setStepFormData] = useState<any>([]);
  console.log(initialSelected, "initialselectedishere");
  // console.log('daaattaa>>>',data)
  // picking up taskdata from redux to hightlight the things which are added in the task
  const TaskData = useSelector(
    (state: RootState) =>
      state.taskForm.currentSubtaskData || {
        name: "",
        Unit: "",
        description: "",
        subtaskWeighatages: 0,
        Tracking: "",
        MaterialId: [],
        ToolId: [],
        MachinaryId: [],
        ManpowerId: [],
        Adminid: [],
        AssigneeId: [],
        Reporter: [],
        MethodId: {
          work_instruction_id: [],
          task_closing_requirement: [],
          Controlplan: [],
        },
      }
  );

  console.log(
    "selected categrry",
    selectedCategories,
    SelectedSecondCategoriesFull,
    secondselectedCategoryes
  );
  console.log("search key", searchKey);

  // Handle close button click
  const onCloseHandler = () => {
    if (showSummary || selectedCategories.length > 0) {
      setShowDiscard(true);
      dispatch(setTypeSearchKey(""));
      showDiscard ? setShowDiscard(false) : setShowSummary(false);
    } else {
      handleClose();
    }
  };

  // Handle actual close action
  const handleClose = () => {
    setIsClosing(true);
    setTimeout(onClose, 400);
    dispatch(setTypeSearchKey(""));
  };

  // useEffect(() => {
  //   setsecondselectedCategoryes([]);
  // }, [selectedCategories]);

  // function to call api according to the selected category if it is step form
  const getAllDesignationBycategoryId = async (
    id: string,
    modelname: string,
    idName: string,
    isTool: boolean
  ) => {
    console.log(id, modelname, idName, "idandmodelname");

    const dbName = await initializeDatabase(modelname);
    const response = await window.electron.getDesignationsData({
      dbName,
      id,
      idName,
      isTool,
    });
    console.log(
      response,
      "this is response of designatinsselectedCategoriesandsecondselectedCategoryes22",
      id
    );
    const data = response?.map((e: any) => ({
      id: e?._id,
      category: e?.name,
      unit: e?.unit,
      parentId: e[idName],
    }));
    setApiData(data);
    console.log(data, "datafromapi");
  };

  const toggleSecondCategory = (categoryId: string) => {
    setsecondselectedCategoryes((prev) =>
      singleSelected
        ? prev.includes(categoryId)
          ? []
          : [categoryId]
        : prev.includes(categoryId)
        ? prev?.filter((id) => id !== categoryId)
        : [...prev, categoryId]
    );
    console.log(
      selectedCategories,
      secondselectedCategoryes,
      "selectedCategories"
    );
  };

  // Toggle category selection
  const toggleCategory = (categoryId: string) => {
    console.log(
      "selectedCategoriesandsecondselectedCategoryes22 hello",
      categoryId
    );
    setApiData([]);
    if (isStepForm) {
      setCurrentStep(2);
      dispatch(setTypeSearchKey(""));
    }

    // console.log(toggleCategory, "togglecategory");
    if (!isStepForm) {
      setSelectedCategories((prev) =>
        singleSelected || title === "Add Task Manager"
          ? prev.includes(categoryId)
            ? []
            : [categoryId]
          : prev.includes(categoryId)
          ? prev?.filter((id) => id !== categoryId)
          : [...prev, categoryId]
      );
    }

    if (isStepForm && currentstep == 1) {
      console.log(modelname, "modelnameforsecondcategory");
      let newModelName = ""; // Initialize with a default value
      let idName = "";
      let isTool;
      switch (modelname) {
        case "MaterialCategory":
          newModelName = "MaterialDesignation";
          idName = "materialCategoryId";
          break;
        case "machinaryCategory":
          newModelName = "MachinaryDesignation";
          idName = "machineryCategoryId";
          break;
        case "ToolCategory":
          idName = "categoryId";
          isTool = true;
          newModelName = "ToolDesignation";
          break;
        case "Manpowercategory":
          idName = "manpowerCategoryId";
          newModelName = "Manpowerdesignation";
          break;
        case "designationdetails":
          idName = "DepartmentId";
          newModelName = "Designationmaster";
          break;
        case "taskManagerDataDetail":
        case "reporterDataDetail":
        case "assigneeDataDetail":
          idName = "DepartmentId";
          newModelName = "Designationmaster";
          break;
        case "allDesignations":
          idName = "DepartmentId";
          newModelName = "Designationmaster";
          isTool = false;
          break;
        // Add more cases here if needed
        default:
          newModelName = ""; // Default value
      }
      if (newModelName) {
        console.log("in modename");
        const ans = getAllDesignationBycategoryId(
          categoryId,
          newModelName,
          idName,
          Boolean(isTool)
        );
        console.log("ans herr", ans);
      }
    }
  };
  const fetchSearchedData = async (searchKey: any, tablename: any) => {
    console.log(searchKey, tablename, "thiis is search key and table nam");
    let response;
    let dbName;
    let dbName2;
    switch (tablename) {
      case "MaterialCategory":
        dbName = await initializeDatabase("MaterialCategory");
        dbName2 = await initializeDatabase("MaterialDesignation");
        response = await window.electron.getDataBySearchingForAddMaterialForm({
          searchTerm: searchKey,
          dbName1: dbName,
          dbName2: dbName2,
          categorykey: "materialCategoryId",
        });
        break;
      case "Manpowercategory":
        dbName = await initializeDatabase("Manpowercategory");
        dbName2 = await initializeDatabase("Manpowerdesignation");
        response = await window.electron.getDataBySearchingForAddMaterialForm({
          searchTerm: searchKey,
          dbName1: dbName,
          dbName2: dbName2,
          categorykey: "manpowerCategoryId",
        });
        break;
      case "ToolCategory":
        dbName = await initializeDatabase("ToolCategory");
        dbName2 = await initializeDatabase("ToolDesignation");
        response = await window.electron.getDataBySearchingForAddMaterialForm({
          dbName,
          searchTerm: searchKey,
          dbName1: dbName,
          dbName2: dbName2,
          categorykey: "categoryId",
          isTool: true,
        });
        break;
      case "machinaryCategory":
        dbName = await initializeDatabase("MachinaryCategory");
        dbName2 = await initializeDatabase("MachinaryDesignation");

        response = await window.electron.getDataBySearchingForAddMaterialForm({
          searchTerm: searchKey,
          dbName1: dbName,
          dbName2: dbName2,
          categorykey: "machineryCategoryId",
        });
        break;
      case "designationdetails":
        dbName = await initializeDatabase("Departmentmaster");
        dbName2 = await initializeDatabase("Designationmaster");

        response = await window.electron.getDataBySearchingForAddMaterialForm({
          searchTerm: searchKey,
          dbName1: dbName,
          dbName2: dbName2,
          categorykey: "DepartmentId",
        });
        break;
      case "taskManagerDataDetail":
      case "reporterDataDetail":
      case "assigneeDataDetail":
        dbName = await initializeDatabase("Departmentmaster");
        dbName2 = await initializeDatabase("Designationmaster");

        response = await window.electron.getDataBySearchingForAddMaterialForm({
          searchTerm: searchKey,
          dbName1: dbName,
          dbName2: dbName2,
          categorykey: "DepartmentId",
        });
        break;
      case "departmentdetails":
        dbName = await initializeDatabase("Departmentmaster");
        response = await window.electron.getDataBySearchingForAddMaterialForm({
          dbName,
        });
        break;
      case "allDesignations":
        dbName = await initializeDatabase("Departmentmaster");
        dbName2 = await initializeDatabase("Designationmaster");
        console.log("response here:>>", searchKey, dbName, dbName2);
        response = await window.electron.getDataBySearchingForAddMaterialForm({
          searchTerm: searchKey,
          dbName1: dbName,
          dbName2: dbName2,
          categorykey: "DepartmentId",
        });
        // response=response.docs
        console.log("response here:>>", response);
        break;

      default:
        console.log("Invalid table name");
        response = null;
    }
    console.log(response?.categories, "this is response for selecte option");
    // if(tablename==="allDesignations"){
    //    return(
    //       response?.docs?.map((e: { _id: string; name: string }) => ({
    //       id: e._id,
    //       category: e.name,
    //     })) || []
    //    )
    // }
    return (
      response?.categories?.map((e: { _id: string; name: string }) => ({
        id: e._id,
        category: e.name,
      })) || []
    );
  };
  useEffect(() => {
    const fetchData = async () => {
      if (isStepForm) {
        const newData = await fetchSearchedData(searchKey, modelname);
        console.log(newData, "these is new data ");
        setStepFormData(newData);
      }
    };
    fetchData();
    // console.log(searchTerm, "thsi is serach term");
  }, [searchKey]);

  // Filter data based on search term and exclude initialSelected items
  // console.log(searchTerm?.length, "this is search term jaggasd");
  const filteredData = !isStepForm
    ? data.filter(
        (item) =>
          item?.category
            ?.toLowerCase()
            .includes(searchKey?.toLowerCase().trim() || "") &&
          (isUnit
            ? !initialSelected.some((e) => e?.name === item?.category)
            : !initialSelected.some((e) => e?._id === item?.id))
      )
    : (searchKey!?.length > 0 &&
        StepfomData?.filter((item: { category: string; id: string }) =>
          isUnit
            ? !initialSelected.some((e) => e?.name === item?.category)
            : !initialSelected.some((e) => e?._id === item?.id)
        )) ||
      data?.filter((item: { category: string; id: string }) =>
        isUnit
          ? !initialSelected.some((e) => e?.name === item?.category)
          : !initialSelected.some((e) => e?._id === item?.id)
      );
  const filteredapiData = apiData?.filter(
    (item: { category: string; id: string }) =>
      item?.category?.toLowerCase().includes(searchKey!.toLowerCase().trim()) &&
      (isUnit
        ? !initialSelected.some((e) => e?.name === item?.category)
        : !initialSelected.some((e) => e?._id === item?.id))
  );

  // Get selected items
  const selectedItems = data?.filter((item) =>
    selectedCategories.includes(item.id)
  );
  const secondSelectedItems = apiData?.filter(
    (item: { category: string; id: string }) =>
      secondselectedCategoryes.includes(item?.id)
  );

  // Handle add button click
  const handleAdd = useCallback(() => {
    console.log(
      selectedCategories,
      secondselectedCategoryes,
      "selectedCategoriesandsecondselectedCategoryes"
    );
    if (!isStepForm) {
      if (selectedCategories.length === 0) {
        setShake(true);
        setTimeout(() => setShake(false), 600);
        return;
      }
    }

    if (isStepForm) {
      console.log(
        openedCategory,
        SelectedSecondCategoriesFull,
        SelectedSecondCategoriesFull?.filter(
          (el: any) => el.parentId == openedCategory?.id
        ),
        "these is opened category in selected items"
      );
      if (
        SelectedSecondCategoriesFull?.filter(
          (el: any) => el.parentId == openedCategory?.id
        )?.length === 0
      ) {
        setShake(true);
        setTimeout(() => setShake(false), 600);
        return;
      }
    }

    if (brandType && !showBrand) {
      setShowBrand(true);
      return;
    }

    if (grade && showBrand) {
      setShowGrade(true);
      return;
    }

    setShowSummary(true);
  }, [
    selectedCategories,
    secondselectedCategoryes,
    brandType,
    openedCategory,
    grade,
    showBrand,
    SelectedSecondCategoriesFull,
  ]);

  // Handle submit action
  const handleSubmit = () => {
    if (isStepForm) {
      onSelect(SelectedSecondCategoriesFull as any, label, selectedItems);
    } else {
      onSelect(selectedItems as any, label, []);
    }

    // onClose();
    handleClose();
  };

  // Handle search input change
  // const handleSearchChange = (e: any) => {
  //   setSearchTerm(e);
  // };

  const [items, setItems] = useState([]);
  const labelToKeymap = (label: string, item: any) => {
    switch (label) {
      case "Machinery":
        if (TaskData["MachinaryId"]?.find((e) => e._id == item.id)) {
          return true;
        }
        break;
      case "Material":
        if (TaskData["MaterialId"]?.find((e) => e._id == item.id)) {
          return true;
        }

        break;
      case "Manpower":
        if (TaskData["ManpowerId"]?.find((e) => e._id == item.id)) {
          return true;
        }
        break;
      case "Tool":
        if (TaskData["ToolId"]?.find((e) => e._id == item.id)) {
          return true;
        }
        break;
      default:
        return false;
    }
  };
  useEffect(() => {
    const newfilteredItem =
      filteredData &&
      filteredData?.map((e: { id: string; category: string }) => {
        return labelToKeymap(label, e)
          ? {
              ...e,
              isSelected: true,
            }
          : {
              ...e,
              isSelected: false,
            };
      });
    console.log("items>>", items);
    const sortedItems =
      newfilteredItem &&
      [...newfilteredItem]?.sort((a, b) => {
        if (a.isSelected && !b.isSelected) return -1;
        if (!a.isSelected && b.isSelected) return 1;
        return 0;
      });

    // Check if the sorted items are different from the current items before setting state
    if (JSON.stringify(sortedItems) !== JSON.stringify(items)) {
      setItems(sortedItems);
    }
  }, [filteredData, items]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isClosing) return;
    if (e.key === "Enter" && e.shiftKey) return;

    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      if (!showDiscard && !showSummary) {
        if (selectedCategories.length == 0) {
          setShake(true);
          setTimeout(() => setShake(false), 600);
          return;
        } else {
          setShowSummary(true);
          dispatch(setTypeSearchKey(""));
        }
      }
      if (showDiscard) {
        handleClose();
      } else if (showSummary) {
        handleSubmit();
      } else if (isStepForm && currentstep === 1) {
        if (selectedCategories.length === 0) {
          setShake(true);
          setTimeout(() => setShake(false), 600);
          return;
        }
        setCurrentStep(2);
        dispatch(setTypeSearchKey(""));
      } else if (isStepForm && currentstep === 2) {
        handleAdd();
      } else {
        handleAdd();
      }
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();

      if (showDiscard) {
        setShowDiscard(false);
        dispatch(setTypeSearchKey(""));
      } else if (showSummary) {
        setShowSummary(false);
        dispatch(setTypeSearchKey(""));
      } else if (isStepForm && currentstep === 2) {
        setCurrentStep(1);
        dispatch(setTypeSearchKey(""));
      } else {
        onCloseHandler();
      }
    }
  };

  const formRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (showDiscard || showSummary) {
      if (formRef.current) {
        formRef?.current?.focus();
      }
    }
  }, [showDiscard, showSummary]);

  useEffect(() => {
    if (isStepForm && SelectedSecondCategoriesFull) {
      console.log(SelectedSecondCategoriesFull, "tejhse aer full details");
      const parentIds = SelectedSecondCategoriesFull?.map(
        (e: { id: string; category: string; parentId: string }) => e?.parentId
      );
      console.log(parentIds, "these are th eparent ddasdf");
      setSelectedCategories(Array.from(new Set([...parentIds])));
    }
  }, [secondselectedCategoryes]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // console.log('outisde click tools',inputValues)
      if (formRef.current && !formRef.current.contains(event.target as Node)) {
        console.log("outisde click tools inside if");
        console.log(
          "selectedCategoriesandsecondselectedCategoryes22",
          selectedCategories,
          SelectedSecondCategoriesFull
        );
        // const isEmp=isEmpty(inputValues,file);
        // const isEmp2=isEmpty2(photoUploadSections?.[0]);
        // console.log('outisde click tools inside if >>isEmp:',isEmp)
        console.log("secondselectedCategoryes22 currentstep>>", currentstep);
        if (selectedCategories.length === 0 && currentstep <= 1) {
          handleClose();
        }
        // if ( isEmp && isEmp2) {
        //   // setIsClosing(true);
        //   // setTimeout(onClose, 400);

        //   console.log('outisde click tools inside inner if')

        //   handleClose();
        // }
        // if(!hasChanges() && !showDiscardConfirmation && !isSummaryPage){
        //   handleClose()
        // }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dispatch, selectedCategories, SelectedSecondCategoriesFull]);

  return (
    <div
      className={`${styles.add_category_type_maindiv} ${
        isClosing ? styles.closing : ""
      }`}
      ref={formRef}
      tabIndex={0}
      onKeyDown={handleKeyDown}
    >
      <div className={styles.categorypopupheading}>
        <h3
          style={{
            textAlign: "center",
            color: showDiscard
              ? "var(--warning_color)"
              : "var(--primary_color)",
            maxWidth: "85%",
          }}
        >
          {showDiscard &&
            isStepForm &&
            secondselectedCategoryes?.length == 0 && (
              <>
                <>
                  {(() => {
                    switch (modelname) {
                      case "MaterialCategory":
                        return (
                          <>Are you sure you want to discard these changes?</>
                        );
                      case "machinaryCategory":
                        return (
                          <>Are you sure you want to discard these changes?</>
                        );
                      case "ToolCategory":
                        return (
                          <>Are you sure you want to discard these changes?</>
                        );
                      case "Manpowercategory":
                        return (
                          <> Are you sure you want to discard these changes?</>
                        );
                      case "departmentdetails":
                        return (
                          <>Are you sure you want to discard these changes?</>
                        );
                      case "designationdetails":
                      case "allDesignations":
                        return (
                          <>
                            Are you sure you want to discard this Designation?
                          </>
                        );
                      case "taskManagerDataDetail":
                      case "reporterDataDetail":
                      case "assigneeDataDetail":
                        return (
                          <>Are you sure you want to discard this Department?</>
                        );
                      default:
                        return <>Unknown </>;
                    }
                  })()}
                </>
              </>
            )}
          {showDiscard &&
            isStepForm &&
            secondselectedCategoryes?.length > 0 && (
              <>
                <>
                  {(() => {
                    switch (modelname) {
                      case "MaterialCategory":
                        return (
                          <>
                            Are you sure you want to discard{" "}
                            {secondselectedCategoryes?.length > 1
                              ? "these Materials"
                              : "this Material"}
                            ?
                          </>
                        );
                      case "machinaryCategory":
                        return (
                          <>
                            Are you sure you want to discard{" "}
                            {secondselectedCategoryes?.length > 1
                              ? "this Machinery"
                              : "this Machine"}
                            ?
                          </>
                        );
                      case "ToolCategory":
                        return (
                          <>
                            Are you sure you want to discard{" "}
                            {secondselectedCategoryes?.length > 1
                              ? "these Tools"
                              : "this Tool"}
                            ?
                          </>
                        );
                      case "Manpowercategory":
                        return (
                          <>
                            {" "}
                            Are you sure you want to discard{" "}
                            {secondselectedCategoryes?.length > 1
                              ? "these Manpower"
                              : "this Manpower"}
                            ?
                          </>
                        );
                      case "departmentdetails":
                        return (
                          <>
                            Are you sure you want to discard{" "}
                            {secondselectedCategoryes?.length > 1
                              ? "these Designations"
                              : "this Designation"}
                            ?
                          </>
                        );
                      case "designationdetails":
                        return (
                          <>
                            Are you sure you want to discard{" "}
                            {secondselectedCategoryes?.length > 1
                              ? "these Designations"
                              : "this Designation"}
                            ?
                          </>
                        );
                      case "taskManagerDataDetail":
                        return (
                          <>
                            Are you sure you want to discard{" "}
                            {secondselectedCategoryes?.length > 1
                              ? "these Task Managers"
                              : "this Task Manager"}
                            ?
                          </>
                        );
                      case "assigneeDataDetail":
                        return (
                          <>
                            Are you sure you want to discard{" "}
                            {secondselectedCategoryes?.length > 1
                              ? "these Assignees"
                              : "this Assignee"}
                            ?
                          </>
                        );
                      case "reporterDataDetail":
                        return (
                          <>
                            Are you sure you want to discard{" "}
                            {secondselectedCategoryes?.length > 1
                              ? "these Reporters"
                              : "this Reporter"}
                            ?
                          </>
                        );
                      case "allDesignations":
                        return (
                          <>Are you sure you want to discard this Designation</>
                        );

                      default:
                        return <>Unknown </>;
                    }
                  })()}
                </>
              </>
            )}
          {showSummary && isStepForm && (
            <>
              <>
                {(() => {
                  switch (modelname) {
                    case "MaterialCategory":
                      return (
                        <>
                          Are you sure you want to add{" "}
                          {secondselectedCategoryes?.length > 1
                            ? "these Materials"
                            : "this Material"}
                          ?
                        </>
                      );
                    case "machinaryCategory":
                      return (
                        <>
                          Are you sure you want to add{" "}
                          {secondselectedCategoryes?.length > 1
                            ? "this Machinery"
                            : "this Machine"}
                          ?
                        </>
                      );
                    case "ToolCategory":
                      return (
                        <>
                          Are you sure you want to add{" "}
                          {secondselectedCategoryes?.length > 1
                            ? "these Tools"
                            : "this Tool"}
                          ?
                        </>
                      );
                    case "Manpowercategory":
                      return (
                        <>
                          {" "}
                          Are you sure you want to add{" "}
                          {secondselectedCategoryes?.length > 1
                            ? "these Manpower"
                            : "this Manpower"}
                          ?
                        </>
                      );
                    case "departmentdetails":
                      return (
                        <>
                          Are you sure you want to add{" "}
                          {secondselectedCategoryes?.length > 1
                            ? "these Designations"
                            : "this Designation"}
                          ?
                        </>
                      );
                    case "taskManagerDataDetail":
                      return (
                        <>
                          Are you sure you want to add{" "}
                          {secondselectedCategoryes?.length > 1
                            ? "these Task Managers"
                            : "this Task Manager"}
                          ?
                        </>
                      );
                    case "reporterDataDetail":
                      return (
                        <>
                          Are you sure you want to add{" "}
                          {secondselectedCategoryes?.length > 1
                            ? "these Reporters"
                            : "this Reporter"}
                          ?
                        </>
                      );
                    case "assigneeDataDetail":
                      return (
                        <>
                          Are you sure you want to add{" "}
                          {secondselectedCategoryes?.length > 1
                            ? "these Assignees"
                            : "this Assignee"}
                          ?
                        </>
                      );
                    case "designationdetails":
                      return (
                        <>
                          Are you sure you want to add{" "}
                          {secondselectedCategoryes?.length > 1
                            ? "these Designations"
                            : "this Designation"}
                          ?
                        </>
                      );
                    case "allDesignations":
                      return (
                        <>
                          Are you sure you want to add{" "}
                          {secondselectedCategoryes?.length > 1
                            ? "these Designations"
                            : "this Designation"}
                          ?
                        </>
                      );
                    default:
                      return <>Unknown </>;
                  }
                })()}
              </>
            </>
          )}
          {isStepForm && !showDiscard && !showSummary && primaryLabel2}
          {!isStepForm && (
            <>
              {showSummary || showDiscard
                ? showDiscard
                  ? `Are you sure you want to discard ${
                      selectedItems.length > 1 ? "these" : "this"
                    } ${
                      label === "Machinery" && selectedItems.length === 1
                        ? "Machine"
                        : label
                    }${
                      selectedItems.length > 1 &&
                      label !== "Manpower" &&
                      label !== "Machinery"
                        ? "s"
                        : ""
                    }?`
                  : `Are you sure you want to add ${
                      selectedItems.length > 1 ? "these" : "this"
                    } ${
                      label === "Machinery" && selectedItems.length === 1
                        ? "Machine"
                        : label
                    }${
                      selectedItems.length > 1 &&
                      label !== "Manpower" &&
                      label !== "Machinery"
                        ? "s"
                        : ""
                    }?`
                : showBrand
                ? showGrade
                  ? "Grade"
                  : "Brand"
                : title || "Select Categories"}
            </>
          )}
        </h3>
      </div>
      <div className={styles.smallcategorypopupheading}>
        <p
          style={{
            textAlign: "center",
            color: showDiscard
              ? "var(--warning_color)"
              : "var(--primary_color)",
            maxWidth: "85%",
          }}
        >
          {isStepForm &&
            (currentstep == 1 || currentstep == 2) &&
            !showDiscard && (
              <div className={styles.brand_top_container}>
                <div className={styles.brand_circle}>
                  {(currentstep === 2 || showSummary) && <SuccessIcon />}
                </div>
                <div className={styles.brand_dottedline}></div>
                {!showSummary && !showDiscard && (
                  <div className={styles.brand_circle}></div>
                )}
                {showSummary && isStepForm && (
                  <div className={styles.brand_circle}>
                    <SuccessIcon />
                  </div>
                )}
              </div>
            )}

          {!showSummary && !showDiscard && currentstep == 1 && (
            <>{primaryLabel}</>
          )}
        </p>
      </div>
      {isStepForm && currentstep == 2 && !showDiscard && !showSummary && (
        <div className={styles.brand_grade_text}>
          <p>
            {(() => {
              switch (modelname) {
                case "MaterialCategory":
                  return <>Material Category</>;
                case "machinaryCategory":
                  return <>Machinery Category</>;
                case "ToolCategory":
                  return <>Tool Category</>;
                case "Manpowercategory":
                  return <>Manpower Category</>;
                case "departmentdetails":
                  return <>Department</>;
                case "designationdetails":
                  return <>Department</>;
                case "taskManagerDataDetail":
                case "reporterDataDetail":
                case "assigneeDataDetail":
                case "allDesignations":
                  return <>Department</>;
                default:
                  return <>Designation </>;
              }
            })()}
            :
          </p>
          <p style={{}}>{openedCategory?.category}</p>
        </div>
      )}
      <div className={styles.smallcategorypopupheading}>
        {" "}
        <p
          style={{
            textAlign: "center",
            color: showDiscard
              ? "var(--warning_color)"
              : "var(--primary_color)",
            maxWidth: "85%",
          }}
        >
          {isStepForm &&
          currentstep == 2 &&
          !showSummary &&
          !showDiscard &&
          (modelname == "taskManagerDataDetail" ||
            modelname == "reporterDataDetail" ||
            modelname == "allDesignations" ||
            modelname == "assigneeDataDetail") ? (
            <>Designation</>
          ) : (
            currentstep == 2 &&
            !showSummary &&
            !showDiscard && (
              <>
                {(() => {
                  switch (modelname) {
                    case "MaterialCategory":
                      return <>Materials</>;
                    case "machinaryCategory":
                      return <>Machinery</>;
                    case "ToolCategory":
                      return <>Tools</>;
                    case "Manpowercategory":
                      return <>Manpower</>;
                    case "departmentdetails":
                      return <>Department</>;
                    case "designationdetails":
                      return <>Designation</>;
                    default:
                      return <>Unknown </>;
                  }
                })()}
              </>
            )
          )}
        </p>
      </div>

      <div className={styles.popup_close_icon}>
        <button onClick={onCloseHandler} className={styles.closebtn_category}>
          <CloseIcon />
        </button>
      </div>

      {showSummary || showDiscard ? (
        isStepForm ? (
          <StepCategorySummary
            isstepform={isStepForm}
            modelname={modelname}
            secondSelectedItems={
              isStepForm ? SelectedSecondCategoriesFull : secondSelectedItems
            }
            selectedItems={selectedItems}
            title={label}
          />
        ) : (
          <CategorySummary
            isstepform={isStepForm}
            modelname={modelname}
            secondSelectedItems={
              isStepForm ? SelectedSecondCategoriesFull : secondSelectedItems
            }
            selectedItems={selectedItems}
            title={label}
          />
        )
      ) : (
        <>
          <div style={{ padding: "0rem 0.5rem 0rem", margin: "0.5rem" }}>
            {!showBrand && (
              <SearchBar
                placeholder={placeholder}
                height={"3.12rem"}
                isTypeForm={true}
                // onChange={handleSearchChange}
                // debounceDelay={1}
              />
            )}

            {showBrand && (
              <div className={styles.brand_container}>
                <div className={styles.brand_top_container}>
                  <div className={styles.brand_circle}>
                    <SuccessIcon />
                  </div>
                  <div
                    className={`${
                      showGrade
                        ? styles.brand_dottedline
                        : styles.brand_dottedline_gray
                    }`}
                  ></div>
                  <div
                    className={`${
                      showGrade ? styles.brand_circle : styles.brand_circle_gray
                    }`}
                  ></div>
                </div>

                <div className={styles.brand_bottom_container}>
                  <div className={styles.brand_grade_content}>
                    <div className={styles.brand_grade_text}>
                      <p>Machinery:</p>
                      <p>Tightening</p>
                    </div>
                    <div className={styles.brand_grade_text}>
                      <p>Brand:</p>
                      <p>Hercules</p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
          {currentstep == 1 && (
            <div className={styles.add_category_type_innerdiv}>
              {items?.map((item: any) => (
                <>
                  {isColorChange && labelToKeymap(label, item) && (
                    <li
                      key={item.id}
                      className={`${styles.category} ${styles.marginBottom} ${
                        selectedCategories.includes(item.id)
                          ? styles.selected
                          : ""
                      } ${shake ? styles.shake : ""}`}
                      style={{
                        border: "1px solid",
                        borderColor: selectedCategories.includes(item.id)
                          ? "#00596b"
                          : "#fff",
                        color: selectedCategories.includes(item.id)
                          ? "var(--primary_color)"
                          : "",
                        cursor: "pointer",
                      }}
                      onClick={() => {
                        setOpenedCategory(item);
                        toggleCategory(item.id);
                      }}
                    >
                      <div className={styles.category_text}>
                        {item.category}
                      </div>
                      {item.unit && (
                        <TargetBadge
                          backgroundColor="#F0F6F6"
                          valueTextClassName="small_text_p"
                          bubbletextClassName={"second_text_for_array"}
                          outerContainerClassName="material_popup"
                          value={item.unit}
                          isArray={Array?.isArray(item.unit) ? true : false}
                          maxlength={2}
                          {...(item?.unit?.length > 2 && {
                            bubbleValue: `${item.unit.length - 2}`,
                          })}
                        />
                      )}
                    </li>
                  )}
                  {!isColorChange && (
                    <li
                      key={item.id}
                      className={`${styles.category} ${styles.marginBottom} ${
                        selectedCategories.includes(item.id)
                          ? styles.selected
                          : ""
                      } ${shake ? styles.shake : ""}`}
                      style={{
                        border: "1px solid",
                        borderColor: selectedCategories.includes(item.id)
                          ? "#00596b"
                          : "#fff",
                        color: selectedCategories.includes(item.id)
                          ? "var(--primary_color)"
                          : "",
                        cursor: "pointer",
                      }}
                      onClick={() => {
                        setOpenedCategory(item);
                        toggleCategory(item.id);
                      }}
                    >
                      <div
                        style={{ width: textWidth }}
                        className={styles.category_text}
                      >
                        {item.category}
                      </div>
                      {item.unit && (
                        <TargetBadge
                          backgroundColor="#F0F6F6"
                          valueTextClassName="small_text_p"
                          bubbletextClassName={"second_text_for_array"}
                          outerContainerClassName="material_popup"
                          value={item.unit}
                          isArray={Array?.isArray(item.unit) ? true : false}
                          maxlength={2}
                          {...(item?.unit?.length > 2 && {
                            bubbleValue: `${item.unit.length - 2}`,
                          })}
                        />
                      )}
                      {SelectedSecondCategoriesFull?.filter(
                        (el: any) => el.parentId == item.id
                      )?.length > 0 && (
                        <div
                          className={styles.numeric_values}
                          style={{
                            color: "#F0F6F6",
                            backgroundColor: "#005968",
                          }}
                        >
                          {
                            SelectedSecondCategoriesFull?.filter(
                              (el: any) => el.parentId == item.id
                            )?.length
                          }
                        </div>
                      )}
                    </li>
                  )}
                </>
              ))}
            </div>
          )}
          {currentstep == 2 && (
            <div className={styles.add_category_type_innerdiv}>
              {filteredapiData?.map((item: any) => (
                <>
                  {isColorChange && labelToKeymap(label, item) && (
                    <li
                      key={item.id}
                      className={`${styles.category} ${styles.marginBottom} ${
                        secondselectedCategoryes.includes(item.id)
                          ? styles.selected
                          : ""
                      } ${shake ? styles.shake : ""}`}
                      style={{
                        border: "1px solid",
                        borderColor: secondselectedCategoryes.includes(item.id)
                          ? "#00596b"
                          : "#fff",
                        color: secondselectedCategoryes.includes(item.id)
                          ? "var(--primary_color)"
                          : "",
                        cursor: "pointer",
                      }}
                      onClick={() => {
                        // setSelectedSecondCategoriesFull((prev: any) =>
                        //   prev.some(
                        //     (selectedItem: any) => selectedItem.id === item.id
                        //   )
                        //     ? prev.filter(
                        //         (selectedItem: any) =>
                        //           selectedItem.id !== item.id
                        //       )
                        //     : [...prev, item]
                        // );

                        toggleSecondCategory(item.id);
                      }}
                    >
                      <div className={styles.category_text}>
                        {item.category}
                      </div>
                      {item.unit && (
                        <TargetBadge
                          backgroundColor="#F0F6F6"
                          valueTextClassName="small_text_p"
                          bubbletextClassName={"second_text_for_array"}
                          outerContainerClassName="material_popup"
                          value={item.unit}
                          isArray={Array?.isArray(item.unit) ? true : false}
                          maxlength={2}
                          {...(item?.unit?.length > 2 && {
                            bubbleValue: `${item.unit.length - 2}`,
                          })}
                        />
                      )}
                    </li>
                  )}
                  {!isColorChange && (
                    <li
                      key={item.id}
                      className={`${styles.category} ${styles.marginBottom} ${
                        secondselectedCategoryes.includes(item.id)
                          ? styles.selected
                          : ""
                      } ${shake ? styles.shake : ""}`}
                      style={{
                        border: "1px solid",
                        borderColor: secondselectedCategoryes.includes(item.id)
                          ? "#00596b"
                          : "#fff",
                        color: secondselectedCategoryes.includes(item.id)
                          ? "var(--primary_color)"
                          : "",
                        cursor: "pointer",
                      }}
                      onClick={() => {
                        setSelectedSecondCategoriesFull((prev: any) => {
                          if (singleSelected) {
                            return prev.some(
                              (selectedItem: any) => selectedItem.id === item.id
                            )
                              ? []
                              : [item];
                          } else {
                            return prev.some(
                              (selectedItem: any) => selectedItem.id === item.id
                            )
                              ? prev.filter(
                                  (selectedItem: any) =>
                                    selectedItem.id !== item.id
                                )
                              : [...prev, item];
                          }
                        });

                        toggleSecondCategory(item.id);
                      }}
                    >
                      <div className={styles.category_text}>
                        {item.category}
                      </div>
                      {item.unit && (
                        <TargetBadge
                          backgroundColor="#F0F6F6"
                          valueTextClassName="small_text_p"
                          outerContainerClassName="material_popup"
                          bubbletextClassName={"second_text_for_array"}
                          value={item.unit}
                          isArray={Array?.isArray(item.unit) ? true : false}
                          maxlength={2}
                          {...(item?.unit?.length > 2 && {
                            bubbleValue: `+${item.unit.length - 2}`,
                          })}
                        />
                      )}
                    </li>
                  )}
                </>
              ))}
            </div>
          )}
        </>
      )}

      <div className={styles.add_category_type_button_div}>
        {showSummary || showDiscard ? (
          <>
            <Button
              type="Cancel"
              Content={showDiscard ? "No" : "Back"}
              Callback={() => {
                showDiscard ? setShowDiscard(false) : setShowSummary(false);
                dispatch(setTypeSearchKey(""));
              }}
            />
            <Button
              type="Next"
              Content={showDiscard ? "Yes" : "Submit"}
              Callback={showDiscard ? handleClose : handleSubmit}
            />
          </>
        ) : isStepForm && currentstep == 1 ? (
          <>
            {selectedCategories.length > 0 && (
              <>
                <Button
                  type="Cancel"
                  Content="Cancel"
                  Callback={onCloseHandler}
                />
                <Button
                  type="Next"
                  Content="Add"
                  Callback={() => {
                    if (selectedCategories.length == 0) {
                      setShake(true);
                      setTimeout(() => setShake(false), 600);
                      return;
                    } else {
                      setShowSummary(true);
                      dispatch(setTypeSearchKey(""));
                    }
                  }}
                />
              </>
            )}
          </>
        ) : (
          <>
            <Button
              type="Cancel"
              Content={`${isStepForm && currentstep == 2 ? "Back" : "Cancel"}`}
              Callback={
                isStepForm && currentstep == 2
                  ? () => {
                      if (modelname == "taskManagerDataDetail") {
                        setSelectedCategories([]);
                        setSelectedSecondCategoriesFull([]);
                        setsecondselectedCategoryes([]);
                      }
                      setCurrentStep(1);
                      dispatch(setTypeSearchKey(""));
                    }
                  : onCloseHandler
              }
            />
            <Button type="Next" Content="Add" Callback={handleAdd} />
          </>
        )}
      </div>
    </div>
  );
};

const CategorySummary: FC<{
  selectedItems: TaskDataType[];
  title: string;
  isstepform?: boolean;
  modelname?: string;
  secondSelectedItems?: TaskDataType[];
}> = ({ selectedItems, title, secondSelectedItems, isstepform }) => {
  console.log(selectedItems, secondSelectedItems, "selectedItemsforsummary");
  return (
    <div className={styles.summaryDataOuterDiv}>
      <div className={styles.summaryDivData}>
        <div className={styles.summaryDataContent}>
          {!isstepform && (
            <p
              style={{ color: "var(--text-black-60)", marginTop: "0.5rem" }}
              className="p_tag_14px"
            >
              {`${
                title === "Machinery" && selectedItems.length === 1
                  ? "Machine"
                  : title
              }${
                selectedItems.length > 1 &&
                title !== "Manpower" &&
                title !== "Machinery"
                  ? "s"
                  : ""
              }`}
            </p>
          )}

          <div
            className={styles.summaryItems}
            style={{ color: "var(--text-black-87)" }}
          >
            {!isstepform &&
              selectedItems?.map((item) => (
                <h4
                  key={item.id}
                  className={styles.summaryItem}
                  style={{ paddingRight: "16px" }}
                >
                  {item.category}
                </h4>
              ))}
            {!isstepform &&
              secondSelectedItems?.map((item) => (
                <h4
                  key={item.id}
                  className={styles.summaryItem}
                  style={{ paddingRight: "16px" }}
                >
                  {item.category}
                </h4>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
};
const StepCategorySummary: FC<{
  selectedItems: TaskDataType[];
  title: string;
  isstepform?: boolean;
  modelname: string;
  secondSelectedItems?: TaskDataType[];
}> = ({ selectedItems, title, secondSelectedItems, modelname, isstepform }) => {
  console.log(
    selectedItems,
    secondSelectedItems,
    "these are second selected items in add categhory tyype"
  );
  return (
    <>
      <div className={styles.summaryDataOuterDiv}>
        {selectedItems?.map((e) => (
          <>
            <div key={e.id} className={styles.summaryDivData}>
              <div className={styles.summaryDataContent}>
                {modelname && (
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    {(() => {
                      switch (modelname) {
                        case "MaterialCategory":
                          return <>Material Category</>;
                        case "machinaryCategory":
                          return <>Machinery Category</>;
                        case "ToolCategory":
                          return <>Tool Category</>;
                        case "Manpowercategory":
                          return <>Manpower Category</>;
                        case "departmentdetails":
                          return <>Designation Category</>;
                        case "designationdetails":
                          return <>Department</>;
                        case "taskManagerDataDetail":
                        case "reporterDataDetail":
                        case "assigneeDataDetail":
                        case "allDesignations":
                          return <>Department</>;
                        default:
                          return <>Unknown </>;
                      }
                    })()}
                  </p>
                )}
                <h4
                  key={e.id}
                  className={styles.summaryItem}
                  style={{ paddingRight: "16px" }}
                >
                  {e.category}
                </h4>
                {(secondSelectedItems ?? []).filter(
                  (el: any) => el.parentId == e.id
                ).length > 0 &&
                  isstepform && (
                    <p
                      style={{
                        color: "var(--text-black-60)",
                        marginTop: "0.5rem",
                      }}
                      className="p_tag_14px"
                    >
                      {modelname === "allDesignations"
                        ? "Designation"
                        : `${
                            title === "Machinery" &&
                            secondSelectedItems?.filter(
                              (element: any) => element.parentId == e.id
                            )?.length == 1 &&
                            isstepform
                              ? "Machine"
                              : title
                          }${
                            (secondSelectedItems?.filter(
                              (item: any) => item.parentId == e.id
                            )?.length || 0) > 1 &&
                            title !== "Manpower" &&
                            title !== "Machinery"
                              ? "s"
                              : ""
                          }`}
                    </p>
                  )}
                {secondSelectedItems
                  ?.filter((el: any) => el.parentId == e.id)
                  ?.map((el: any) => (
                    <h4
                      key={e.id}
                      className={styles.summaryItem}
                      style={{ paddingRight: "16px" }}
                    >
                      {el.category}
                    </h4>
                  ))}
              </div>
            </div>
          </>
        ))}
      </div>
    </>
  );
};
export default AddCategoryType;
