.subVersionContainers_accordian_main_container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-end;



}

.subVersionContainers_accordian_labels {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

}

.subVersionContainers_accordian_labels h4 {
    color: var(--text-white-100);
    white-space: nowrap;
    letter-spacing: 0.5px;
}

.subVersionContainers_accordian_version_label {

    background-color: var(--primary_color);
    border-radius: 24px;
    align-self: flex-start;
    cursor: pointer;
    display: flex;
    /* align-items: center; */
    justify-content: center;
    padding: 0.3rem 0.8rem;

}

.subVersionContainers_main_container {
    width: 100%;
    margin-left: 0.3rem;
    border-left: 2px dashed var(--text-black-28);

}

/* .subVersionContainers_main_container::before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    height: 50%;
    width: 0;
    border-left: 2px dashed var(--text-black-28);
} */

.subVersionContainers_main_parent {
    /* padding: 1rem; */
    display: flex;
    flex-direction: column;

}

.subVersionContainers_version_container {
    display: flex;
    align-items: center;
    position: relative;

}

.sub_hover {
    display: flex;
    align-items: center;

    cursor: pointer;
    height: 72%;
    width: 80%;
    position: absolute;

    left: 17%;
    top: 25%;


    z-index: 999;

}

.subVersionContainers_attaching_line {
    width: 25px;
    height: 0.1px;
    border-bottom: 2px dashed var(--text-black-28);
    transition: 0.3s ease;
}

.subVersionContainers_attaching_line_selected {
    width: 30px;
    margin-top: 1rem;
    transition: 0.3s ease;
    height: 0.1px;
    border-bottom: 2px dashed var(--primary_color);
}

.subVersionContainers_detailed_history {
    width: 100%;
    border: 2px solid transparent;
    border-radius: 20px;
    transition: 0.2s ease-in;
}

.subVersionContainers_detailed_history_selected {
    width: 100%;
    border-radius: 20px;
    background-color: #FFFFFF;
    border: 2px solid var(--primary_color);
    margin-top: 1rem;
    transition: 0.2s ease-in;
}

.subversion_status_container {
    /* padding: 0.3rem 0.4rem; */
    margin: 0rem 0.4rem;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 4px;

}

.DetailVersion_detailedHistory_container {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding: 0rem 0.5rem;
    gap: 0.5rem;
    cursor: pointer;
    border: 2px solid transparent;
    border-radius: 20px;
    margin: 0.5rem 0rem;
    transition: 0.5s ease;
    height: 45px;
    width: 110px;









}

.DetailVersion_detailedHistory_container img {
    width: 2.2rem;
    border-radius: 42px;
}

.DetailVersion_indicatar_line {
    border: 1px dashed var(--text-black-28);
    height: calc(100% - 1rem);
    align-self: flex-start;
}

.DetailVersion_detailedHistory_header {

    display: flex;
    align-items: center;
    height: 100%;
}

.DetailVersion_detail {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    height: 100%;
}

.DetailVersion_detailedHistory_images_container {
    display: flex;
    justify-content: center;
}

.DetailVersion_detailedHistory_header_content {
    display: flex;
    width: 100%;
}

.DetailVersion_detail_heading {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    color: var(--text-black-60);
    margin-top: 0.2rem;
}

.DetailVersion_detail_heading p:nth-child(1) {
    color: var(--text-black-87);
    line-height: 1rem;
}

.DetailVersion_detail_heading p:nth-child(2) {
    color: var(--text-black-60);
    line-height: 1rem;
}

.sub_hover:hover~.subVersionContainers_detailed_history {
    background-color: #FFFFFF;
    border: 2px solid var(--primary_color);
    margin-top: 1rem;

}

.sub_hover:hover~.subVersionContainers_attaching_line {
    width: 30px;
    margin-top: 1rem;
    border-bottom: 2px dashed var(--primary_color);
}

.subversion_icon {
    background-color: var(--text-white-100);
    border-radius: 50%;
    padding: 6px;
    display: grid;
    align-items: center;
    justify-content: center;
    /* padding: 0.5rem; */
}