import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { Loader } from "../../../../../../assets/loader";
import TMMMNav from "../../../../../../components/Reusble/TMMMNav";
import { initializeDatabase } from "../../../../../../functions/functions";
import { useNestedPouchSearch } from "../../../../../../functions/useNestedLocalSearch";
import {
  clearFetchedMasters,
  SetCategoryId,
  setFetchedMastersDesignation,
  setSearchData,
} from "../../../../../../redux/features/Modules/Masters";
import { setNavigate } from "../../../../../../redux/features/Modules/Reusble/navigationSlice";
import { useAppSelector } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { RootState } from "../../../../../../redux/store";
import styles from "../Styles/Department.module.css";
import DepartmentCard from "./DepartmentCard/DepartmentCard";
import { SiCountingworkspro } from "react-icons/si";

const DesignationPage = () => {
  const [page, setPage] = useState<number>(1);
  const [searchLocalKey, setSearchLocalKey] = useState<string>("");
  const [width, setWidth] = useState<number | null>(
    window.innerWidth < 1200 ? window.innerWidth : 1400
  );
  const [params, setParams] = useState<string>();
  const navRef = useRef<HTMLDivElement>(null);
  const mainContentRef = useRef<HTMLDivElement>(null);
  const condition =
    mainContentRef.current &&
    mainContentRef.current.getBoundingClientRect().width < 1200;
  const condition2 = window.innerWidth < 1200;
  const searchData = useSelector(
    (state: RootState) => state.masterReduxSlice.searchedData
  );
  // const detectChanges = useAppSelector((state) => state.backupSlice.isOpen);
  const desStateData = useSelector(
    (state: RootState) => state.masterReduxSlice.fetchedData
  );
  // console.log("state data////", desStateData);

  // const localChange = useAppSelector(
  //   (state) => state.backupSlice.isLocalChange
  // );
  const navigationArray = useSelector(
    (state: RootState) => state.navigateData.navigateArray
  );
  const searchKey = useAppSelector((state) => state.taskMaster.searchKey);
  const { departmentId } = useParams();
  const dispatch = useDispatch();

  // Syncs params with navigation route when navigationArray changes.
  useEffect(() => {
    setParams(navigationArray[2]?.route);
    if (navigationArray[2]?.route === "/deleted/#") {
      dispatch(setFetchedMastersDesignation({ data: [], page: 1 }));
      setPage(1); 
    }
  }, [navigationArray]);

  // Cleanup on unmount: reset page and clear data.
  useEffect(() => {
    return () => {
      setPage(1);
      dispatch(setSearchData([]));
      dispatch(clearFetchedMasters());
    };
  }, []);

  // Reset page if localChange detected.
  // useEffect(() => {
  //   if (localChange) setPage(1);
  // }, [detectChanges, localChange]);

  // Syncs search key with local state.
  useEffect(() => {
    setSearchLocalKey(searchKey || "");
  }, [searchKey]);

  // Handles responsive layout: runs clientFunction on mount and window resize, cleans up listener on unmount.
  useEffect(() => {
    if (window.innerWidth <= 1200) {
      //   setWidth(window.innerWidth);
      // console.log
      navRef.current?.style.setProperty("width", "auto");
    }
    clientFunction();
    const handleResize = () => clientFunction();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [window.innerWidth, condition]);

  // Handles local search for designations.
  useNestedPouchSearch({
    pathRecord: "Designationmaster",
    searchKey: searchLocalKey,
    setData: setSearchData,
    setPage,
    key: "name",
    extraSearchParams: {
      catId: departmentId,
      categoryId: "DepartmentId",
      isDeletedNext: navigationArray[2]?.route === "/deleted/#",
    },
  });

  // Fetches designation data from local DB and updates Redux state.
  const getDatafromDb = async (p: any, id: string) => {
    console.log("Deleted Data deldesignation/// here on top", params, "page : ", page);
    try {
      const dbName = await initializeDatabase("Designationmaster");
      const fetchedData = await window.electron.getDocumentByParentId({
        dbName,
        catId: id,
        categoryId: "DepartmentId",
        isDeletedNext: params ? true : false,
        page: p,
        needSorting: true,
      });

      if (p === 1) {
        dispatch(setFetchedMastersDesignation({ data: fetchedData, page: p }));
      } else {
        const newData = [...desStateData, ...fetchedData];
        dispatch(setFetchedMastersDesignation({ data: newData, page: p }));
      }
    } catch (error) {
      console.log("designation error", error);
    }
  };

  // Handles infinite scroll: loads next page when user scrolls to bottom.
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const { scrollHeight, clientHeight, scrollTop } = target;
    if (scrollTop + clientHeight >= scrollHeight - 5) {
      setPage((prev) => prev + 1);
    }
  };

  // Handles responsive navigation/sidebar width based on window and content size.
  const clientFunction = () => {
    if (window.innerWidth <= 1200) {
      navRef.current?.style.setProperty("width", `${width}px`);
    }
    const mainContentWidth =
      mainContentRef?.current?.getBoundingClientRect()?.width;
    if (mainContentWidth && mainContentWidth > width) {
      setWidth(mainContentWidth);
      navRef.current?.style.setProperty("width", `${mainContentWidth}px`);
    }
  };

  useEffect(() => {
    console.log("Deleted Data designation///", departmentId, page);
    if (departmentId && page) {
      // console.log("designation///2", departmentId, page);
      getDatafromDb(page, departmentId);
      if (departmentId) dispatch(SetCategoryId(departmentId as string));
    }
  }, [departmentId, page, params]);       

  // console.log("Deleted Data designation parmas", params, desStateData);


  return (
    <div style={{ width: condition2 ? "fit-content" : "" }}>
      <div ref={navRef}>
        <TMMMNav
          Label={"Designation"}
          variant={"materialCategory"}
          TargetForm={"AddDesignationForm"}
          deletedNeeded={params ? false : true}
          handleDelete={() => {
            dispatch(
              setNavigate({
                route: `/deleted/#`,
                title: "Deleted",
              })
            );
            setParams("/deleted/#");
          }}
        />
      </div>
      {/* <AddDesignationForm /> */}
      <div
        ref={mainContentRef}
        onScroll={(e) => handleScroll(e)}
        className={styles.designation_content}
        style={{
          width: desStateData.length <= 0 && condition2 ? width : "",
        }}
      >
        {/* {data?.data?.data?.map((item: any, index) => { */}
        {desStateData && desStateData.length > 0 ? (
          (searchData && searchData.length > 0
            ? searchData
            : desStateData || []
          )?.map((item: any, index) => {
            const isDeleted = item?.isDeleted;
            return (
              <DepartmentCard
                type="Desigination"
                data={item}
                key={item?._id || index}
                cardHandler={() => {}}
                isSoftDeleted={isDeleted || params === "/deleted/#"}
              />
            );
          })
        ) : (
          <div className={styles.loader_loading}>
            <img
              src={Loader.suryaconLogo}
              alt="Loading..."
              className={styles.loader_loading_image}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default DesignationPage;
