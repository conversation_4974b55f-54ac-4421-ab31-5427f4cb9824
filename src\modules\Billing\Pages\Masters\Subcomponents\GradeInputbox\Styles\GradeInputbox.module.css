.gradeinputbox_container {
    min-height: 2.6rem;
    min-width: 4rem; /* Start small */
    cursor: pointer;
    backdrop-filter: blur(40px);
    border-radius: 25px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding-inline: 0.6rem;
    box-shadow: 0px 0px 4px 0px #91a1a180;
    border: 1px solid var(--primary_color);
    width: fit-content;
    position: relative;
    max-width: 31rem;
}

.gradeinputbox {
    min-height: 2.6rem;
    max-height: 8rem;
    background: transparent;
    font-size: 16px;
    border: none;
    outline: none;
    resize: none;
    overflow: hidden;
    padding: 0.5rem;
    font-family: inherit;
    min-width: 4rem; /* Ensures minimum width */
    width: 4rem; /* Small initial size */
    white-space: nowrap;
}

/* Cursor Effect */
.gradeinputbox::before {
    content: "|";
    color: gray;
    opacity: 0.7;
    font-size: 16px;
    animation: blink 1s infinite;
    position: absolute;
    left: 12px; /* Adjust for proper positioning */
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
}

/* Remove cursor effect when user types */
.gradeinputbox.filled::before {
    content: "";
}

/* Cursor Blink Animation */
@keyframes blink {
    50% {
        opacity: 0;
    }
}
