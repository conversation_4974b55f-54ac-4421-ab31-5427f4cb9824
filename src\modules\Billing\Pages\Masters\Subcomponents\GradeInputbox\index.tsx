import React, { useRef, useState } from "react";
import styles from "./Styles/GradeInputbox.module.css";

const GradeInputbox: React.FC = () => {
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [value, setValue] = useState("");

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setValue(e.target.value);

    if (inputRef.current) {
      inputRef.current.style.width = "4rem"; // Start small
      inputRef.current.style.width = `${Math.max(
        40, // Minimum width in pixels (4rem)
        inputRef.current.scrollWidth
      )}px`;
    }
  };

  return (
    <div className={styles.gradeinputbox_container}>
      <textarea
        ref={inputRef}
        className={`${styles.gradeinputbox} ${value ? styles.filled : ""}`} 
        value={value}
        onChange={handleInputChange}
        rows={1}
        placeholder="Grade"
      />
    </div>
  );
};

export default GradeInputbox;
