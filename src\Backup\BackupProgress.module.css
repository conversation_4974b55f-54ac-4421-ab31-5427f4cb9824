.backup_wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    background:var(--primary_background);
    height: 100vh;
    font-family: sans-serif;
  }
  
  .backup_box {
    background: var(--primary_background);
    padding: 2rem 3rem;
    border-radius: 10px;
    box-shadow: var(--primary-shadow);;
    text-align: center;
  }
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 4px dashed var(--primary_color);
    border-radius: 50%;
    margin: 0 auto 1rem;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .message {
    font-size: 1.1rem;
    color: #374151;
  }
  