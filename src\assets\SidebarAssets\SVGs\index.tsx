import { FC } from "react";

interface ColorProps {
  color: string;
}

export const SummarySVG: FC<ColorProps> = ({ color }) => {
  return (
    <svg
      className="svgDimension"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.6734 7.47316C15.8558 7.10127 15.7022 6.65193 15.3303 6.46951C14.9584 6.28709 14.5091 6.44068 14.3266 6.81256L12.8663 9.78961C12.4137 10.7124 11.0833 10.6673 10.6942 9.71603C9.815 7.56698 6.80955 7.46517 5.78698 9.5498L4.32665 12.5268C4.14423 12.8987 4.29782 13.3481 4.6697 13.5305C5.04158 13.7129 5.49093 13.5593 5.67335 13.1874L7.13369 10.2104C7.58632 9.28764 8.91668 9.3327 9.30584 10.284C10.185 12.433 13.1904 12.5348 14.213 10.4502L15.6734 7.47316Z"
        fill={color}
        fillOpacity="0.87"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.4635 0.373728C13.3214 0.249985 11.8818 0.249992 10.0452 0.25H9.95475C8.11821 0.249992 6.67861 0.249985 5.53648 0.373728C4.37094 0.500006 3.42656 0.762324 2.62024 1.34815C2.13209 1.70281 1.70281 2.13209 1.34815 2.62024C0.762324 3.42656 0.500006 4.37094 0.373728 5.53648C0.249985 6.67861 0.249992 8.11821 0.25 9.95475V10.0452C0.249992 11.8818 0.249985 13.3214 0.373728 14.4635C0.500006 15.6291 0.762324 16.5734 1.34815 17.3798C1.70281 17.8679 2.13209 18.2972 2.62024 18.6518C3.42656 19.2377 4.37094 19.5 5.53648 19.6263C6.67859 19.75 8.11817 19.75 9.95465 19.75H10.0453C11.8818 19.75 13.3214 19.75 14.4635 19.6263C15.6291 19.5 16.5734 19.2377 17.3798 18.6518C17.8679 18.2972 18.2972 17.8679 18.6518 17.3798C19.2377 16.5734 19.5 15.6291 19.6263 14.4635C19.75 13.3214 19.75 11.8818 19.75 10.0453V9.95473C19.75 8.11824 19.75 6.67859 19.6263 5.53648C19.5 4.37094 19.2377 3.42656 18.6518 2.62024C18.2972 2.13209 17.8679 1.70281 17.3798 1.34815C16.5734 0.762324 15.6291 0.500006 14.4635 0.373728ZM3.50191 2.56168C4.00992 2.19259 4.66013 1.97745 5.69804 1.865C6.74999 1.75103 8.10843 1.75 10 1.75C11.8916 1.75 13.25 1.75103 14.302 1.865C15.3399 1.97745 15.9901 2.19259 16.4981 2.56168C16.8589 2.82382 17.1762 3.14111 17.4383 3.50191C17.8074 4.00992 18.0225 4.66013 18.135 5.69804C18.249 6.74999 18.25 8.10843 18.25 10C18.25 11.8916 18.249 13.25 18.135 14.302C18.0225 15.3399 17.8074 15.9901 17.4383 16.4981C17.1762 16.8589 16.8589 17.1762 16.4981 17.4383C15.9901 17.8074 15.3399 18.0225 14.302 18.135C13.25 18.249 11.8916 18.25 10 18.25C8.10843 18.25 6.74999 18.249 5.69804 18.135C4.66013 18.0225 4.00992 17.8074 3.50191 17.4383C3.14111 17.1762 2.82382 16.8589 2.56168 16.4981C2.19259 15.9901 1.97745 15.3399 1.865 14.302C1.75103 13.25 1.75 11.8916 1.75 10C1.75 8.10843 1.75103 6.74999 1.865 5.69804C1.97745 4.66013 2.19259 4.00992 2.56168 3.50191C2.82382 3.14111 3.14111 2.82382 3.50191 2.56168Z"
        fill={color}
        fillOpacity="0.87"
      />
    </svg>
  );
};

export const AnalyticsSvg: FC<ColorProps> = ({ color }) => {
  return (
    <svg
      className="svgDimension"
      viewBox="0 0 20 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.5 15C8.5 16.1046 7.60457 17 6.5 17H3C1.89543 17 1 16.1046 1 15L1 7C1 5.89543 1.89543 5 3 5L6.5 5C7.60457 5 8.5 5.89543 8.5 7L8.5 15Z"
        stroke={`${color}`}
        strokeOpacity="0.87"
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19 15C19 16.1046 18.1046 17 17 17H13.5C12.3954 17 11.5 16.1046 11.5 15L11.5 3C11.5 1.89543 12.3954 1 13.5 1L17 1C18.1046 1 19 1.89543 19 3V15Z"
        stroke={`${color}`}
        strokeOpacity="0.87"
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const BillSvg: FC<ColorProps> = ({ color }) => {
  return (
    <svg
      className="svgDimension"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 13.5H18M10 17.5H18M7 14H5C3.89543 14 3 14.8954 3 16V18C3 19.6569 4.34315 21 6 21H10M7 20V6C7 4.34315 8.34315 3 10 3H18C19.6569 3 21 4.34315 21 6V18C21 19.6569 19.6569 21 18 21H8C7.44772 21 7 20.5523 7 20ZM11 6.5H13C13.5523 6.5 14 6.94772 14 7.5V8.5C14 9.05228 13.5523 9.5 13 9.5H11C10.4477 9.5 10 9.05228 10 8.5V7.5C10 6.94772 10.4477 6.5 11 6.5Z"
        stroke={color}
        strokeOpacity="0.87"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ReportSvg: FC<ColorProps> = ({ color }) => {
  return (
    <svg
      className="svgDimension"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10 13.5H18M10 17.5H18M7 14H5C3.89543 14 3 14.8954 3 16V18C3 19.6569 4.34315 21 6 21H10M7 20V6C7 4.34315 8.34315 3 10 3H18C19.6569 3 21 4.34315 21 6V18C21 19.6569 19.6569 21 18 21H8C7.44772 21 7 20.5523 7 20ZM11 6.5H13C13.5523 6.5 14 6.94772 14 7.5V8.5C14 9.05228 13.5523 9.5 13 9.5H11C10.4477 9.5 10 9.05228 10 8.5V7.5C10 6.94772 10.4477 6.5 11 6.5Z"
        stroke={color}
        strokeOpacity="0.87"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const SettingsSvg: FC<ColorProps> = ({ color }) => {
  return (
    <svg
      className="svgDimension"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11 1H9.00004C7.89547 1 7.00004 1.89543 7.00004 3C7.00004 3.80171 6.13216 4.30278 5.43786 3.90192C4.48127 3.34964 3.25809 3.67739 2.70581 4.63397L1.70581 6.36602C1.15352 7.32261 1.48127 8.54579 2.43786 9.09808C3.13215 9.49893 3.13213 10.5011 2.43783 10.9019C1.48125 11.4542 1.1535 12.6774 1.70578 13.634L2.70578 15.366C3.25807 16.3226 4.48125 16.6504 5.43783 16.0981C6.13214 15.6972 7.00004 16.1983 7.00004 17C7.00004 18.1046 7.89547 19 9.00004 19H11C12.1046 19 13 18.1046 13 17C13 16.1983 13.8679 15.6972 14.5622 16.0981C15.5188 16.6504 16.742 16.3226 17.2943 15.366L18.2943 13.634C18.8465 12.6774 18.5188 11.4542 17.5622 10.9019C16.8679 10.5011 16.8679 9.49892 17.5622 9.09807C18.5188 8.54578 18.8465 7.3226 18.2942 6.36602L17.2942 4.63397C16.742 3.67738 15.5188 3.34963 14.5622 3.90191C13.8679 4.30276 13 3.8017 13 3C13 1.89543 12.1046 1 11 1Z"
        stroke={color}
        strokeOpacity="0.87"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 10C12 11.1046 11.1046 12 10 12C8.89547 12 8.00004 11.1046 8.00004 10C8.00004 8.89543 8.89547 8 10 8C11.1046 8 12 8.89543 12 10Z"
        stroke={color}
        strokeOpacity="0.87"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const BenefitsSvg: FC<ColorProps> = ({ color }) => {
  return (
    <svg
      className="svgDimension"
      viewBox="0 0 14 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5 3H4C2.34315 3 1 4.34315 1 6V16C1 17.6569 2.34315 19 4 19H10C11.6569 19 13 17.6569 13 16V6C13 4.34315 11.6569 3 10 3H9M5 3H9M5 3C5 1.89543 5.89543 1 7 1C8.10457 1 9 1.89543 9 3M4 12.5H10M4 9.5H10M4 6.5H10M4 15.5H10"
        stroke={color}
        strokeOpacity="0.87"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const SupportSvg: FC<ColorProps> = ({ color }) => {
  return (
    <svg
      className="svgDimension"
      viewBox="0 0 20 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1 10C1 5.02944 5.02944 1 10 1C14.9706 1 19 5.02944 19 10M1 10C1 11.7282 1.16203 13.3427 1.88056 14.7136C2.21255 15.347 2.91551 15.6573 3.63064 15.6573C4.9392 15.6573 6 14.5965 6 13.288V12.8287C6 11.2664 4.73356 10 3.17133 10H1ZM19 10C19 11.7282 18.838 13.3427 18.1194 14.7136C17.7874 15.347 17.0845 15.6573 16.3694 15.6573C15.0608 15.6573 14 14.5965 14 13.288V12.8287C14 11.2664 15.2664 10 16.8287 10H19Z"
        stroke={color}
        strokeOpacity="0.87"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const DashboardSvg: FC<ColorProps> = ({ color }) => {
  return (
    <svg
      className="svgDimension"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M1.5 3.5C1.5 2.39543 2.39543 1.5 3.5 1.5H6.5C7.60457 1.5 8.5 2.39543 8.5 3.5V6.5C8.5 7.60457 7.60457 8.5 6.5 8.5H3.5C2.39543 8.5 1.5 7.60457 1.5 6.5V3.5Z"
        stroke={color}
        strokeOpacity="0.6"
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
      <path
        d="M11.5 3.5C11.5 2.39543 12.3954 1.5 13.5 1.5H16.5C17.6046 1.5 18.5 2.39543 18.5 3.5V6.5C18.5 7.60457 17.6046 8.5 16.5 8.5H13.5C12.3954 8.5 11.5 7.60457 11.5 6.5V3.5Z"
        stroke={color}
        strokeOpacity="0.6"
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
      <path
        d="M11.5 13.5C11.5 12.3954 12.3954 11.5 13.5 11.5H16.5C17.6046 11.5 18.5 12.3954 18.5 13.5V16.5C18.5 17.6046 17.6046 18.5 16.5 18.5H13.5C12.3954 18.5 11.5 17.6046 11.5 16.5V13.5Z"
        stroke={color}
        strokeOpacity="0.6"
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
      <path
        d="M1.5 13.5C1.5 12.3954 2.39543 11.5 3.5 11.5H6.5C7.60457 11.5 8.5 12.3954 8.5 13.5V16.5C8.5 17.6046 7.60457 18.5 6.5 18.5H3.5C2.39543 18.5 1.5 17.6046 1.5 16.5V13.5Z"
        stroke={color}
        strokeOpacity="0.6"
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
    </svg>
  );
};
