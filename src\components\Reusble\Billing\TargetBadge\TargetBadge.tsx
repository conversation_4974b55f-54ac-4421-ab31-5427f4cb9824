import React from "react";
import styles from "./Styles/TargetBadge.module.css";
import { TargetBadgeProps } from "../interfaces/interface";

const TargetBadge: React.FC<TargetBadgeProps> = ({
  value,
  order,
  outerContainerClassName,
  bubbleValue,
  bubbleTextTagName = "span", // Default to 'span'
  bubbletextClassName,
  valueTextTagName = "p", // Default to 'p'
  valueTextClassName,
  secondValueTextTagName = "p", // Default to 'p'
  secondvalue,
  secondValueTextClassName,
  bubbleClassname,
  active,
  bubbleBackgroundColor,
  backgroundColor,
  icon, // Icon prop
  onClick,
}) => {
  const combineClassNames = (classNames?: string | string[]) => {
    if (!classNames) return "";
    if (Array.isArray(classNames)) {
      return classNames
        .map((className) => styles[className] || className)
        .join(" ");
    }
    return styles[classNames] || classNames;
  };

  return (
    <div>
      <div
        className={`${styles.target_badge_outer_container} ${
          active && styles.border
        } ${order && styles.order} ${combineClassNames(
          outerContainerClassName
        )}`}
        style={{ backgroundColor: backgroundColor }}
        onClick={onClick}
      >
        <div>
          {React.createElement(
            valueTextTagName,
            { className: combineClassNames(valueTextClassName) },
            value ? value : ""
          )}
          {secondvalue &&
            React.createElement(
              secondValueTextTagName,
              { className: combineClassNames(secondValueTextClassName) },
              secondvalue ? secondvalue : ""
            )}
        </div>
        {icon ? <div className={styles.iconContainer}>{icon}</div> : ""}
        {bubbleValue && (
          <div
            className={`${styles.targetBubble} ${combineClassNames(
              bubbleClassname
            )}`}
            style={{ backgroundColor: bubbleBackgroundColor }}
          >
            {React.createElement(
              bubbleTextTagName,
              { className: combineClassNames(bubbletextClassName) },
              bubbleValue
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TargetBadge;
