//Author <PERSON><PERSON><PERSON><PERSON><PERSON>
import { createSlice } from "@reduxjs/toolkit";
// ========================================================== planning tower and task progress in billing steps and indexes setup by ab<PERSON><PERSON><PERSON> raj ======================================================================
const initialState = {
  steps: [],
  currentStepIndex: 0,
};

const planningProgressSlice = createSlice({
  name: "planningProgress",
  initialState,
  reducers: {
    setSteps(state, action) {
      state.steps = action.payload;
    },
    setCurrentStepIndex(state, action) {
      state.currentStepIndex = action.payload;
    },
  },
});

export const { setSteps, setCurrentStepIndex } = planningProgressSlice.actions;

export default planningProgressSlice.reducer;
