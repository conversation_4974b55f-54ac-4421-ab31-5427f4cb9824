import { createSlice } from "@reduxjs/toolkit";

const initialstate = {
  loading: false,
  message: "",
};
const loadingSlice = createSlice({
  name: "loadingSlice",
  initialState: initialstate,
  reducers: {
    setLoading: (state, action) => {
      state.loading = action.payload.loading;
      state.message = action.payload.message;
    },
  },
});
export const { setLoading } = loadingSlice.actions;
export default loadingSlice.reducer;
export const selectLoading = (state: any) => state.loadingSlice.loading;
export const selectLoadingMessage = (state: any) => state.loadingSlice.message;
