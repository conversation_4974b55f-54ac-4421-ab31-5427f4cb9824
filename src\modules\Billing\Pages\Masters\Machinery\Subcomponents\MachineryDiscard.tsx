import React, { <PERSON> } from "react";
import styles from "../Styles/Machinery.module.css";
import {
  isValidValue,
  slicedData,
} from "../../../../../../functions/functions";

interface MachineryProps {
  formData: any;
  initialFormData?: any;
  formMode?: any;
  //   deletedGradeData: Array<Record<number, any[]>>;
  //   deletedFormData: any;
}

const MachineryDiscard: FC<MachineryProps> = ({
  formData,
  initialFormData,
  formMode,
  //   deletedGradeData,
  //   deletedFormData,
}) => {
  return (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <div style={{ display: "flex", flexWrap: "wrap" }}>
        {formData?.Name && isValidValue(formData?.Name) && (
          <div
            className={styles.summaryDivData}
            style={{ width: formData?.Photo?.name ? "50%" : "100%" }}
          >
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Name
              </p>
              <h4
                style={{
                  color: "var(--text-black-87)",
                  marginTop: "0.3rem",
                }}
              >
                {formData?.Name}
              </h4>
            </div>
          </div>
        )}
        {formData?.Photo?.name && isValidValue(formData?.Photo?.name) && (
          <div
            className={styles.summaryDivData}
            style={{ width: formData?.Name ? "50%" : "100%" }}
          >
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Cover Photo
              </p>
              <h4
                style={{
                  color: "var(--text-black-87)",
                  marginTop: "0.3rem",
                }}
              >
                {slicedData(formData?.Photo?.name, 14)}
              </h4>
            </div>
          </div>
        )}
      </div>
      {formData?.Description && isValidValue(formData?.Description) && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <div>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Description
              </p>
              <h4
                style={{
                  color: "var(--text-black-87)",
                  marginTop: "0.3rem",
                }}
              >
                {formData?.Description}
              </h4>
            </div>
          </div>
        </div>
      )}
      {formData?.Brands &&
        formData?.Brands?.length > 0 &&
        formData?.Brands?.[0]?.brand?.name && (
          <>
            <h4 style={{ margin: "0.6rem" }}>Brand</h4>
            {formData?.Brands?.map((item: any) => (
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Name
                  </p>
                  <h4
                    style={{
                      color: "var(--text-black-87)",
                      marginTop: "0.3rem",
                    }}
                  >
                    {item?.brand?.name}
                  </h4>
                  {formData?.Brands?.[0]?.Grade &&
                    formData?.Brands?.[0]?.Grade.length > 0 &&
                    formData?.Brands?.[0]?.Grade?.[0] && (
                      <>
                        <p
                          style={{
                            color: "var(--text-black-60)",
                            marginTop: "0.3rem",
                          }}
                          className="p_tag_14px"
                        >
                          Grades
                        </p>
                        <div
                          style={{
                            color: "var(--text-black-87)",
                            display: "flex",
                            marginTop: "0.3rem",
                            gap: "0.3rem 2rem",
                            flexWrap: "wrap",
                          }}
                        >
                          {item?.Grade?.map((item: any) => (
                            <h4>{item}</h4>
                          ))}
                        </div>
                      </>
                    )}
                </div>
              </div>
            ))}
          </>
        )}
      {formData?.Tools && formData?.Tools?.length > 0 && (
        <>
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Tools
              </p>
              <div
                style={{
                  color: "var(--text-black-87)",
                  display: "flex",
                  marginTop: "0.3rem",
                  gap: "0.3rem 2rem",
                  flexWrap: "wrap",
                }}
              >
                {formData?.Tools?.map((item: any, index: number) => (
                  <h4
                    style={{
                      color: "var(--text-black-87)",
                    }}
                  >
                    {item?.name}
                  </h4>
                ))}
              </div>
            </div>
          </div>
        </>
      )}
      {formData?.Fuel && isValidValue(formData?.Fuel) && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <div>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Fuel Type
              </p>
              <h4
                style={{
                  color: "var(--text-black-87)",
                  marginTop: "0.3rem",
                  textTransform: "capitalize",
                }}
              >
                {formData?.Fuel}
              </h4>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MachineryDiscard;
