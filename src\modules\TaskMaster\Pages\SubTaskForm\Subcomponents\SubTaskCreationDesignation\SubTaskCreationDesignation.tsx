import React, { useCallback, useEffect, useState } from "react";
import styles from "../../Styles/SubtaskCreationForm.module.css";
import { useDispatch, useSelector } from "react-redux";
import { GetTaskBuildingBlocks } from "../../../../../../redux/hooks/Modules/TaskMaster/TaskMasterHook";
import {
  setChangeAPiFlag,
  setIsChangeSubtask,
  settaskChangeAPiFlag,
  updateSubtaskData,
  updateTaskData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import {
  closePopup,
  openPopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import { SuryconLogo } from "../../../../../../assets/icons";
import AddToolTip from "../../../../../../components/Reusble/Global/AddToolTip";
import AddCategoryType from "../../../../../../components/Reusble/Global/AddCategoryType";
import Dialogbox from "../../../../../../components/Reusble/Global/DialogBox";
import ReportingLevel from "../../../../../../components/Reusble/TaskMaster/ReportingLevel";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import { RootState, store } from "../../../../../../redux/store";
import {
  AddDataDesignation,
  AddDataSubTaskDesignation,
  requiredthings,
  TaskDataType,
} from "../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import { saveSyncData } from "../../../../../../Backup/BackupFunctions/BackupFunctions";
import { initializeDatabase } from "../../../../../../functions/functions";
import { useToast } from "../../../../../../hooks/ToastHook";
import { useParams } from "react-router-dom";

const roles = ["Task Manager", "Assign To", "Reporter"] as const;

interface SubTaskCreationDesignation {
  isEdit: boolean;
}

const SubaskCreationDesignation: React.FC<SubTaskCreationDesignation> = ({
  isEdit = false,
}) => {
  const subTaskData = useSelector(
    (state: RootState) =>
      state?.taskMaster?.currentSubtaskData || {
        _id: "",
        name: "",
        Unit: "",
        description: "",
        subtaskWeighatages: 0,
        Tracking: "",
        MaterialId: [],
        ToolId: [],
        MachinaryId: [],
        ManpowerId: [],
        AdminId: [],
        AssigneeId: [],
        TaskmasterId: {},
        ReporterId: { Reporter: [] },
        MethodId: {
          work_instruction_id: [],
          task_closing_requirement: [],
          Controlplan: [],
          Failuremode: [],
        },
      }
  );
  const showToast = useToast();
  const [deleteDepartmentId, setDeleteDepartmentId] = useState<string>();
  const [reporterAdded, setReporterAdded] = useState<boolean>(false);
  const [reporterIndex, setReporterIndex] = useState();
  const [initialReporterData, setInitialReporterData] = useState<any>([]);
  const [requiredThingsDelete, setRequiredThingsDelete] =
    useState<requiredthings>();
  const [requiredThingsDeleteName, setRequiredThingsDeleteName] =
    useState<string>();
  const [deleteId, setDeleteId] = useState<string>();

  const dispatch = useDispatch();
  const { popups } = useSelector((state: RootState) => state.popup);
  const [currentLevel, setCurrentLevel] = useState<number | string>();
  const [isOpen, setIsOpen] = useState(false);
  // const { data } = GetTaskBuildingBlocks();
  // const taskBuildingBlocks = {
  //   designation:
  //     data?.data?.response?.designation?.map((item: any) => ({
  //       id: item._id,
  //       category: item.name,
  //     })) || [],
  // };
  const { taskId } = useParams();
  const reportedData = useSelector(
    (state: RootState) =>
      state.taskMaster.currentSubtaskData?.ReporterId?.Reporter
  );
  const TaskData = useSelector(
    (state: RootState) =>
      state.taskForm.currentSubtaskData || {
        _id: taskId,
        name: "",
        Unit: "",
        description: "",
        subtaskWeighatages: 0,
        Tracking: "",
        MaterialId: [],
        ToolId: [],
        MachinaryId: [],
        ManpowerId: [],
        Adminid: [],
        AssigneeId: [],
        TaskmasterId: {},
        ReporterId: { Reporter: [] },
        MethodId: {
          work_instruction_id: [],
          task_closing_requirement: [],
          Controlplan: [],
          Failuremode: [],
        },
      }
  );

  const fetchSubtasks = async () => {
    const dbName = await initializeDatabase("SubTaskForm");

    const response = await window.electron.getSubtasksByTaskId({
      dbName,
      taskId,
    });
    return response || [];
  };

  // delete designations from task
  const deleteDesignationsFromTask = async (
    ids: string[],
    departments: string[]
  ) => {
    const latesttaskData = store.getState().taskForm.currentSubtaskData;
    const dbName = await initializeDatabase("SubTaskForm");
    let response = await window.electron.getSubtasksByTaskId({
      dbName,
      taskId,
    });
    const latestSubtaskData = store.getState().taskMaster.currentSubtaskData;
    // Remove the current subtask from the response and add latesttaskData
    if (Array.isArray(response)) {
      response = [
        ...response.filter((subtask: any) => subtask._id !== subTaskData?._id),
        latestSubtaskData,
      ];
    }

    const allTaskManagers = response?.map((e: any) => e?.AdminId)?.flat();
    const allAssignees = response?.map((e: any) => e?.AssigneeId)?.flat();

    const allReporters =
      response
        ?.map((e: any) => e["ReporterId"] || { Reporter: [] })
        ?.filter(
          (e: any) =>
            Array.isArray(e?.Reporter) &&
            e.Reporter.length > 0 &&
            e.Reporter[0]?.designationId
        )[0]
        ?.Reporter?.map((e: any) => e?.designationId) // fallback to empty array if undefined
        ?.flat() || [];
    console.log(
      allReporters,
      allTaskManagers,
      allAssignees,
      "this is all reporters bro check here"
    );
    const taskManagersInTask = latesttaskData?.Adminid;
    const assigneesInTask = latesttaskData?.AssigneeId;
    const reportersInTask =
      latesttaskData?.ReporterId?.Reporter?.length > 0
        ? latesttaskData?.ReporterId.Reporter.map((e: any) => e?.designationId)
        : [];

    const allData = [
      ...allTaskManagers,
      ...allAssignees,
      ...allReporters,
      ...taskManagersInTask,
      ...assigneesInTask,
      ...reportersInTask?.flat(),
    ];

    console.log(allData, "ths is alldata check here bro ");
    const foundItem = allData?.filter((e) =>
      Array.isArray(ids) ? ids.includes(e._id) : e._id === ids
    );
    const foundDepartment = allData?.filter((e) =>
      e.DepartmentId && Array.isArray(departments)
        ? departments.includes(e.DepartmentId)
        : e.DepartmentId === departments
    );
    console.log(foundDepartment, "this is found department bro check here");
    console.log(foundItem, "this is find item bro check here");
    console.log(allData, "this is unique data bro check here");
    // Remove DesignationId if not found
    if (foundItem?.length === 0) {
      dispatch(
        updateTaskData({
          ...(latesttaskData as any),
          DesignationId: [
            ...((TaskData as any).DesignationId || []).filter(
              (item: any) =>
                !(Array.isArray(ids)
                  ? ids.includes(item?._id)
                  : item?._id === ids)
            ),
          ],
        })
      );

      await saveSyncData(
        {
          ...latesttaskData,
          DesignationId: [
            ...((latesttaskData as any).DesignationId || []).filter(
              (item: any) =>
                !(Array.isArray(ids)
                  ? ids.includes(item?._id)
                  : item?._id === ids)
            ),
          ],
        },
        "time",
        "TaskForm"
      );
      dispatch(settaskChangeAPiFlag(true));
    }
    const TaskDataAfter = store.getState().taskForm.currentSubtaskData;
    // Remove DepartmentId if not found
    if (foundDepartment?.length === 0) {
      dispatch(
        updateTaskData({
          ...(TaskDataAfter as any),
          DepartmentId: [
            ...((TaskDataAfter as any).DepartmentId || []).filter(
              (item: any) =>
                !(Array.isArray(departments)
                  ? departments.includes(item?._id)
                  : item?.DepartmentId === departments)
            ),
          ],
        })
      );

      await saveSyncData(
        {
          ...TaskDataAfter,
          DepartmentId: [
            ...((TaskDataAfter as any).DepartmentId || []).filter(
              (item: any) =>
                !(Array.isArray(departments)
                  ? departments.includes(item?._id)
                  : item?.DepartmentId === departments)
            ),
          ],
        },
        "time",
        "TaskForm"
      );
      dispatch(settaskChangeAPiFlag(true));
    }
  };
  // Handle selection of categories and update subtask data
  const handleSelect = useCallback(
    async (
      categoryName: keyof AddDataDesignation,
      selectedItems: TaskDataType[],
      label: string,
      parents: any
    ) => {
      const newselectedItems = selectedItems.map((item) => {
        return {
          _id: item?.id,
          name: item?.category,
          DepartmentId: item?.parentId, // Include parentId if available
        };
      });
      console.log(subTaskData, "thse are parents bo checakojsdfasdf");
      const formattedParents = parents.map((item: any) => {
        return {
          _id: item.id,
          name: item.category,
        };
      });

      let prevAdminId = [];
      let presentInAnotherSubtask = [];
      let presentInReporter: any = [];
      let foundDepartment = [];
      if (categoryName === "AdminId") {
        prevAdminId = (subTaskData as any)[categoryName] || [];
      }

      if (selectedItems.length > 0) {
        dispatch(
          updateSubtaskData({
            ...(subTaskData as any),

            [categoryName]:
              categoryName === ("AdminId" as keyof AddDataDesignation)
                ? newselectedItems // Replace the array entirely for AdminId
                : [
                    ...((subTaskData as any)[categoryName] || []).filter(
                      (item: any) =>
                        !newselectedItems.some(
                          (selected: any) => selected._id === item._id
                        )
                    ),
                    ...newselectedItems,
                  ], // Append for other cases
          })
        );

        await saveSyncData(
          {
            ...subTaskData,

            [categoryName]:
              categoryName === "AdminId"
                ? newselectedItems // Replace the array entirely for AdminId
                : [
                    ...(
                      ((subTaskData as any)[categoryName] || []) as any
                    ).filter(
                      (item: any) =>
                        !newselectedItems.some(
                          (selected: any) => selected._id === item._id
                        )
                    ),
                    ...newselectedItems,
                  ], // Append for other cases
          },
          "time",
          "SubTaskForm"
        );

        if (categoryName === "AdminId" && prevAdminId?.[0]?._id) {
          const prevId = prevAdminId[0]._id;

          // Find if prevAdminId exists in reporters
          presentInReporter = Array.isArray(subTaskData?.ReporterId?.Reporter)
            ? subTaskData.ReporterId.Reporter.flatMap(
                (reporter: any) => reporter?.designationId || []
              ).filter((item: any) => item?._id === prevId)
            : [];

          const allsubtasks = await fetchSubtasks();

          presentInAnotherSubtask = allsubtasks
            .flatMap((subtask: any) => [
              ...(subtask?.AdminId || []),
              ...(Array.isArray(subtask?.ReporterId?.Reporter)
                ? subtask.ReporterId.Reporter.flatMap(
                    (reporter: any) => reporter?.designationId || []
                  )
                : []),
            ])
            .filter(
              (item: any, index: number, self: any[]) =>
                item?._id === prevId &&
                index === self.findIndex((i) => i?._id === item._id)
            );

          const latesttaskData = store.getState().taskForm.currentSubtaskData;
          const dbName = await initializeDatabase("SubTaskForm");
          let response = await window.electron.getSubtasksByTaskId({
            dbName,
            taskId,
          });
          const latestSubtaskData =
            store.getState().taskMaster.currentSubtaskData;
          // Remove the current subtask from the response and add latesttaskData
          if (Array.isArray(response)) {
            response = [
              ...response.filter(
                (subtask: any) => subtask._id !== subTaskData?._id
              ),
              latestSubtaskData,
            ];
          }

          const allTaskManagers = response?.map((e: any) => e?.AdminId)?.flat();
          const allAssignees = response?.map((e: any) => e?.AssigneeId)?.flat();

          const allReporters =
            response
              ?.map((e: any) => e["ReporterId"] || { Reporter: [] })
              ?.filter(
                (e: any) =>
                  Array.isArray(e?.Reporter) &&
                  e.Reporter.length > 0 &&
                  e.Reporter[0]?.designationId
              )[0]
              ?.Reporter?.map((e: any) => e?.designationId) // fallback to empty array if undefined
              ?.flat() || [];
          console.log(
            allReporters,
            allTaskManagers,
            allAssignees,
            "this is all reporters bro check here"
          );
          const taskManagersInTask = latesttaskData?.Adminid;
          const assigneesInTask = latesttaskData?.AssigneeId;
          const reportersInTask =
            latesttaskData?.ReporterId?.Reporter?.length > 0
              ? latesttaskData?.ReporterId.Reporter.map(
                  (e: any) => e?.designationId
                )
              : [];

          const allData = [
            ...allTaskManagers,
            ...allAssignees,
            ...allReporters,
            ...taskManagersInTask,
            ...assigneesInTask,
            ...reportersInTask?.flat(),
          ];

          foundDepartment = allData?.filter((e) =>
            e.DepartmentId && Array.isArray(prevAdminId?.[0]?.DepartmentId)
              ? prevAdminId?.[0]?.DepartmentId.includes(e.DepartmentId)
              : e.DepartmentId === prevAdminId?.[0]?.DepartmentId
          );
        }

        console.log("reporter dataaaaa", foundDepartment);

        // FOR TASK

        dispatch(
          updateTaskData({
            ...(TaskData as any),

            DepartmentId: [
              ...((TaskData as any).DepartmentId || []).filter((item: any) =>
                !formattedParents.some(
                  (parent: any) => parent._id === item._id
                ) && foundDepartment?.length === 0
                  ? !(Array.isArray(prevAdminId?.[0]?.DepartmentId)
                      ? prevAdminId?.[0]?.DepartmentId.includes(item?._id)
                      : item?._id === prevAdminId?.[0]?.DepartmentId)
                  : true
              ),
              ...formattedParents,
            ].filter(
              (item, index, self) =>
                index === self.findIndex((t) => t._id === item._id)
            ),
            DesignationId: [
              ...((TaskData as any).DesignationId || []).filter(
                (item: any) =>
                  !newselectedItems.some(
                    (selected: any) => selected._id === item._id
                  ) &&
                  (presentInReporter.length > 0 ||
                  presentInAnotherSubtask.length > 0
                    ? true
                    : item._id !== prevAdminId?.[0]?._id)
              ),
              ...newselectedItems,
            ],
            // Append for other cases
          })
        );

        await saveSyncData(
          {
            ...TaskData,
            DepartmentId: [
              ...((TaskData as any).DepartmentId || []).filter((item: any) =>
                !formattedParents.some(
                  (parent: any) => parent._id === item._id
                ) && foundDepartment?.length === 0
                  ? !(Array.isArray(prevAdminId?.[0]?.DepartmentId)
                      ? prevAdminId?.[0]?.DepartmentId.includes(item?._id)
                      : item?.DepartmentId === prevAdminId?.[0]?.DepartmentId)
                  : true
              ),
              ...formattedParents,
            ].filter(
              (item, index, self) =>
                index === self.findIndex((t) => t._id === item._id)
            ),
            DesignationId: [
              ...(((TaskData as any).DesignationId || []) as any).filter(
                (item: any) =>
                  !newselectedItems.some(
                    (selected: any) => selected._id === item._id
                  ) &&
                  (presentInReporter.length > 0 ||
                  presentInAnotherSubtask.length > 0
                    ? true
                    : item._id !== prevAdminId?.[0]?._id)
              ),
              ...newselectedItems,
            ],
          },
          "time",
          "TaskForm"
        );
        showToast({
          messageContent: `${
            selectedItems.length > 1 ? label : selectedItems[0]?.category
          } Added Successfully!`,
          type: "success",
        });

        dispatch(settaskChangeAPiFlag(true));
        dispatch(setChangeAPiFlag(true));
        console.log(subTaskData, "this is task data bro check here");
        // dispatch(
        //   updateTaskData({
        //     ...TaskData,
        //     DesignationId: newselectedItems, // Replace the array entirely
        //   })
        // );
      }
    },
    [dispatch, subTaskData, TaskData]
  );
  // const handleSelect = useCallback(
  //   (
  //     categoryName: keyof AddDataSubTaskDesignation,
  //     selectedItems: TaskDataType[]
  //   ) => {
  //     console.log(selectedItems, "selected items are here");
  //     const newselectedItems = selectedItems.map((item) => {
  //       return {
  //         _id: item.id,
  //         name: item.category,
  //       };
  //     });

  //     dispatch(setChangeAPiFlag(true));

  //     if (selectedItems.length > 0) {
  //       dispatch(
  //         updateSubtaskData({
  //           ...subTaskData,
  //           [categoryName]:
  //             categoryName === "AdminId"
  //               ? newselectedItems
  //               : categoryName === "ReporterId"
  //               ? {
  //                   ...subTaskData[categoryName],
  //                   Reporter: [
  //                     ...(subTaskData[categoryName]?.Reporter || []),
  //                     ...newselectedItems,
  //                   ],
  //                 }
  //               : [...(subTaskData[categoryName] || []), ...newselectedItems], // Append for other cases
  //         })
  //       );

  //       saveSyncData(
  //         {
  //           ...subTaskData,
  //           [categoryName]:
  //             categoryName === "AdminId"
  //               ? newselectedItems
  //               : categoryName === "ReporterId"
  //               ? {
  //                   ...subTaskData[categoryName],
  //                   Reporter: [
  //                     ...(subTaskData[categoryName]?.Reporter || []),
  //                     ...newselectedItems,
  //                   ],
  //                 }
  //               : [...(subTaskData[categoryName] || []), ...newselectedItems], // Append for other cases
  //         },
  //         "time",
  //         "SubTaskForm"
  //       );

  //       dispatch(
  //         setToast({
  //           isOpen: true,
  //           messageContent: `${selectedItems[0]?.category} Added Successfully`,
  //           type: "success",
  //         })
  //       );
  //     }
  //   },
  //   [dispatch, subTaskData]
  // );

  useEffect(() => {
    console.log("useeffect in effect");
    setReporterAdded(false);
    setInitialReporterData([]);
  }, [subTaskData?._id]);

  // Toggle dropdown visibility
  // Toggle dropdown visibility
  const getCategories = async (tablename: string) => {
    console.log("fetheced category called");

    let response;
    let dbName;
    switch (tablename) {
      case "MaterialCategory":
        dbName = await initializeDatabase("MaterialCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "Manpowercategory":
        dbName = await initializeDatabase("ManpowerCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "ToolCategory":
        dbName = await initializeDatabase("ToolCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "machinaryCategory":
        dbName = await initializeDatabase("MachinaryCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "departmentdetails":
        dbName = await initializeDatabase("Departmentmaster");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "designationdetails":
        dbName = await initializeDatabase("Designationmaster");
        response = await window.electron.getCategoryData({ dbName });
        break;
      default:
        console.log("Invalid table name");
        response = null;
    }
    console.log(response, "this is response for selecte option");
    return (
      response?.map((e: { _id: string; name: string }) => ({
        id: e._id,
        category: e.name,
      })) || []
    );
  };
  const handleToggleDropdown = async (name: string, modelname?: string) => {
    if (modelname) {
      const data = await getCategories(modelname);
      setSelectedOptionApidata(data);
      (() => {
        switch (modelname) {
          case "MaterialCategory":
            SetprimaryLabelForAddCategoryType("Add Materials Category");
            return;
          case "machinaryCategory":
            SetprimaryLabelForAddCategoryType("Add Machinery Category");
            return;
          case "ToolCategory":
            SetprimaryLabelForAddCategoryType("Add Tool Category");
            return;
          case "Manpowercategory":
            SetprimaryLabelForAddCategoryType("Add Manpower Category");
            return;
          case "departmentdetails":
            SetprimaryLabelForAddCategoryType("Add Department");
            return;
          default:
            return <>Unknown </>;
        }
      })();
    }

    dispatch(openPopup(name));
  };
  const handleReporter = (item: any[], reportedData: any, parents: any) => {
    const newFormat = item.map((el) => ({
      _id: el.id,
      name: el.category,
      DepartmentId: el.parentId,
    }));
    console.log(item, "these are item in reporterasdfasdfasdf");
    console.log(newFormat);
    const formattedParents = parents.map((item: any) => {
      return {
        _id: item.id,
        name: item.category,
      };
    });
    let updatedReporters = [...(subTaskData.ReporterId?.Reporter || [])];

    // Find if the currentLevel exists in the array
    const existingIndex = updatedReporters.findIndex(
      (_, index) => index === currentLevel
    );

    if (existingIndex !== -1) {
      // If index exists, update designationId
      updatedReporters = updatedReporters.map((reporter, index) =>
        index === currentLevel
          ? {
              ...reporter,
              designationId: [...(reporter.designationId || []), ...newFormat], // Append new designation
            }
          : reporter
      );
    } else {
      // If index does not exist, add a new entry
      updatedReporters.push({
        _id: String(Date.now()), // Ensure unique _id
        Level: updatedReporters.length + 1, // Add the missing Level property
        designationId: newFormat,
      });
    }

    console.log("updatedata", updatedReporters);

    // Create a new state object with updated Reporter data
    const updatedSubtaskData = {
      ...subTaskData,

      DepartmentId: [
        ...((subTaskData as any).DepartmentId || []).filter(
          (item: any) =>
            !formattedParents.some((parent: any) => parent._id === item._id)
        ),
        ...formattedParents,
      ],
      DesignationId: [
        ...((subTaskData as any).DesignationId || []).filter(
          (item: any) =>
            !newFormat.some((selected: any) => selected._id === item._id)
        ),
        ...newFormat,
      ],

      ReporterId: {
        _id: subTaskData.ReporterId?._id || "1", // Ensure _id is always a string
        Reporter: updatedReporters, // Update only the Reporter array
      },
    };
    // in task start
    const updatedtaskData = {
      ...TaskData,
      DepartmentId: [
        ...((TaskData as any).DepartmentId || []).filter(
          (item: any) =>
            !formattedParents.some((parent: any) => parent._id === item._id)
        ),
        ...formattedParents,
      ],
      DesignationId: [
        ...((TaskData as any).DesignationId || []).filter(
          (item: any) =>
            !newFormat.some((selected: any) => selected._id === item._id)
        ),
        ...newFormat,
      ],
    };

    dispatch(updateTaskData(updatedtaskData as any));
    saveSyncData(updatedtaskData, "time", "TaskForm");

    // in task end
    showToast({
      messageContent: `${
        item.length > 1 ? "Reporters" : "Reporter"
      } Added Successfully!`,
      type: "success",
    });

    dispatch(updateSubtaskData(updatedSubtaskData as any));
    saveSyncData(updatedSubtaskData, "time", "SubTaskForm");
    dispatch(setChangeAPiFlag(true));
    dispatch(settaskChangeAPiFlag(true));
  };

  const deleteLevel = async (level: any) => {
    console.log(level, "devsire");
    let updatedReporters = [...(subTaskData.ReporterId?.Reporter ?? [])];

    // Remove the reporter at the given level
    updatedReporters = updatedReporters.filter((e, index) => index !== level);

    const updatedSubtaskData = {
      ...subTaskData,
      ReporterId: {
        _id: subTaskData.ReporterId?._id || "1",
        Reporter: updatedReporters || [],
      },
    };
    // Extract designation IDs from the reporter at the given level
    const designationIdsToDelete =
      subTaskData.ReporterId?.Reporter?.[level]?.designationId?.map(
        (d: any) => d
      ) || [];
    console.log("tjese are ids to delete", designationIdsToDelete);

    dispatch(setChangeAPiFlag(true));
    await dispatch(updateSubtaskData(updatedSubtaskData as any));
    await saveSyncData(updatedSubtaskData, "time", "SubTaskForm");
    await deleteDesignationsFromTask(
      designationIdsToDelete?.map((d: any) => d._id),
      designationIdsToDelete?.map((d: any) => d.DepartmentId)
    );
  };

  const [designationId, setdesignationId] = useState();
  const deleteSublevel = async (level: number, designationId: string) => {
    console.log(deleteDepartmentId, designationId, "this is deparment id");
    let updatedReporters = subTaskData.ReporterId?.Reporter.map((reporter) => ({
      ...reporter,
      designationId: [...reporter.designationId], // Create a copy of designationId array
    }));

    // Ensure level exists
    if (updatedReporters && updatedReporters[level]) {
      // Remove the specific designation
      updatedReporters[level].designationId = updatedReporters[
        level
      ].designationId.filter((item) => item._id !== designationId);

      if (updatedReporters[level].designationId.length === 0) {
        updatedReporters.splice(level, 1);
      }
    }
    dispatch(setChangeAPiFlag(true));
    await dispatch(
      updateSubtaskData({
        ...subTaskData,
        ReporterId: {
          _id: subTaskData.ReporterId?._id || "1",
          Reporter: updatedReporters || [],
        },
      })
    );

    await saveSyncData(
      {
        ...subTaskData,
        ReporterId: {
          _id: subTaskData.ReporterId?._id || "1",
          Reporter: updatedReporters || [],
        },
      },
      "time",
      "SubTaskForm"
    );
    await deleteDesignationsFromTask(
      [designationId],
      [deleteDepartmentId as any]
    );
  };
  const [levelIndex, setLevelIndex] = useState(null);
  console.log(levelIndex, "aayush");
  const handleDeleteReporter = async () => {
    dispatch(setChangeAPiFlag(true));
    dispatch(
      updateSubtaskData({
        ...subTaskData,
        ReporterId: { _id: subTaskData.ReporterId?._id || "1", Reporter: [] },
      })
    );

    await saveSyncData(
      {
        ...subTaskData,
        ReporterId: { _id: subTaskData.ReporterId?._id || "1", Reporter: [] },
      },
      "time",
      "SubTaskForm"
    );
  };

  useEffect(() => {
    console.log("after", reportedData);
  }, [reportedData]);

  const [selectedOption, setSelectedOption] = useState("");
  const [selectedOptionApidata, setSelectedOptionApidata] = useState([]);
  const [primaryLabelForAddCategoryType, SetprimaryLabelForAddCategoryType] =
    useState("");
  return (
    <div className={styles.subtask_creation_designation_container}>
      <div className={styles.subtask_creation_designation_header}>
        <SuryconLogo />
        <h3>Task Allocation</h3>
      </div>
      <div className={styles.subtask_creation_master_row}>
        <div style={{ minWidth: "50px", position: "relative" }}>
          <AddToolTip
            label="Task Manager"
            onClick={() => {
              dispatch(closePopup("DeleteTaskAllocation"));
              setTimeout(() => {
                handleToggleDropdown("TaskManager", "departmentdetails");
                setSelectedOption("departmentdetails");
              }, 400);
            }}
            className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
            data={subTaskData?.AdminId}
            isEdit={isEdit}
            handleDelete={(item) => {
              dispatch(closePopup("DeleteTaskAllocation"));

              setTimeout(() => {
                setDeleteId("AdminId");
                setRequiredThingsDelete(item);
                setRequiredThingsDeleteName("Task Manager");
                dispatch(openPopup("DeleteTaskAllocation"));
              }, 400);
            }}
          />
          {popups["TaskManager"] && (
            <AddCategoryType
              primaryLabel2="Add Task Manager"
              primaryLabel={"Department"}
              modelname={"taskManagerDataDetail"}
              singleSelected={true}
              isStepForm={true}
              title="Add Task Manager"
              data={selectedOptionApidata as TaskDataType[]}
              initialSelected={[
                ...(subTaskData?.AdminId || []),
                ...(subTaskData?.AssigneeId || []),
              ]}
              placeholder="Search"
              label="Task Manager"
              buttonLabel="Add Category"
              onSelect={(item, label, parents) => {
                console.log(item, "these items are hrer");
                handleSelect("AdminId", item, "", parents);
              }}
              onClose={() => dispatch(closePopup("TaskManager"))}
            />
          )}
        </div>

        <div style={{ minWidth: "50px", position: "relative" }}>
          <AddToolTip
            label="Assign To"
            onClick={() => {
              dispatch(closePopup("DeleteTaskAllocation"));
              setTimeout(() => {
                handleToggleDropdown("AssigneeId", "departmentdetails");
                setSelectedOption("departmentdetails");
              }, 400);
            }}
            className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
            data={subTaskData.AssigneeId}
            isEdit={isEdit}
            handleDelete={(item) => {
              dispatch(closePopup("DeleteTaskAllocation"));
              setTimeout(() => {
                setDeleteId("AssigneeId");
                setRequiredThingsDelete(item);
                setRequiredThingsDeleteName("Assignee");
                dispatch(openPopup("DeleteTaskAllocation"));
              }, 400);
            }}
          />
          {popups["AssigneeId"] && (
            <AddCategoryType
              primaryLabel2="Add Assignees"
              isStepForm={true}
              primaryLabel={"Department"}
              modelname={"assigneeDataDetail"}
              data={selectedOptionApidata as TaskDataType[]}
              placeholder="Search"
              label="Assignee"
              buttonLabel="Add Category"
              initialSelected={[
                ...(subTaskData?.AdminId || []),
                ...(subTaskData?.AssigneeId || []),
                ...(Array.isArray(subTaskData?.ReporterId?.Reporter)
                  ? subTaskData.ReporterId.Reporter.flatMap((reporter) =>
                      Array.isArray(reporter.designationId)
                        ? reporter.designationId.filter(Boolean)
                        : []
                    )
                  : []),
              ]}
              onSelect={(item, label, parents) => {
                handleSelect("AssigneeId", item, "Assignees", parents);
              }}
              onClose={() => dispatch(closePopup("AssigneeId"))}
            />
          )}
        </div>

        <div style={{ minWidth: "50px", position: "relative" }}>
          <AddToolTip
            label="Reporters"
            isReporter={true}
            isEdit={isEdit}
            setReporterAdded={setReporterAdded}
            reporterAdded={reporterAdded}
            onClick={() => {
              setInitialReporterData([
                {
                  _id: "1",
                  Level: -1,
                  designationId: [
                    {
                      _id: "1",
                      name: "",
                    },
                  ],
                },
              ]);
              setReporterAdded(true);
              setIsOpen(true);
            }}
            className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
          />
          <Dialogbox isOpen={reportedData ? true : isOpen}>
            <ReportingLevel
              key={JSON.stringify(reportedData)}
              isEdit={isEdit}
              setReporterAdded={setReporterAdded}
              reportedData={
                reportedData && reportedData?.length > 0
                  ? reportedData
                  : initialReporterData
              }
              closeDialogBox={() => {
                handleDeleteReporter();
                setIsOpen(false);
              }}
              onAddLevel={() => console.log("yes")}
              onAddLevelData={(id) => {
                setCurrentLevel(id);
                handleToggleDropdown("Reporter", "departmentdetails");
                setSelectedOption("departmentdetails");
              }}
              // handleDelete={(item) => {
              //
              // }}
              onDeleteReportedData={(index) => {
                if (index !== undefined && index !== null) {
                  setLevelIndex(index);
                  if (subTaskData?.ReporterId?.Reporter?.[index] == undefined) {
                    deleteLevel(index);
                    return;
                  }
                  dispatch(openPopup("DeleteReporterLevel"));
                }
              }}
              onDeleteRoleName={(
                levelIndex,
                designationIdIndex,
                departmentId
              ) => {
                setDeleteDepartmentId(departmentId);
                setReporterIndex(levelIndex);
                setdesignationId(designationIdIndex);
                dispatch(openPopup("DeleteReporter"));
              }}
            />
          </Dialogbox>
          {popups["Reporter"] && (
            <AddCategoryType
              primaryLabel2="Add Reporters"
              isStepForm={true}
              primaryLabel={"Department"}
              modelname={"reporterDataDetail"}
              title="Reporters"
              label="Reporter"
              data={selectedOptionApidata as TaskDataType[]}
              placeholder="Search"
              initialSelected={[
                ...(Array.isArray(subTaskData?.ReporterId?.Reporter)
                  ? subTaskData.ReporterId.Reporter.map((reporter) =>
                      reporter?.designationId ? reporter.designationId : []
                    ).reduce((acc, curr) => acc.concat(curr), [])
                  : []),
                ...(subTaskData?.AssigneeId || []),
              ]}
              buttonLabel="Add Category"
              onSelect={(item, label, parents) => {
                handleReporter(item, reportedData, parents);
              }}
              onClose={() => dispatch(closePopup("Reporter"))}
            />
          )}
          {popups["DeleteTaskAllocation"] && (
            <DeletePopup
              width="23rem"
              height="calc(100% - 9rem)"
              heightupperlimit="0rem"
              header={`Are you sure you want to delete this ${requiredThingsDeleteName} ?`}
              callbackDelete={async () => {
                if (deleteId) {
                  const newRequiredThings = (
                    subTaskData[deleteId as keyof AddData] as {
                      _id: string;
                      name: string;
                    }[]
                  )?.filter((item) => item._id !== requiredThingsDelete?._id);
                  dispatch(
                    updateSubtaskData({
                      ...subTaskData,
                      [deleteId]: newRequiredThings,
                    })
                  );
                  console.log(subTaskData, "these is subtraskasdfasdfasdhere");
                  await saveSyncData(
                    {
                      ...subTaskData,
                      [deleteId]: newRequiredThings,
                    },
                    "time",
                    "SubTaskForm"
                  );
                  console.log(
                    requiredThingsDelete,
                    "this is required things delete"
                  );
                  await deleteDesignationsFromTask(
                    [requiredThingsDelete?._id as any],
                    [requiredThingsDelete?.DepartmentId as any]
                  );
                  dispatch(settaskChangeAPiFlag(true));
                  dispatch(setChangeAPiFlag(true));

                  dispatch(closePopup("DeleteTaskAllocation"));
                  showToast({
                    messageContent: `${requiredThingsDelete?.name} deleted Successfully!`,
                    type: "success",
                  });
                }
              }}
              onClose={() => {
                dispatch(closePopup("DeleteTaskAllocation"));
              }}
            >
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    {requiredThingsDeleteName}
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4 style={{ color: "var(--text-black-87)" }}>
                      {requiredThingsDelete?.name}
                    </h4>
                  </div>
                </div>
              </div>
            </DeletePopup>
          )}
          {popups["DeleteReporter"] && (
            <DeletePopup
              width="23rem"
              height="calc(100% - 9rem)"
              heightupperlimit="0rem"
              header={`Are you sure you want to delete this Reporter ?`}
              callbackDelete={async () => {
                if (
                  reporterIndex !== undefined &&
                  designationId !== undefined
                ) {
                  deleteSublevel(reporterIndex, designationId);
                  showToast({
                    messageContent: `${
                      subTaskData?.ReporterId?.Reporter[
                        reporterIndex
                      ]?.designationId?.find((e) => e?._id == designationId)
                        ?.name
                    } deleted successfully!`,
                    type: "success",
                  });
                  dispatch(setChangeAPiFlag(true));
                }
              }}
              onClose={() => {
                dispatch(closePopup("DeleteReporter"));
              }}
            >
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Reporter
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4 style={{ color: "var(--text-black-87)" }}>
                      {
                        subTaskData?.ReporterId?.Reporter[
                          reporterIndex
                        ]?.designationId?.find((e) => e?._id == designationId)
                          ?.name
                      }
                    </h4>
                  </div>
                </div>
              </div>
            </DeletePopup>
          )}
          {popups["DeleteReporterLevel"] && (
            <DeletePopup
              width="23rem"
              height="calc(100% - 9rem)"
              heightupperlimit="0rem"
              header={`Are you sure you want to delete these Reporters ?`}
              callbackDelete={async () => {
                if (levelIndex !== null) {
                  deleteLevel(levelIndex);
                  showToast({
                    messageContent: `Reporters deleted successfully!`,
                    type: "success",
                  });
                  dispatch(setChangeAPiFlag(true));
                }
              }}
              onClose={() => {
                dispatch(closePopup("DeleteReporterLevel"));
              }}
            >
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Reporters
                  </p>
                  <div
                    className=""
                    // style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    {subTaskData?.ReporterId?.Reporter[
                      levelIndex
                    ]?.designationId?.map((e) => (
                      <h4 style={{ color: "var(--text-black-87)" }}>
                        {e?.name}
                      </h4>
                    ))}
                  </div>
                </div>
              </div>
            </DeletePopup>
          )}
        </div>
      </div>
    </div>
  );
};

export default SubaskCreationDesignation;
