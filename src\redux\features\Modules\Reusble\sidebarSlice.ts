import { createSlice } from "@reduxjs/toolkit";
import { mainMenuItems } from "../../../../utills/Routes/menuItems";
import { SideBarState } from "../../../Interfaces/Modules/Reuseable/Reuseable";


const initialState:SideBarState={
    label: "Tools",
    currentActiveSubRouteIndex:0,
    currentActiveRotueIndex: 6,
    subRoutes:[],
}

const sidebarslice = createSlice({  
    name: "label",
    initialState,
    reducers: {
        setLabel: (state, action) => {
            state.label = action.payload;
        },
        setSubRoutes:((state,action)=>{
            state.subRoutes=action.payload
        }),
        currentActiveSubRouteIndex:((state ,action: { payload: number})=>{
            state.currentActiveSubRouteIndex= action.payload
        })
    }
})

export const { setLabel, setSubRoutes, currentActiveSubRouteIndex } =
  sidebarslice.actions;
export default sidebarslice.reducer;
