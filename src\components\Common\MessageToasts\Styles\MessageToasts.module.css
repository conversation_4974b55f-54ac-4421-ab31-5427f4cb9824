.messageToast_container {
  position: fixed;
  top: 18px;
  left: 40%;
  z-index: 10000000;
  min-width: 20rem;
  min-height: 5rem;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0px 0px 6px 0px #91a1a199;
  margin: 1rem;
  /* padding: 0.4rem 0.8rem 0 0.5rem; */
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  text-align: center;
}

/* .messageToast_messageContent {
  text-transform: capitalize;
} */

.warningAnimation {
  animation: bounceIn 1.2s ease,
    vanishInward 1s ease 2.5s forwards;
}

.dangerAnimation {
 animation: bounceIn 1.2s ease, swing 0.4s ease 1.2s,
    vanishInward 1s ease 1.8s forwards;
}

.successAnimation {
 animation: bounceIn 2.2s ease, vanishInward 1s ease 2.9s forwards;
}

.messageToast_header {
  text-align: right;
  position: absolute;
  right: 15px;
  top: 10px;
}

.messageToast_messageIcon {
  width: 48px;
  height: 48px;
  /* background-color: #fff6d9; */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.messageToast_messageContainer {
  display: flex;
  gap: 1rem;
  align-items: center;
  padding: 0.8rem;
  color: var(--text-black-87);
  /* word-break: break-all; */
  word-wrap: normal;
}

.successIcon {
  width: 26px;
  height: 26px;
  animation: fillIcon 3s ease-in-out forwards;
}

@keyframes strokeCircleAnimation {
  0% {
    stroke-dashoffset: 80;

    stroke: #FFFFFF;

  }

  100% {
    stroke-dashoffset: 0;

    stroke: #005968;

  }
}

@keyframes strokeTickAnimation {
  0% {
    stroke-dashoffset: 40;
    stroke: #005968;

  }

  100% {
    stroke-dashoffset: 0;
    stroke: #005968;

  }
}

.successIcon {
  width: 26px;
  height: 26px;
  animation: strokeCircleAnimation 1s ease-in-out forwards, strokeTickAnimation 1s ease-in-out 1s forwards;

}


.successIcon path:first-child {
  animation: strokeCircleAnimation 1s ease-in-out forwards;
}

.successIcon path:last-child {
  animation: strokeTickAnimation 0.7s ease-in-out 0.7s forwards;

}

@keyframes bounceIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }

  50% {
    transform: scale(1.1);

    opacity: 1;
  }

  75% {
    transform: scale(0.9);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes swing {
  20% {
    transform: rotate(5deg);
  }

  40% {
    transform: rotate(-5deg);
  }

  60% {
    transform: rotate(5deg);
  }

  80% {
    transform: rotate(-5deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

@keyframes vanishInward {
  0% {
    opacity: 1;
    transform: scale(1);
  }


  100% {
    opacity: 0;
    transform: scale(0);
  }
}