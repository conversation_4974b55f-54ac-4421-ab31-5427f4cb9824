.trigger_event_dialog_form_container {
  /* margin: 4rem; line to remove */
  width: 34rem;
  padding: 2rem 1.5rem;
  /* height: 50rem; */
  /* min-height: 70vh; */
  /* max-height: 40rem; */
  border-radius: 2.3rem;
  backdrop-filter: blur(150px);
  animation: slideIn 0.5s ease-out;
  box-shadow: var(--extra-shadow-five);
  position: fixed;
  top: 7rem;
  right: 1.5rem;
  height: calc(100% - 8.5rem);
  z-index: 999;
  display: flex;
  flex-direction: column;
  overflow: auto;
  /* justify-content: space-between; */
}

.unitselected {
  border: 1px solid;
  color: var(--primary_color);
  /* backdrop-filter: blur(100px); */
  box-shadow: 0px 0px 4px 0px #91a1a1bf;
  border-radius: 24px;
  border-color: var(--primary_color);
}

.disable_field {
  opacity: 0.5;
  cursor: not-allowed;
}

.action_time_interval_overlay{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.subtask_confirmation_subcard {
  padding: 1rem;
  border-radius: 0.75rem;
  margin-top: 0.5rem;
  background-color: #ffffff99;
}

.subtask_action_discard_time_interval {
  margin-top: 0.5rem;
  display: flex;
  justify-content: space-between;
  position: relative;
}

.error {
  border-color: var(--warning_color) !important;
}

.time_unit {
  border-radius: 3.5625rem;
  background-color: var(--primary_background);
  padding: 0.25rem 0.5rem;
  color: var(--primary_color);
}

@keyframes slideIn {
  from {
    transform: translate(100%, 0);
  }

  to {
    transform: translate(0, 0);
  }
}

@keyframes slideOut {
  from {
    transform: translate(0, 0);
  }

  to {
    transform: translate(100%, 0);
  }
}

.trigger_event_dialog_form_container.closing {
  animation: slideOut 0.5s ease-out;
}

.trigger_event_dialog_form_first_main {
  display: flex;
  /* flex-direction: column; */
  flex-wrap: wrap;
  /* row-gap:1rem ; */
  /* row-gap:1rem ; */
  column-gap: 1.5rem;
  /* height: calc(100% - 8.5rem); */
  /* background-color: red; */
  overflow: auto;
}
.trigger_event_dialog_form_top_header{
  position: relative;
}
.trigger_event_dialog_form_top {
  position: relative;
  height: calc(100% - 6rem);
  overflow: auto;
}
.trigger_event_dialog_form_top_text {
  margin: auto;
  text-align: center;
  color: var(--primary_color);
  margin-bottom: 1.5rem;
  position: relative;
  max-width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  /* white-space: nowrap; */
}
.trigger_event_cross_icon {
  position: absolute;
  top: 0.25rem;
  right: 0.5rem;
  outline: none !important;
  border: none;
  background: transparent;
  cursor: pointer;
}
.trigger_event_button_div {
  margin-top: 2rem;
  display: flex;
  column-gap: 1.5rem;
  justify-content: center;
  position: relative;
}
.trigger_event_response_div {
  width: 100%;
  /* background-color: aquamarine; */
}
.trigger_event_keyHeader {
  color: var(--text-black-60);
  text-align: left;
  margin-bottom: 0.75rem;
}
.trigger_event_action_section {
  margin-top: 1rem;
  width: 100%;
  /* background-color: red; */
}
.trigger_event_action_radio_keys {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 0.5rem;
}
.trigger_event_response_time {
  margin-top: 0.8rem;
  width: 100%;
}
.mt_confirmation_button_div {
  display: flex;
  column-gap: 1.5rem;
  justify-content: center;
  position: fixed;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
}
@media (max-width: 1536px) {
  .trigger_event_dialog_form_container {
    width: 32rem;
  }
  /* .trigger_event_dialog_form_first_main {
    max-height: 65vh;
  } */
}
/*🍁Trigger event  form_css from here*/
.mt_radio_component {
  display: flex;
  align-items: center;
  border: 1px solid var(--text-black-28);
  border-radius: 1.5rem;
  padding: 1rem;
  width: 100%;
}

.mt_radio_button {
  /* border: 1px solid var(--text-black-28); */
  accent-color: var(--primary_color);
  margin-right: 0.5rem;
}

.mt_radio_button:checked {
  accent-color: var(--primary_color);
}
.mt_radio_button:hover {
  accent-color: var(--primary_color);
}

.mt_radio_button_label_text {
  color: var(--text-black-87);
}

/*🍁. Reusable form popup css from here */
.mt_popup_component {
  border-radius: 1.5rem;
  position: relative;
  border: none;
  width: 100%;
}

.mt_popup_inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}
.mt_popup_title {
  color: var(--text-black-87);
}
.mt_popup_label {
  padding: 0.2rem 0.4rem;
  /* background-color: var(--text-white-100); */
  backdrop-filter: blur(10px);
  border-radius: 0.35rem;
  color: var(--text-black-60);
  position: absolute;
  transition: transform 0.2s ease-in;
  /* top: -1.75rem; */
}
.mt_popup_label.dropdownOpen {
  position: absolute;
  transform: translateY(-1.8rem);
  /* background-color: var(--text-white-100); */
  transition: all 0.1s ease-in;
}
.mt_popup_arrowicon {
  position: relative;
  transform: rotate(180deg) translateY(-0.2rem);
  transition: transform 0.3s ease;
  cursor: pointer;
}

.mt_popup_arrowicon.selected {
  transform: rotate(0deg) translateY(-0.1rem);
  transition: transform 0.3s ease;
}
.unit_popup_mt_container {
  position: absolute;
  top: 3rem;
  right: 0rem;
  background-color: #005968;
  animation: animate 0.3s ease-in;
}
@keyframes animate {
  0% {
    top: 1rem;
    z-index: -3;
    opacity: 0;
  }
  50% {
    opacity: 0;
  }
  100% {
    top: 3rem;
    opacity: 1;
    z-index: 1;
  }
}
@keyframes animate2 {
  0% {
    top: 3rem;
    z-index: 1;
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    top: 0;
    opacity: 0;
    z-index: -1;
  }
}

.time_unit {
  position: absolute;
  top: 50%;
  border-radius: 3.5625rem;
  background-color: var(--primary_background);
  transform: translateY(-55%);
  padding: 0.25rem 0.5rem;
  color: var(--primary_color);
  right: 2.5rem;
}

.trigger_event_start_after_time {
  position: relative;
  top: 0;
  bottom: 0;
}

.unit_popup_mt_container.selected {
  animation: animate 0.3s ease-in;
}
.unit_popup_mt_container.notSelected {
  animation: animate2 1s ease-in-out;
}

.error {
  border-color: var(--warning_color) !important;
}
