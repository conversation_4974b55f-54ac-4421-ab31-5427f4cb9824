/* styles for tareget Badge end by <PERSON><PERSON><PERSON><PERSON> singh */
.target_badge_outer_container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem;
  box-shadow: 0px 0px 3px 0px #91a1a180;
  gap: 0.5rem;
  border-radius: 10rem;
  width: fit-content;
  border: 1px solid transparent;
  min-width: 94px;
  position: relative;
  transition: all 0.2s ease;
  margin: 0;
  box-sizing: border-box;
}

.border {
  border: 1px solid var(--primary_color);
  box-sizing: border-box;
  margin: -1px;
}

.order {
  flex-direction: row-reverse;
}

.targetBubble {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
  /* min-width: 22px;
  min-height: 22px; */
}

.iconContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.toggle_with_bubble {
  box-shadow: none;
}

/* styles for tareget Badge end by <PERSON><PERSON><PERSON><PERSON> singh */

.approval_button_text {
  color: var(--text-white-100);
}

.monthly_target_header_buttons {
  padding-right: 1rem;
  padding-left: 1rem;
}

/* styles for target_badge for floor listing in monthly target start by rattan<PERSON><PERSON> singh */
/* Floor badge styles - updated */
.floor_badge {
  height: 3.5rem;
  background-color: #ffffffde !important; /* Light gray background for normal state */
  padding: 0.7rem 1.2rem !important;
  border-radius: 2rem !important;
  min-width: 150px !important;
  box-sizing: border-box;
  position: relative;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

/* Approved floor style */
.floor_badge_approved {
  height: 3.5rem;
  background-color: #f0f8f8 !important; /* Light teal/mint background */
  padding: 0.7rem 1.2rem !important;
  border-radius: 2rem !important;
  box-shadow: 0px 8px 40px #91a1a180 !important; /* Unified box shadow */
  min-width: 150px !important;
  box-sizing: border-box;
  position: relative;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

/* Rejected/declined floor style */
.floor_badge_rejected {
  height: 3.5rem;
  background-color: #fff0f0 !important; /* Light pink/red background */
  padding: 0.7rem 1.2rem !important;
  border-radius: 2rem !important;
  box-shadow: 0px 8px 40px #91a1a180 !important; /* Unified box shadow */
  min-width: 150px !important;
  box-sizing: border-box;
  position: relative;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

/* Selected floor style */
.floor_badge_selected {
  height: 3.5rem;
  background-color: white !important;
  padding: 0.7rem 1.2rem !important;
  border-radius: 2rem !important;
  border: 2px solid var(--primary_color) !important;
  box-shadow: 0px 0px 5px #91a1a180 !important; /* Updated for uniform shadow */
  min-width: 150px !important;
  box-sizing: border-box;
  position: relative;
  transition: all 0.2s ease;
  margin: 0;
}

/* Floor badge bubble styles */
.floor_badge_bubble {
  background-color: #e6f0f0 !important; /* Light teal background for normal state */
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--primary_color) !important;
  padding: 0.3rem !important;
  border-radius: 100%;
  width: 3rem;
  height: 2rem;
  font-weight: 500;
  box-shadow: 0px 8px 40px #91a1a180 !important; /* Unified box shadow */
}

/* Selected floor bubble style */
.floor_badge_bubble_selected {
  background-color: var(--primary_color) !important;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white !important;
  padding: 0.3rem !important;
  border-radius: 100%;
  width: 3rem;
  height: 2rem;
  font-weight: 500;
  box-shadow: 0px 8px 40px #91a1a180 !important; /* Unified box shadow */
}

/* Floor text styles */
.floor_badge_text {
  color: #555555;
  font-weight: 500;
}

.floor_badge_text_selected {
  color: var(--primary_color);
  font-weight: 600;
}

.floor_badge_text_approved {
  color: #006666; /* Darker teal for approved text */
  font-weight: 500;
}

.floor_badge_text_rejected {
  color: #cc0000; /* Red for rejected text */
  font-weight: 500;
}

.assigned_task_slide_floor_bubble_text {
  color: var(--text-black-60);
}

.assigned_task_slide_floor_bubble_target_badge {
  white-space: nowrap;
  padding: 0.25rem;
}

.target_badge_bubble_for_subtasks {
  background-color: var(--main_background) !important;
  color: var(--primary_color) !important;
  width: 3rem;
  height: 2rem;
}
.target_badge_for_subtasks {
  min-width: 9.5rem !important;
  background-color: var(--primary_color);
  color: var(--main_background);
}
.secondtarget_badge_for_subtasks {
  background-color: var(--primary_background);
  height: 48px;
  justify-content: start !important;
  min-width: 130px;
}
.secondtarget_badge_for_subtasks p {
  color: #000000de;
}
.secondtarget_badge_bubble_for_subtasks {
  width: 58px;
  height: 38px;
  background-color: var(--main_background);
}
/* styles for targetBadge for planningTable header */
.target_badge_for_planning_table_header {
  color: white;
  cursor: pointer;
  box-shadow: -0.0625rem -0.125rem 0.375rem 0rem rgba(255, 255, 255, 0.15),
    -0.125rem -0.25rem 0.75rem 0rem rgba(255, 255, 255, 0.15),
    0.1875rem 0.25rem 0.75rem 0rem rgba(0, 0, 0, 0.33),
    0.0625rem 0.125rem 0.375rem 0rem rgba(0, 0, 0, 0.35);
  padding: 0.375rem 0.5rem 0.375rem 0.5rem;
  margin-top: .5rem;
  margin-bottom: .5rem;
}

.task_assigned_selected_floor_slide_selected {
  border: 1px solid var(--primary_color);
}

.task_assigned_selected_floor_slide_not_selected {
  border: 1px solid transparent;
}

.assigned_task_slide_floor_bubble_text_selected {
  color: white;
}

.project_summary_quality_button {
  padding: 0.625rem 0.75rem;
  cursor: pointer;
}

.project_summary_quality_btn_text {
  color: var(--warning_color);
}

.project_summary_export_button {
  padding: 0.7rem 1rem;
  cursor: pointer;
}

.project_summary_export_btn_text {
  color: var(--text-black-60);
}
.mt_card_input {
  border: none;
  outline: none;
  color: #000000de;

  background-color: transparent;
}
.project_summary_comments_button {
  padding: 0.7rem 0.625rem 0.7rem 0.625rem;
  cursor: pointer;
}

.project_summary_export_comments_text {
  color: var(--text-white-100);
}

.assigned_task_add_task_btn {
  padding: 0.75rem 1rem;
}

.assigned_task_add_task_text {
  color: var(--text-white-100);
}

@media (max-height: 820px) {
  .assigned_task_slide_floor_bubble_target_badge {
    padding: 0.25rem;
  }
}

@media (max-width: 1280px) {
  .approval_button_text {
    font-size: 0.8rem;
  }

  .export_button_text {
    font-size: 0.8rem;
  }
}

/* material popup */
.material_popup {
  color: var(--primary_color);
  height: 24px;
  width: 40px;
}

/* button in task header */
.task_header_padding {
  padding: 0.6rem 1rem;
}

/* button in task header */
.task_header_padding {
  padding: 0.6rem 1rem;
}

.secondtarget_badge_for_subtasks_bubble {
  color: var(--primary_color);
}
.planningtableheadertargetbadge {
  border-radius: 10px;
  background-color: var(--main_background);
  height: 56px !important;
}

.array_target_badge {
  background-color: #00596b;
  color: var(--text-white-87);
  width: 1rem;
  height: 1rem;
  border-radius: 5rem;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.second_text_for_array {
  font-size: 0.4rem;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 50%;
  /* right: 0.85rem; */
  transform: translate(-50%, -50%);
  border-radius: 10rem;
  background-color: #00596b;
  width: 1rem;
  height: 1rem;
  color: white;
  z-index: 5;
}

.monthly_target_badge {
  height: 2.6rem;
  background-color: #ffffffde !important; /* Light gray background for normal state */
  padding: 0.7rem 1.2rem !important;
  border-radius: 2rem !important;

  box-sizing: border-box;
  position: relative;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}
.monthly_target_badge_selected {
  height: 2.6rem;
  background-color: white !important;
  padding: 0.7rem 1.2rem !important;
  border-radius: 2rem !important;
  border: 2px solid var(--primary_color) !important;
  box-shadow: 0px 0px 5px #005968a8 !important; /* Updated for uniform shadow */

  box-sizing: border-box;
  position: relative;
  transition: all 0.2s ease;
  margin: 0;
}
.monthly_target_badge_text {
  color: #555555;
  font-weight: 500;
}
.monthly_target_badge_text_selected {
  color: var(--primary_color);
  font-weight: 600;
}


.disabled {
  background-color: #c3d7db !important;
  color: #ffffff !important;
  filter: none;
  opacity: 1;
  pointer-events: none;
  cursor: not-allowed;
}