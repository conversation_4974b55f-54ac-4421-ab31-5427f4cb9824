.mt_target_card_delete {
  position: absolute;
  right: -0.8rem;
  cursor: pointer;
  top: -0.8rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--secondary-warning-color);
  padding: 0.25rem;
  border-radius: 1rem;
}

.verticalProgress {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 2%;
}

.planningProgress_container {
  flex: 0 0 25%;
  min-width: 15rem;
  padding: 1rem 3rem 2rem;
  max-height: 80vh;
  overflow: scroll;
}

.progressSteps {
  display: flex;
  flex-direction: column;
}

.progressStep {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  position: relative;
  transition: all 0.3s ease;
}

.stepMarker {
  width: 0px;
  height: 0px;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  transition: transform 0.3s ease-in-out;
  transform-origin: center center;
  color: white;
  background: var(--primary_color);
  border: 4px solid #e8b000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.stepAddMarker {
  top: 5px;
  width: 46px;
  height: 46px;
  left: -1.2rem;
  border-radius: 50%;
  position: relative;
  cursor: pointer;
  transition: transform 0.3s ease;
  transform-origin: center center;
  color: white;
  background: var(--primary_color) !important;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0px 4px 4px 0px #00000040;
}

/* .stepMarker:hover, */
.stepMarker.active {
  transform: scale(1);
}
.loader {
  transition: transform 0.3s ease;
}

.loader:hover {
  transform: scale(1.2);
  transition: transform 0.3s ease;
}

.stepMarker::after {
  content: "";
  position: absolute;
  top: 25px;
  left: 50%;
  width: 15px;
  height: 65px;
  background: var(--primary_color) !important;
  transform: translateX(-50%);
}
.selectedloader:hover {
  transform: scale(1) !important;
}
.progressStep:last-child .stepMarker::after {
  display: none;
}
.activeMarker:hover {
  transform: scale(1) !important;
}
.stepLabel {
  margin-left: 2.5rem;
  line-height: 21.82px;
  text-align: left;
  backdrop-filter: blur(40px);
  box-shadow: var(--extra-shdow-four);
  border-radius: 24px;
  width: 14.5rem;
  height: 2.8rem;
  position: relative;
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  top: 0.4rem;

  transition: padding 0.1s ease-in-out;
  overflow: hidden;
}

.stepLabel h4 {
  color: var(--primary_color);
  white-space: nowrap; /* Prevents wrapping */
  overflow: hidden; /* Hides overflow text */
  text-overflow: ellipsis; /* Adds "..." at the end */
  max-width: 150px; /* Set based on your design */
  display: block;
}
.active.stepLabel {
  font-size: 1.3rem;
  height: 5rem;
  font-weight: 600;
  transition: padding 0.1s ease-in-out;
  border: 1px solid var(--primary_color);
}
.active.stepLabel h4 {
  font-size: 21px;
}
.stepLine {
  position: absolute;
  top: 80%;
  left: 28%;
  width: 15px;
  height: 100px;
  background: var(--primary_color) !important;
  visibility: hidden;
  margin-left: -2rem;
  z-index: 0;
}

.stepLine.activeLine {
  visibility: visible;
}

.progressCircle {
  position: absolute;
  width: 88px;
  height: 88px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
  z-index: 1;
  background: transparent;
  box-shadow: 0px 4px 4px 0px #00000040;
  margin-left: -2.75rem;
}
.plus_tag {
  color: var(--text-white-100);
  font-size: 1.5rem;
}

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 2rem;
  z-index: 10;
  width: 300px;
}

.modalBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9;
}

.modal input {
  width: 100%;
  padding: 0.5rem;
  margin-bottom: 1rem;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.modal button {
  width: 100%;
  padding: 0.5rem;
  border: none;
  border-radius: 5px;
  background: #004d4d;
  color: white;
  cursor: pointer;
}

.stepContainer {
  display: flex;
  align-items: center;
}

.activeLoader {
  width: 6rem !important;
  transition: transform 0.3s ease-in-out;
}

.shadow {
  -webkit-filter: drop-shadow(0px 4px 4px #000000a1);
  filter: drop-shadow(0px 4px 4px #000000bb);
}

@media (max-width: 1700px) and (min-width: 1201px) {
  .planningProgress_container {
    flex: 0 0 23%;
    min-width: 13rem;
    padding: 1rem 3rem 2.3rem;
    max-height: 80vh;
    overflow: auto;
  }
}

@media (max-width: 1700px) {
  .stepLabel {
    width: 11rem;
    height: 2.6rem;
    padding: 0.45rem 0.9rem;
    margin-left: 2.2rem;
    font-size: 0.95rem;
  }

  .stepLabel h4 {
    font-size: 1rem;
  }

  .active.stepLabel {
    height: 4.7rem;
    font-size: 1.15rem;
  }

  .active.stepLabel h4 {
    font-size: 1.05rem;
  }
}

.stepLabelFill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #92bec5;
  opacity: 0.2; /* subtle background */
  z-index: 0;
  border-radius: 24px;
  transition: width 0.3s ease;
}

.stepLabel h4 {
  position: relative; /* Keep text above the fill */
  z-index: 1;
}

.summaryDivData {
  background: #fff;
  border-radius: 12px;
  padding: 16px 18px;
  margin-bottom: 0;
  box-shadow: 0 1px 8px 0 rgba(60,60,60,0.07);
}
.summaryDataContent {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
