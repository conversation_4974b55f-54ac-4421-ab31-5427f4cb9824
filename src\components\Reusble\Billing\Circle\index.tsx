import React from "react";
import styles from "./Styles/Circle.module.css";

interface CircleProps {
  content?: React.ReactNode;
  icon?: React.ReactNode;
  nameclass?: string;
  contentColor?: string;
  contentBackgroundColor?: string;
}

const Circle: React.FC<CircleProps> = ({
  nameclass,
  content,
  icon,
  contentColor,
  contentBackgroundColor,
}) => {
  const appliedClass = nameclass && styles[nameclass] ? styles[nameclass] : "";
  // export color variables"backgroundColor,color"  :: Lovely Sehotra
  return (
    <div
      className={`${styles.circle} ${appliedClass}`}
      style={{
        backgroundColor: contentBackgroundColor,
        color: contentColor,
      }}
    >
      {icon ? icon : content}
    </div>
  );
};

export default Circle;
