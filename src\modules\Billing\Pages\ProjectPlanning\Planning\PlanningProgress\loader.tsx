import { NavLink, useLocation } from "react-router-dom";
import styles from './Styles/PlanningProgress.module.css'
export const CircleProgress = ({
    colour,
    percentage,
  }: {
    colour: string;
    percentage: number ;
  }) => {
    const r = 50;
    const circ = 2 * Math.PI * r;
    const strokePct = ((100 - percentage) * circ) / 100;
  
    return (
      <circle
        r={r}
        cx={100}
        cy={100}
        fill="transparent"
        stroke={strokePct !== circ ? colour : ""}
        strokeWidth={"01rem"}
        strokeDasharray={circ}
        strokeDashoffset={percentage ? strokePct : 0}
        strokeLinecap="butt"
        style={{ shapeRendering: "geometricPrecision" }} 
      ></circle>
    );
  };
  
  export function Loader({
    percentage,
    colour,
    text,
    number,
    height,
    isactive,
    width,

  }: {
    percentage: string | number;
    colour: string;
    text?: string;
    number?: string;
    height?: string | number;
    width?: string | number;
    isactive:boolean;
  }) {
    const location = useLocation();
    const locationArr = location.pathname.split("/");
    locationArr.pop();
    const newPath = locationArr.join("/");
  
    return (
      <div
        style={{
          position: "relative",
          top: "0.5rem",
          display: "flex",
          alignItems: "center",
          gap: "0rem",
          zIndex:2
        }}
        className={`${styles.loader} ${isactive && styles.selectedloader}`}
      >
        <svg
          width={width ?? "2.75rem"}
          height={height ?? "2.75rem"}
          viewBox="0 0 200 200"
          className={`${styles.shadow}`}
        >
          {/* Background Circle */}
          <circle
            cx="100"
            cy="100"
            r="50"
            fill="#00596b" // Background color inside the circle
          />
  
          {/* Progress Circles */}
          <g style={{ transform: "rotate(-90deg)", transformOrigin: "center" }}>
            <CircleProgress percentage={100} colour="#00596b" />
            <CircleProgress colour={colour}  percentage={typeof percentage==='string'?parseFloat(percentage):percentage} />
          </g>
  
          {/* Text in the center */}
          <text
  x="100"
  y="100"
  textAnchor="middle"
  dy=".4em"
  fontSize={isactive?'1.5rem':'1.5rem'}
  fontWeight="600"
  fill="white" // Ensure text is visible over the background color
>
  {number ?? ""}
</text>
        </svg>
        {/* <NavLink
          to={`${newPath}/${linkTo}`}
          className={({ isActive }) => (isActive ? "active" : "non-active")}
        >
          <p>{text}</p>
        </NavLink> */}
      </div>
    );
  }