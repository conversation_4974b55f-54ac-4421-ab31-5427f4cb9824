import { useEffect, useState } from "react";

import styles from "../Styles/SubVersionContainers.module.css";
import DetailVersion from "./DetailVersion";
import { RootState } from "../../../redux/store";
import { openPopup } from "../../../redux/features/Modules/Reusble/popupSlice";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import {
  setSubCard,
  setSubDetail,
} from "../../../redux/features/Modules/Billing/BillingApproval/Slices/VersionHistorySlice";
import { useSelector } from "react-redux";
import { Calendar } from "../../../assets/icons";

interface SubVersionContainersProps {
  showDetail: boolean;
  versionData: {
    version: string;
    employees: [];
  };
  versionIndex: number;
}

const SubVersionContainers: React.FC<SubVersionContainersProps> = ({
  showDetail,
  versionData,
  versionIndex,
}) => {
  const dispatch = useAppDispatch();
  const currentOpenPopup = useAppSelector(
    (state: RootState) => state.popup.popups
  );
  const { subCard, versionId } = useSelector(
    (state: RootState) => state.versionHistorySlice
  );
  const [selected, setSelected] = useState(false);
  const handleClick = () => {
    // setSelected(selected);
    // dispatch(openPopup("versionHistory"));
  };
  const handleOpenSubDetail = (versionId: string, employeeId: string) => {
    dispatch(
      setSubDetail({
        subDetail: {
          _id: "681371eb83c1b5b05fe0d523",
          version: "V_0052",
          data: [
            {
              updated: {
                subtask_details: [
                  {
                    _id: "67b859cbd9d52a583c01981d",
                    Master_subtask_id: "67b5d458d1fd531e1257f35c",
                    quantity: "123",
                    weightage: "123",
                    manpowerId: [
                      {
                        _id: "67c68d0bdf50726817d42fcd",
                        Mastermanpower_id: "67729985f0c1fd1d8c78b175",
                        quantity: 130,
                      },
                      {
                        _id: "67c591064c08a75feec6f67a",
                        Mastermanpower_id: "67729985f0c1fd1d8c78b175",
                        quantity: 123123,
                      },
                      {
                        _id: "67c590e34c08a75feec6f5f8",
                        Mastermanpower_id: "67729851f0c1fd1d8c78b16f",
                        quantity: 222,
                      },
                      {
                        _id: "67c1a13a37a6daab1b7bee97",
                        Mastermanpower_id: "6772974bf0c1fd1d8c78b16b",
                        quantity: 1032323232,
                      },
                      {
                        _id: "67c1a12837a6daab1b7bee60",
                        Mastermanpower_id: "6772974bf0c1fd1d8c78b16b",
                        quantity: 1,
                      },
                    ],
                    machinaryId: [
                      {
                        _id: "67bdae9671336979f5bffe07",
                        Mastermachinary_id: "6777b74b56b96d3fa39235df",
                        quantity: 10232432,
                        Masterbrand_id: "6777b74b56b96d3fa39235dc",
                        spec: "MIG welding ,550-650 VPM,250 A,220 V,50-60 Hz",
                      },
                    ],
                    tool_id: [
                      {
                        _id: "67bdae5c71336979f5bffda0",
                        MasterTool_id: "67777b561548196957b68003",
                        quantity: 10,
                        Masterbrand_id: "67777b561548196957b68000",
                        spec: "Nylon, 50m length, 200kg load capacity",
                      },
                      {
                        _id: "67bdac664de129365409bd5a",
                        MasterTool_id: "6776a226f35f75dee22f3cf6",
                        quantity: 10,
                        Masterbrand_id: "6776a226f35f75dee22f3cf3",
                        spec: "48-inch length",
                      },
                      {
                        _id: "67bda2012e901d22105b45bb",
                        MasterTool_id: "67777d251548196957b6801e",
                        quantity: 10,
                        Masterbrand_id: "67777d251548196957b6801c",
                        spec: "1.5m handle, high-carbon steel blade",
                      },
                      {
                        _id: "67bda1b02e901d22105b4537",
                        MasterTool_id: "6776a226f35f75dee22f3cf6",
                        quantity: 10,
                        Masterbrand_id: "6776a226f35f75dee22f3cf4",
                        spec: "40 mm blade",
                      },
                      {
                        _id: "67bda1b02e901d22105b4538",
                        MasterTool_id: "67777c4b1548196957b68011",
                        quantity: 10,
                        Masterbrand_id: "67777c4b1548196957b6800f",
                        spec: "Electric, 1.5 HP motor",
                      },
                      {
                        _id: "67bda1b02e901d22105b4539",
                        MasterTool_id: "67777bc61548196957b6800a",
                        quantity: 10232332,
                        Masterbrand_id: "67777bc61548196957b68008",
                        spec: "600W motor, 10mm chuck size, 3000 RPM.",
                      },
                      {
                        _id: "67bda1b02e901d22105b453a",
                        MasterTool_id: "67777cc11548196957b68018",
                        quantity: 10,
                        Masterbrand_id: "67777cc11548196957b68016",
                        spec: "10/7 capacity, diesel engine, 300L drum.",
                      },
                      {
                        _id: "67bd9d4df24b2b872ead8f0c",
                        MasterTool_id: "67777cc11548196957b68018",
                        quantity: 10,
                        Masterbrand_id: "67777cc11548196957b68016",
                        spec: "10/7 capacity, diesel engine, 300L drum.",
                      },
                      {
                        _id: "67bd9195a154c7210646264f",
                        MasterTool_id: "6776a226f35f75dee22f3cf6",
                        quantity: 10,
                        Masterbrand_id: "6776a226f35f75dee22f3cf3",
                        spec: "48-inch length",
                      },
                      {
                        _id: "67bd9195a154c72106462650",
                        MasterTool_id: "6776a226f35f75dee22f3cf6",
                        quantity: 10,
                        Masterbrand_id: "6776a226f35f75dee22f3cf4",
                        spec: "40 mm blade",
                      },
                      {
                        _id: "67bd912fa154c72106462598",
                        MasterTool_id: "67777a821548196957b67ff5",
                        quantity: 10,
                        Masterbrand_id: "67777a821548196957b67ff3",
                        spec: "ergonomic handle.",
                      },
                      {
                        _id: "67bd9132a154c721064625b5",
                        MasterTool_id: "67777a821548196957b67ff5",
                        quantity: 10,
                        Masterbrand_id: "67777a821548196957b67ff3",
                        spec: "ergonomic handle.",
                      },
                      {
                        _id: "67bd913ea154c721064625d7",
                        MasterTool_id: "67777a821548196957b67ff5",
                        quantity: 10,
                        Masterbrand_id: "67777a821548196957b67ff3",
                        spec: "ergonomic handle.",
                      },
                      {
                        _id: "67bd8f0b0e8301ad93ab4d30",
                        MasterTool_id: "67777b561548196957b68003",
                        quantity: 10,
                        Masterbrand_id: "67777b561548196957b68001",
                        spec: "Nylon, 100m length, 400kg load capacity",
                      },
                    ],
                    materialId: [
                      {
                        _id: "67c16eacbe57359f19d3890b",
                        Mastermaterial_id: "6777941d9ec4bcde79d4ba77",
                        quantity: 10,
                        Masterbrand_id: "6777941d9ec4bcde79d4ba75",
                        spec: "Material: Mild Steel",
                      },
                    ],
                    updatedAt: 1741074800280,
                  },
                ],
                _id: "67b591b3c5c46dde4b4a29ef",
                payment: 6262,
                Area: 233345,
                Duration: 36,
                Shuttering: 10,
                Master_taskid: "67b429d49a4d63daab5a508d",
              },
            },
            {
              updated: {
                subtask_details: {
                  "0": {
                    quantity: "",
                    weightage: "423426",
                    manpowerId: {
                      "0": {
                        quantity: 13012,
                      },
                      "2": {
                        quantity: 22212,
                      },
                      "4": {
                        quantity: 11234,
                      },
                    },
                    updatedAt: 1741075149347,
                  },
                },
              },
            },
            {
              added: {
                subtask_details: {
                  "0": {
                    manpowerId: {
                      "0": {
                        type: "unskilled",
                        __v: 0,
                      },
                      "5": {
                        _id: "67c1a12837a6daab1b7bee60",
                        Mastermanpower_id: "6772974bf0c1fd1d8c78b16b",
                        quantity: 11234,
                      },
                    },
                  },
                },
              },
              updated: {
                subtask_details: {
                  "0": {
                    quantity: null,
                    weightage: 423426,
                    manpowerId: {
                      "0": {
                        Mastermanpower_id: "677299e2f0c1fd1d8c78b177",
                        quantity: 2,
                        _id: "67c6b306ac9d39aca7b4106d",
                      },
                      "1": {
                        _id: "67c68d0bdf50726817d42fcd",
                        quantity: 13012,
                      },
                      "2": {
                        _id: "67c591064c08a75feec6f67a",
                        Mastermanpower_id: "67729985f0c1fd1d8c78b175",
                        quantity: 123123,
                      },
                      "3": {
                        _id: "67c590e34c08a75feec6f5f8",
                        Mastermanpower_id: "67729851f0c1fd1d8c78b16f",
                        quantity: 22212,
                      },
                      "4": {
                        _id: "67c1a13a37a6daab1b7bee97",
                        quantity: 1032323232,
                      },
                    },
                    updatedAt: 1741075207096,
                  },
                },
              },
            },
          ],
          updatedBy: "SURYADEV0028",
          createdAt: "2025-03-04T07:59:45.538Z",
          itemtype: "LocationDetailversionshistory",
          location_id: "67f3a097203625ce495428cc",
          updatedAt: "2025-03-04T08:10:47.539Z",
          __v: 0,
          id: "681371eb83c1b5b05fe0d523",
        },
      })
    );
  };

  return (
    <div
      className={`${styles.subVersionContainers_accordian_main_container}`}
      onClick={handleClick}
    >
      <div className={`${styles.subVersionContainers_accordian_labels}`}>
        <div
          className={`small_text_p ${styles.subVersionContainers_accordian_version_label}`}
          style={{
            backgroundColor:
              versionIndex === 0
                ? "var(--primary_color)"
                : "var(--text-white-100)",
          }}
        >
          <h4
            style={{ color: versionIndex !== 0 ? "var(--primary_color)" : "" }}
          >
            {versionData?.version ?? "V 006"}
          </h4>
        </div>
        <div
          className={styles.subversion_status_container}
          style={{
            color: versionIndex == 0 ? "var(--secondary_color)" : "",
            backgroundColor: versionIndex == 0 ? "var(--extra_color)" : "",
          }}
        >
          {versionIndex !== 0 && (
            <div className={styles.subversion_icon}>
              <Calendar />
            </div>
          )}
          <p
            className={versionIndex == 0 ? "very_small_text_p" : "small_text_p"}
            style={{
              whiteSpace: "nowrap",
              color: versionIndex !== 0 ? "var(--border-secondary)" : "",
            }}
          >
            {versionData?.employees?.length
              ? " Pending For Approval"
              : "UpdateTime"}
          </p>
        </div>
      </div>
      {showDetail && (
        <>
          <div className={`${styles.subVersionContainers_main_container}`}>
            <div className={`${styles.subVersionContainers_main_parent}`}>
              {versionData.employees &&
                versionData.employees.map((item: any) => (
                  <div
                    onClick={() => {
                      dispatch(setSubCard({ subCard: !subCard }));
                      subCard &&
                        handleOpenSubDetail(versionId!, item.employeeId);
                    }}
                    className={`${styles.subVersionContainers_version_container}`}
                  >
                    {/* <div className={styles.sub_hover}></div> */}
                    <div
                      className={`${
                        subCard
                          ? styles.subVersionContainers_attaching_line_selected
                          : styles.subVersionContainers_attaching_line
                      } }`}
                    ></div>
                    <div
                      className={`${
                        subCard
                          ? styles.subVersionContainers_detailed_history_selected
                          : styles.subVersionContainers_detailed_history
                      }`}
                    >
                      <DetailVersion employeeData={item} />
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default SubVersionContainers;
