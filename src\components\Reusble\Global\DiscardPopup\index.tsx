import React, { useState } from "react";

import { DiscardPopupProps } from "./Interfaces/interface";
import styles from "./Styles/Discard.module.css";
import Button from "../Button";
import { CloseIcon } from "../../../../assets/icons";
import { closePopup } from "../../../../redux/features/Modules/Reusble/popupSlice";
import { useAppDispatch } from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";

const DiscardPopup: React.FC<DiscardPopupProps> = ({
  header,
  callbackCancel,
  isClosing = false,
  callbackDiscard,
  children,
  height,
  top,
  width,
  right,
}) => {

  const handleClose = () => {
    callbackDiscard();
  };

  return (
    <div
      className={`${styles.discard_popup_form_container} ${
        isClosing ? styles.closing : ""
      }`}
      style={{ height: height, width: width, right: right, top: top }}
    >
      <div className={styles.discard_popup_header}>
        <h3
          className={styles.discard_popup_form_top_text}
          style={{ color: "var(--warning_color)" }}
        >
          {header}
        </h3>
        <button className={styles.closeButton} onClick={callbackCancel}>
          <CloseIcon />
        </button>
      </div>
      <div className={styles.discard_popup_main_body}>{children}</div>
      <div className={styles.discard_popup_button_div}>
        <Button type="Cancel" Content="No" Callback={callbackCancel} />
        <Button type="Approve" Content="Yes" Callback={handleClose} />
      </div>
    </div>
  );
};

export default DiscardPopup;
