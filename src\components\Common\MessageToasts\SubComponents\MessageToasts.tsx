import {
  Danger,

  Success,
  YellowWarning,
} from "../../../../assets/icons";
import { ToastProps } from "../../CommonInterface/CommonInterface";
import styles from "../Styles/MessageToasts.module.css";

// type ToastType = "warning" | "danger" | "success";

const Toast: React.FC<ToastProps> = ({ type, messageContent }) => {
  let icon, backgroundColor, toastStyle, animationClass;

  // Conditional logic based on the toast type
  switch (type) {
    case "warning":
      icon = <YellowWarning />;
      backgroundColor = "#FFF6D9";
      toastStyle = styles.warningToast;
      animationClass = styles.warningAnimation;
      break;
    case "danger":
      icon = <Danger />;
      backgroundColor = "#F6E6E6";
      toastStyle = styles.dangerToast;
      animationClass = styles.dangerAnimation;
      break;
    case "success":
      icon = <Success className={styles.successIcon} />;
      backgroundColor = "#F0F6F6";
      toastStyle = styles.successToast;
      animationClass = styles.successAnimation;
      break;
    default:
      icon = <YellowWarning />;
      backgroundColor = "#FFF6D9";
      toastStyle = styles.warningToast;
      animationClass = styles.warningAnimation;
      break;
  }

  return (
    <div
      className={`${styles.messageToast_container} ${toastStyle} ${animationClass}`}
    >
      <div className={styles.messageToast_messageContainer}>
        <div>
          <div
            className={styles.messageToast_messageIcon}
            style={{ background: backgroundColor }}
          >
            {icon}
          </div>
        </div>
        <div className={styles.messageToast_messageContent}>
          {messageContent}
        </div>
      </div>
    </div>
  );
};

export default Toast;
