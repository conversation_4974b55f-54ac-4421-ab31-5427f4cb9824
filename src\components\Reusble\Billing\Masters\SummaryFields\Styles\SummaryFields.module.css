.summarymaster_container {
  display: flex;
  flex-wrap: wrap;
  /* Allow wrapping if there are many items */
  gap: 16px;
  /* Adjust gap between items as needed */
}
.flexValue {
  display: flex;
  gap: 16px; /* Adjust the gap as needed */
  flex-wrap: wrap; /* Ensures proper wrapping if needed */
}

.summarymaster_inputs {
  flex: 1;
  padding: 0.45rem;
  overflow-y: auto;
}

/* Default: Single-column layout */
.summaryDivData {
  display: flex;
  align-items: center;
}

.summaryDataContent {
  display: flex;
  flex-direction: column;
  background: #ffffff99;
  border-radius: 0.75rem;
  width: 30.8rem;
  /* max-width: 28.5rem; */
  min-height: 3.188rem;
  padding: 1rem;
  /* word-break: break-all; */
  overflow-wrap: break-word;
  white-space: normal;
  margin: 0.6rem;
  gap: 0.2rem;
  line-height: 1.363rem;
  text-align: left;
}

/* Two-column layout when flexLayout is true */
.flexLayout .summaryDivData {
  flex-basis: calc(50% - 8px);
  /* 50% width minus the gap */
  max-width: calc(50% - 8px);
}
