.toggle_main {
  background: var(--main_background);
  border-radius: 10rem;
  max-width: 100%;
}

.toggleContainer {
  position: relative;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  border-radius: 100px;
  max-width: 100%;
  height: 42px;
  align-items: center;
}

.threeColumns {
  /* grid-template-columns: repeat(3, 1fr); */
   grid-template-columns: repeat(2, 1fr);
}

.slider {
  position: absolute;
  width: 50%;
  height: calc(100% - 4px);
  background: var(--primary_color);
  border-radius: 100px;
  transition: transform 0.3s ease;
  left: 2px;
  top: 2px;
}

.threeColumns .slider {
  /* width: calc(33.333% - 2px); */
  width: calc(50% - 2px);
}

.sliderCenter {
  transform: translateX(100%);
}

.sliderRight {
  transform: translateX(200%);
}

.toggle_label {
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  transition: color 0.3s ease;
  cursor: pointer;
  color: var(--text-black-60);
  padding: 0 8px;
  user-select: none;
}

.toggle_label:focus {
  outline: none;
}

.toggle_label.selected {
  color: var(--text-white-100);
}

.targetBubbleUnchecked {
  background-color: var(--primary_background) !important;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #00000099 !important;
  margin-left: 0.25rem;
  border-radius: 50%;
  min-width: 22px;
  min-height: 22px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.targetBubblechecked {
  background-color: var(--main_background);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--primary_color) !important;
  margin-left: 0.25rem;
  border-radius: 50%;
  min-width: 22px;
  min-height: 22px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.billing_toggle_bubble {
  background-color: var(--line-color) !important;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white !important;
  margin-left: 0.25rem;
  border-radius: 50%;
  min-width: 22px;
  min-height: 22px;
  transition: background-color 0.3s ease, color 0.3s ease;
}
