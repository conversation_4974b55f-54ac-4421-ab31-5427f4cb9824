.versionHistory_accordian_container {
    top: 7rem;
    left: 0.1rem;
    /* background-color: red; */
    height: 90%;

    overflow-y: scroll;
    padding: 0.1rem 1rem;
    display: flex;
    flex-direction: column;
    margin-left: -1rem;
    gap: 1rem;
    margin-top: 1rem;
}

.versionHistory_accordian_parent {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    z-index: 3;

}

.versionHistory_accordian_icons {
    padding: 0.5rem;
}

.versionHistory_accordian {
    width: 50%;
    box-shadow: var(--primary-shadow);
    border-radius: 24px;
    margin-top: .1rem;
    margin-right: 0.1rem;
    padding: 1rem;
    color: var(--text-black-60);
    display: flex;
    flex-direction: column;
    gap: 1rem;

}

.versionHistory_accordian_timeStapms {
    display: flex;
    gap: 2rem;
    align-items: center;
    width: fit-content;
}

.versionHistory_accordian_labels {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.versionHistory_accordian_timeStapms_labels {
    display: flex;
    gap: 0.3rem;
    align-items: center;
    justify-content: center;
}

.versionHistory_accordian_version_label {
    background-color: var(--primary_background);
    padding: 0.5rem 0.7rem;
    border-radius: 24px;
}

@media screen and (max-width: 1536px) {
    .versionHistory_accordian_container {
        height: 70%;
        min-height: 50%;
    }
}