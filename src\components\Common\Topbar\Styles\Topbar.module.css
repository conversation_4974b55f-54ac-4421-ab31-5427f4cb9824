/* Parent Container */
.topbar_parent {
  gap: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-content: baseline;
  margin-top: 1rem;
  width: 100%;
  margin-left: 3.3rem;

}

/* Heading Section */
.topbar_heading {
  font-weight: 600;
  font-size: 2.25rem;
  flex: 1;

  min-width: 150px;

  max-width: 300px;

  white-space: nowrap;
  overflow: hidden;

  text-overflow: ellipsis;

}

.topbar_heading h1 {
  max-width: 100%;

  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Search Bar Section */
.topbar_searchbar_container {
  width: 22rem;

  flex-shrink: 0;

  margin: 0 auto;

}

@media only screen and (max-width: 1400px) {
  .topbar_searchbar_container {
    width: 18rem;

  }
}



/* Utilities Section */
.topbar_utilities {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-shrink: 0;

}

/* HR Details Section */
.topbar_Hr_details {
  display: flex;
  align-items: center;
  gap: 1rem;
  border: 1px solid;
  border-image-source: linear-gradient(130.72deg,
      rgba(237, 231, 231, 0.07) -16.06%,
      rgba(251, 251, 251, 0.05) 82.03%);
  box-shadow: var(--primary-shadow);
  padding: 0.4rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 500;
}

.topbar_Hr_details_image{
  display: flex;
  align-items: center;
}

.topbar_Hr_details_image img {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
}

.topbar_Hr_details_name {
  color: var(--text-black-87);
}

.topbar_Hr_details_dropdown {
  display: flex;
  justify-content: center;
}

@media screen and (max-width: 1200px) {
  .topbar_searchbar_container {
    width: 14rem;

  }


  .topbar_heading {
    max-width: 200px;
  }

  .topbar_parent {
    margin-left: 2.3rem;
  }
  .topbar_utilities{
    display: flex;
    gap: 0.75rem;
  }

  .topbar_Hr_details_image img {
    height: 2.75rem;
    width: 2.75rem;
  }
  
  .topbar_Hr_details{
    padding: 0.5rem 0.25rem 0.25rem 0.4rem;
  }
  
}