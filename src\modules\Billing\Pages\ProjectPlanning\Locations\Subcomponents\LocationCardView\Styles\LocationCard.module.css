.location_container {
  overflow-y: auto;
  max-height: 79vh;
  background-color: var(--main_background);
  
}
/* Custom scrollbar */
.location_container::-webkit-scrollbar {
  width: 4px;
}

.location_container::-webkit-scrollbar-track {
  background: transparent;
}

.location_container::-webkit-scrollbar-thumb {
  background-color: #006d77;
  border-radius: 4px;
}


.location_cards {
  padding: 0.5rem;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.location_cards > * {
  overflow: hidden;
  width: 100%;
}

.location_cards h2, 
.location_cards h3, 
.location_cards p {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media(max-width:1568px){
    .location_cards{
        grid-template-columns: repeat(2, 1fr);
    }
}
@media(max-width:1200px){
  .location_container{
    width: fit-content;
  }
    .location_cards{
        grid-template-columns: repeat(3, 1fr);
        width:1510px;
    }
}