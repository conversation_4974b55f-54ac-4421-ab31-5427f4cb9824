import React, { useEffect, useState } from "react";
import PlanningtableHeader from "../PlanningTable/SubComponents/PlanningtableHeader";
import { initializeDatabase } from "../../../../../../functions/functions";
import MaterialSubCardSummary from "./SubComponents/MaterialSubCardSummary";
import AddToolTip from "../../../../../../components/Reusble/Global/AddToolTip";
import TargetCards from "../../../../../../components/Reusble/Global/TargetCards/TargetCards";
import styles from "./Styles/MaterialsTable.module.css";
import { useAppSelector, useAppDispatch } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import Circle from "../../../../../../components/Reusble/Billing/Circle";
import { SuryconLogo } from "../../../../../../assets/icons";
import { Loader } from "../../../../../../assets/loader";

const MaterialsTable: React.FC = () => {
  const [edit, setEdit] = useState(false);
  const [materials, setMaterials] = useState<any[]>([]);
  const [total, setTotal] = useState<number>(0);

  const selectedLocationId = useAppSelector(
    (state) => state.projectPlanning.selectedLocationId
  );
  const selectedLocationTaskId = useAppSelector(
    (state) => state.projectPlanning.selectedLocationTaskId
  );



  const fetchMaterialDataFromLocalDB = async (): Promise<void> => {
    if (!selectedLocationId || !selectedLocationTaskId) return;

    const dbName = await initializeDatabase("materialtable");

    const fetchedDoc = await window.electron.getDocumentByParentId({
      dbName,
      categoryId: "taskId",
      catId: selectedLocationTaskId,
    });

    console.log("Fetched Document:", fetchedDoc);

    if (Array.isArray(fetchedDoc)) {
        const transformedData = fetchedDoc.map((category: any) => ({
      ...category,
      materials: category.materials.map((material: any) => ({
        ...material,
        quantity: material.subtasks?.reduce(
          (sum: number, subtask: any) => sum + (subtask.quantity || 0),
          0
        ), 
      })),
    }));
      setMaterials(transformedData);
      console.log(transformedData,"sahil ka data")
    } else {
      setMaterials([]);
    }
  };

  useEffect(() => {
    if (selectedLocationId && selectedLocationTaskId) {
      fetchMaterialDataFromLocalDB();
    }
  }, [selectedLocationId, selectedLocationTaskId]);

  useEffect(() => {
    const totalItemsLength = materials?.reduce(
      (total: number, category: any) =>
        total +
        (category.materials?.reduce(
          (catTotal: number, item: any) => catTotal + 1,
          0
        ) || 0),
      0
    );
    setTotal(totalItemsLength);
  }, [materials]);

  const toggleEditState = () => {
    setEdit((prev) => !prev);
  };

  return (
    <>
      <div className={styles.planningTableContainer}>
        <PlanningtableHeader
          isPlanning={false}
          edit={edit}
          totalItems={total}
          toggleEditState={toggleEditState}
        />
        <div className="subtasks-container">
          <div className={styles.target_details_lower_outer_container}>
            <div id="scroll-container" className={styles.monthly_target_cards}>
              {materials.length === 0 ? (
                <div className={styles.loader_loading}>
                  <img
                    src={Loader.suryaconLogo}
                    alt="No Data Found"
                    className={styles.loader_loading_image}
                  />
                </div>
              ) : (
                <>
                  {materials.map((category, categoryIndex) => (
                    <React.Fragment key={category.categoryId || categoryIndex}>
                      <div className={styles.mt_card_outer_container}>
                        <div className={styles.mt_card_inner_container}>
                          <AddToolTip
                            label={category.categoryName}
                            onClick={() => console.log("tooltip clicked")}
                            isPllaning={false}
                            className={styles.material_add_tooltip_subcontainer}
                          />
                          <Circle
                            nameclass={"material_summary_circle"}
                            content={
                              <p className="small_text_p material_summary_circle_p">
                                {category.materials?.length || 0}
                              </p>
                            }
                          />
                        </div>
                        <TargetCards
                          data={category.materials || []}
                          edit={edit}
                        />
                      </div>
                      {categoryIndex !== materials.length - 1 && (
                        <div className={styles.taskcreation_line_container}>
                          <span className={styles.dottedline_wrapper}></span>
                          <SuryconLogo />
                          <span className={styles.dottedline_wrapper}></span>
                        </div>
                      )}
                    </React.Fragment>
                  ))}
                </>
              )}
            </div>

              <MaterialSubCardSummary />
            
          </div>
        </div>
      </div>
    </>
  );
};

export default MaterialsTable;