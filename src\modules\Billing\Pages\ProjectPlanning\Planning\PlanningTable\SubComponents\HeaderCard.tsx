import React, { useEffect, useLayoutEffect, useRef, useState } from "react";
import styles from "../Styles/PlanningTable.module.css";
import { DesignationIcon } from "../../../../../../../assets/icons";
import { useDispatch, useSelector } from "react-redux";
import { setCurrentCardId } from "../../../../../../../redux/features/Modules/Billing/BillingApproval/Slices/BillingApprovalSlice";
import { RootState } from "../../../../../../../redux/store";

const HeaderCard = ({
  taskid,
  regex,
  id,
  item,
  title,
  edit = false,
  onChange,
  maxlength = 10,
  unit = "sft",
  icon: Icon,
}) => {
  console.log(item, "item in header card");
  const inputRef = useRef<HTMLInputElement>(null);
  const [inputWidth, setInputWidth] = useState("1px");
  const [localInputValue, setLocalInputValue] = useState(
    item?.toString() || ""
  );

  console.log(localInputValue, "this is lcoal input value in header cards");
  const currentCardEditId = useSelector(
    (state: RootState) => state?.monthlyTarget?.CurrentEditcardId
  );
  const dispatch = useDispatch();
  console.log(localInputValue, "this is local input value in header card");

  useLayoutEffect(() => {
    if (inputRef.current) {
      const span = document.createElement("span");
      span.style.visibility = "hidden";
      span.style.position = "absolute";
      span.style.whiteSpace = "nowrap";
      span.style.fontSize = getComputedStyle(inputRef.current).fontSize;
      span.textContent = localInputValue || "0";

      document.body.appendChild(span);
      setInputWidth(`${span.offsetWidth + 2}px`);
      document.body.removeChild(span);
    }
  }, [localInputValue]);
  useEffect(() => {
    setLocalInputValue(item?.toString() || "");
    //   if (currentCardEditId === id && inputRef.current) {
    //   inputRef.current.blur();
    // }
  }, [item, taskid, id]);

  useEffect(() => {
    if (currentCardEditId === id && inputRef.current) {
      inputRef.current.focus();
    }
  }, [currentCardEditId, id]);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (edit) {
      dispatch(setCurrentCardId(id));
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;
    if (!edit) return;

    if (new RegExp(regex).test(val)) {
      setLocalInputValue(val);
      onChange?.(e);
    }
  };

    const handleBlur = () => {
    dispatch(setCurrentCardId(""));
  };

  const shouldShowInput = edit && currentCardEditId === id;
  
  return (
    <div
      onClick={handleClick}
      className={styles.planningtable_header_rightdivs}
    >
      {Icon ? <Icon /> : <DesignationIcon />}
      <div>
        <p className="small_text_p">{title}</p>
        <p style={{ display: 'flex'}}>
          {(shouldShowInput ||
            (localInputValue && localInputValue !== "0")) && (
            <>
              <input
                ref={inputRef}
                onChange={handleChange}
                onBlur={handleBlur}
                value={localInputValue === "0" ? "" : localInputValue || ""}
                readOnly={!edit}
                className={`${styles.mt_card_input} input_16px`}
                type="text"
                style={{ width: inputWidth }}
                maxLength={maxlength}
              />
              <span>{unit}</span>
            </>
          )}
        </p>
      </div>
    </div>
  );
};

export default HeaderCard;
