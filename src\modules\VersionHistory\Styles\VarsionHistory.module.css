.versionHistory_main {

  height: 95%;
  width: 250px;
  position: absolute;
  right: 2%;
  z-index: 2;

  color: var(--main_background);
  background-color: var(--main-background);

  backdrop-filter: blur(150px);
  /* border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -22.43%,
    rgba(251, 251, 251, 0.05) 75.66%
  ); */
  border: 1px solid 1px solid linear-gradient(130.72deg, rgba(237, 231, 231, 0.07) -22.43%, rgba(251, 251, 251, 0.05) 75.66%);
  box-shadow: var(--pop-up-card-shadow);
  display: flex;
  border-radius: 20px;
  padding: 2rem 1rem;
  overflow: hidden;
  transition: 0.4s ease-in;
  width: 320px;
  /* border: 1px solid; */
}

.icon_container {
  height: 20px;
  width: 20px;
  position: absolute;
  right: 20px;
  top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.versionHistory_main_expand {
  width: 700px;
}

.versionHistory_container {
  height: 100%;
}

.versionHistory_heading {
  width: 100%;
  text-align: center;
}

.versionHistory_parent {
  /* height: 100%; */
  padding: 1rem 0rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1.3rem;
  width: fit-content;
  color: var(--text-black-60);
  position: relative;
}

.versionHistory_background_line {
  height: 95%;
  width: 1px;
  background-color: var(--text-black-28);
  position: absolute;
  left: 1rem;
  z-index: 0;
}

.versionHistory_version_labels {
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 1rem;
  width: fit-content;
  margin-bottom: -1rem;
}

.versionHistory_version_labels h4 {
  color: var(--primary_color);
  line-height: 1.2rem;
  white-space: nowrap;
}

.versionHistory_list_box {
  /* background-color: aqua; */
  /* block-size: 90%; */
  min-block-size: 550px;
  max-block-size: 550px;
  overflow-y: scroll;
}