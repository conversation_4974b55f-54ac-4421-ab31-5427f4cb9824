import { useSelector } from "react-redux";
import { CloseIcon, User, VersionIcon } from "../../assets/icons";
import { closePopup } from "../../redux/features/Modules/Reusble/popupSlice";
import {
  useAppDispatch,
  useAppSelector,
} from "../../redux/hooks/Modules/Reduxhooks/ReduxHooks";

import { RootState } from "../../redux/store";
import styles from "./Styles/VarsionHistory.module.css";
import UserVersionHistory from "./SubComponents/UserVersionHistory";
import VersionHistoryContainers from "./SubComponents/VersionHistoryContainers";
import VersionHistoryIcons from "./SubComponents/VersionHistoryIcons";

const VersionHistory = () => {
  const dispatch = useAppDispatch();
  const currentOpenPopup = useAppSelector(
    (state: RootState) => state.popup.popups
  );
  const { subCard, subDetail } = useSelector(
    (state: RootState) => state.versionHistorySlice
  );
  const handleClick = () => {
    dispatch(closePopup("versionHistory"));
  };
  // console.log("versionHistory", versionHistory);
  const versionData = [
    {
      version: "V_003",
      employees: [
        {
          employeename: "RATTAN_DEV",
          employeeId: "SURYADEV0028",
          designation: "siteengineer",
          photo: "demo",
          createdAt: "2025-04-07T19:41:13.027Z",
        },
        {
          employeename: "RATTAN_DEV",
          employeeId: "SURYADEV0028",
          designation: "siteengineer",
          photo: "demo",
          createdAt: "2025-03-04T07:59:45.538Z",
        },
      ],
    },

    {
      _id: "680b6f9adf71b04794a5a7bc",
      version: "V_002",
      data: {
        updated: {
          name: "pa1111111111111fggf111oinbnouodsd1111nkaj t",
          Description: "dhdfgdf1111111111iunbinugfgfggf11dsds1111111gggg",
          updatedAt: "2025-04-25T11:18:50.694Z",
        },
      },
      updatedBy: "SURYADEV0026",
      createdAt: "2025-04-25T11:18:25.216Z",
      itemtype: "LocationDetails_approved_history",
      updatedAt: "2025-04-25T11:18:50.780Z",
      __v: 0,
      location_id: "67f3a097203625ce495428cc",
    },
    {
      _id: "680b6f9adf71b04794a5a7bc",
      version: "V_002",
      data: {
        updated: {
          name: "pa1111111111111fggf111oinbnouodsd1111nkaj t",
          Description: "dhdfgdf1111111111iunbinugfgfggf11dsds1111111gggg",
          updatedAt: "2025-04-25T11:18:50.694Z",
        },
      },
      updatedBy: "SURYADEV0026",
      createdAt: "2025-04-25T11:18:25.216Z",
      itemtype: "LocationDetails_approved_history",
      updatedAt: "2025-04-25T11:18:50.780Z",
      __v: 0,
      location_id: "67f3a097203625ce495428cc",
    },
    {
      _id: "680b6f9adf71b04794a5a7bc",
      version: "V_002",
      data: {
        updated: {
          name: "pa1111111111111fggf111oinbnouodsd1111nkaj t",
          Description: "dhdfgdf1111111111iunbinugfgfggf11dsds1111111gggg",
          updatedAt: "2025-04-25T11:18:50.694Z",
        },
      },
      updatedBy: "SURYADEV0026",
      createdAt: "2025-04-25T11:18:25.216Z",
      itemtype: "LocationDetails_approved_history",
      updatedAt: "2025-04-25T11:18:50.780Z",
      __v: 0,
      location_id: "67f3a097203625ce495428cc",
    },
    {
      _id: "680b6f9adf71b04794a5a7bc",
      version: "V_002",
      data: {
        updated: {
          name: "pa1111111111111fggf111oinbnouodsd1111nkaj t",
          Description: "dhdfgdf1111111111iunbinugfgfggf11dsds1111111gggg",
          updatedAt: "2025-04-25T11:18:50.694Z",
        },
      },
      updatedBy: "SURYADEV0026",
      createdAt: "2025-04-25T11:18:25.216Z",
      itemtype: "LocationDetails_approved_history",
      updatedAt: "2025-04-25T11:18:50.780Z",
      __v: 0,
      location_id: "67f3a097203625ce495428cc",
    },
  ];
  // console.log("subDetail", subDetail);
  return (
    //

    <div
      className={
        currentOpenPopup["versionHistory"]
          ? `${styles.versionHistory_main} ${
              subCard && styles.versionHistory_main_expand
            }`
          : styles.versionHistory_main
      }
    >
      <div className={styles.icon_container} onClick={handleClick}>
        <CloseIcon />
      </div>

      <div className={`${styles.versionHistory_container}`}>
        <div className={`${styles.versionHistory_parent}`}>
          <div className={`${styles.versionHistory_version_labels}`}>
            <VersionHistoryIcons icon={VersionIcon} padding="0.3rem 0.5rem" />
            <h4>Version History</h4>
          </div>
          <div className={`${styles.versionHistory_background_line}`}></div>
          <div className={styles.versionHistory_list_box}>
            {versionData &&
              versionData.map((version: any, index) => (
                <VersionHistoryContainers
                  versionData={version}
                  versionIndex={index}
                />
              ))}
            {/* <VersionHistoryContainers /> */}
            {/* <VersionHistoryContainers />
            <VersionHistoryContainers />
            <VersionHistoryContainers />
       
            <VersionHistoryContainers />
            <VersionHistoryContainers />
            <VersionHistoryContainers /> */}
          </div>

          <div className={`${styles.versionHistory_version_labels}`}>
            <VersionHistoryIcons icon={VersionIcon} padding="0.3rem 0.5rem" />
            <p>Older Version</p>
          </div>
        </div>
      </div>
      <UserVersionHistory />
    </div>
  );
};

export default VersionHistory;
