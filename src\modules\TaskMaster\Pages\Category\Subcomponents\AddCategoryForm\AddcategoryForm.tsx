import { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import {
  useAddTaskCategoryMutation,
  useEditTaskCategoryMutation,
} from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import styles from "./AddcategoryForm.module.css";
import { RootState } from "../../../../../../redux/store";
import {
  generateSummaryData,
  isValidValue,
} from "../../../../../../functions/functions";

import { resetInputValues } from "../../../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { CloseIcon } from "../../../../../../assets/icons";
import SummaryPage from "../../../../../../components/Reusble/Global/SummaryPage/SummaryPage";
import FloatingLabelInput from "../../../../../../components/Reusble/Global/FloatingLabel";
import Button from "../../../../../../components/Reusble/Global/Button";
import { closePopup } from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import {
  useAddManpowerCategoryMutation,
  useRegisterMachineryCategoryMutation,
  useRegisterMaterialCategoryMutation,
  useRegisterToolCategoryMutation,
  useUpdateMachineryCategoryMutation,
  useUpdateManpowerCategoryMutation,
  useUpdateMaterialsCategoryMutation,
  useUpdateToolsCategoryMutation,
} from "../../../../../../redux/api/Modules/Billing/Billingapi";
import { useToast } from "../../../../../../hooks/ToastHook";
import { setIsLocalChange } from "../../../../../../redux/features/Modules/Reusble/backupSlice";
interface AddCategoryFormProps {
  onClose: () => void;
  catId?: string | null;
  variant?: string;
}

export function AddCategoryForm({
  onClose,
  variant,
  catId = null,
}: AddCategoryFormProps) {
  const [AddTaskCategoryApi] = useAddTaskCategoryMutation();
  const [editTaskCategory] = useEditTaskCategoryMutation();

  //for master's categories
  const [editToolCategory] = useUpdateToolsCategoryMutation();
  const [editMaterialCategory] = useUpdateMaterialsCategoryMutation();
  const [editManpowerCategory] = useUpdateManpowerCategoryMutation();
  const [editMachineryCategory] = useUpdateMachineryCategoryMutation();
const showToast = useToast();
  //for master's categories
  const [addManpowerCategory] = useAddManpowerCategoryMutation();
  const [addMaterialCategory] = useRegisterMaterialCategoryMutation();
  const [addToolCategory] = useRegisterToolCategoryMutation();
  const [addMachineryCategory] = useRegisterMachineryCategoryMutation();

  const [wasTrue, setWasTrue] = useState(false);
  const [discard, setDiscard] = useState<boolean>(false);
  const dispatch = useDispatch();
  const inputValues = useSelector(
    (state: RootState) => state.floatingLabel.inputValues
  );

  const [isSummaryPage, setIsSummaryPage] = useState(false);
  const [categoryName, setCategoryName] = useState<any>(
    inputValues?.CategoryName ?? ""
  );
  const [Description, setDescription] = useState<any>(
    inputValues?.Description ?? ""
  );
  const [summaryData, setSummaryData] = useState<
    Array<{ label: string; value: string; isChanged: boolean }>
  >([]);
  const [errors, setErrors] = useState({ CategoryName: false });

  // useEffect(() => {
  //   setCategoryName(inputValues?.CategoryName ?? "");
  //   setDescription(inputValues?.Description ?? "");
  // }, []);

  useEffect(() => {
    const fields = [
      { label: "CategoryName", value: categoryName },
      { label: "Description", value: Description },
    ];

    const result = catId
      ? String(catId)
      : inputValues?._id
      ? String(inputValues._id)
      : undefined;

    const summary = generateSummaryData(inputValues, fields, result, null);
    console.log(
      catId || (inputValues?._id && String(inputValues?._id)),
      "set summary"
    );
    setSummaryData(summary);
  }, [inputValues, categoryName, Description]);

  const [isClosing, setIsClosing] = useState<boolean>(false);

  const handleNextClick = () => {
    if (!inputValues.CategoryName?.toString().trim()) {
      console.log('this is hitt')
      setErrors({ CategoryName: true });
      showToast({
        messageContent: "Enter Required Fields!",
        type: "warning",
      });
      return;
    }
    setErrors({ CategoryName: false });

    setIsSummaryPage(true);
  };

  const handleBackClick = () => setIsSummaryPage(false);
  const handleInputChange = (field: string) => () => {
    setErrors((prev) => ({ ...prev, [field]: false }));
  };
  const handleAddApi = async (variant: string) => {
    switch (variant) {
      case "manpowerCategory": {
        const data = await addManpowerCategory({
          name: inputValues?.CategoryName?.toString(),
          Description: inputValues?.Description?.toString(),
        });
        dispatch(setIsLocalChange(true));
        return data;
      }
      case "materialCategory": {
        const data = await addMaterialCategory({
          name: inputValues?.CategoryName?.toString(),
          Description: inputValues?.Description?.toString(),
        });
        dispatch(setIsLocalChange(true));
        return data;
      }
      case "machineryCategory": {
        const data = await addMachineryCategory({
          Name: inputValues?.CategoryName?.toString(),
          Description: inputValues?.Description?.toString(),
        });
        dispatch(setIsLocalChange(true));
        return data;
      }
      case "toolCategory": {
        const data = await addToolCategory({
          name: inputValues?.CategoryName?.toString(),
          Description: inputValues?.Description?.toString(),
        });
        dispatch(setIsLocalChange(true));
        return data;
      }
      default: {
        const data = await AddTaskCategoryApi({
          Categoryname: inputValues?.CategoryName?.toString(),
          CategoryDescription: inputValues?.Description?.toString(),
        });
        return data;
      }
    }
  };
  const handleEditApi = async (variant: string) => {
    switch (variant) {
      case "manpowerCategory": {
        const data = (await editManpowerCategory({
          _id: inputValues?._id,
          name: inputValues?.CategoryName?.toString(),
          Description: inputValues?.Description?.toString(),
        })) as any;
        return data;
      }
      case "materialCategory": {
        const data = (await editMaterialCategory({
          _id: inputValues?._id,
          name: inputValues?.CategoryName?.toString(),
          Description: inputValues?.Description?.toString(),
        })) as any;
        return data;
      }
      case "machineryCategory": {
        const data = (await editMachineryCategory({
          _id: inputValues?._id,
          name: inputValues?.CategoryName?.toString(),
          Description: inputValues?.Description?.toString(),
        })) as any;
        return data;
      }
      case "toolCategory": {
        const data = (await editToolCategory({
          _id: inputValues?._id,
          name: inputValues?.CategoryName?.toString(),
          Description: inputValues?.Description?.toString(),
        })) as any;
        return data;
      }
      default: {
        if (catId) {
          const data = (await editTaskCategory({
            categoryId: catId,
            name: inputValues?.CategoryName?.toString(),
            Description: inputValues?.Description?.toString(),
          })) as any;
          return data;
        }
      }
    }
  };
  const submitHandler = async () => {
    try {
      const res = (await handleAddApi(variant!)) as any;

      console.log(res, "response is here");
      const message = res.error
        ? res.error.data.message
        : "Category Added Successfully!";
      const type = res.error ? "danger" : "success";

      showToast({ messageContent: message, type });
      if (res.error) {
        return;
      }

    // Add closing animation before closing
    setIsClosing(true);
    setTimeout(() => {
      onClose();
      dispatch(resetInputValues());
    }, 400);
    } catch (error) {
      console.log(error, "message");
      showToast({
        messageContent: "Oops! Something went wrong",
        type: "danger",
      });
    }
  };

  const editHandler = async () => {
    try {
      const res = await handleEditApi(variant!);
      showToast({
        messageContent: res.error
          ? res.error.data.message
          : "Category Updated Successfully!",
        type: res.error ? "danger" : "success",
      });
      if (res.error) {
        return;
      }
    setIsClosing(true);
    setTimeout(() => {
      onClose();
      dispatch(resetInputValues());
    }, 400);
    } catch (error) {
      console.log(error);
      showToast({
        messageContent: "Oops! Something went wrong",
        type: "danger",
      });
      return;
    }
  };

  const formHandler = () => {
    if (catId || inputValues?._id) {
      const noChanges =
        inputValues?.CategoryName?.toString().trim() === categoryName &&
        inputValues?.Description?.toString().trim() === Description;
      if (noChanges) {
        showToast({
          messageContent: "There were no changes!",
          type: "info",
        });
        setIsSummaryPage(false);
        return;
      }
      editHandler();
    } else {
      const allEmpty =
        !inputValues?.CategoryName?.toString().trim() &&
        !inputValues?.Description?.toString().trim();
      if (allEmpty) {
        showToast({
          messageContent: "Enter required fields!",
          type: "warning",
        });
        return;
      }
      submitHandler();
    }
  };

  const hasFormChanges = () => {
    console.log(
      "haschanges>>>>21",
      inputValues,
      "categoryName",
      categoryName,
      "description>>",
      Description
    );
    if (catId || inputValues?._id) {
      return (
        inputValues?.CategoryName?.toString().trim() !== categoryName ||
        inputValues?.Description?.toString().trim() !== Description
      );
    } else {
      return (
        inputValues?.CategoryName?.toString().trim() ||
        inputValues?.Description?.toString().trim()
      );
    }
  };

  const handleClose = useCallback(() => {
    const hasChanges = hasFormChanges();
    if (hasChanges) {
      setDiscard(true);
      return;
    }

    setIsClosing(true);
    setTimeout(onClose, 400);
  }, [catId, inputValues, categoryName, Description, onClose]);

  //using this logic will cause minor issues kindlly check it!!!
  // /* 🐱‍👤 Code by abhishek Raj Start*/
  //   const formRef = useRef<HTMLDivElement>(null);
  //   const handleClickOutside = useCallback((event: MouseEvent) => {
  //     if (formRef.current && !formRef.current.contains(event.target as Node)) {
  //       handleClose();
  //     }
  //   }, [handleClose]);

  //   useEffect(() => {
  //     document.addEventListener("mousedown", handleClickOutside);
  //     return () => {
  //       document.removeEventListener("mousedown", handleClickOutside);
  //     };
  //   }, [handleClickOutside]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if(isClosing) return;
    if (e.key === "Enter" && e.shiftKey) return;

    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      if (!isSummaryPage && !discard) {
        handleNextClick();
      }
      if (isSummaryPage) {
        formHandler();
      } else if (discard) {
        setIsClosing(true);
        setTimeout(() => {
          dispatch(closePopup("AddCategoryForm"));
          dispatch(resetInputValues());
        }, 400);
      }
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      if (isSummaryPage) {
        setIsSummaryPage(false);
      } else if (discard) {
        setDiscard(false);
      } else {
        handleClose();
      }
    }
  };
  const formRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    if (isSummaryPage || discard) {
      if (formRef.current) {
        formRef.current.focus();
      }
    }
  }, [isSummaryPage, discard]);

  const isEmpty = (value: any) => {
    console.log("outisde click tools inside if>>val:", value);
    return !Object.values(value).some((val) => {
      // console.log('outisde click tools inside if>>val:', val)
      return val !== undefined && val !== null && val !== "";
    });
  };

  const categoryFormRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      console.log("outisde click tools", inputValues);
      if (formRef.current && !formRef.current.contains(event.target as Node)) {
        console.log("outisde click tools inside if");
        const isEmp = isEmpty(inputValues);
        console.log("outisde click tools inside if >>isEmp:", isEmp);
        if (isEmp) {
          // setIsClosing(true);
          // setTimeout(onClose, 400);

          console.log("outisde click tools inside inner if");

          handleClose();
        }
        const hasChanges = hasFormChanges();
        console.log("haschanges>>>>", hasChanges);
        if (!discard && !isSummaryPage && !hasChanges) {
          handleClose();
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [inputValues, dispatch]);

  return (
    <div
      className={`${styles.addcategoryform_container} ${
        isClosing ? styles.closing : ""
      }`}
      tabIndex={0}
      ref={formRef}
      onKeyDown={handleKeyDown}
    >
      <div
        ref={categoryFormRef}
        className={styles.categoryform_header}
        style={{ color: discard ? "var(--warning_color)" : "" }}
      >
        <h3 className={styles.categoryform__header_text}>
          {isSummaryPage
            ? catId || inputValues?._id
              ? "Are you sure you want to update this category?"
              : "Are you sure you want to add this category?"
            : discard
            ? "Are you sure you want to discard these changes?"
            : catId || inputValues?._id
            ? "Edit Category"
            : "Add Category"}
        </h3>
        <button
          className={styles.closeButton}
          onClick={() => {
            if (isSummaryPage && !hasFormChanges()) {
              handleClose();
              return;
            }
            if (isSummaryPage) {
              setDiscard(true);
              setWasTrue(true);
              setIsSummaryPage(false);
              return;
            }
            if (discard && !wasTrue) {
              setDiscard(false);
              return;
            }
            if (discard && wasTrue) {
              setDiscard(false);
              setWasTrue(false);
              setIsSummaryPage(true);
              return;
            }

            handleClose();
          }}
        >
          <CloseIcon />
        </button>
      </div>

      <div className={styles.categoryform_datainputs}>
        {isSummaryPage ? (
          <SummaryPage summaryData={summaryData} />
        ) : discard ? (
          <div style={{ display: "flex", flexWrap: "wrap" }}>
            {summaryData.map((item) => {
              return (
                <div
                  key={item.label}
                  style={{
                    width:
                      (item.label === "Unit" || item.label === "Quantity") &&
                      isValidValue(item?.value)
                        ? "50%"
                        : "100%",
                  }}
                >
                  {item?.value && isValidValue(item?.value) && (
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          className="p_tag_14px_weight"
                          style={{ color: "#444444" }}
                        >
                          {item?.label === "CategoryName" ||
                          item?.label === "TaskName"
                            ? "Name"
                            : item.label}
                        </p>
                        <h4
                          style={{
                            color: item?.isChanged
                              ? "var(--secondary_color)"
                              : "#191919",
                            marginTop: "0.3rem",
                          }}
                        >
                          {item.value}
                        </h4>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <div className={styles.categoryform_inputfields}>
            <FloatingLabelInput
              label="Name"
              id="CategoryName"
              focusOnInput={true}
              value={inputValues?.CategoryName}
              placeholder="Category Name"
              isInvalid={errors.CategoryName}
              props="one_line"
              onInputChange={handleInputChange("CategoryName")}
            />
            <FloatingLabelInput
              label="Description"
              id="Description"
              props={"description_prop"}
              placeholder="Description"
              value={inputValues?.Description}
              isInvalid={false}
              onInputChange={handleInputChange("Description")}
            />
          </div>
        )}
      </div>

      <div className={styles.categoryform_btngrp}>
        {isSummaryPage ? (
          <>
            <Button type="Cancel" Content="Back" Callback={handleBackClick} />
            <Button type="Next" Content="Submit" Callback={formHandler} />
          </>
        ) : discard ? (
          <>
            <Button
              type="Cancel"
              Content="No"
              Callback={() => {
                if (discard && wasTrue) {
                  setDiscard(false);
                  setIsSummaryPage(true);
                  setWasTrue(false);
                  return;
                }
                setDiscard(false);
              }}
            />
            <Button
              type="Approve"
              Content="Yes"
              Callback={() => {
                setIsClosing(true);
                setTimeout(() => {
                  dispatch(closePopup("AddCategoryForm"));
                  dispatch(resetInputValues());
                }, 400);
              }}
            />
          </>
        ) : (
          <>
            <Button type="Cancel" Content="Cancel" Callback={handleClose} />
            <Button
              type="Next"
              Content={catId || inputValues?._id ? "Update" : "Add"}
              Callback={handleNextClick}
            />
          </>
        )}
      </div>
    </div>
  );
}
