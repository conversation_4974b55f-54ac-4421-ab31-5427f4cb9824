/*  AUTHOR NAME : CHARVI */
import React from "react";

import styles from "./SummaryPage.module.css";
import { SummaryPageProps } from "../GlobalInterfaces/GlobalInterface";
import { isValidValue, isValidValue2 } from "../../../../functions/functions";

const SummaryPage: React.FC<SummaryPageProps> = ({
  summaryData,
  hasQuantityAndUnit,
}) => {
  console.log(summaryData, "this is sumemrry data for add category from",hasQuantityAndUnit,"helotype");
  console.log(hasQuantityAndUnit, "HASSSSSSSSSSS");
  return (
    <div
      style={{
        display: "flex",
        flexWrap: "wrap",
        flexDirection: "row",
      }}
    >
      {summaryData.map((item) => {
        return (
          <div
            key={item.label}
            style={{
              width:
                hasQuantityAndUnit &&
                (item.label =="Unit" || item.label == "Quantity") &&
                isValidValue2(item?.value)
                  ? "50%"
                  : "100%",
            }}
          >
            {isValidValue2(item?.value) && (
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p className="p_tag_14px_weight" style={{ color: "#444444" }}>
                    {item?.label === "CategoryName" ||
                    item?.label === "TaskName"
                      ? "Name"
                      : item.label}
                  </p>
                  <h4
                    style={{
                      color: item?.isChanged
                        ? "var(--secondary_color)"
                        : "#191919",
                      marginTop: "0.3rem",
                    }}
                  >
                    {item.value}
                  </h4>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default SummaryPage;
