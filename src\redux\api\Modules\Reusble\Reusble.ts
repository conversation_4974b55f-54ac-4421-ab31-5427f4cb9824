// reusble api calling here
// reusble api calling here
import { baseApi } from "../..";
import { url } from "../../../../config/urls";

export const ReusbleApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    downloadImage: builder.query<any, void>({
      query: () => ({
        url: "https://www.ayrusnoc.com/api/v1/downloadimage/getimage?key=public/TaskMasterData/image02.jpg-1739447472437.jpeg",
        method: "GET",
      }),
    }),
    openCloseTaskMaster: builder.mutation({
      query: (isEditData) => {
        return {
          url: `${url}/taskmaster/openclosetaskmaster`,
          method: "POST",
          body: isEditData,
        };
      },
      invalidatesTags: [],
    }),
    openclosesubtask:builder.mutation({
      query: (isEditData) => {
        return {
          url: `${url}/taskmaster/openclosesubtaskfortaskmaster`,
          method: "POST",
          body: isEditData,
        };
      },
      invalidatesTags: [],
    }),
       opencloseprojectplanning:builder.mutation({
      query: (data) => {
        return {
          url: `${url}/billingmaster/ProjectData/locations/opencloseprojecttask`,
          method: "POST",
          body: data,
        };
      },
    }),
    
  }),
});

export const { useDownloadImageQuery, useOpenCloseTaskMasterMutation,useOpenclosesubtaskMutation,useOpencloseprojectplanningMutation } =
  ReusbleApi;
