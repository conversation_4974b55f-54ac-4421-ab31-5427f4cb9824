import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import { AddTriggerEvent } from "../../../../Interfaces/Modules/TaskMaster/TaskMasterInterface";

export const TriggerEventData = (name: string) => ({
  action: [
    {
      id: "101",
      name: `Start Immediately when ${name} starts`,
    },
    {
      id: "102",
      name: `Start after a time interval after ${name} starts`,
    },
    {
      id: "103",
      name: `Start Immediately after ${name} is completed`,
    },
    {
      id: "104",
      name: `Start Immediately after a time interval after ${name} is completed`,
    },
  ],
});

const initialTitle = "default";

const initialState: AddTriggerEvent = {
  triggerMode: null,
  title: initialTitle,
  IsActive: true,
  allsubroutes: [{
    _id: "",
    name: ""
  }],
  isOnlyResponse: false,
  triggerStructureData: TriggerEventData(initialTitle),
  triggerFormData: {
    TriggerAction: {
      ActionName: null,
      ActionTime: "",
    },
    TriggerResponse: null,
    ResponseTime: "",
  },
};

const AddTriggerEventSlice = createSlice({
  name: "addTrigger",
  initialState,
  reducers: {
    setTriggerMode: (state, action: PayloadAction<string>) => {
      state.triggerMode = action.payload || "";
    },
    clearTriggerMode: (state) => {
      state.triggerMode = "add";
    },
    setName: (state, action: PayloadAction<string>) => {
      state.title = action.payload || "";
      state.triggerStructureData = TriggerEventData(state.title);
    },
    changeActiveState: (state, action: PayloadAction<{ active?: boolean }>) => {
      state.IsActive = action.payload.active || !state.IsActive;
    },
    setIsOnlyResponse: (state, action: PayloadAction<boolean>) => {
        state.isOnlyResponse = action.payload;
    },
    setAllSubRoutes: (state, action: PayloadAction<any>) => {
      state.allsubroutes = action.payload?.data;
    },
    setTriggerFormData: (state, action: PayloadAction<{
      TriggerAction: {
        ActionName: {
          id: string | number,
          name: string
        } | null,
        ActionTime: number | string,
      },
      TriggerResponse: {
        _id: string | number,
        name: string,
        isFirst?: boolean,
      } | null,
      ResponseTime: number | string,
    }>) => {
      state.triggerFormData = action.payload;
    }
  },
});

export const { changeActiveState, setName, setTriggerMode, clearTriggerMode, setTriggerFormData, setIsOnlyResponse, setAllSubRoutes } =
  AddTriggerEventSlice.actions;
export default AddTriggerEventSlice.reducer;
