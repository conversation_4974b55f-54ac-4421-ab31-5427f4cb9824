import React from "react";
import MainMenu from "../../../../../../components/Common/Sidebar/SubComponents/MainMenu";
import NavigationComponent from "../../../../../../components/Reusble/Global/navigationComponents/commonHeaderComponent";
import TargetBadge from "../../../../../../components/Reusble/Global/TargetBadge/TargetBadge";
import { History, Uploadicon } from "../../../../../../assets/icons";
import { RootState } from "../../../../../../redux/store";
import { useDispatch, useSelector } from "react-redux";
import styles from "./TaskHeader.module.css";
import { useAuth } from "../../../../../../AuthProvider";
import Button from "../../../../../../components/Reusble/Global/Button";
import { useAppDispatch } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import {
  openPopup,
  setApprovalFormStep,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import { setInputValue } from "../../../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { ApprovalForm } from "../../../../../../components/Reusble/TaskMaster/ApprovalForm";
import { useState, useEffect } from "react";
interface TaskHeaderProps {
  width?: string | number;
}

const TaskHeader: React.FC<TaskHeaderProps> = ({ width }) => {
  const navigateArray = useSelector(
    (state: RootState) => state.navigateData.navigateArray
  );

  const TaskData = useSelector(
    (state: RootState) => state.taskForm.currentSubtaskData
  );
  const subTaskData = useSelector(
    (state: RootState) => state.taskMaster.currentSubtaskData
  );

  const currentSubTaskId = useSelector(
    (state: RootState) => state.taskMaster.currentSubTaskRoute
  );

  const isSubtask = Boolean(currentSubTaskId);

  const data = isSubtask ? subTaskData : TaskData;
  const mode = isSubtask ? "subtask" : "taskdetails";

  const popups = useSelector((state: RootState) => state.popup.popups);

  const [currentOpenPopup, setcurrentOpenPopup] = useState(popups);

  useEffect(() => {
    setcurrentOpenPopup(popups);
  }, [popups]);

  const dispatch = useAppDispatch();

  // Hook these up to your buttons:

  const handleApprovalAction = (step: "approve" | "decline" | "reason") => {
    const designations =
      data?.DesignationId?.map((item: any) => item.name) ?? [];
    const departments = data?.DepartmentId?.map((item: any) => item.name) ?? [];
    const machinery = data?.MachinaryId?.map((item: any) => item.name) ?? [];
    const manpower = data?.ManpowerId?.map((item: any) => item.name) ?? [];
    const materials = data?.MaterialId?.map((item: any) => item.name) ?? [];
    const tools = data?.ToolId?.map((item: any) => item.name) ?? [];
    const taskmanager = data?.Adminid?.map((item: any) => item.name) ?? [];
    const assignTo = data?.AssigneeId?.map((item: any) => item.name) ?? [];

    const reporters =
      data?.ReporterId?.Reporter?.map((reporter: any) => ({
        level: reporter.Level,
        designations: reporter.designationId?.map((d: any) => d.name) ?? [],
      })) ?? [];

    const qualityControlPlan =
      data?.MethodId?.Controlplan?.map((item: any) => item.Description) ?? [];

    const qualityEnsuringMeasures =
      data?.MethodId?.Failuremode?.map((item: any) => ({
        description: item.Description,
        severity: item.severity,
        solution: item.solution,
      })) ?? [];

    const workInstructions =
      data?.MethodId?.work_instruction_id?.map((wi: any) => ({
        description: wi.Description ?? "",
        file: wi.file
          ? {
              name: wi.file.name ?? "",
              type: wi.file.type ?? "",
            }
          : null,
        machinery: Array.isArray(wi.machinaryId)
          ? wi.machinaryId.map((m: any) => m.name)
          : [],
        manpower: Array.isArray(wi.manpowerId)
          ? wi.manpowerId.map((m: any) => m.name)
          : [],
        materials: Array.isArray(wi.materialId)
          ? wi.materialId.map((mat: any) => mat.name)
          : [],
        tools: Array.isArray(wi.toolsId)
          ? wi.toolsId.map((tool: any) => tool.name)
          : [],
        optionSelected: wi.optionselected ?? "",
        photos: Array.isArray(wi.photoref?.photos)
          ? wi.photoref.photos.map((photo: any) => ({
              fileName: photo.fileName ?? "",
              details: photo.details ?? "",
            }))
          : [],
      })) ?? [];

    const taskClosingRequirements =
      data?.MethodId?.task_closing_requirement?.map((tci: any) => ({
        description: tci.Description,
        optionSelected: tci.optionselected,
        file: tci.file
          ? {
              name: tci.file.name ?? "",
              type: tci.file.type ?? "",
            }
          : null,
        photos: Array.isArray(tci.photoref?.photos)
          ? tci.photoref.photos.map((photo: any) => ({
              fileName: photo.fileName ?? "",
              details: photo.details ?? "",
            }))
          : [],
      })) ?? [];

    const payload = {
      mode,
      TaskName: data?.name ?? "",
      Description: data?.Description ?? "",
      Quantity: data?.subtaskWeighatages ?? 0,
      Unit: data?.Unit ?? "",
      Departments: departments,
      Designation: designations,
      Manpower: manpower,
      Machinery: machinery,
      Tools: tools,
      Materials: materials,
      TaskManager: taskmanager,
      AssignTo: assignTo,
      Reporter: reporters,
      ControlPlan: qualityControlPlan,
      FailureMode: qualityEnsuringMeasures,
      work_instruction_id: workInstructions,
      task_closing_requirement: taskClosingRequirements,
      Reason: data?.MDdeniedComment,
    };

    dispatch(setInputValue(payload)); // Set form data
    dispatch(setApprovalFormStep(step)); // "approve" or "decline"
    dispatch(openPopup("ApprovalForm")); // Open modal
  };

  const handleApproveClick = () => handleApprovalAction("approve");
  const handleDeclineClick = () => handleApprovalAction("decline");
  const handleReasonClick = () => handleApprovalAction("reason");

  const { user } = useAuth();
  const isMD = user?.designationId?.name === "MD";
  const isHOD = user?.designationId?.name === "HOD";

  const condition = window.innerWidth < 1200;
  console.log("width in header:", width + "px");

  return (
    <>
      <div
        style={{ width: condition ? `${width + "px"}` : "" }}
        className={styles.task_header}
      >
        <div className={styles.tasknav_conatiner}>
          <div className={styles.tasknav_left}>
            <MainMenu mt={"-0.5rem"} />
            {navigateArray && <NavigationComponent route={navigateArray} />}
          </div>
          <div className={styles.tasknav_rightbtns}>
            <TargetBadge
              outerContainerClassName={"task_header_padding"}
              value={"Version History"}
              valueTextTagName="h4"
              icon={<History />}
              onClick={() => {}}
            />
            {!isMD && (
              <TargetBadge
                outerContainerClassName={"task_header_padding"}
                value={"Export"}
                valueTextTagName="h4"
                icon={<Uploadicon />}
              />
            )}

            {/* // Buttons to handle approval actions so dont delete this code will use in future */}

            {/* {isMD && (
              <Button
                Callback={handleDeclineClick}
                type="Decline2"
                Content="Decline"
              />
            )}
            {isMD && (
              <Button
                Callback={handleApproveClick}
                type="Next"
                Content="Approve"
              />
            )}

            {isHOD && (
              <Button
                Callback={handleReasonClick}
                type="Reason"
                Content="Reason"
              />
            )}
            {isHOD && (
              <Button
                Callback={handleReasonClick}
                type="Next"
                Content="Send Approval"
              />
            )} */}

            {currentOpenPopup["ApprovalForm"] && <ApprovalForm />}
          </div>
        </div>
      </div>
    </>
  );
};

export default TaskHeader;
