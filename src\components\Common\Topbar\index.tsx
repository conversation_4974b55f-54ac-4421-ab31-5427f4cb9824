import { FC, useEffect, useState } from "react";
import styles from "./Styles/Topbar.module.css";
import { useSelector } from "react-redux";
import { RootState } from "../../../redux/store";
import SearchBar from "../../Reusble/Global/SearchBar";
import { useAppSelector } from "../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";

const Topbar: FC = () => {
  const title = useSelector((state: RootState) => state.navigateData.title);

  const userDetail = useAppSelector((state: RootState) => state.auth.user);

  // const [search, setSearch] = useState("");

  const handleSearch = (value: string) => {
    console.log(value, "is search bar working");
  };
  return (
    <div className={styles.topbar_parent}>
      {/* Heading Section */}
      <div className={styles.topbar_heading}>
        <h1>{title}</h1>
      </div>

      {/* Search Bar Section */}
      <div className={styles.topbar_searchbar_container}>
        <SearchBar placeholder="Search" />
      </div>

      {/* Utilities Section */}
      <div className={styles.topbar_utilities}>
        {/* <InfoIcon />
        <LightSvg />
        <Notification /> */}

        {/* HR Details Section */}
        <div className={styles.topbar_Hr_details}>
          <div className={styles.topbar_Hr_details_image}>
            <img
              src="https://pinnacle.works/wp-content/uploads/2022/06/dummy-image.jpg"
              alt="HR"
            />
          </div>
          <div>
            <h4> {userDetail?.name ?? ""}</h4>
            <p className={styles.topbar_Hr_details_name}>
              {userDetail?.designationId?.name ?? ""}
            </p>
          </div>
          <div className={styles.topbar_Hr_details_dropdown}>
            {/* <DesignerDownArrow color={"#000000DE"} /> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Topbar;
