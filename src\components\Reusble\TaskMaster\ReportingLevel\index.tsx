import { useEffect, useState } from "react";

import styles from "./Styles/ReportingLevel.module.css";

import {
  AddCategoryIcon,
  DeleteIcon,
  Deletelevel,
} from "../../../../assets/icons";
import {
  reportedDataProp,
  ReportingLevelProps,
} from "../TaskMasterInterfaces/TaskMasterInterface";
import { useAppSelector } from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";

const ReportingLevel: React.FC<ReportingLevelProps> = ({
  key,
  isEdit,
  reportedData,
  setReporterAdded,
  onAddLevelData,
  onDeleteRoleName,
  onDeleteReportedData,
  closeDialogBox,
}) => {
  const [data, setData] = useState<reportedDataProp>(reportedData ?? []);
  const isDeletedNext = useAppSelector(
    (state) => state.isDeletedSLice.isDeletedNext
  );

  const handleAddLevel = (addLevel: any) => {
    if (data && data[data.length - 1]._id) {
      setData([...data, addLevel]);
    }
  };
  const handleSubLevel = (index: number) => {
    setData((pre) => {
      return pre.map((item, idx) =>
        idx === index
          ? {
              ...item,
              designationId: [...item.designationId, { name: "" }],
            }
          : item
      );
    });
  };
  const handleAddLevelData = (id: any) => {
    if (onAddLevelData) {
      onAddLevelData(id);
    }
  };
  const handleDeleteSublevel = (
    levelIndex: any,
    designationIdIndex: any,
    departmentId: any
  ) => {
    if (onDeleteRoleName) {
      onDeleteRoleName(levelIndex, designationIdIndex, departmentId);
    }
  };
  const handleDeleteLevel = (index: any) => {
    if (onDeleteReportedData) {
      onDeleteReportedData(index);
    }
  };

  useEffect(() => {
    if (reportedData.length > 0) {
      setData(reportedData);
      if (setReporterAdded) {
        setReporterAdded(true);
      }
    } else {
      setData([]);
      if (setReporterAdded) {
        setReporterAdded(false);
      }
    }
  }, [reportedData]);

  const handleDelete = () => {
    setData([]);
    if (setReporterAdded) {
      setReporterAdded(false);
    }
  };

  console.log(
    "data hereee>>:",
    data.map((item) => item.designationId.some((item) => !item._id))
  );

  const isDesignationFieldempty = data.map((item) =>
    item.designationId.some((item) => !item._id)
  );

  return (
    reportedData.length > 0 &&
    data.length > 0 && (
      <div className={styles.reported_container} key={key}>
        {isEdit &&
          reportedData.length === 1 &&
          !reportedData[0].designationId[0]?.name && (
            <button
              className={styles.reported_container_deleteIcon}
              onClick={() => handleDelete()}
            >
              <DeleteIcon />
            </button>
          )}
        <div className={styles.reported_containerList}>
          {data &&
            data.map((item, levelIndex) => (
              <div
                className={`${styles.reported_containerItem} ${
                  !item?.designationId && styles.reported_containerLastItem
                }`}
                key={levelIndex}
              >
                <div
                  className={`${styles.timeline_icon} ${
                    isEdit && levelIndex !== 0 && styles.reporting_level_delete
                  }`}
                >
                  {" "}
                  {isEdit && levelIndex !== 0 && (
                    <button
                      className={styles.reporting_level_deleteIcon}
                      onClick={() => handleDeleteLevel(levelIndex)}
                    >
                      <Deletelevel />
                    </button>
                  )}
                </div>
                <div className={styles.timeline_content}>
                  <div>
                    <h3 className={styles.card_title}>
                      <time
                        dateTime={`2020-07-08`}
                        className={styles.reporting_levels}
                      >
                        Level {levelIndex + 1}
                      </time>
                    </h3>
                    <div className={styles.transition_div}>
                      <div className={styles.reported_level_box}>
                        {item?.designationId?.map((role, index) => {
                          return (
                            <>
                              <div className={styles.reported_level_subBox}>
                                <div
                                  className={styles.level_role}
                                  style={{
                                    border:
                                      role.name &&
                                      " 1px solid var(--primary_color)",
                                  }}
                                >
                                  <h4>
                                    {role.name ? role.name : "Designation"}
                                  </h4>
                                  {isEdit && role.name && (
                                    <button
                                      className={
                                        styles.reported_container_deleteIcon
                                      }
                                      onClick={() => {
                                        console.log(role, "this is role hecer");
                                        handleDeleteSublevel(
                                          levelIndex,
                                          role._id,
                                          role.DepartmentId
                                        );
                                      }}
                                    >
                                      <DeleteIcon />
                                    </button>
                                  )}
                                  {!role.name && isEdit && !isDeletedNext && (
                                    <button
                                      className={`${styles.addbtnlevelpopup} ${styles.reporting_addbtn_secondary}`}
                                      onClick={() =>
                                        handleAddLevelData(levelIndex)
                                      }
                                    >
                                      <AddCategoryIcon />
                                    </button>
                                  )}
                                </div>
                                {item?.designationId[
                                  item.designationId.length - 1
                                ].name &&
                                  isEdit &&
                                  !isDeletedNext &&
                                  index == item.designationId.length - 1 && (
                                    <button
                                      className={`${styles.addbtnlevelpopup} ${styles.reporting_addbtn_secondary} `}
                                      onClick={() => handleSubLevel(levelIndex)}
                                    >
                                      <AddCategoryIcon />
                                    </button>
                                  )}
                              </div>
                            </>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          {data[data.length - 1]?.designationId && !isDeletedNext && (
            <div
              className={`${styles.timeline_addIcon} ${
                !isEdit && styles.reporting_edit
              } `}
              onClick={() => {
                if (data[data.length - 1]?.designationId[0].name) {
                  if (isDesignationFieldempty.every((item) => item === false)) {
                    handleAddLevel({
                      Level: -1,
                      designationId: [
                        {
                          name: "",
                        },
                      ],
                    });
                  }
                }
              }}
            >
              {!isEdit ? (
                <div
                  className={`${styles.timeline_icon} ${
                    isEdit && styles.reporting_level_delete
                  }`}
                  // style={{ marginLeft: "0.1rem" }}
                >
                  {" "}
                </div>
              ) : (
                <div style={{ marginLeft: "0.27rem" }}>
                  <AddCategoryIcon fill="var(--main_background)" />
                </div>
              )}
            </div>
          )}
          <div className={styles.reported_containerList}>
            <span className={styles.badge}></span>
          </div>
        </div>
      </div>
    )
  );
};

export default ReportingLevel;
