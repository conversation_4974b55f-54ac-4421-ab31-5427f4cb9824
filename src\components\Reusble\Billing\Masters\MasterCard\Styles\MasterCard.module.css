.mastercard_first_outercontainer {
    position: relative;
    block-size: 147px;
    border-radius: 2rem;
}

.mastercard_outercontainer {
    background-color: var(--main_background);
    box-shadow: 0px 0px 3px 0px #91a1a180;
    /* height: 100%; */
    padding: 1.5rem 1.5rem;
    margin:0 0.5rem;
    border-radius: 2rem;
    /* position: absolute; */
    border: linear-gradient(130.72deg,
            rgba(237, 231, 231, 0.07) -16.06%,
            rgba(251, 251, 251, 0.05) 82.03%);
    width: 100%;
    transition: block-size 0.3s ease-in-out;
}

.mastercard__subheading_item {
    margin-block: 8px;
}

.mastercard_outercontainer_subbox {
    display: flex;
    align-items: center;
    gap: 1rem;

}

.mastercard_outercontainer_expand {
    block-size: max-content;


}


.mastercard_rightcontainer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    /* background-color: antiquewhite; */
    min-height: 96px;
    /* gap: 1.2rem; */
}

.mastercard_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    width: 100%;
    margin-top: 0;

}

.mastercard_header_unitdiv {
    background-color: var(--primary_background);
    border-radius: 2rem;
    max-width: 8rem;
    text-align: center;
    box-shadow: 0px 4px 8px 0px #ffffff;
    border: 1px solid;
    padding: 0.2rem 0.6rem;
    border-image-source: linear-gradient(130.72deg,
            rgba(237, 231, 231, 0.07) -16.06%,
            rgba(251, 251, 251, 0.05) 82.03%);
}

.mastercard__header_heading {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 225px;
}

.mastercard__subheading_box {
    
    opacity: 0;
    visibility: hidden;
    max-height: 0;
    overflow: scroll;
    transition: opacity 0.5s ease, visibility 0.5s ease, max-height 0.5s ease;
    /* background-color: aqua; */
}

.mastercard__subheading_box::-webkit-scrollbar {
    width: 3px;
}

.mastercard__subheading_box::-webkit-scrollbar-track {
    border-radius: 10px;
}

.mastercard__subheading_box::-webkit-scrollbar-thumb {
    background: var(--primary_color);
    border-radius: 10px;
}

.mastercard__subheading_box_visible {
    opacity: 1;
    visibility: visible;
    block-size: 100%;
    max-block-size: 312px;
    margin-block-start: 8px;


}
.mastercard__subheading {
    color: var(--primary_color);
    font-weight: 600;
    padding-block-end: 8px;
}

.masterCard_header_toggleBox {
    display: flex;
    gap: 16px;
}

.mastercard__header_eyetoggle {
    display: flex;
    align-items: center;
    justify-content: center;
}

.mastercard__subheading_item_value {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.mastercard_header_left {
    display: flex;
    gap: 0.9rem;
    align-items: center;
    /* justify-content: center; */
    width: 80%;
}

.mastercard_header_righticon {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    position: relative;
}

.mastercard_items {

    display: grid;
    grid-template-columns: repeat(3, 1fr);
    flex-grow: 1;
    gap: 0.9rem;
    margin-top: 1rem;
    justify-items: center;
}

@media screen and (max-height: 800px) {
    .mastercard_items {
        margin-top: 1.3rem;
    }
    
}

.mastercard_leftimgcontainer_machinery {
    width: 100%;
    height: 100%;
    max-width: 160px;
    max-height: 170px;
    height: 10rem;
    border-radius: 0.75rem;


}

.mastercard_image {
    width: 100%;
    height: 100%;
    max-width: 100px;
    min-height: 98px;
    max-height: 97px;
    min-width: 20%;
    border-radius: 0.75rem;
    /* background-color: aquamarine; */
}

@media (min-width:1280px) and (max-width:1536px) {
    .mastercard_outercontainer {
        padding: 1.5rem 1.5rem;
        width: 100%;
        max-width: 100%;
        max-width: 30rem;
    }

    .mastercard__header_heading {
        max-width: 7.1rem;
    }

}

@media  (max-width: 1200px) {
    .mastercard_outercontainer {
        padding: 1.5rem 1.5rem;
        width: 100%;
        max-width: 100%;
        max-width: 30rem;
        min-width: 25rem;
    }
    
}