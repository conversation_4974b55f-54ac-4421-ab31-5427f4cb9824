import { useEffect, useRef, useState } from "react";
import {
  CloseIcon,
  AttachmentIcon,
  AddCategoryIcon,
  DropDownArrowUpIcon,
  DropDownCategoryIcon,
  VideoIcon,
  ImageIcon,
  AudioIcon,
  RedCross,
  DeleteIcon,
  ReverseArrow,
  Attachment,
  CopyIcon,
  PasteIcon,
  Cross,
} from "../../../../../../assets/icons";
import Datafield from "../../../../../../components/Reusble/Billing/Masters/Datafield";
import Button from "../../../../../../components/Reusble/Global/Button";
import FloatingLabelInput from "../../../../../../components/Reusble/Global/FloatingLabel";
import styles from "../Styles/Machinery.module.css";
import UnitPopup from "../../../../../../components/Reusble/Global/UnitPopup";
import DynamicGradeInput from "../../Subcomponents/DynamicGradeInput";
import {
  resetDeletedGradeData,
  resetDeletedToolData,
  resetDeleteFormData,
  setDeletedFormData,
  setDeletedGradeData,
  setDeletedToolData,
  setFormMachineryData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import { useSelector } from "react-redux";
import { RootState, store } from "../../../../../../redux/store";
import { useAppDispatch } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { useGetAllBrandsQuery } from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";

import {
  compressImage,
  isValidValue,
  slicedData,
} from "../../../../../../functions/functions";
import {
  useAddMachineryDesignationMutation,
  useUpdateMachineryDesignationMutation,
} from "../../../../../../redux/api/Modules/Billing/Billingapi";
import { useParams } from "react-router-dom";
import MachinerySummary from "./MachinerySummary";
import MachineryDiscard from "./MachineryDiscard";
import { setIsLocalChange } from "../../../../../../redux/features/Modules/Reusble/backupSlice";
import { useToast } from "../../../../../../hooks/ToastHook";

const AddMachineryForm: React.FC<{
  isClosing?: boolean;
  handleClose: (targetForm: string) => void;
  setIsClosing?: React.Dispatch<React.SetStateAction<boolean>>;
}> = ({ isClosing = false, handleClose, setIsClosing }) => {
  const [isFuelPopUpVisible, setFuelPopUpVisible] = useState(false);
  const [fileLoader, setFileLoader] = useState(false);
  const [selectedFuelTypeId, setSelectedFuelTypeId] = useState<number | null>(
    null
  );
  const contentRef = useRef<HTMLDivElement>(null);
  const currentFileState = useRef(fileLoader);
  const [errors, setErrors] = useState<{
    Photo: boolean;
    Name: boolean;
    Brand: string[];
    Grade: string[];
    Fuels: boolean;
  }>({
    Photo: false,
    Name: false,
    Brand: [],
    Grade: [],
    Fuels: false,
  });
  const [emptyError, setEmptyError] = useState<{
    Photo: boolean;
    Name: boolean;
    Brand: boolean;
    Grade: boolean;
    Fuels: boolean;
  }>({
    Photo: false,
    Name: false,
    Brand: false,
    Grade: false,
    Fuels: false,
  });
  const formData = useSelector(
    (state: RootState) => state.masterForm.formMachineryData
  );
  const initialFormData = useSelector(
    (state: RootState) => state.masterForm.initialFormMachineryData
  );
  const formMode = useSelector((state: RootState) => state.masterForm.formMode);
  //state which keeps the track of deleted form data
  const deletedGradeData = useSelector(
    (state: RootState) => state.masterForm.deleteGradeData
  );
  //state which keeps the track of deleted form data
  const deletedFormData = useSelector(
    (state: RootState) => state.masterForm.deleteFormData
  );
  //state which keeps the track of deleted Tool data
  const deletedToolData = useSelector(
    (state: RootState) => state.masterForm.deleteToolData
  );
  const [showSummary, setShowSummary] = useState(false);
  const [wasTrue, setWasTrue] = useState(false);
  const [file, setFile] = useState<{
    name: string;
    type: string;
    file: File;
  } | null>(formData?.Photo || null);

  //this is for managing suggestion popup
  const [isOpen, setIsOpen] = useState<{ [key: number]: boolean } | null>({});

  //this is for suggestion in brand name
  const [searchKey, setSearchKey] = useState<{ [key: number]: string } | null>(
    {}
  );
  const [addMachineryDesignation] = useAddMachineryDesignationMutation();
  const [updateMachineryDesignation] = useUpdateMachineryDesignationMutation();

  //params
  const { machineryCategoryId } = useParams();

  // console.log("input values", formData);

  const [discard, setDiscard] = useState(false);
  const dispatch = useAppDispatch();
  const showToast = useToast();

  //api to fetch all the brands
  const { data: allBrands, refetch } = useGetAllBrandsQuery({});

  const [copyGrades, setCopyGrades] = useState<string[]>([]);

  //copy function
  const copyGradesfun = (grades: string[]) => {
    setCopyGrades(grades);
    showToast({
      messageContent: "Grades Copied!",
      type: "success",
    });
  };

  //paste function
  const pasteGradesfun = (brandIndex: string) => {
    dispatch(
      setFormMachineryData({
        ...formData,
        Brands: formData?.Brands?.map((brand, index) => {
          if (brand?._id === brandIndex) {
            return {
              ...brand,
              Grade: copyGrades,
            };
          }
          return brand;
        }),
      })
    );

    //empty the copy grades after pasting
    setCopyGrades([]);
  };

  const toggleFuelPopUp = () => {
    setFuelPopUpVisible((prev) => !prev);
  };

  const handleFuelTypeSelect = (fuel: { id: number; label: string }) => {
    setSelectedFuelTypeId(fuel.id);
    setErrors({ ...errors, Fuels: false });
    dispatch(
      setFormMachineryData({
        ...formData,
        Fuel: fuel.label,
      })
    );
    setFuelPopUpVisible(false);
  };

  const addBrandSection = () => {
    // console.log("brand section", formData.Brands);
    if (formData?.Brands?.[formData?.Brands?.length - 1]?.brand?.name === "") {
      showToast({
        messageContent: "Please Enter Brand Name!",
        type: "warning",
      });
      return;
    }

    dispatch(
      setFormMachineryData({
        ...formData,
        Brands: Array.isArray(formData?.Brands)
          ? [
              ...formData.Brands,
              { _id: Date.now().toString(), brand: { name: "" }, Grade: [] },
            ]
          : [{ _id: Date.now().toString(), brand: { name: "" }, Grade: [] }],
      })
    );
    showToast({
      messageContent: "Brand Section Added!",
      type: "success",
    });
  };

  const fuelTypeData = [
    { id: 1, label: "Petrol" },
    { id: 2, label: "Diesel" },
    { id: 3, label: "Electric" },
  ];

  // Handle Input Change
  const handleInputChange = (id: string, value: string) => {
    dispatch(
      setFormMachineryData({
        ...formData,
        [id]: value,
      })
    );
  };

  // Next Button Click
  const handleNext = () => {
    if (
      !formData?.Name?.trim() ||
      !formData?.Fuel?.trim() ||
      !formData?.Photo?.name?.trim() ||
      formData?.Brands?.some(
        (brand) => !brand?.brand?.name?.trim() || !brand?.Grade?.[0]?.trim()
      )
    ) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        Photo: !formData?.Photo?.name?.trim() || prevErrors.Photo,
        Name: !formData?.Name?.trim() || prevErrors.Name,
        Brand: formData?.Brands?.map((item) =>
          !item?.brand?.name?.trim() ? item._id : undefined
        ).filter((id): id is string => Boolean(id)),
        Grade: formData?.Brands?.map((item) => {
          if (!item?.Grade?.[0]?.trim()) {
            return item._id;
          }
          return undefined;
        }).filter((id): id is string => Boolean(id)),
        Fuels: !formData?.Fuel || prevErrors.Fuels,
      }));
      setEmptyError((prevErrors) => ({
        ...prevErrors,
        Brand: !formData.Brands[0]?.brand?.name?.trim() ? true : false,
        Grade: !formData.Brands[0]?.Grade.length ? true : false,
      }));
      showToast({
        messageContent: "Enter Required Fields!",
        type: "warning",
      });
      return;
    }
    setShowSummary(true);
  };

  // Back Button Click
  const handleBack = () => {
    setDiscard(false);
    setShowSummary(false);
  };

  //function to handle cover photo change
  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFile(null);
    const files = event.target.files;
    if (files && files.length > 0) {
      setFileLoader(true);
      const selectedFile = await compressImage(files[0], 0.2);
      // // console.log(files[0]);
      // // const compressFile = ;
      // console.log("compressFile", selectedFile);
      if (currentFileState.current) {
        setFile({
          name: selectedFile.name,
          type: selectedFile.name.split(".").pop()?.toLowerCase() || "",
          file: selectedFile,
        });
        setErrors({ ...errors, Photo: false });

        const latestFormData = store.getState().masterForm.formMachineryData;

        dispatch(
          setFormMachineryData({
            ...latestFormData,
            Photo: {
              name: selectedFile.name,
              type: selectedFile.name.split(".").pop()?.toLowerCase() || "",
              file: selectedFile,
            },
          })
        );
        updateClipPath("coverphoto", false, selectedFile.name);
      }
      setFileLoader(false);
    }
  };

  const areArraysDifferent = (arr1: {}[], arr2: {}[]) => {
    if (!arr1 || !arr2) return true;
    if (arr1.length !== arr2.length) return true;

    return arr1.some(
      (item, index) => JSON.stringify(item) !== JSON.stringify(arr2[index])
    );
  };
  const hasFormChanged = () => {
    if (formMode === "Add") {
      return (
        formData?.Name.trim() ||
        formData?.Description?.trim() ||
        formData?.Photo?.name?.trim() ||
        formData?.Tools?.[0]?.name ||
        formData?.Brands?.[0]?.brand?.name?.trim() ||
        formData?.Brands?.[0]?.Grade?.[0]?.trim() ||
        formData?.Fuel?.trim()
      );
    } else {
      return (
        formData?.Name?.trim() !== initialFormData?.Name?.trim() ||
        formData?.Description?.trim() !==
          initialFormData?.Description?.trim() ||
        formData?.Photo?.name?.trim() !==
          initialFormData?.Photo?.name?.trim() ||
        formData?.Fuel?.trim() !== initialFormData?.Fuel?.trim() ||
        areArraysDifferent(formData?.Tools, initialFormData?.Tools) ||
        areArraysDifferent(formData?.Brands, initialFormData?.Brands)
      );
    }
  };

  const handleCancel = () => {
    const hasChanged = hasFormChanged();

    if (hasChanged) {
      setDiscard(true);
      return;
    }

    handleClose("AddMachineryForm");
  };

  useEffect(() => {
    setSelectedFuelTypeId(
      fuelTypeData?.find(
        (v) => v?.label?.toLowerCase() === formData?.Fuel?.toLowerCase()
      )?.id ?? null
    );
  }, [formData, formData?.Tools]);

  //for submission api call
  const handleSubmit = async () => {
    try {
      const hasChanged = hasFormChanged();

      if (!hasChanged) {
        setShowSummary(false);
        showToast({
          messageContent: "There were no changes!",
          type: "warning",
        });
        return;
      }

      //formatted the data into the form acceptable by backend so that in future minimum changes are required
      const formatedData = {
        ...(formMode === "Edit" && formData?._id ? { _id: formData._id } : {}),
        name: formData?.Name,
        Description: formData?.Description,
        Brand: formData?.Brands?.map((item) => ({
          ...(formMode === "Add"
            ? { Brandname: item?.brand?.name }
            : item?.brand?._id && !/^\d{13}$/.test(item?.brand?._id)
            ? { BrandId: [item?.brand?._id] }
            : { Brandname: item?.brand?.name }),
          Specs: item?.Grade,
          ...(item?.brand?._id && !/^\d{13}$/.test(item?.brand?._id)
            ? { _id: item?.brand?._id }
            : {}),
        })),
        Fueltype: formData?.Fuel?.toLowerCase(),
        tools: formData?.Tools,
        ...(formMode === "Add"
          ? { machineryCategoryId: machineryCategoryId }
          : {}),
        images: formData?.Photo?.file,
      };

      if (formMode === "Add") {
        await addMachineryDesignation(formatedData).unwrap();
        showToast({
          messageContent: "Machine added successfully!",
          type: "success",
        });
      } else {
        await updateMachineryDesignation(formatedData).unwrap();
        showToast({
          messageContent: "Machine updated successfully!",
          type: "success",
        });
      }
      refetch();
      handleClose("AddMachineryForm");
      dispatch(setIsLocalChange(true));
    } catch (error) {
      showToast({
        messageContent:
          (error as { data?: { message?: string } })?.data?.message ||
          "Oops! Something went wrong",
        type: "danger",
      });
    }
  };

  function updateClipPath(
    id: string,
    resetBorder: boolean,
    value?: string | number
  ) {
    const inputWrapper =
      document.getElementById(id)?.parentElement?.parentElement;
    // console.log("selected input wrapper:", inputWrapper);
    if (!inputWrapper) return;

    const label = document.querySelector(".photo_tag");
    // console.log("label input wrapper:", label);
    const input = inputWrapper;
    // console.log("input2 input wrapper:", input);

    if (label && input) {
      if (!resetBorder || value) {
        const labelWidth = (label as HTMLElement).offsetWidth + 20;
        const inputWidth = input.offsetWidth;

        // Calculating clip-path based on label width
        const leftPercentage = (labelWidth / inputWidth) * 100;
        // input.style.clipPath = "none";
        input.style.clipPath = `polygon(0 0, ${leftPercentage}% 0, ${leftPercentage}% 2px, 0 2px, 0 100%, 100% 100%, 100% 0, 100% 0, 100% 0, 100% 2px, 100% 0)`;
      } else {
        input.style.clipPath = "none";
      }
    }
  }

  useEffect(() => {
    if (file?.name) {
      updateClipPath("coverphoto", false, file.name);
    } else {
      updateClipPath("coverphoto", true);
    }
  }, [discard, showSummary, file]);
  useEffect(() => {
    currentFileState.current = fileLoader;
    // console.log(currentFileState);
  }, [fileLoader]);
  // console.log("errors", emptyError);
  // console.log("formData", formData);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if(isClosing) return;
    if (e.key === "Enter" && e.shiftKey) return;

    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      if (showSummary) {
        handleSubmit();
        dispatch(resetDeletedToolData());
        dispatch(resetDeleteFormData());
        dispatch(resetDeletedGradeData());
      }
      if (!showSummary && !discard) {
        handleNext();
      }
      if (discard) {
        handleClose("AddMachineryForm");
        dispatch(resetDeletedToolData());
        dispatch(resetDeleteFormData());
        dispatch(resetDeletedGradeData());
      }
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      if (!showSummary && !discard) {
        handleCancel();
      }

      if (showSummary) {
        handleBack();
      }
      if (discard) {
        if (discard && wasTrue) {
          setDiscard(false);
          setShowSummary(true);
          setWasTrue(false);
          return;
        }
        setDiscard(false);
      }
    }
  };
  const formRef = useRef(null);
  useEffect(() => {
    if (showSummary || discard) {
      if (formRef.current) {
        formRef.current.focus();
      }
    }
  }, [showSummary, discard]);
  console.log("machinery form data::", formData);
  const [formEmpty, setFormEmpty] = useState(true);
  const isEmpty = (data): boolean => {
    if (
      data?.Name === "" &&
      data?.Description === "" &&
      data?.Fuel === "" &&
      data?.Name === "" &&
      data?.Photo === null &&
      data?.Brands?.[0]?.brand?.name === "" &&
      data?.Brands?.[0]?.Grade.length === 0 &&
      data?.Tools.length === 0
    ) {
      console.log("issempty>>: true");
      return true;
    } else {
      console.log("issempty>>: false");
      return false;
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // console.log('outisde click tools',inputValue)
      if (formRef.current && !formRef.current.contains(event.target as Node)) {
        const isEmp = isEmpty(formData);
        setFormEmpty(isEmp);
        if (isEmp) {
          handleClose("AddMachineryForm");
          return;
        }
        if (!hasFormChanged() && !discard) {
          handleClose("AddMachineryForm");
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [formData, dispatch]);
  useEffect(() => {
    requestAnimationFrame(() => {
      if (contentRef.current) {
        contentRef.current.scrollTop = 0;
      }
    });
  }, [showSummary, discard]);

  return (
    <div
      className={`${styles.addmachineryform_container} ${
        isClosing ? styles.closing : ""
      }`}
      tabIndex={0}
      onKeyDown={handleKeyDown}
      ref={formRef}
    >
      <div
        className={styles.addmachineryform_header}
        style={{ color: discard ? "var(--warning_color)" : "" }}
      >
        <h3>
          {showSummary
            ? `Are you sure you want to ${
                formMode === "Add" ? "add" : "update"
              } this Machine?`
            : discard
            ? "Are you sure you want to discard these changes?"
            : formMode === "Add"
            ? "Add Machine"
            : "Edit Machine"}
        </h3>
        <button
          className={styles.closeButton}
          onClick={() => {
            if (!hasFormChanged() && showSummary) {
              handleClose("AddMachineryForm");
              return;
            }
            if (showSummary) {
              setDiscard(true);
              setWasTrue(true);
              setShowSummary(false);
              return;
            }
            if (discard && !wasTrue) {
              setDiscard(false);
              return;
            }
            if (discard && wasTrue) {
              setDiscard(false);
              setWasTrue(false);
              setShowSummary(true);
              return;
            }

            handleCancel();
          }}
        >
          <CloseIcon />
        </button>
      </div>
      <div className={styles.addmachineryform_datainputs} ref={contentRef}>
        {showSummary ? (
          <MachinerySummary
            formData={formData}
            initialFormData={initialFormData}
            formMode={formMode}
            deletedFormData={deletedFormData}
            deletedGradeData={deletedGradeData}
            deletedToolData={deletedToolData}
          />
        ) : discard ? (
          <MachinerySummary
            formData={formData}
            initialFormData={initialFormData}
            formMode={formMode}
            deletedFormData={deletedFormData}
            deletedGradeData={deletedGradeData}
            deletedToolData={deletedToolData}
          />
        ) : (
          <>
            <div className={styles.addmachineryform_datainputs_flexrow}>
              <FloatingLabelInput
                label="Name"
                id="name"
                focusOnInput={true}
                placeholder="Name"
                props="one_line"
                error={errors?.Name}
                value={formData?.Name}
                onInputChange={(value: any) => {
                  handleInputChange("Name", value);
                  setErrors({ ...errors, Name: false });
                }}
              />
              <div className={`${styles.photo_input_wrapper}`}>
                {
                  <label
                    className={`photo_tag `}
                    style={{
                      position: "absolute",
                      left: file ? 20 : 16,
                      color: file
                        ? "var(--text-black-87)"
                        : "var(--text-black-60)",
                      zIndex: 9999,
                      top: file ? "" : "30%",
                      borderRadius: "5px",
                      padding: "0.2rem",
                      fontSize: file ? "0.75rem" : "",
                      transform: file ? "translateY(40%)" : "translateY(40%)",
                      transition: "transform 0.3s ease-in-out",
                    }}
                  >
                    Cover Photo
                  </label>
                }
                <div
                  className={styles.cover_photo}
                  style={{
                    justifyContent: file ? "space-between" : "flex-end",
                    border: errors?.Photo
                      ? "1px solid var(--warning_color)"
                      : "",
                    marginTop: "1.3rem",
                  }}
                >
                  {file && (
                    <div className={styles.tcr_fileNames_div}>
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          margin: "-0.5rem",
                          paddingInline: "0.5rem",
                          position: "relative",
                          maxWidth: "10rem",
                          overflow: "hidden",
                        }}
                        className={`${styles.tcr_fileNames} small_text_p_400`}
                      >
                        {file.type === "jpg" ||
                        file.type === "jpeg" ||
                        file.type === "png" ? (
                          <ImageIcon />
                        ) : null}
                        {file.type === "mp4" ? <VideoIcon /> : null}
                        {file.type === "mp3" ? <AudioIcon /> : null}

                        <p
                          style={{
                            marginLeft: "0.5rem",
                            width: "80%",
                            whiteSpace: "nowrap",
                            textOverflow: "ellipsis",
                            overflow: "hidden",
                          }}
                          className="small_text_p_400"
                        >
                          {file.name}
                        </p>
                      </div>
                    </div>
                  )}

                  <div
                    className={
                      !file ? styles.tcrpopup_header_attachmentbtn : ""
                    }
                    style={{
                      cursor: "pointer",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                    onClick={
                      fileLoader
                        ? () => {
                            setFileLoader(false);
                          }
                        : () => document.getElementById("coverphoto")?.click()
                    }
                    >
                    {file ? (
                      <ReverseArrow />
                    ) : fileLoader ? (
                      <>
                        <Cross />
                      </>
                    ) : (
                      <Attachment />
                    )}
                    
                    <input
                      id="coverphoto"
                      type="file"
                      style={{ display: "none" }}
                      accept="image/jpeg,image/jpg,image/png"
                      onChange={handleFileChange}
                    />
                  </div>
                  {fileLoader && (
                    <div className={styles.progress_bar_container}>
                      <div className={styles.progress_bar}></div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <FloatingLabelInput
              label="Description"
              id="Description"
              placeholder="Description"
              value={formData?.Description}
              props="description_prop"
              onInputChange={(value: any) => {
                handleInputChange("Description", value);
              }}
            />

            <div
              className={styles.addmachineryform_brandheader}
              style={{ marginTop: "1.3rem" }}
            >
              <h4>Brand</h4>
              <div
                onClick={addBrandSection}
                className={styles.addBrandsectionCategoryIcon}
              >
                <AddCategoryIcon />
              </div>
            </div>

            {formData &&
              formData?.Brands?.map((brandItem: any, index) => (
                <div
                  key={index}
                  className={styles.addmachineryform_brandsection}
                  style={{
                    border:
                      errors?.Brand.find((val) => val == brandItem._id) ||
                      errors?.Grade.find((id) => id == brandItem._id) ||
                      (emptyError.Brand && !brandItem?.brand?.name) ||
                      (emptyError.Grade && !brandItem?.Grade[0])
                        ? "1px solid var(--warning_color) !important"
                        : "",
                  }}
                >
                  <div
                    className={styles.addmachineryform_brandsectioninputs}
                    style={{ position: "relative" }}
                  >
                    <div style={{ position: "relative" }}>
                      <FloatingLabelInput
                        label="Name"
                        id={`brand-name-${index}`}
                        marginTop="0rem"
                        placeholder="Name"
                        props="one_line"
                        error={
                          errors?.Brand.find((val) => val == brandItem._id) ||
                          (emptyError.Brand && !brandItem?.brand?.name)
                            ? true
                            : false
                        }
                        value={
                          formData?.Brands?.find(
                            (item: any) => item?._id === brandItem?._id
                          )?.brand?.name ?? ""
                        }
                        onInputChange={(value: any) => {
                          setErrors((prevErrors) => ({
                            ...prevErrors,
                            Brand: prevErrors.Brand.filter(
                              (val, i) => val !== brandItem?._id
                            ),
                          }));
                          setEmptyError((prevErrors) => ({
                            ...prevErrors,
                            Brand: false,
                            Grade: false,
                          }));
                          dispatch(
                            setFormMachineryData({
                              ...formData,
                              Brands: formData.Brands?.map(
                                (brand: any, i: number) =>
                                  brand?._id === brandItem?._id
                                    ? {
                                        ...brand,
                                        brand: { name: value },
                                        Grade: [...brand?.Grade],
                                        ...(brand?.updateId
                                          ? { updateId: brand?.updateId }
                                          : {}),
                                      }
                                    : brand
                              ),
                            })
                          );

                          //if value is empty no suggestion should be given
                          if (value?.trim() == "") {
                            setIsOpen(() => ({ [brandItem?._id]: false }));
                            setSearchKey(() => ({
                              [brandItem?._id]: value,
                            }));
                            return;
                          }

                          //if value is present give suggestion
                          setIsOpen(() => ({ [brandItem?._id]: true }));
                          setSearchKey(() => ({ [brandItem?._id]: value }));

                          //logic to close the isOpen if no match is found
                          const brands =
                            allBrands.data.length > 0
                              ? allBrands.data.filter((item: any) => {
                                  const safePattern = value?.replace(
                                    /[-\/\\^$*+?.()|[\]{}]/g,
                                    "\\$&"
                                  );
                                  return safePattern
                                    ? new RegExp(safePattern, "i").test(
                                        item?.Brandname
                                      )
                                    : false;
                                })
                              : [];

                          if (brands.length === 0) {
                            setIsOpen(() => ({ [brandItem?._id]: false })); // Correct way to update state
                          }
                        }}
                      />

                      {/* this is the dropdown in which suggestion will be shown */}
                      {isOpen?.[brandItem?._id] && (
                        <div
                          className={`${styles.unit_popup_mt_container} ${
                            isOpen?.[brandItem?._id]
                              ? `${styles.selected}`
                              : "notSelected"
                          }`}
                        >
                          <UnitPopup
                            property={"unit_popup_class"}
                            alignment="absolute"
                            left="0"
                            top="57px"
                            width="100%"
                            data={(() => {
                              const filtered =
                                (searchKey
                                  ? allBrands?.data?.filter((item: any) => {
                                      const safePattern = searchKey[
                                        brandItem?._id
                                      ].replace(
                                        /[-\/\\^$*+?.()|[\]{}]/g,
                                        "\\$&"
                                      );
                                      return new RegExp(safePattern, "i").test(
                                        item?.Brandname
                                      );
                                    })
                                  : allBrands?.data) || [];

                              const mapped = filtered
                                .map((item: any) => ({
                                  id: item?._id,
                                  label: item?.Brandname,
                                }))
                                .filter((currentItem: any) => {
                                  const isInFormData = formData?.Brands?.some(
                                    (b) => b.brand._id === currentItem?.id
                                  );
                                  return !isInFormData;
                                });

                              if (mapped.length === 0) {
                                setIsOpen?.(null);
                              }

                              return mapped;
                            })()}
                            onSelect={(item) => {
                              //setting brand name with the _id
                              dispatch(
                                setFormMachineryData({
                                  ...formData,
                                  Brands: formData.Brands?.map(
                                    (brand: any, i: number) =>
                                      brand?._id === brandItem?._id
                                        ? {
                                            ...brand,
                                            brand: {
                                              _id: item?.id,
                                              name: item?.label,
                                            },
                                          }
                                        : brand
                                  ),
                                })
                              );
                              setSearchKey(null);
                              setIsOpen(null);
                            }}
                            selectedId={null} //we dont need this as of now, will see if there is any need
                          />
                        </div>
                      )}
                    </div>
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        marginTop: "1rem",
                      }}
                    >
                      <h4>Grade / Model</h4>
                      {formData?.Brands?.length > 1 &&
                        (!formData?.Brands[formData?.Brands?.length - 1]
                          ?.Grade?.[0] &&
                        index !== formData?.Brands?.length - 1 ? (
                          <div
                            onClick={() =>
                              copyGradesfun(
                                formData?.Brands?.find(
                                  (item) => item?._id === brandItem?._id
                                )?.Grade!
                              )
                            }
                            className={styles.copy_button}
                          >
                            <span style={{ marginInlineEnd: "0.5rem" }}>
                              Copy
                            </span>{" "}
                            <CopyIcon />
                          </div>
                        ) : (
                          copyGrades?.length > 0 &&
                          !formData?.Brands[formData?.Brands?.length - 1]?.Grade
                            ?.length && (
                            <div
                              onClick={() => pasteGradesfun(brandItem?._id)}
                              className={styles.copy_button}
                            >
                              <span style={{ marginInlineEnd: "0.5rem" }}>
                                Paste
                              </span>{" "}
                              <PasteIcon />
                            </div>
                          )
                        ))}
                    </div>
                    <div style={{ paddingTop: "0.5rem" }}>
                      {/* <GradeInputbox /> */}
                      <DynamicGradeInput
                        label="Add"
                        variant="grade"
                        callbackDelete={(deleteIndex) => {
                          const deletedGradeValue = formData?.Brands?.find(
                            (item: any) => item?._id === brandItem?._id
                          )?.Grade[deleteIndex];

                          const deletedGrade =
                            formData?.Brands?.find(
                              (item: any) => item?._id === brandItem?._id
                            )?.Grade?.includes(deletedGradeValue!) &&
                            initialFormData?.Brands?.find(
                              (item: any) => item?._id === brandItem?._id
                            )?.Grade.includes(deletedGradeValue!);
                          // const deletedGrade = formData?.Brands?.find(
                          //   (item: any) => item?._id === brandItem?._id
                          // )?.Grade[deleteIndex];
                          // console.log("deletedGrade", deletedGrade);
                          dispatch(
                            setFormMachineryData({
                              ...formData,
                              Brands: formData?.Brands?.map(
                                (brand, brandIdx) =>
                                  brandIdx === index
                                    ? {
                                        ...brand,
                                        Grade: Array.isArray(brand?.Grade)
                                          ? brand.Grade.filter(
                                              (_, i) => i !== deleteIndex
                                            ) // Remove Grade at `index`
                                          : [],
                                      }
                                    : brand // Keep other brands unchanged
                              ),
                            })
                          );
                          if (formMode !== "Add" && deletedGrade) {
                            const existing = deletedGradeData?.find(
                              (item: any) => item[brandItem._id] !== undefined
                            );

                            let updatedDeleteGradeData;

                            if (existing) {
                              updatedDeleteGradeData = deletedGradeData?.map(
                                (item: any) => {
                                  if (item[brandItem._id] !== undefined) {
                                    const updatedSet = new Set([
                                      ...item[brandItem._id],
                                      deletedGradeValue,
                                    ]);
                                    return {
                                      [brandItem._id]: Array.from(updatedSet),
                                    };
                                  }
                                  return item;
                                }
                              );
                            } else {
                              updatedDeleteGradeData = [
                                ...deletedGradeData,
                                { [brandItem._id]: [deletedGradeValue] },
                              ];
                            }

                            // Dispatch with the new data
                            dispatch(
                              setDeletedGradeData(updatedDeleteGradeData)
                            );
                          }
                        }}
                        error={
                          errors?.Grade.find((id) => id == brandItem._id) ||
                          (emptyError.Grade && !brandItem?.Grade[0])
                            ? true
                            : false
                        }
                        initialData={
                          formData?.Brands?.find(
                            (item: any) => item?._id === brandItem?._id
                          )?.Grade || []
                        }
                        onGradesUpdate={(grades) => {
                          setErrors((prevErrors) => ({
                            ...prevErrors,
                            Brand: prevErrors.Brand.filter(
                              (val, i) => val !== brandItem?._id
                            ),
                            Grade: prevErrors.Grade.filter(
                              (val, i) => val !== brandItem?._id
                            ),
                          }));
                          setEmptyError((prevErrors) => ({
                            ...prevErrors,
                            Brand: false,
                            Grade: false,
                          }));
                          dispatch(
                            setFormMachineryData({
                              ...formData,
                              Brands: formData.Brands?.map(
                                (brand: any, i: any) =>
                                  brand?._id === brandItem?._id
                                    ? { ...brand, Grade: grades }
                                    : brand
                              ),
                            })
                          );
                        }}
                      />
                    </div>
                    {formData?.Brands?.length > 1 && (
                      <div
                        className={styles.delete_icon_tooltip}
                        onClick={() => {
                          const isInInitial = initialFormData?.Brands?.find(
                            (item) => item?._id === brandItem?._id
                          )?._id;

                          if (isInInitial) {
                            dispatch(
                              setDeletedFormData([
                                ...deletedFormData,
                                initialFormData?.Brands?.find(
                                  (item) => item?._id === brandItem?._id
                                ),
                              ])
                            );
                          }
                          dispatch(
                            setFormMachineryData({
                              ...formData,
                              Brands: formData?.Brands?.filter(
                                (brand, brandIdx) =>
                                  brand?._id !== brandItem?._id
                              ),
                            })
                          );
                        }}
                      >
                        <DeleteIcon />
                      </div>
                    )}
                  </div>
                </div>
              ))}

            <h4 style={{ marginTop: "1.5rem", marginBottom: "0.5rem" }}>
              Tools
            </h4>
            <Datafield
              label="Add"
              selectedValues={formData?.Tools ?? []}
              varient="AddMachineryForm"
              callbackDelete={(id) => {
                const isInInitial =
                  !!formData?.Tools?.find((tool) => tool?._id === id) &&
                  !!initialFormData?.Tools?.find((tool) => tool?._id === id);
                // console.log("isInInitial", id, isInInitial);
                if (isInInitial) {
                  dispatch(
                    setDeletedToolData([
                      ...deletedToolData,
                      initialFormData?.Tools?.find((tool) => tool?._id === id),
                    ])
                  );
                }
                dispatch(
                  setFormMachineryData({
                    ...formData,
                    Tools: formData?.Tools?.filter((tool) => tool?._id !== id),
                  })
                );
              }}
              setIsClosing={setIsClosing}
            />

            {/* Unit Selection */}
            <div style={{ position: "relative", marginBottom: "" }}>
              <FloatingLabelInput
                label="Fuel Type"
                id="fuelType"
                marginBottom="-0.1rem"
                placeholder="Select Fuel Type"
                isDisabled={true}
                error={errors?.Fuels}
                onInputChange={(value: any) => {
                  handleInputChange("Fuels", value);
                  setErrors({ ...errors, Fuels: false });
                }}
                Icon={
                  isFuelPopUpVisible
                    ? DropDownArrowUpIcon
                    : DropDownCategoryIcon
                }
                iconClick={toggleFuelPopUp}
                value={
                  selectedFuelTypeId === null
                    ? ""
                    : fuelTypeData?.find((v) => v?.id === selectedFuelTypeId)
                        ?.label
                }
              />
              {isFuelPopUpVisible && (
                <div
                  style={{
                    position: "relative",
                    left: "0%",
                    top: "10%",
                    zIndex: 10,
                  }}
                >
                  <UnitPopup
                    data={fuelTypeData}
                    onSelect={(fuel) =>
                      handleFuelTypeSelect(
                        fuel as { id: number; label: string }
                      )
                    }
                    selectedId={selectedFuelTypeId}
                    property="customWidth"
                  />
                </div>
              )}
            </div>
          </>
        )}
      </div>
      <div className={styles.addmachineryform_btngroup}>
        {showSummary ? (
          <>
            <Button type="Cancel" Content="Back" Callback={handleBack} />
            <Button
              type="Next"
              Content="Submit"
              Callback={() => {
                handleSubmit();
                dispatch(resetDeletedToolData());
                dispatch(resetDeleteFormData());
                dispatch(resetDeletedGradeData());
              }}
            />
          </>
        ) : discard ? (
          <>
            <Button
              type="Cancel"
              Content="No"
              Callback={() => {
                if (discard && wasTrue) {
                  setDiscard(false);
                  setShowSummary(true);
                  setWasTrue(false);
                  return;
                }
                setDiscard(false);
              }}
            />
            <Button
              type="Next"
              Content="Yes"
              Callback={() => {
                handleClose("AddMachineryForm");
                dispatch(resetDeletedToolData());
                dispatch(resetDeleteFormData());
                dispatch(resetDeletedGradeData());
              }}
            />
          </>
        ) : (
          <>
            <Button type="Cancel" Content="Cancel" Callback={handleCancel} />
            <Button
              type="Next"
              Content={formMode === "Add" ? "Add" : "Update"}
              Callback={handleNext}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default AddMachineryForm;
