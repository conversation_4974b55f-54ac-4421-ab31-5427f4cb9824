import { useEffect, useRef, useState } from "react";
import { Loader } from "../../../../../../assets/loader";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import TMMMNav from "../../../../../../components/Reusble/TMMMNav";
import styles from "../Styles/Tools.module.css";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import { closePopup } from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import {
  initializeDatabase,
  isValidValue,
  pathTableMap,
} from "../../../../../../functions/functions";
import CategoryCard from "../../../../../../components/Reusble/Billing/Masters/CategoryCard/Index";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../../redux/store";
import { useDeleteToolCategoryByIdMutation } from "../../../../../../redux/api/Modules/Billing/Billingapi";
import { useToast } from "../../../../../../hooks/ToastHook";
import { resetInputValues } from "../../../../../../redux/features/Modules/Reusble/floatinglabelslice";
// import { useLocation } from "react-router-dom";
import { usePouchSearch } from "../../../../../../functions/useLocalSearch";
import {
  clearFetchedMasters,
  setFetchedMasters,
  setSearchData,
} from "../../../../../../redux/features/Modules/Masters";

const ToolsCategory = () => {
  const currentOpenPopup = useAppSelector((state) => state.popup.popups);
  // const [toolCategoriesData, setToolCategoriesData] = useState<any>([]);

  const [searchtoolCategoriesData, setSearchToolCategoriesData] = useState<any>(
    []
  );

  // const location = useLocation();

  // const currentPath = location.pathname;

  const [page, setPage] = useState<number>(1);

  const [searchLocalKey, setSearchLocalKey] = useState<string>("");
  const searchKey = useAppSelector((state) => state.taskMaster.searchKey);
  // const detectChanges = useAppSelector((state) => state.backupSlice.isOpen);
  const inputValues = useSelector(
    (state: RootState) => state.floatingLabel.inputValues
  );
  const data = useSelector(
    (state: RootState) => state.masterReduxSlice.fetchedToolCatData
  );
   const searchedData = useSelector(
    (state: RootState) => state.masterReduxSlice.searchedData
  );
  const dispatch = useAppDispatch();
  const showToast = useToast();

  //delete api
  const [deleteToolCategory] = useDeleteToolCategoryByIdMutation();

  useEffect(() => {
    if (searchKey) {
      setSearchLocalKey(searchKey);
    } else {
      setSearchLocalKey("");
    }
  }, [searchKey]);

  //localdb logic
  const getDatafromDb = async (p: any) => {
    const dbName = await initializeDatabase("ToolCategory");
    const fetchedData = await window.electron.bulkGet({
      dbName,
      page: p,
      deleted: false,
    });
    console.log(p, "check for p here");
    console.log("fetchedData", fetchedData.docs);

    if (p === 1) {
      dispatch(
        setFetchedMasters({ data: fetchedData.docs, page: p, type: "tool" })
      );
      // setToolCategoriesData(fetchedData.docs);
    } else {
      const newData = [...data, ...fetchedData.docs];
      dispatch(setFetchedMasters({ data: newData, page: p, type: "tool" }));
      // setToolCategoriesData((prev: any) => [...prev, ...fetchedData.docs]);
    }
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;

    if (target) {
      const { scrollHeight, clientHeight, scrollTop } = target;

      if (scrollTop + clientHeight >= scrollHeight - 1) {
        console.log("page changed", page);
        setPage((prev) => prev + 1);
      }
    }
  };

  // useEffect(() => {
  //   if (localChange) {
  //     setPage(1);
  //   }
  // }, [detectChanges]);

  useEffect(() => {
    if (page) {
      getDatafromDb(page);
    }
  }, [page]);

  usePouchSearch({
    pathTableMap,
    searchKey: searchLocalKey,
    setData: setSearchData,
    setPage,
  });


  const navRef = useRef<HTMLDivElement>(null);
  const mainContentRef = useRef<HTMLDivElement>(null);
  const [width, setWidth] = useState<null | number>(null);

  const clientFunction = () => {
    const mainContentWidth =
      mainContentRef?.current?.getBoundingClientRect()?.width;
    // console.log('details of card view container in useEffect details',details,);
    // setWidth(details?.width);
    console.log("inner width");
    // if (window.innerWidth < 1200) {
    navRef.current?.style.setProperty("width", `${mainContentWidth}px`);
    // }
  };
  const condition =
    mainContentRef.current &&
    mainContentRef.current.getBoundingClientRect().width < 1200;

  useEffect(() => {
    clientFunction();
    const handleResize = () => clientFunction();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [window.innerWidth, condition]);

  console.log(data, "this is my datadddddd");
  useEffect(() => {
    return () => {
      dispatch(clearFetchedMasters());
    };
  }, []);
  console.log(searchedData,"data im gettign")

  return (
    <>
      {/* <SummaryFields /> */}
      <div ref={navRef}>
        <TMMMNav
          Label={"Category"}
          variant={"toolCategory"}
          TargetForm={"AddCategoryForm"}
        />
      </div>
      <div style={{ marginTop: "1.5rem" }}>
        <div
          ref={mainContentRef}
          className={styles.cardview}
          onScroll={(e) => handleScroll(e)}
        >
          <div className={styles.inner_cardview}>
            {(searchedData.length > 0
              ? searchedData
              : data
            )?.map((item: any) => (
              <CategoryCard
                key={item?._id}
                editData={item}
                data={{
                  title: item?.name,
                  _id: item?._id,
                  total: item?.totalTools,
                  path: "tools-master",
                  items: [
                    { title: "Brands", name: item?.totalBrands },
                    { title: "Grades", name: item?.totalGrades },
                    { title: "Users", name: item?.totalUser },
                  ],
                }}
              />
            ))}

            {/* Show message if no data at all */}
            {data?.length === 0 && searchtoolCategoriesData?.length === 0 && (
              <div className={styles.loader_loading}>
                <img
                  src={Loader.suryaconLogo}
                  alt="Loading..."
                  className={styles.loader_loading_image}
                />
              </div>
            )}
          </div>
        </div>
      </div>
      {currentOpenPopup["deleteCategory"] && (
        <DeletePopup
          header="Are you sure you want to delete this Category?"
          height="calc(100% - 9rem)"
          callbackDelete={async () => {
            await deleteToolCategory({
              toolId: inputValues?._id,
            }).unwrap();
            showToast({
              messageContent: `Tool Category Deleted Successfully!`,
              type: "success",
            });
            dispatch(closePopup("deleteCategory"));
            dispatch(resetInputValues());
          }}
          onClose={() => {
            dispatch(closePopup("deleteCategory"));
            dispatch(resetInputValues());
          }}
        >
          <div style={{ display: "flex", flexWrap: "wrap" }}>
            {inputValues?.CategoryName &&
              isValidValue(inputValues?.CategoryName) && (
                <div
                  className={styles.summaryDivData}
                  style={{
                    width: "100%",
                  }}
                >
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Name
                    </p>
                    <h4
                      style={{
                        color: "var(--text-black-87)",
                        marginTop: "0.3rem",
                      }}
                    >
                      {inputValues?.CategoryName}
                    </h4>
                  </div>
                </div>
              )}
            {inputValues?.Description &&
              isValidValue(inputValues?.Description) && (
                <div
                  className={styles.summaryDivData}
                  style={{
                    width: "100%",
                  }}
                >
                  <div className={styles.summaryDataContent}>
                    <div>
                      <p
                        style={{ color: "var(--text-black-60)" }}
                        className="p_tag_14px"
                      >
                        Description
                      </p>
                      <h4
                        style={{
                          color: "var(--text-black-87)",
                          marginTop: "0.3rem",
                        }}
                      >
                        {inputValues?.Description}
                      </h4>
                    </div>
                  </div>
                </div>
              )}
          </div>
        </DeletePopup>
      )}
    </>
  );
};

export default ToolsCategory;
