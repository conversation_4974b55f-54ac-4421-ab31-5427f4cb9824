import React from "react";
import styles from "../Styles/BillingApproval.module.css";
import { ToggleTowerSwitch } from "./ToggleTowerSwitch";
import TargetBadge from "../../../../../components/Reusble/Global/TargetBadge/TargetBadge";
import { SendIcon, Uploadicon } from "../../../../../assets/icons";




const MonthlyTargetHeader: React.FC = () => {

  return (
     <>
    <div className={styles.monthly_target_header}>
      <ToggleTowerSwitch
        leftLabel={<p>Current Month</p>}
        leftbubbleValue="4"
        bubbletextClassName="small_text_p"
        bubbleTextTagName="p"
        rightbubbleValue="3"
        rightLabel={<p>Next Month</p>}
        onToggle={() => {}}
        width="20rem"
        id="toggle-ongoing-completed"
      />
    </div>

    <div className={styles.monthly_target_header_buttons_rhs}>
      <TargetBadge
        value="Export"
        outerContainerClassName="monthly_target_header_buttons"
        valueTextClassName="export_button_text"
        icon={<Uploadicon />}
      />
      <TargetBadge
        backgroundColor="var(--primary_color)"
        outerContainerClassName="monthly_target_header_buttons"
        valueTextClassName="approval_button_text"
        value="Approval"
        icon={<SendIcon />}
      />
    </div>
  </>  
  );
};

export default MonthlyTargetHeader;