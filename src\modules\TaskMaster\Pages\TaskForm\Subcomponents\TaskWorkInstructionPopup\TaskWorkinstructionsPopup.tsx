import { useDispatch, useSelector } from "react-redux";
import WorkInstructionsPopup from "../../../../../../components/Reusble/TaskMaster/WorkInstructionsPopup";
import { useCallback, useEffect, useRef, useState } from "react";
import { useGetTaskBuildingBlocksQuery } from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import {
  closePopup,
  openPopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import {
  CheckBox,
  DeleteIcon,
  ImageIcon,
  SuryconLogo,
} from "../../../../../../assets/icons";
import AddToolTip from "../../../../../../components/Reusble/Global/AddToolTip";
import AddCategoryType from "../../../../../../components/Reusble/Global/AddCategoryType";
import styles from "../../Styles/TaskCreationForm.module.css";

import { RootState, store } from "../../../../../../redux/store";
import {
  requiredthings,
  TaskDataType,
  TaskWorkInstructionsPopupProps,
  WorkInstructionsData,
} from "../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import {
  setCurrentPopupId,
  setRequiredThings,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/WorkInstructionSlice";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import {
  getFileName,
  initializeDatabase,
  isBase64,
} from "../../../../../../functions/functions";
import {
  settaskChangeAPiFlag,
  updateTaskData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import { saveSyncData } from "../../../../../../Backup/BackupFunctions/BackupFunctions";
import { useToast } from "../../../../../../hooks/ToastHook";

const TaskWorkInstructionsPopup: React.FC<TaskWorkInstructionsPopupProps> = ({
  data,
  onUpdateWorkInstructions,
  popupId,
  workId,
  isEdit = false,
  onClick,
  handleDelete,
  initaldata,
  categoryData,
  onUpdateCategoryData,
  setPopupIdParent,
  popupIdParent,
  setCategoryDataParent,
  categortDataParent,
  setDeleteIdParent,
  deleteIdParent,
  id,
}) => {
  if (!data) return null;
  console.log(data, "data check bro fully>>>>>>>>>>>>>>");
  const dispatch = useDispatch();
  const { popups } = useSelector((state: RootState) => state.popup);
  const [selectedOption, setSelectedOption] = useState("");
  const [primaryLabelForAddCategoryType, SetprimaryLabelForAddCategoryType] =
    useState("");
  const [selectedOptionApidata, setSelectedOptionApidata] = useState([]);
  const { requiredThingsDeleteName } = useSelector(
    (state: RootState) => state.WorkInstructionReducer
  );
  const showToast = useToast();
  const { currentPopupId } = useSelector(
    (state: RootState) => state.WorkInstructionReducer
  );

  const [isPhotoCheckboxPage, setIsPhotoCheckboxPage] = useState(false);
  const TaskData = useSelector(
    (state: RootState) =>
      state.taskForm.currentSubtaskData || {
        name: "",
        Unit: "",
        description: "",
        subtaskWeighatages: 0,
        Tracking: "",
        MaterialId: [],
        ToolId: [],
        MachinaryId: [],
        ManpowerId: [],
        AdminId: [],
        AssigneeId: [],
        Reporter: [],
        MethodId: {
          work_instruction_id: [],
          task_closing_requirement: [],
          Controlplan: [],
          Failuremode: [],
        },
      }
  );

  type taskBuildingBlocks = {
    [key: string]: requiredthings[];
  };

  const handleSelect = useCallback(
    async (
      categoryName: keyof typeof categoryData,
      selectedItems: TaskDataType[],
      label: string
    ) => {
      console.log(selectedItems, "selectedItems");
      const newselectedItems = selectedItems.map((item) => {
        return {
          _id: item.id,
          name: item.category,
        };
      });
      if (newselectedItems.length > 0) {
        onUpdateCategoryData(popupId, categoryName, [
          ...categoryData[categoryName],
          ...newselectedItems,
        ]);
      }

      let catName;
      switch (categoryName) {
        case "Manpower":
          catName = "manpowerId";
          break;
        case "Machinery":
          catName = "machinaryId";
          break;
        case "Tools":
          catName = "toolsId";
          break;
        case "Materials":
          catName = "materialId";
          break;
      }

      const updatedMethodTask = {
        ...TaskData,
        MethodId: {
          ...TaskData?.MethodId,
          work_instruction_id: TaskData?.MethodId?.work_instruction_id?.map(
            (item: any) => {
              if (item?._id === workId) {
                return {
                  ...item,
                  [catName]: [...(item?.[catName] || []), ...newselectedItems],
                };
              }
              return item;
            }
          ),
        },
      };
      await dispatch(updateTaskData(updatedMethodTask as any));
      await saveSyncData(updatedMethodTask, "time", "TaskForm");

      showToast({
        messageContent: `${
          selectedItems.length > 1 ? label : selectedItems[0]?.category
        } added Successfully!`,
        type: "success",
      });

      let newCateName;
      switch (categoryName) {
        case "Manpower":
          newCateName = "ManpowerId";
          break;
        case "Machinery":
          newCateName = "MachinaryId";
          break;
        case "Tools":
          newCateName = "ToolId";
          break;
        case "Materials":
          newCateName = "MaterialId";
          break;
      }

      const TaskDataLatest = store.getState().taskForm.currentSubtaskData;
      const uniqueItemsInTask = newselectedItems.filter(
        (newItem: any) =>
          !(TaskDataLatest as any)[newCateName as any]?.some(
            (existingItem: any) => existingItem._id === newItem._id
          )
      );
      await dispatch(
        updateTaskData({
          ...(TaskDataLatest as any),
          [newCateName]: [
            ...((TaskDataLatest as any)[newCateName] || []),
            ...uniqueItemsInTask,
          ],
        })
      );

      await saveSyncData(
        {
          ...(TaskDataLatest as any),
          [newCateName]: [
            ...((TaskDataLatest as any)[newCateName] || []),
            ...uniqueItemsInTask,
          ],
        },
        "time",
        "TaskForm"
      );

      dispatch(settaskChangeAPiFlag(true));
    },
    [popupId, categoryData, onUpdateCategoryData]
  );

  const getPopupId = (category: string) => `${popupId}-${category}`;

  const handlePhotoPopupClose = () => {
    setIsPhotoCheckboxPage(false);
    dispatch(closePopup(getPopupId("workinstructionsphoto")));
  };
  const handlePhotoPopupSubmit = (newData: WorkInstructionsData) => {
    console.log(newData, "newData");
    if (data && onUpdateWorkInstructions && !initaldata) {
      const updatedData = {
        ...data,
        photoDetails: [
          ...(data.photoDetails || []),
          ...(newData.photoDetails || []),
        ],
      };
      onUpdateWorkInstructions(updatedData);
    } else {
      if (onUpdateWorkInstructions) {
        onUpdateWorkInstructions(newData);
      }
    }

    handlePhotoPopupClose();
  };

  const detleWorkInstructionItems = useCallback(async () => {
    console.log(deleteIdParent, "some time work some time not");
    if (deleteIdParent) {
      const newCategories = categortDataParent[
        deleteIdParent as keyof typeof categoryData
      ]?.filter(
        (e: { _id: string }) => e?._id !== requiredThingsDeleteName?._id
      );
      let newKey: string | undefined;
      switch (deleteIdParent) {
        case "Tools":
          newKey = "toolsId";
          break;
        case "Materials":
          newKey = "materialId";
          break;
        case "Machinery":
          newKey = "machinaryId";
          break;
        case "Manpower":
          newKey = "manpowerId";
          break;
        default:
          console.error("Invalid deleteIdParent value:", deleteIdParent);
          return;
      }
      onUpdateCategoryData(popupIdParent, deleteIdParent, newCategories);
      const updateTask = {
        ...TaskData,
        MethodId: {
          ...TaskData?.MethodId,
          work_instruction_id: TaskData?.MethodId?.work_instruction_id?.map(
            (item: any) => {
              if (item?._id === workId) {
                return {
                  ...item,
                  [newKey!]: (item[newKey!] || []).filter(
                    (el: any) => el?._id !== requiredThingsDeleteName._id
                  ),
                };
              }
              return item;
            }
          ),
        },
      };
      dispatch(updateTaskData(updateTask as any));
      try {
        await saveSyncData(updateTask, "time", "TaskForm");
      } catch (error) {
        console.error("Failed to save subtask data to local DB:", error);
      }

      const latestTask = store.getState().taskForm.currentSubtaskData;
      const allItems = latestTask?.MethodId?.work_instruction_id?.map(
        (el: any) => {
          return el[newKey];
        }
      );

      const flattenItems = allItems?.flat();
      console.log(flattenItems, "thisi isabasdfasdfasdf");
      const presentInAnotherWI = flattenItems?.filter(
        (el: any) => el?._id == requiredThingsDeleteName?._id
      );
      let newKey2: string | undefined;
      switch (deleteIdParent) {
        case "Tools":
          newKey2 = "ToolId";
          break;
        case "Materials":
          newKey2 = "MaterialId";
          break;
        case "Machinery":
          newKey2 = "MachinaryId";
          break;
        case "Manpower":
          newKey2 = "ManpowerId";
          break;
        default:
          console.error("Invalid deleteIdParent value:", deleteIdParent);
      }
      if (newKey2 && presentInAnotherWI && presentInAnotherWI?.length == 0) {
        dispatch(
          updateTaskData({
            ...(latestTask as any),
            [newKey2]: [
              ...((latestTask as any)[newKey2] || [])?.filter(
                (el: any) => el?._id !== requiredThingsDeleteName?._id
              ),
            ],
          })
        );
        await saveSyncData(
          {
            ...(latestTask as any),
            [newKey2 as any]: [
              ...((latestTask as any)[newKey2 as any] || [])?.filter(
                (el: any) => el?._id !== requiredThingsDeleteName?._id
              ),
            ],
          },
          "time",
          "TaskForm"
        );
      }
      dispatch(settaskChangeAPiFlag(true));
      showToast({
        messageContent: `${requiredThingsDeleteName.name} deleted Successfully!`,
        type: "success",
      });
    }

    dispatch(closePopup("DeleteWipRequiredThingsNamet"));
  }, [
    categoryData,
    deleteIdParent,
    requiredThingsDeleteName,
    dispatch,
    onUpdateCategoryData,
    popupIdParent,
    categortDataParent,
  ]); // Dependencies for useCallback
  const getCategories = async (tablename: string) => {
    console.log("fetheced category called");

    let response;
    let dbName;
    switch (tablename) {
      case "MaterialCategory":
        dbName = await initializeDatabase("MaterialCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "Manpowercategory":
        dbName = await initializeDatabase("Manpowercategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "ToolCategory":
        dbName = await initializeDatabase("ToolCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "machinaryCategory":
        dbName = await initializeDatabase("MachinaryCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "departmentdetails":
        dbName = await initializeDatabase("Departments");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "designationdetails":
        dbName = await initializeDatabase("MaterialCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      default:
        console.log("Invalid table name");
        response = null;
    }
    console.log(response, "this is response for selecte option");
    return (
      response?.map((e: { _id: string; name: string }) => ({
        id: e._id,
        category: e.name,
      })) || []
    );
  };
  const handleToggleDropdown = async (name: string, modelname?: string) => {
    if (modelname) {
      const data = await getCategories(modelname);
      setSelectedOptionApidata(data);
      (() => {
        switch (modelname) {
          case "MaterialCategory":
            SetprimaryLabelForAddCategoryType("Category");
            return;
          case "machinaryCategory":
            SetprimaryLabelForAddCategoryType("Category");
            return;
          case "ToolCategory":
            SetprimaryLabelForAddCategoryType("Category");
            return;
          case "Manpowercategory":
            SetprimaryLabelForAddCategoryType("Category");
            return;
          case "departmentdetails":
            SetprimaryLabelForAddCategoryType("Department");
            return;
          default:
            return <>Unknown </>;
        }
      })();
    }
    const namespacedPopupId = getPopupId(name);
    console.log(namespacedPopupId, "theseare name sapisdf");
    dispatch(openPopup(namespacedPopupId));
  };
  console.log("categoryData>>>::", categoryData);
  return (
    <div
      className={styles.subtask_method_popup_container}
      style={{ position: "relative" }}
    >
      <div
        onClick={(e) => (isEdit ? onClick(e) : () => {})}
        className={styles.subtask_method_popup_header}
      >
        <div className={styles.tcr_desc_details}>
          <div className={styles.tcr_tooltip}>
            <h4>{data.description}</h4>
          </div>

          {data.file && (
            <div className={styles.tcr_photoname}>
              <p className="small_text_p_400">{data?.file?.name}</p>
            </div>
          )}
        </div>

        {/* Photo sections */}
        {data.category === "photo" && data.photoDetails && (
          <>
            {data.photoDetails.map((photo, index) => (
              <div key={index} className={styles.tcr_tooltip_photo_container}>
                <div className={styles.tcr_tooltip_photo}>
                  <ImageIcon />{" "}
                  <h4>
                    {isBase64(photo?.photo)
                      ? photo?.fileName
                      : getFileName(photo?.photo)}
                  </h4>
                </div>
              </div>
            ))}
          </>
        )}

        {/* Checkbox section */}
        {data.category === "checkbox" && (
          <div className={styles.tcr_tooltip_photo}>
            <CheckBox /> <h4>Checkbox</h4>
          </div>
        )}
      </div>

      {/* Photo popup */}
      {popups[getPopupId("workinstructionsphoto")] && (
        <WorkInstructionsPopup
          onCancel={handlePhotoPopupClose}
          onSubmit={handlePhotoPopupSubmit}
          initialData={initaldata}
          startWithPhotoCheckboxPage={isPhotoCheckboxPage}
          isEdit={initaldata ? true : false}
        />
      )}

      <div
        className={styles.subtaskcreation_line_container}
        style={{ marginBlock: "0rem" }}
      >
        <span className={styles.dottedline_wrapper}></span>
        <SuryconLogo />
        <span className={styles.dottedline_wrapper}></span>
      </div>

      {/* Category Selection Section */}
      <div className={styles.subtask_method_popup_row}>
        <div style={{ minWidth: "50px", position: "relative" }}>
          <AddToolTip
            label="Manpower"
            onClick={(e) => {
              e.stopPropagation();
              handleToggleDropdown("Manpower", "Manpowercategory");
              setSelectedOption("Manpowercategory");
            }}
            isEdit={isEdit}
            handleDelete={(item) => {
              setPopupIdParent(popupId);
              setCategoryDataParent(categoryData);
              setDeleteIdParent("Manpower");
              dispatch(setRequiredThings({ ...item, category: "Manpower" }));
              setTimeout(() => {
                dispatch(closePopup("DeleteWipRequiredThingsNamet" + `${id}`));
              }, 400);
              setTimeout(() => {
                dispatch(openPopup("DeleteWipRequiredThingsNamet" + `${id}`)); // Open after short delay
              }, 400);
            }}
            className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
            data={categoryData.Manpower}
          />
          {popups[getPopupId("Manpower")] && (
            <AddCategoryType
              primaryLabel2="Add Manpower"
              modelname={selectedOption}
              isStepForm={true}
              primaryLabel={primaryLabelForAddCategoryType}
              title="Add Manpower"
              label="Manpower"
              data={selectedOptionApidata as TaskDataType[]}
              placeholder="Search"
              buttonLabel="Add Category"
              initialSelected={categoryData?.Manpower}
              onSelect={(item: any) =>
                handleSelect("Manpower", item, "Manpower")
              }
              onClose={() => dispatch(closePopup(getPopupId("Manpower")))}
            />
          )}
        </div>

        <div style={{ minWidth: "50px", position: "relative" }}>
          <AddToolTip
            label="Machinery"
            onClick={(event: any) => {
              handleToggleDropdown("Machinery", "machinaryCategory");
              setSelectedOption("machinaryCategory");
            }}
            isEdit={isEdit}
            handleDelete={(item) => {
              setPopupIdParent(popupId);
              setCategoryDataParent(categoryData);
              setDeleteIdParent("Machinery");

              dispatch(setRequiredThings({ ...item, category: "Machine" }));
              setTimeout(() => {
                dispatch(closePopup("DeleteWipRequiredThingsNamet" + `${id}`));
              }, 400);
              setTimeout(() => {
                dispatch(openPopup("DeleteWipRequiredThingsNamet" + `${id}`));
              }, 400);
            }}
            className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
            data={categoryData.Machinery}
          />
          {popups[getPopupId("Machinery")] && (
            <AddCategoryType
              primaryLabel2="Add Machinery"
              primaryLabel={primaryLabelForAddCategoryType}
              modelname={selectedOption}
              isStepForm={true}
              title="Add Machinery"
              label="Machinery"
              data={selectedOptionApidata as TaskDataType[]}
              initialSelected={categoryData?.Machinery}
              placeholder="Search"
              buttonLabel="Add Category"
              onSelect={(item: any) =>
                handleSelect("Machinery", item, "Machinery")
              }
              onClose={() => dispatch(closePopup(getPopupId("Machinery")))}
            />
          )}
        </div>
      </div>

      <div
        className={styles.subtask_method_popup_row}
        style={{ paddingBottom: "1rem" }}
      >
        <div style={{ minWidth: "50px", position: "relative" }}>
          <AddToolTip
            label="Tools"
            onClick={(e) => {
              e.stopPropagation();
              handleToggleDropdown("Tools", "ToolCategory");
              setSelectedOption("ToolCategory");
            }}
            isEdit={isEdit}
            handleDelete={(item) => {
              setPopupIdParent(popupId);
              setCategoryDataParent(categoryData);
              setDeleteIdParent("Tools");
              dispatch(setRequiredThings({ ...item, category: "Tool" }));
              setTimeout(() => {
                dispatch(closePopup("DeleteWipRequiredThingsNamet" + `${id}`));
              }, 400);
              setTimeout(() => {
                dispatch(openPopup("DeleteWipRequiredThingsNamet" + `${id}`));
              }, 400);
            }}
            className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
            data={categoryData?.Tools as requiredthings[]}
          />
          {popups[getPopupId("Tools")] && (
            <AddCategoryType
              primaryLabel2="Add Tools"
              primaryLabel={primaryLabelForAddCategoryType}
              modelname={selectedOption}
              isStepForm={true}
              title="Add Tools"
              label="Tool"
              data={selectedOptionApidata as TaskDataType[]}
              placeholder="Search"
              initialSelected={categoryData?.Tools}
              buttonLabel="Add Category"
              onSelect={(item: any) => handleSelect("Tools", item, "Tools")}
              onClose={() => dispatch(closePopup(getPopupId("Tools")))}
            />
          )}
        </div>
        <div style={{ minWidth: "50px", position: "relative" }}>
          <AddToolTip
            label="Materials"
            onClick={(e) => {
              e.stopPropagation();
              handleToggleDropdown("Materials", "MaterialCategory");
              setSelectedOption("MaterialCategory");
            }}
            isEdit={isEdit}
            handleDelete={(item) => {
              setPopupIdParent(popupId);
              setCategoryDataParent(categoryData);
              setDeleteIdParent("Materials");
              dispatch(setRequiredThings({ ...item, category: "Material" }));
               setTimeout(() => {
                dispatch(closePopup("DeleteWipRequiredThingsNamet" + `${id}`));
              }, 400);
              setTimeout(() => {
                dispatch(openPopup("DeleteWipRequiredThingsNamet" + `${id}`));
              }, 400);
            }}
            className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
            data={categoryData.Materials}
          />
          {popups[getPopupId("Materials")] && (
            <AddCategoryType
              primaryLabel2="Add Materials"
              primaryLabel={primaryLabelForAddCategoryType}
              modelname={selectedOption}
              isStepForm={true}
              title="Add Materials"
              data={selectedOptionApidata as any}
              placeholder="Search"
              label="Material"
              initialSelected={categoryData?.Materials}
              buttonLabel="Add Category"
              onSelect={(item: any) =>
                handleSelect("Materials", item, "Materials")
              }
              onClose={() => dispatch(closePopup(getPopupId("Materials")))}
            />
          )}

          {popups["DeleteWipRequiredThingsNamet" + `${id}`] && (
            <DeletePopup
              width="23rem"
              height="calc(100% - 9rem)"
              heightupperlimit="0rem"
              header={`Are you sure you want to delete this ${requiredThingsDeleteName.category} ?`}
              callbackDelete={detleWorkInstructionItems}
              onClose={() => {
                dispatch(closePopup("DeleteWipRequiredThingsNamet" + `${id}`));
              }}
            >
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    {(() => {
                      switch (deleteIdParent) {
                        case "Materials":
                          return "Material";
                        case "Tools":
                          return "Tool";
                        case "Manpower":
                          return "Manpower";
                        case "Machinery":
                          return "Machine";
                        default:
                          return "";
                      }
                    })()}
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4 style={{ color: "var(--text-black-87)" }}>
                      {requiredThingsDeleteName.name}
                    </h4>
                  </div>
                </div>
              </div>
            </DeletePopup>
          )}
        </div>
      </div>

      {isEdit && handleDelete && (
        <div className={styles.delete_icon_tooltip} onClick={handleDelete}>
          <DeleteIcon />
        </div>
      )}
    </div>
  );
};

export default TaskWorkInstructionsPopup;
