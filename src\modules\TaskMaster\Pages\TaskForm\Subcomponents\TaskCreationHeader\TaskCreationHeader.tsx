import React, { useEffect, useMemo, useState } from "react";
import styles from "../../Styles/TaskCreationForm.module.css";
import { TaskForm } from "../../../Tasks/Subcomponents/AddTaskForm";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import {
  DeleteIcon,
  DesignationIcon,
  RedCrossIcon,
  TaskEditPencil,
  WieghtPercentageIcon,
} from "../../../../../../assets/icons";
import {
  closePopup,
  openPopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import {
  resetInputValues,
  setInputValue,
} from "../../../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { RootState } from "../../../../../../redux/store";
import {
  setcurrentTaskData,
  currentSubtaskData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import {
  useDeleteTaskMutation,
  useGetTaskBasicsDetailsQuery,
} from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import { Task } from "../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import {
  SubtaskSvg,
  UnitSvgs,
} from "../../../../../../assets/TopbarAssets/SVGs";
import { removeNavigate } from "../../../../../../redux/features/Modules/Reusble/navigationSlice";
import { useAppSelector } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import {
  fileTypeMapper,
  getFileName,
  slicedData,
} from "../../../../../../functions/functions";
import TaskDetails from "./../TaskDetails/TaskDetails";
import { useToast } from "../../../../../../hooks/ToastHook";
import { useAuth } from "../../../../../../AuthProvider";
import SummaryPopup from "../../../../../../components/Reusble/Global/SummaryPopup";

// Skeleton Loader
const SkeletonBox = ({
  width = "100px",
  height = "24px",
  className = styles.skeleton_box,
}) => (
  <span
    className={className}
    style={{
      width,
      height,
    }}
  />
);
const FixedBox = ({ width, height, children }: any) => (
  <span
    className={styles.fixed_box}
    style={{
      width,
      height,
    }}
  >
    {children}
  </span>
);

const TaskCreationHeader: React.FC<{
  taskId: string | undefined;
  editState: boolean;
  loading: boolean;
  TaskData: any;
  onclick: () => void;
}> = ({ taskId, editState, loading, onclick, TaskData }) => {
  const taskdetail = useSelector(
    (state: RootState) => state?.taskForm?.currentSubtaskData
  );
  const showToast = useToast();
  const [showFullDescription, setShowFullDescription] = useState(false);
  const navigate = useNavigate();
  const [deleteTask] = useDeleteTaskMutation();
  const [disableButton, setDisableButton] = useState<boolean>(false);
  const { catId } = useParams<{ catId: string }>();
  const dispatch = useDispatch();
  // const [editTask] = useEditTask();
  const isDeletedNext = useAppSelector(
    (state) => state.isDeletedSLice.isDeletedNext
  );

  console.log("task details ka header he", taskdetail);

  const popups = useSelector((state: RootState) => state?.popup?.popups);

  const title = loading ? (
    <SkeletonBox width="180px" height="32px" />
  ) : (
    <span className={styles.title_text}>{TaskData?.name || "--"}</span>
  );

  const slicedDescription = useMemo(() => {
    if (!TaskData?.Description) return "";
    return TaskData?.Description.length <= 200
      ? TaskData?.Description
      : TaskData?.Description.slice(0, 200);
  }, [TaskData?.Description]);

  const description = loading ? (
    <SkeletonBox width="240px" height="18px" />
  ) : (
    <span className={styles.description_text}>
      {slicedDescription}
      {TaskData?.Description && TaskData?.Description.length > 200 && (
        <>
          {" "}
          <span
            style={{
              color: "var(--secondary_color)",
              cursor: "pointer",
              fontWeight: 500,
              textDecoration: "underline",
              textUnderlineOffset: "1.5px",
              textDecorationThickness: "1px",
              textDecorationColor: "var(--secondary_color)",
            }}
            onClick={(e) => {
              e.stopPropagation();
              setShowFullDescription(true);
            }}
          >
            View all
          </span>
        </>
      )}
      {!TaskData?.Description && "--"}
    </span>
  );

  const quantity = (
    <FixedBox minWidth="32px" height="18px">
      {loading ? (
        <SkeletonBox
          width="32px"
          height="18px"
          className={styles.skeleton_box_light}
        />
      ) : (
        <span className={styles.content_text}>
          {TaskData?.subtaskWeighatages ?? "--"}
        </span>
      )}
    </FixedBox>
  );

  const unit = (
    <FixedBox minWidth="32px" height="18px">
      {loading ? (
        <SkeletonBox
          width="32px"
          height="18px"
          className={styles.skeleton_box_light}
        />
      ) : (
        <span className={styles.content_text}>{TaskData?.Unit || "--"}</span>
      )}
    </FixedBox>
  );

  const subtaskCount = (
    <FixedBox minWidth="32px" height="18px">
      {loading ? (
        <SkeletonBox
          width="32px"
          height="18px"
          className={styles.skeleton_box_light}
        />
      ) : (
        <span className={styles.content_text}>
          {TaskData?.Subtaskdetails?.length ?? "--"}
        </span>
      )}
    </FixedBox>
  );

  // const handleeditClose = () => {
  //   dispatch(resetInputValues());
  //   dispatch(closePopup("TaskEdit"));

  const { user } = useAuth();
  const isMD = user?.designationId?.name === "MD";
  const isHOD = user?.designationId?.name === "HOD";

  return (
    <div
      className={styles.task_creation_header}
      onClick={
        popups["TaskEdit"]
          ? () => {}
          : () => {
              if (editState) {
                dispatch(openPopup("TaskEdit"));
                dispatch(
                  setInputValue({
                    tskId: taskdetail?._id ?? "",
                    TaskName: taskdetail?.name ?? "",
                    Description: taskdetail?.Description ?? "",
                    Quantity: taskdetail?.subtaskWeighatages ?? "",
                    Unit: taskdetail?.Unit ?? "",
                  })
                );
              }
            }
      }
    >
      <div className={styles.task_creation_titleandDesc}>
        <h2>{title}</h2>
        <p style={{ marginTop: "0.5rem" }}>{description}</p>
      </div>

      {showFullDescription && (
        <SummaryPopup
          header="View"
          callbackCross={() => setShowFullDescription(false)}
          callbackBack={() => setShowFullDescription(false)}
          callbackApprove={() => setShowFullDescription(false)}
        >
          <div style={{ display: "flex", flexWrap: "wrap" }}>
            <div
              className={styles.summaryDivData}
              style={{ flex: 1, minWidth: 220 }}
            >
              <div className={styles.summaryDataContent}>
                {/* <p className="p_tag_14px">Name</p> */}
                <p
                  style={{ color: "var(--text-black-60)", marginTop: "0.5rem" }}
                  className="p_tag_14px"
                >
                  Name
                </p>
                <h4>{TaskData?.name || "--"}</h4>
              </div>
            </div>
          </div>
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)", marginTop: "0.5rem" }}
                className="p_tag_14px"
              >
                Description
              </p>
              <h4>{TaskData?.Description || "--"}</h4>
            </div>
          </div>
          <div style={{ display: "flex", flexWrap: "wrap" }}>
            <div
              className={styles.summaryDivData}
              style={{ flex: 1, minWidth: 220 }}
            >
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)", marginTop: "0.5rem" }}
                  className="p_tag_14px"
                >
                  Quantity
                </p>
                <h4>{TaskData?.subtaskWeighatages ?? "--"}</h4>
              </div>
            </div>
            <div
              className={styles.summaryDivData}
              style={{ flex: 1, minWidth: 220 }}
            >
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)", marginTop: "0.5rem" }}
                  className="p_tag_14px"
                >
                  Unit
                </p>
                <h4>{TaskData?.Unit || "--"}</h4>
              </div>
            </div>
            <div
              className={styles.summaryDivData}
              style={{ flex: 1, minWidth: 220 }}
            >
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)", marginTop: "0.5rem" }}
                  className="p_tag_14px"
                >
                  Subtasks
                </p>
                <h4>{TaskData?.Subtaskdetails?.length ?? "--"}</h4>
              </div>
            </div>
          </div>
        </SummaryPopup>
      )}

      {/* {popups["TaskEdit"] && (
        <TaskForm
          mode="edit"
          isTaskPage={true}
          isEdit={true}
          onClose={handleeditClose}
        />
      )} */}
      <div className={styles.task_creation_right}>
        <div className={styles.task_creation_tooltip}>
          <div className={styles.task_creation_tooltip_icon}>
            {TaskData?.Unit || "--"}
          </div>
          <div>
            <p
              className="small_text_p"
              style={{ color: "var( --text-black-60)" }}
            >
              Quantity
            </p>
            <p
              className="small_text_p"
              style={{ color: "var( --text-black-87)" }}
            >
              {quantity}
            </p>
          </div>
        </div>
        <div className={styles.task_creation_tooltip}>
          <div className={styles.task_creation_tooltip_icon}>
            <UnitSvgs />
          </div>
          <div>
            <p
              className="small_text_p"
              style={{ color: "var( --text-black-60)" }}
            >
              Unit
            </p>
            <p
              className="small_text_p"
              style={{ color: "var( --text-black-87)" }}
            >
              {unit}
            </p>
          </div>
        </div>
        <div className={styles.task_creation_tooltip}>
          <div className={styles.task_creation_tooltip_icon}>
            <SubtaskSvg />
          </div>
          <div>
            <p
              className="small_text_p"
              style={{ color: "var( --text-black-60)" }}
            >
              Subtasks
            </p>
            <p
              className="small_text_p"
              style={{ color: "var( --text-black-87)" }}
            >
              {subtaskCount}
            </p>
          </div>
        </div>
      </div>

      {!editState && !isDeletedNext && !isMD && (
        <div
          className={styles.taskeditpencil}
          onClick={(e) => {
            e.stopPropagation();
            if (loading) return;
            onclick();
          }}
        >
          <TaskEditPencil />
        </div>
      )}
      {editState && (
        <div className={styles.taskcreation_editableState}>
          <div
            className={styles.redcrossiconstyle}
            onClick={(e) => {
              e.stopPropagation();
              onclick();
            }}
          >
            <RedCrossIcon />
          </div>
          <div
            className={styles.taskcreation_dustbinicon}
            onClick={(e) => {
              e.stopPropagation();
              dispatch(openPopup("DeleteTask"));
            }}
          >
            <DeleteIcon />
          </div>
        </div>
      )}
      {popups["DeleteTask"] && (
        <DeletePopup
          header="Are you sure you want to delete this Task?"
          height="calc(100% - 7.25rem)"
          heightupperlimit="0.25rem"
          callbackDelete={
            disableButton
              ? () => {}
              : async () => {
                  setDisableButton(true);
                  const res = await deleteTask({ taskId: taskId });

                  if (res.error) {
                    showToast({
                      messageContent: "Oops! Something went wrong!",
                      type: "danger",
                    });

                    return;
                  }
                  showToast({
                    messageContent: `Task Deleted successfully!`,
                    type: "success",
                  });

                  console.log(catId, taskId, "both are here bro");
                  navigate(`/category/${catId}`);
                  dispatch(removeNavigate());
                  dispatch(
                    setcurrentTaskData({
                      _id: "",
                      name: "",
                      Unit: "",
                      Description: "",
                      subtaskWeighatages: 0,
                      Tracking: "",
                      DepartmentId: [],
                      DesignationId: [],
                      MaterialId: [],
                      ToolId: [],
                      MachinaryId: [],
                      ManpowerId: [],
                      Adminid: [],
                      TaskmasterId: {},
                      ReporterId: {
                        Reporter: [],
                      },
                      AssigneeId: [],
                      Subtaskdetails: [],
                      MethodId: {
                        work_instruction_id: [],
                        task_closing_requirement: [],
                        Controlplan: [],
                      },
                    })
                  );
                  dispatch(closePopup("DeleteTask"));
                  setDisableButton(false);
                }
          }
          onClose={() => {
            dispatch(closePopup("DeleteTask"));
          }}
        >
          <DeleteTaskBody data={taskdetail} />
        </DeletePopup>
      )}
    </div>
  );
};
interface DeleteTaskBodyProps {
  data: currentSubtaskData;
}
const DeleteTaskBody: React.FC<DeleteTaskBodyProps> = ({ data }) => {
  console.log("data1111", data);
  return (
    <div>
      <div className={styles.flexContainer}>
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Name
            </p>
            <h4 style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}>
              {data?.name || "No name provided"}
            </h4>
          </div>
        </div>
      </div>

      {data?.Description && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Description
            </p>
            <h4 style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}>
              {data?.Description || "No description"}
            </h4>
          </div>
        </div>
      )}

      <div className={styles.flexContainer}>
        {(data?.subtaskWeighatages === 0 || data?.subtaskWeighatages > 0) && (
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent_weightage}>
              <div>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Quantity
                </p>
                <h4
                  style={{
                    marginTop: "0.3rem",
                    color: "var(--text-black-87)",
                  }}
                >
                  {data?.subtaskWeighatages
                    ? data?.subtaskWeighatages
                    : data?.subtaskWeighatages === 0
                    ? 0
                    : "No weightage"}
                </h4>
              </div>
            </div>
          </div>
        )}
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Unit
            </p>
            <h4 style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}>
              {data?.Unit || "No unit"}
            </h4>
          </div>
        </div>
      </div>

      {(data?.DepartmentId?.length ?? 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Departments
            </p>
            <div
              className=""
              style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
            >
              {data?.DepartmentId.map((e) => (
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {e?.name}
                </h4>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {data?.DesignationId?.length > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Designations
            </p>
            <div
              className=""
              style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
            >
              {data?.DesignationId.map((e) => (
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {e?.name}
                </h4>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {(data?.Subtaskdetails?.length ?? 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Subtasks
            </p>
            <div
              className=""
              style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
            >
              {data?.Subtaskdetails.map((e) => (
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {e?.name}
                </h4>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {((data?.ToolId?.length ?? 0) > 0 ||
        (data?.MaterialId?.length ?? 0) > 0 ||
        (data?.ManpowerId?.length ?? 0) > 0 ||
        (data?.MachinaryId?.length ?? 0) > 0) && (
        <h4 style={{ color: "var(--primary_color)", margin: "0.6rem" }}>
          Resources
        </h4>
      )}
      {(data?.ManpowerId?.length ?? 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Manpower
            </p>
            <div
              className=""
              style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
            >
              {data?.ManpowerId.map((e) => (
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {e?.name}
                </h4>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {(data?.MachinaryId?.length ?? 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Machinery
            </p>
            <div
              className=""
              style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
            >
              {data?.MachinaryId.map((e) => (
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {e?.name}
                </h4>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {(data?.ToolId?.length ?? 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Tools
            </p>
            <div
              className=""
              style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
            >
              {data?.ToolId.map((e) => (
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {e?.name}
                </h4>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {(data?.MaterialId?.length ?? 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Materials
            </p>
            <div
              className=""
              style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
            >
              {data?.MaterialId.map((e) => (
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {e?.name}
                </h4>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {/* Task allocation section */}
      {((data?.Adminid?.length ?? 0) > 0 ||
        (data?.AssigneeId?.length ?? 0) > 0) && (
        <h4 style={{ color: "var(--primary_color)", margin: "0.6rem" }}>
          Task Allocation
        </h4>
      )}
      {(data?.Adminid?.length ?? 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Task Manager
            </p>
            <div
              className=""
              style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
            >
              {data?.Adminid.map((e) => (
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {e?.name}
                </h4>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {(data?.AssigneeId?.length ?? 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Assign To
            </p>
            <div
              className=""
              style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
            >
              {data?.AssigneeId.map((e) => (
                <h4
                  style={{ marginTop: "0.3rem", color: "var(--text-black-87)" }}
                >
                  {e?.name}
                </h4>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {(data?.ReporterId && data?.ReporterId?.Reporter?.length > 0) > 0 && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Reporter
            </p>
            <div className="">
              {data?.ReporterId?.Reporter?.map((e) => (
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    marginTop: "0.5rem",
                  }}
                >
                  <h4
                    style={{
                      marginTop: "0.3rem",
                      color: "var(--text-black-87)",
                      fontWeight: 700,
                    }}
                  >
                    Level {e?.Level}
                  </h4>
                  <div
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    {e?.designationId &&
                      e?.designationId?.map((item) => (
                        <h4
                          style={{
                            marginTop: "0.3rem",
                            color: "var(--text-black-87)",
                          }}
                        >
                          {item?.name}
                        </h4>
                      ))}
                  </div>
                </div>
              )) || "No description"}
            </div>
          </div>
        </div>
      )}
      {((data?.MethodId?.work_instruction_id?.length ?? 0) > 0 ||
        (data?.MethodId?.Controlplan?.length ?? 0) > 0 ||
        (data?.MethodId?.Failuremode?.length ?? 0) > 0 ||
        (data?.MethodId?.task_closing_requirement?.length ?? 0) > 0) && (
        <h4 style={{ color: "var(--primary_color)", margin: "0.6rem" }}>
          Method
        </h4>
      )}
      {(data?.MethodId?.work_instruction_id?.length ?? 0) > 0 && (
        <>
          {data?.MethodId?.work_instruction_id?.map((e: any, index: any) => (
            <>
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Work Instructions {index + 1}
                  </p>
                  <p
                    style={{
                      color: "var(--text-black-60)",
                      marginTop: "0.5rem",
                    }}
                    className="p_tag_14px"
                  >
                    Description
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                      }}
                    >
                      {e?.Description}
                    </h4>
                  </div>
                </div>
              </div>
              {e?.file?.name && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      {fileTypeMapper(e?.file)}
                    </p>
                    <div
                      className=""
                      style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      <h4
                        style={{
                          marginTop: "0.3rem",
                          color: "var(--text-black-87)",
                          textTransform: "capitalize",
                        }}
                      >
                        {e?.file?.name}
                      </h4>
                    </div>
                  </div>
                </div>
              )}
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Action
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                        textTransform: "capitalize",
                      }}
                    >
                      {e?.optionselected}
                    </h4>
                  </div>
                </div>
              </div>
              {e?.optionselected == "Photo" ||
                (e?.optionselected == "photo" && (
                  <div className={styles.summaryDivData}>
                    <div className={styles.summaryDataContent}>
                      {e?.photoref?.photos?.map((e) => (
                        <>
                          <p style={{ color: "var(--text-black-87)" }}>
                            {e?.fileName || getFileName(e?.photo)}
                          </p>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            Reference Detail
                          </p>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              gap: "1rem",
                              flexWrap: "wrap",
                            }}
                          >
                            <h4
                              style={{
                                marginTop: "0.3rem",
                                color: "var(--text-black-87)",
                              }}
                            >
                              {e?.details}
                            </h4>
                          </div>
                        </>
                      ))}
                    </div>
                  </div>
                ))}
              {e?.manpowerId?.length > 0 && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Manpower
                    </p>
                    <div
                      className=""
                      style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      {e?.manpowerId?.map((e) => (
                        <h4
                          style={{
                            marginTop: "0.3rem",
                            color: "var(--text-black-87)",
                          }}
                        >
                          {e?.name}
                        </h4>
                      )) || "No description"}
                    </div>
                  </div>
                </div>
              )}
              {e?.machinaryId?.length > 0 && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Machinery
                    </p>
                    <div
                      className=""
                      style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      {e?.machinaryId?.map((e) => (
                        <h4
                          style={{
                            marginTop: "0.3rem",
                            color: "var(--text-black-87)",
                          }}
                        >
                          {e?.name}
                        </h4>
                      )) || "No description"}
                    </div>
                  </div>
                </div>
              )}
              {e?.toolsId?.length > 0 && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Tools
                    </p>
                    <div
                      className=""
                      style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      {e?.toolsId?.map((e) => (
                        <h4
                          style={{
                            marginTop: "0.3rem",
                            color: "var(--text-black-87)",
                          }}
                        >
                          {e?.name}
                        </h4>
                      )) || "No description"}
                    </div>
                  </div>
                </div>
              )}
              {e?.materialId?.length > 0 && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Materials
                    </p>
                    <div
                      className=""
                      style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      {e?.materialId?.map((e) => (
                        <h4
                          style={{
                            marginTop: "0.3rem",
                            color: "var(--text-black-87)",
                          }}
                        >
                          {e?.name}
                        </h4>
                      )) || "No description"}
                    </div>
                  </div>
                </div>
              )}
            </>
          ))}
        </>
      )}
      {(data?.MethodId?.task_closing_requirement?.length ?? 0) > 0 && (
        <>
          {data?.MethodId?.task_closing_requirement?.map((e, index) => (
            <>
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Task Closing Requirements {index + 1}
                  </p>
                  <p
                    style={{
                      color: "var(--text-black-60)",
                      marginTop: "0.5rem",
                    }}
                    className="p_tag_14px"
                  >
                    Description
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                      }}
                    >
                      {e?.Description}
                    </h4>
                  </div>
                </div>
              </div>
              {e?.file?.name && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      {fileTypeMapper(e?.file)}
                    </p>
                    <div
                      className=""
                      style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      <h4
                        style={{
                          marginTop: "0.3rem",
                          color: "var(--text-black-87)",
                          textTransform: "capitalize",
                        }}
                      >
                        {e?.file?.name}
                      </h4>
                    </div>
                  </div>
                </div>
              )}
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Action
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                        textTransform: "capitalize",
                      }}
                    >
                      {e?.optionselected}
                    </h4>
                  </div>
                </div>
              </div>
              {(e?.optionselected == "Photo" ||
                e?.optionselected == "photo") && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    {e?.photoref?.photos?.map((e) => (
                      <>
                        <p style={{ color: "var(--text-black-87)" }}>
                          {" "}
                          {e?.fileName || getFileName(e?.photo)}
                        </p>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Reference Detail
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4
                            style={{
                              marginTop: "0.3rem",
                              color: "var(--text-black-87)",
                            }}
                          >
                            {e?.details}
                          </h4>
                        </div>
                      </>
                    ))}
                  </div>
                </div>
              )}
            </>
          ))}
        </>
      )}
      {/* this is for control plan */}
      {(data?.MethodId?.Controlplan?.length ?? 0) > 0 && (
        <>
          {data?.MethodId?.Controlplan?.map((e, index) => (
            <>
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Quality Control Plans {index + 1}
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                      }}
                    >
                      {e?.Description}
                    </h4>
                  </div>
                </div>
              </div>
            </>
          ))}
        </>
      )}
      {/* this is for failure mode */}
      {(data?.MethodId?.Failuremode?.length ?? 0) > 0 && (
        <>
          {data?.MethodId?.Failuremode?.map((e, index: number) => (
            <>
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Quality Ensuring Measures {index + 1}
                  </p>
                  <p
                    style={{
                      color: "var(--text-black-60)",
                      marginTop: "0.5rem",
                    }}
                    className="p_tag_14px"
                  >
                    Description
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                      }}
                    >
                      {e?.Description}
                    </h4>
                  </div>
                </div>
              </div>
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Solution
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                      }}
                    >
                      {e?.solution}
                    </h4>
                  </div>
                </div>
              </div>
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Severity
                  </p>
                  <div
                    className=""
                    style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                  >
                    <h4
                      style={{
                        marginTop: "0.3rem",
                        color: "var(--text-black-87)",
                      }}
                    >
                      {e?.severity}
                    </h4>
                  </div>
                </div>
              </div>
            </>
          ))}
        </>
      )}
    </div>
  );
};
export default TaskCreationHeader;
