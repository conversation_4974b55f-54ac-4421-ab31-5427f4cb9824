import { CardItemsProps } from "../TaskMasterInterfaces/TaskMasterInterface";
import styles from "./Styles/CardItems.module.css";

export function CardItems({ title, name }: CardItemsProps) {
  return (
    <div className={styles.carditem_container}>
      <div className={styles.carditem_details}>
        <p className={`${styles.carditem_head} small_text_p`}>{title}</p>
        <p className={`${styles.carditem_num}`}>{name}</p>
      </div>
    </div>
  );
}
