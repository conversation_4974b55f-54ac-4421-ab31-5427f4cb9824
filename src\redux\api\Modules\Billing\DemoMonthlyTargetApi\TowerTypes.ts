// Tower and NonTower Details Types
export interface TowerDetails {
  Floors: number;
  BasementLevels: number;
  SwimmingPool: number;
  ParkArea: number;
  Budget: number;
}

// Resource Types
export interface ManpowerItem {
  Quantity: number;
  Skill: string;
}

export interface ResourceItem {
  Quantity: number;
  CompanyName: string;
  Unit: string;
}

// Task Type
export interface Task {
  TaskName: string;
  Weightage: number;
  Quantity: number;
  Manpower: Record<string, ManpowerItem>;
  Machinery: Record<string, ResourceItem>;
  Tools: Record<string, ResourceItem>;
  Materials: Record<string, ResourceItem>;
}

// Floor Details Type
export interface FloorDetail {
  Floor: number;
  Area: number;
  StartDate: string;
  EndDate: string;
  Remarks: string;
  MainTaskDescription: string;
  Tasks: Task[];
}

// Tower Type
export interface Tower {
  Details: TowerDetails;
  FloorDetails: FloorDetail[];
}

// API Response Type
export interface TowerApiResponse {
  Towers: Record<string, Tower>;
  NonTowers: Record<string, Tower>;
}