import { Outlet } from "react-router-dom";
import styles from "./Styles/Category.module.css";
import CategoryView from "../TaskMasterNavbar/TaskMasterNav";
import { useDispatch } from "react-redux";

const Category = () => {
  const dispatch = useDispatch();

  return (
    <div className={styles.taskContainer}>
      <div className={styles.taskcreation_header}>
        <CategoryView />
      </div>
      <Outlet />
    </div>
  );
};

export default Category;