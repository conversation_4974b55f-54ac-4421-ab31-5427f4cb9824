import styles from "../Styles/SubVersionContainers.module.css";
import dummyImage from "../../../assets/images/pm.png";

import { Arrow } from "../../../assets/icons";
import { FC, useState } from "react";
interface DetailVersionProps {
  employeeData: {
    employeename: string;
    employeeId: string;
    designation: string;
    photo: string;
    createdAt: string;
  };
}
const DetailVersion: FC<DetailVersionProps> = ({ employeeData }) => {
  const [showDetail, setShowDetail] = useState(false);

  const toggleDetail = () => {
    setShowDetail(!showDetail);
  };

  return (
    <div className={`${styles.DetailVersion_detailedHistory_container}`}>
      <div className={`${styles.DetailVersion_detailedHistory_header}  `}>
        <img src={dummyImage} alt="" />
      </div>
      <div
        className={`${styles.DetailVersion_detailedHistory_header_content} ' `}
      >
        <div className={`${styles.DetailVersion_detail_heading}`}>
          <p className="small_text_p">
            {employeeData?.employeename || "Mukesh"}
          </p>
          <p
            className={`small_text_p  ${styles.DetailVersion_detail_heading_arrow}`}
          >
            02:00PM
          </p>
        </div>
      </div>
    </div>
  );
};

export default DetailVersion;
