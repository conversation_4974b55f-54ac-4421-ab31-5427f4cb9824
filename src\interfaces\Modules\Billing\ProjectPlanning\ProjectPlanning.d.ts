import { ProjectData } from "../../../../modules/Billing/Pages/ProjectPlanning/Projects/AddProjectForm/Interfaces/interface";

// interfaces for AddLocationFormProps
export interface AddLocationFormProps {
  onClose: () => void;
}
//   interface for locationheaderprops
interface LocationHeaderProps {
  ontoggle?: (value: boolean) => void;
  tableview?: boolean;
}

// interface for PlanningProgress
interface Step {
  id: string;
  name: string;
  planningPercentage: number;
  source: string;
}

interface PlanningProgressProps {
  steps: Step[];
  currentStepIndex: number;
  edit?: boolean;
}
// interface for  subtaskcard

interface SubtaskCardProps {
  edit?: boolean;
  onSubtaskClick?: (subtaskId: string) => void;
}
// interface for add project form

interface AddProjectFormProps {
  onClose: () => void;
}
//   interface for NavAddNewProject

interface NavAddNewProjectProps {
  setIsTableView: (isTableView: boolean) => void;
  isTableView: boolean;
}
// interface for progresscard
interface ProgressCardProps {
  _id: string;
  percentage: number;
  isapproved?: boolean;
  projectName?: string;
  clientName: string;
  area: string;
  startdate: string;
  duration: string;
  estimatedCost: string;
}
// interface for  POC
interface POCProps {
  keyname: string;
  value: string;
  subtextColor?: string;
  color?: string;
}

// interface for TableViewprojects
interface TableViewProjectsProps {
  data: ProjectDatap[];
  searchedData:  ProjectDatap[];
  scroll: (e: React.UIEvent<HTMLDivElement>) => void;
}
