/*  AUTHOR NAME : CHARVI */
import { AddCategoryIcon } from "../../../../../../../assets/icons";
import AddCategoryType from "../../../../../../../components/Reusble/Global/AddCategoryType";
import Button from "../../../../../../../components/Reusble/Global/Button";
import { TaskSubFieldsProps } from "../../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";

import styles from '../AddTask.module.css'
/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect } from "react";


export function TaskSubFields({
  title,
  buttonContents,
  onAddCategory,
  popupOpen,
  setFormData,
  formData,
  currentField,
  sampleData,
  setPopupOpen,
  handleSelectCategories,
}: TaskSubFieldsProps) {
  useEffect(() => {
    setFormData({ ...formData, [title]: buttonContents });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [buttonContents]);
  return (
    <div className={styles.subfeild_container}>
      <div className={styles.subfeild_header} style={{ position: "relative" }}>
        <p>{title}</p>
        <span onClick={onAddCategory} className={styles.subfeild_addicon}>
          <AddCategoryIcon />
        </span>
        {popupOpen && currentField === title && (
          <AddCategoryType
            data={sampleData[currentField]}
            title={`Select ${currentField}`}
            placeholder={`Select ${currentField}`}
            buttonLabel={`Add ${currentField}`}
            onSelect={handleSelectCategories}
            onClose={() => setPopupOpen(false)}
          />
        )}
      </div>
      <div className={styles.subcategories}>
        {buttonContents.map((content, index) => (
          <Button key={index} type="Normal" Content={content} />
        ))}
      </div>
    </div>
  );
}
