// interface for Message toast
export interface ToastProps {
  type: string;
  messageContent: string;
}
// interface for sidebar
export interface sidebarItemProps {
  SvgElement?: React.ElementType;
  label?: string;
  isSelected?: boolean;
  onclick: () => void;
}

export interface SubRoute {
  route: string;
  label: string;
  icon?: React.ElementType;
}
// interafces for Toasts
interface ToastMessageItemProps {
  message: string;
  heading: string;
  icon: string;
  type: string;
}

interface ToastContainerProps {
  ToastMessageItems: ToastMessageItemProps[];
}
