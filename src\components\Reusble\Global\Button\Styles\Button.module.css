/* // ---------------------------------- Author: <PERSON><PERSON> -------------------------------- */
.button_Main {
  cursor: pointer;
  width: fit-content;
}

.button_decline_button {
  border-radius: 100px;
  padding: 0.5rem;
  color: var(--warning_color);
  box-shadow: var(--primary-shadow);
  width: 9rem;
  text-align: center;
  background-color: var(--secondary_warning_background);
}

.button_decline_button2 {
  border-radius: 100px;
  padding: 0.5rem;
  color: var(--warning_color);
  box-shadow: var(--primary-shadow);
  width: 9rem;
  display: flex;
  justify-content: center;
  align-items: center;
}
.editRequest_button {
  border-radius: 100px;
  padding: 0.5rem;
  box-shadow: var(--primary-shadow);
  width: 9rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #E8B000;
  background-color:#FFF6D9;
}
.reason_button {
  border-radius: 100px;
  padding: 0.5rem;
  box-shadow: var(--primary-shadow);
  width: 9rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #A80000;
  background-color: #F6E6E6;
}

.button_navigate_button {
  border: 1px solid var(--primary_color);
  padding: 0.5rem 1rem;
  border-radius: 100px;
  color: var(--primary_color);
  text-align: center;
}

.button_navigate_button2 {
  border: 1px solid var(--text-black-28);
  padding: 0.5rem;
  border-radius: 100px;
  color: var(--text-black-28);
  text-align: center;
}

.button_normal_button {
  border-radius: 6.25rem;
  padding: 0.8rem 1.5rem;
  border: 1px solid var(--primary_color);
  box-shadow: var(--primary-shadow);
  margin-top: 0.5rem;
  white-space: nowrap;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}

.button_next_button {
  background-color: var(--primary_color);
  color: var(--text-white-100);
  border: none;
  width: 9rem;
  text-align: center;
  padding: 0.6rem;
  border-radius: 6.25rem;
    background-color: var(--primary_color);
    color: var(--text-white-100);
    border: none;
    width: 9rem;
    text-align: center;
    padding: 0.6rem;
    border-radius: 6.25rem;

}

.button_cancel_button {
  color: var(--primary_color);
  background-color: white;
  border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -16.06%,
    rgba(251, 251, 251, 0.05) 82.03%
  );
  width: 9rem;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem;
  box-shadow: var(--primary-shadow);
  border-radius: 6.25rem;
}

.button_disable_button {
  width: 9rem;
  background-color: var(--text-black-28);
  text-align: center;
  padding: 0.5rem;
  border-radius: 100px;
  color: var(--text-white-100);
  box-shadow: 0px 0px 4px 0px #91a1a1bf;
}

.button_delete_button {
  color: var(--warning_color);
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem;
  border-radius: 100px;
  background-color: var(--secondary_warning_background);
  width: 9rem;
  box-shadow: 0px 0px 4px 0px #91a1a1bf;
}

.button_Main.monthly_target_button {
  padding: 0.5rem 0.75rem;
  height: fit-content;
  width: 5rem;
  font-size: 0.75rem;

  /* background-color: blue; */
}


/* add new button for the monthly target page */
.monthly_target_approve_button {
  width: 80px;
  height: 32px;
  border-radius: 100px;
  padding-top: 8px;
  padding-right: 16px;
  padding-bottom: 8px;
  padding-left: 16px;
  display: flex;
  justify-content: center;
  gap: 8px;
  align-items: center;
  background: rgba(0, 89, 104, 1);
  color: var(--main_background);
  cursor: pointer;
  box-shadow: 0px 0px 4px 0px rgba(145, 161, 161, 0.75);
  font-family: Nunito;
  font-weight: 600;
  font-size: 12px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  vertical-align: middle;
}


/* add new button for the monthly target page */
.monthly_target_approve_button {
  width: 80px;
  height: 32px;
  border-radius: 100px;
  padding-top: 8px;
  padding-right: 16px;
  padding-bottom: 8px;
  padding-left: 16px;
  display: flex;
  justify-content: center;
  gap: 8px;
  align-items: center;
  background: rgba(0, 89, 104, 1);
  color: var(--main_background);
  cursor: pointer;
  box-shadow: 0px 0px 4px 0px rgba(145, 161, 161, 0.75);
  font-family: Nunito;
  font-weight: 600;
  font-size: 12px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  vertical-align: middle;
}

.monthly_target_decline_button {
  width: 80px;
  height: 32px;
  border-radius: 100px;
  padding-top: 8px;
  padding-right: 16px;
  padding-bottom: 8px;
  padding-left: 16px;
  display: flex;
  justify-content: center;
  gap: 8px;
  align-items: center;
  background-color: var(--main_background);
  color: var(--warning_color);
  cursor: pointer;
  box-shadow: 0px 0px 4px 0px rgba(145, 161, 161, 0.75);
  font-family: Nunito;
  font-weight: 600;
  font-size: 12px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  vertical-align: middle;
} 


.monthly_target_decline_button {
  width: 80px;
  height: 32px;
  border-radius: 100px;
  padding-top: 8px;
  padding-right: 16px;
  padding-bottom: 8px;
  padding-left: 16px;
  display: flex;
  justify-content: center;
  gap: 8px;
  align-items: center;
  background-color: var(--main_background);
  color: var(--warning_color);
  cursor: pointer;
  box-shadow: 0px 0px 4px 0px rgba(145, 161, 161, 0.75);
  font-family: Nunito;
  font-weight: 600;
  font-size: 12px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  vertical-align: middle;
} 


.mtcd_form_button {
  padding: 0.5rem 0.75rem;
}

.mtc_decline_form_cancel_button {
  padding: 0.5rem 0.75rem;
  min-width: 10rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mtc_decline_form_button_decline {
  padding: 0.5rem 0.75rem;
  min-width: 10rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--secondary_warning_background);
}