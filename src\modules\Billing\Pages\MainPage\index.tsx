import React, { useState, useEffect, useRef } from "react";
import {
  Outlet,
  useParams,
  useLocation,
  useNavigate,
} from "react-router-dom";
import styles from "./Styles/MainPage.module.css";
import Sidebar from "./Subcomponents/sidebar";
import MainMenu from "../../../../components/Common/Sidebar/SubComponents/MainMenu";
import LocationHeader from "../ProjectPlanning/Locations/Subcomponents/LocationHeader";
import MonthlyTargetHeader from "../BillingApproval/Subcomponents/MonthlyTargetHeader";
import MTcreationHeader from "../BillingApproval/Subcomponents/MTcreationHeader";
import NavigationComponent from "../../../../components/Reusble/Global/navigationComponents/commonHeaderComponent";
import PlanningTableHeader from "../ProjectPlanning/Planning/SendAprovalHeader/PlanningTableHeader";
import { useSelector } from "react-redux";
import { RootState } from "../../../../redux/store";

const routeDisplayNames: Record<string, string> = {
  summary: "Summary",
  location: "Planning",
  "monthly-target": "Monthly Target",
  "billing-approval": "Billing Approval",
  settings: "Settings",
  support: "Support",
  masters: "Masters",
  "actual-work": "Actual Work",
  inventory: "Inventory",
  "petty-contractors": "Petty Contractors",
  "site-billing": "Site Billing",
};

const RouteHeader = ({ route }: { route: string }) => {
  const location = useLocation();
  const { towerLocationId } = useParams<{
    projectId: string;
    towerLocationId: string;
  }>();

  const isPlanningTablePage =
    towerLocationId &&
    location.pathname.includes(`/location/${towerLocationId}`);

  switch (route) {
    case "location":
      if (isPlanningTablePage) {
        return <PlanningTableHeader />;
      }
      return <LocationHeader />;
    case "monthly-target":
      return <MonthlyTargetHeader />;
    case "inventory":
      return <MTcreationHeader />;
    default:
      return null;
  }
};

const BillingMainPage: React.FC = () => {
  const { projectId, towerLocationId } = useParams<{
    projectId: string;
    towerLocationId: string;
  }>();
  const [currentRoute, setCurrentRoute] = useState<string>("");
  const [projectName, setProjectName] = useState<string>("");
  const location = useLocation();
  const navigate = useNavigate();
  const mainContentRef = useRef<HTMLDivElement>(null);

  const towerLocationName = useSelector(
    (state: RootState) => state.projectLocalDb.selectedTowerLocationName
  );

  console.log("Breadcrumb towerLocationName:", towerLocationName, "towerLocationId:", towerLocationId);

  useEffect(() => {
    if (mainContentRef.current) {
      mainContentRef.current.scrollTop = 0;
    }
  }, [location.pathname]);

  const selectOpenedProject = useSelector(
    (state: RootState) => state.projectLocalDb?.openedProject
  );

  useEffect(() => {
    if (projectId) {
      if (selectOpenedProject) {
        setProjectName(selectOpenedProject);
      } else {
        setProjectName(`Project ${projectId}`);
      }
    }
  }, [projectId, selectOpenedProject]);

  useEffect(() => {
    const pathParts = location.pathname.split("/");
    const currentPathIndex =
      pathParts.findIndex((part) => part === projectId) + 1;
    if (currentPathIndex < pathParts.length) {
      setCurrentRoute(pathParts[currentPathIndex]);
    }
  }, [location, projectId]);

  const handleRouteChange = (route: string) => {
    setCurrentRoute(route);
  };

  const currentPageName = routeDisplayNames[currentRoute] || currentRoute;

  const navigationRoutes = (() => {
    const baseRoutes = [
      { route: "/billing", title: "Projects" },
      { route: `/billing/main/${projectId}`, title: projectName },
    ];

    if (towerLocationId && location.pathname.includes(`/location/${towerLocationId}`)) {
      return [
        ...baseRoutes,
        { route: `/billing/main/${projectId}/location`, title: "Planning" },
        {
          route: `/billing/main/${projectId}/location/${towerLocationId}`,
          title:  towerLocationName,
        },
      ];
    }

    if (location.pathname.includes("/location")) {
      return [
        ...baseRoutes,
        { route: `/billing/main/${projectId}/location`, title: "Planning" },
      ];
    }

    return [...baseRoutes, { route: "#", title: currentPageName }];
  })();

  const handleOutsideNavigation = (title: string, route: string) => {
    if (route !== "#") {
      navigate(route);
    }
  };

useEffect(() => {
  const handleEsc = (event: KeyboardEvent) => {
    if (event.key === "Escape") {
      if (location.pathname === `/billing/main/${projectId}/summary`) {
        navigate(`/billing`);
      } else {
        navigate(-1);
      }
    }
  };
  window.addEventListener("keydown", handleEsc);
  return () => window.removeEventListener("keydown", handleEsc);
}, [navigate, location.pathname, projectId]);

  return (
    <div className={styles.page_container}>
      <div className={styles.header_container}>
        <div className={styles.navigation_container}>
          <MainMenu />
          <NavigationComponent
            route={navigationRoutes}
            handleOutsideNavigation={handleOutsideNavigation}
          />
        </div>
        <RouteHeader route={currentRoute} />
      </div>
      <div className={styles.content_wrapper}>
        <div className={styles.sidebar_container}>
          <Sidebar onRouteChange={handleRouteChange} />
        </div>
        <div ref={mainContentRef} className={styles.main_content}>
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default BillingMainPage;
