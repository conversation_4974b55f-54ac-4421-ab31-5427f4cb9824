/*  AUTHOR NAME : CHARVI */
.taskcard_container {
  background-color: var(--main_background);
  position: relative;
  transition: border 0.3s ease;
  box-shadow: 0px 0px 3px 0px #91a1a180;
  min-height: 13.6625rem;
  height: 0;
  padding: 0.5rem;
  margin: 0.1rem 0 0 0.5rem;
  border-radius: 2rem;
  position: relative;
  overflow: hidden;
  padding: 1.5rem 1.5rem;
  border: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -16.06%,
    rgba(251, 251, 251, 0.05) 82.03%
  );
}

/* Extra styles if the user is MD */
.mdHoverEffect:hover {
  border: 1px solid #00596a;
}

/*  Button wrapper for MD */
.mdActionBtnWrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 8;
}

/* Show the button only when <PERSON> hovers over the card */
.mdHoverEffect:hover .mdActionBtnWrapper {
  opacity: 1;
  pointer-events: auto;
}

.HODactionBtnWrapper {
  position: absolute;
  display: flex;
  column-gap: 16px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 8;
}
.HODhoverEffect:hover .HODactionBtnWrapper {
  opacity: 1 ;
  pointer-events: auto;
}

.blur_content {
  width: 100%;
  height: 100%;
  transition: filter 0.1s ease;
  filter: grayscale(80%);
  opacity: 0.5;
}

.skeleton_box_light {
  display: inline-block;
  background: linear-gradient(
    90deg,
    #e0e0e0 25%,
    #f0ebeb 40%,
    #f5f5f5 50%,
    #f0ebeb 60%,
    #e0e0e0 75%
  );
  background-size: 200% 100%;
  border-radius: 7px;
  vertical-align: middle;
  background-position: 100% 0;
  animation: skeleton-wave-rtl 1.2s ease-in-out infinite;
}

@keyframes skeleton-wave-rtl {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.blur_for_now {
  width: 100%;
  height: 100%;
  filter: grayscale(80%);
  opacity: 0.5;
}


.denied {
  border: 1px solid rgba(168, 0, 0, 0.5);
}

.noBlurCard {
  filter: none !important;
  opacity: 1;
}


/* .taskcard_container:hover .blur_content {
  opacity: 0.1;
} */

.taskcard_header {
  display: Flex;
  justify-content: space-between;
  align-items: center;
  /* margin-top: -0.3rem; */
  flex-shrink: 0;
}

.taskcard_header_heading {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 270px;
}

.taskcard_header_left {
  display: flex;
  gap: 0.9rem;
  align-items: center;
  justify-content: center;
}

.taskcard_header_right_tasks {
  display: flex;
  gap: 0.938rem;
  align-items: center;
}

.taskcard_header_righticon {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: relative;
}

.cat_popup {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 0;
  background-color: var(--white-50-background);
  /* width: 7rem; */
  /* height: 9.25rem; */
  border: 1px solid;
  border-radius: 20px;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -22.43%,
    rgba(251, 251, 251, 0.05) 75.66%
  );
  backdrop-filter: blur(150px);
  box-shadow: 0px 4px 20px 0px #00000033;
  display: flex;
  flex-direction: column;
  row-gap: 0.25rem;
  padding: 1rem;
}

.cat_popup_delete {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 0;
  background-color: var(--main_background);
  width: 7rem;
  height: 6rem;
  border: 1px solid;
  border-radius: 20px;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -22.43%,
    rgba(251, 251, 251, 0.05) 75.66%
  );
  backdrop-filter: blur(150px);

  box-shadow: 0px 4px 20px 0px #00000033;
}

.cat_popup_view,
.cat_popup_edit,
.cat_popup_dlt {
  display: flex;
  /* padding: 0.6rem 0.7rem; */
  gap: 0.4rem;
  color: var(--text-black-60);
  align-items: center;
  background-color: var(--main_background);
  padding: 0.25rem;
  padding-right: 0.75rem;
  border-radius: 50px;
}

.cat_popup_viewicon {
  background: var(--primary_background);
  width: 28px;
  height: 28px;
  border-radius: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.cat_popup_editicon {
  background: #fff6d9;
  width: 28px;
  height: 28px;
  border-radius: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.cat_popup_restoreicon {
  background: #fff6d9;
  width: 28px;
  height: 28px;
  border-radius: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.cat_popup_dlticon {
  background: #f6e6e6;
  width: 28px;
  height: 28px;
  border-radius: 29px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.taskcard_header_leftpara {
  background-color: var(--primary_background);
  border-radius: 2rem;
  max-width: 8rem;
  text-align: center;
  box-shadow: 0px 4px 8px 0px #ffffff;
  border: 1px solid;
  padding: 0.2rem 0.6rem;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -16.06%,
    rgba(251, 251, 251, 0.05) 82.03%
  );
}

.taskcard_items {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  flex-grow: 1;
  gap: 0.9rem;
  margin-top: 1rem;
  justify-items: center;
}

.hiddenrequestbtn {
  display: none;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.taskcard_container:hover .hiddenrequestbtn {
  display: block;
}

@media only screen and (max-width: 1536px) {
  .taskcard_header_heading {
    max-width: 231px;
  }
}

@media only screen and (max-width: 1856px) {
  .taskcard_header_heading {
    max-width: 210px;
  }
}

@media only screen and (max-width: 1280px) {
  .taskcard_header_heading {
    max-width: 240px;
  }
}

@media only screen and (max-width: 1200px) {
  .taskcard_container {
    min-width: 25rem;
  }
}

.cat_popup_transition_div_edit {
  position: absolute;
  height: 0rem;
  width: 0rem;
  border-radius: 100px;
  z-index: 1;
  background-color: var(--extra_color);
  transition: all 0.3s ease-in-out;
}

.cat_popup_edit > h4 {
  position: relative;
  z-index: 2;
}

.cat_popup_edit.edit_hovered .cat_popup_transition_div_edit {
  animation: animateIn 0.3s ease-in-out forwards !important;
}

.cat_popup_edit.edit_notHovered .cat_popup_transition_div_edit {
  animation: animateOut 0.3s ease-in-out forwards !important;
}

@keyframes animateIn {
  0% {
    width: 0rem;
    height: 0rem;
    left: 1.5rem;
  }

  20% {
    width: 2.25rem;
    height: 1.7rem;
    left: 1.3rem;
  }

  100% {
    width: calc(100% - 2.2rem);
    height: 2rem;
    left: 1.1rem;
  }
}

@keyframes animateOut {
  0% {
    width: calc(100% - 2.2rem);
    height: 2rem;
    left: 1.1rem;
  }

  40% {
    width: 2.25rem;
    height: 2rem;
    left: 1.3rem;
  }

  100% {
    width: 0rem;
    height: 0rem;
    left: 1.5rem;
  }
}

.cat_popup_edit.edit_hovered .cat_popup_transition_div_edit {
  animation: animateIn 0.3s ease-in-out forwards !important;
}

.cat_popup_edit.edit_notHovered .cat_popup_transition_div_edit {
  animation: animateOut 0.3s ease-in-out forwards !important;
}

.cat_popup_transition_div_delete {
  position: absolute;
  height: 0rem;
  width: 0rem;
  border-radius: 100px;
  z-index: 1;
  background-color: var(--secondary_warning_background);
  transition: all 0.3s ease-in-out;
}

.cat_popup_dlt > h4 {
  position: relative;
  z-index: 2;
}

.cat_popup_dlt.dlt_hovered .cat_popup_transition_div_delete {
  animation: animateIn 0.3s ease-in-out forwards !important;
}

.cat_popup_dlt.dlt_notHovered .cat_popup_transition_div_delete {
  animation: animateOut 0.3s ease-out forwards !important;
}

.cat_popup_transition_div_view {
  position: absolute;
  height: 0rem;
  width: 0rem;
  border-radius: 100px;
  z-index: 1;
  background-color: var(--primary_background);
  transition: all 0.3s ease-in-out;
}

.cat_popup_view > h4 {
  position: relative;
  z-index: 2;
}

.cat_popup_view.view_hovered .cat_popup_transition_div_view {
  animation: animateIn 0.3s ease-in-out forwards !important;
}

.cat_popup_view.view_notHovered .cat_popup_transition_div_view {
  animation: animateOut 0.3s ease-in-out forwards !important;
}
.cat_popup_view.view_notHovered .cat_popup_transition_div_view {
  animation: animateOut 0.3s ease-in-out forwards !important;
}

.cat_popup_restoreicon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  border-radius: 29px;
  position: relative;
  z-index: 2;
  background-color: var(--secondary_background);
}