// Aayush Malviya Electron Main Process Setup
import {
  app,
  BrowserWindow,
  ipcMain,
  session,
  screen,
  // webContents,
  // protocol,
} from "electron";
import path from "path";
import unzipper from "unzipper";
import { fileURLToPath } from "url";
// import isDev from "electron-is-dev";
import PouchDB from "pouchdb";
import PouchDBFind from "pouchdb-find";
import fs from "fs";
import express from "express";
import axios from "axios";
// import os from "os";
import * as dotenv from "dotenv";
import pkg from "node-machine-id";
// import { loadESLint } from "eslint";
// import { LuChartNoAxesColumnDecreasing } from "react-icons/lu";

const { machineIdSync } = pkg;

const server = express();
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const envPath = path.resolve(__dirname, "../../.env");

dotenv.config({ path: envPath });

PouchDB.plugin(PouchDBFind);
// Example Usage
//run the express server serve the static build

const userDataPath = app.getPath("userData");
const dbPath = path.join(userDataPath, "db");
const imagesPath = path.join(dbPath, "images");
const cookiePath = path.join(dbPath, "credentials");
const imageDir = path.join(userDataPath, "db/images");

server.use("/images", express.static(imageDir));
server.use(express.static(path.resolve(__dirname, "../../dist")));

server.get("*", (req, res) => {
  res.sendFile(path.resolve(__dirname, "../../dist", "index.html"));
});

const PORT = process.env.VITE_PORT || 5174;
// const PORT = 5173;
server.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`);
});

let mainWindow;
let isSyncDone = false;
let syncContext = null;

// create Window for desktop app by aayush malviya
const createWindow = () => {
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width, height } = primaryDisplay.bounds; // Get full screen size
  mainWindow = new BrowserWindow({
    width: width,
    height: height,
    show: false,
    icon: path.join(__dirname, "../../public/SuryaconLogo.ico"),
    webPreferences: {
      nodeIntegration: false,
      disableHtmlFullscreenWindowResize: false,
      contextIsolation: true,
      preload: path.join(__dirname, "preload.js"),
      secure: true,
      // webSecurity: false,
    },
  });

  mainWindow.once("ready-to-show", () => {
    mainWindow.maximize();
    mainWindow.show();
  });

  mainWindow.on("close", (event) => {
    if (!isSyncDone) {
      event.preventDefault();

      console.log("App close requested. Sync context is:", syncContext);
      if (syncContext === "subtask") {
        mainWindow.webContents.send("trigger-subtask-sync");
      } else if (syncContext === "task") {
        mainWindow.webContents.send("trigger-task-sync");
      } else if (syncContext == "locationDetail") {
        mainWindow.webContents.send("trigger-location-sync");
      } else {
        console.warn("No sync context set. Skipping sync.");
        isSyncDone = true;
        app.quit();
      }
    }
  });
  // Agar full screen chahiye to bhi ensure karein
  // mainWindow.setFullScreen(true);

  // before the build run on url then run build index file by aayush
  const startURL = "http://localhost:5173";
  mainWindow.loadURL(startURL);

  //save the cookies on log in request get cookies and set cookies
  session.defaultSession.webRequest.onCompleted(
    {
      urls: [
        process.env.VITE_ENV_TYPE === "main"
          ? "*://www.ayrusnoc.com/api/v1/hrmaster/employee/loginEmployee"
          : `*://www.suryacon.net/${process.env.VITE_ENV_TYPE}/api/v1/hrmaster/employee/loginEmployee`,
      ],
    },
    // {
    //   urls: ["*://www.ayrusnoc.com/api/v1/hrmaster/employee/loginEmployee"],
    // },
    // {
    //   urls: [
    //     `*://www.suryacon.net/testing/api/v1/hrmaster/employee/loginEmployee`,
    //   ],
    // },
    (details) => {
      if (details.responseHeaders && details.responseHeaders["Set-Cookie"]) {
        const setCookieHeader = details.responseHeaders["Set-Cookie"];
        if (setCookieHeader) {
          fs.writeFileSync(
            cookieFilePath,
            JSON.stringify(setCookieHeader, null, 2)
          );
        }
        setStoredCookies();
      }
    }
  );

  //on refresh app set cookies to cookies tab
  mainWindow.webContents.on("did-start-loading", (e) => {
    setStoredCookies();
  });

  //delete bradcram data on close app aayush
  mainWindow.on("close", () => {
    mainWindow.webContents.executeJavaScript(`
      localStorage.removeItem('navigateArray');
    `);
  });
};

// Function to ensure `db/images` directory existsp
const ensureDBImagesDirectory = () => {
  if (!fs.existsSync(dbPath)) {
    fs.mkdirSync(dbPath, { recursive: true }); // "db" create karega agar nahi hai
  }

  if (!fs.existsSync(imagesPath)) {
    fs.mkdirSync(imagesPath, { recursive: true }); // "db/images" create karega agar nahi hai
  }

  if (!fs.existsSync(cookiePath)) {
    fs.mkdirSync(cookiePath, { recursive: true });
  }
};

ensureDBImagesDirectory();

const download = async (imagedata, cb) => {
  try {
    if (!imagedata?.url) throw new Error("Invalid image data: Missing URL");

    const extractedName = path.basename(
      Array.isArray(imagedata?.name)
        ? imagedata?.name[0]
        : imagedata?.name || "default.jpg"
    );
    const destination = path.join(userDataPath, "db", "images", extractedName);

    // Ensure the directory exists
    fs.mkdirSync(path.dirname(destination), { recursive: true });

    const response = await axios({
      url: imagedata?.url,
      method: "GET",
      responseType: "stream",
    });

    const writer = fs.createWriteStream(destination);
    response.data.pipe(writer);

    writer.on("finish", () => {
      console.log(`Download complete: ${destination}`);
      if (cb) cb(null, destination);
    });

    writer.on("error", (err) => {
      console.error("File write error:", err);
      if (cb) cb(err);
    });
  } catch (error) {
    console.error("Error downloading the file:", error);
    if (cb) cb(error);
  }
};

const downloadZipFile = async (data, cb) => {
  try {
    const unzipDestination = path.join(userDataPath, "db", "resourcess");
    const zipFilePath = path.join(userDataPath, "db", "temp.zip");

    // Clean previous files/folders
    if (fs.existsSync(unzipDestination)) {
      fs.rmSync(unzipDestination, { recursive: true, force: true });
      console.log("Old unzip folder deleted.");
    }

    if (fs.existsSync(zipFilePath)) {
      fs.unlinkSync(zipFilePath);
      console.log("Old zip file deleted.");
    }

    fs.mkdirSync(path.dirname(zipFilePath), { recursive: true });

    const response = await axios({
      url: data.url,
      method: "GET",
      responseType: "stream",
    });

    const writer = fs.createWriteStream(zipFilePath);
    response.data.pipe(writer);

    writer.on("finish", async () => {
      console.log(`Zip downloaded to: ${zipFilePath}`);

      try {
        await fs
          .createReadStream(zipFilePath)
          .pipe(unzipper.Extract({ path: unzipDestination }))
          .promise();

        fs.unlinkSync(zipFilePath);
        console.log("Zip extracted and deleted.");

        if (cb) cb(null, true);
      } catch (unzipErr) {
        console.error("Error while unzipping:", unzipErr);
        if (cb) cb(false);
      }
    });

    writer.on("error", (err) => {
      console.error("Write error:", err);
      if (cb) cb(false);
    });
  } catch (error) {
    console.error("Error downloading zip:", error);
    if (cb) cb(false);
  }
};

console.log();
PouchDB.plugin(PouchDBFind);

//setup encyption and decryption of cookies
const cookieFilePath = path.join(cookiePath, "cookies.json");
// Step 2: Read and Set Cookies
function setStoredCookies() {
  if (!fs.existsSync(cookieFilePath)) {
    console.log("No cookies file found.");
    return;
  }

  const rawData = fs.readFileSync(cookieFilePath, "utf-8");
  const cookiesArray = JSON.parse(rawData);

  cookiesArray.forEach((cookieString) => {
    const parsedCookie = parseCookieString(cookieString);

    if (parsedCookie) {
      session.defaultSession.cookies
        .set(parsedCookie)
        .then(() => console.log("Cookie set successfully:"))
        .catch((error) => console.error("Failed to set cookie:", error));
    }
  });
}

//Helper Function to Parse Cookies
function parseCookieString(cookieString) {
  const parts = cookieString.split("; ");
  const [name, value] = parts[0].split("=");

  let cookieObj = {
    url:
      process.env.VITE_ENV_TYPE === "main"
        ? "https://www.ayrusnoc.com"
        : "https://www.suryacon.net", // Must match domain
    // url: "https://www.suryacon.net",
    // url: "https://www.ayrusnoc.com",
    name,
    value,
    domain:
      process.env.VITE_ENV_TYPE === "main" ? ".ayrusnoc.com" : ".suryacon.net",
    // domain: ".suryacon.net",
    // domain: ".ayrusnoc.com",
    sameSite: "no_restriction",
  };

  parts.slice(1).forEach((part) => {
    const [key, val] = part.split("=");
    if (key.toLowerCase() === "domain") cookieObj.domain = val;
    if (key.toLowerCase() === "path") cookieObj.path = val;
    if (key.toLowerCase() === "expires") cookieObj.Expires = val;
    if (key.toLowerCase() === "secure") cookieObj.secure = false;
    if (key.toLowerCase() === "httponly") cookieObj.httpOnly = true;
  });

  return cookieObj;
}

// local db instances here by aayush
let dbInstances = {};

//delete images from local folder

if (ipcMain.listenerCount("delete-images") === 0) {
  ipcMain.handle("delete-images", async (event, files) => {
    try {
      for (const file of files) {
        const extractedName = path.basename(file);
        const destination = path.join(userDataPath, "db", "images");

        const filePath = path.join(destination, extractedName);

        fs.access(filePath, fs.constants.F_OK, (err) => {
          if (err) {
            console.error("File does not exist:", filePath);
          } else {
            console.log("File exists, deleting...");
            fs.unlink(filePath, (error) => {
              if (error) {
                console.error("Error deleting file:", error);
              } else {
                console.log("File deleted successfully.");
              }
            });
          }
        });
      }
    } catch (error) {
      console.error("Error during delete images", error);
    }
  });
}

// if (ipcMain.listenerCount("delete-images") === 0) {
//   ipcMain.handle("delete-images", async (event, imagename) => {

//     // try {
//     //     for (const file of files) {
// //       const extractedName = "\\" + path.basename(imagename);
// const destination=path.join(userDataPath, `db/images/${imagename}`);
//     //       try {
//     //           await fs.unlink(path.join(destination, file));
//     //           console.log(`Deleted: ${file}`);
//     //       } catch (error) {
//     //           console.error(`Error deleting ${file}:`, error.message);
//     //       }
//     //   }
//     // } catch (error) {
//     //   console.error("Error during delete images", error);
//     // }
//   });
// }

if (ipcMain.listenerCount("add-images") === 0) {
  ipcMain.handle("add-images", async (event, data) => {
    return new Promise((resolve, reject) => {
      download(data, (err, filePath) => {
        if (err) {
          console.error("Download failed!", err);
          reject(err);
        } else {
          console.log("File saved successfully!", filePath);
          resolve(filePath); // Return the saved file path
        }
      });
    });
  });
}

ipcMain.handle("set-sync-context", (_, context) => {
  console.log("Renderer set sync context to:", context);
  syncContext = context;
});

ipcMain.handle("sync-complete", async () => {
  console.log("Renderer reported sync complete. Quitting app.");
  isSyncDone = true;
  app.quit();
});

//get image from  file system of user
if (ipcMain.listenerCount("get-image-path") === 0) {
  ipcMain.handle("get-image-path", async (event) => {
    try {
      const imagePath = path.join(userDataPath, "db/images");
      if (!fs.existsSync(imagePath)) {
        ensureDBImagesDirectory();
      }

      return `safe-file://${imagePath}`;
    } catch (error) {
      console.error("Error reading images:", error);
      return null;
    }
  });
}
// bulk data insert updated data update from here by aayush

if (ipcMain.listenerCount("init-db") === 0) {
  ipcMain.handle("init-db", async (event, dbname) => {
    try {
      if (!dbInstances[dbname]) {
        const userDataPath = app.getPath("userData");

        const dbDirectory = path.join(userDataPath, "db");
        const dbPath = path.join(dbDirectory, dbname);

        if (!fs.existsSync(dbDirectory)) {
          fs.mkdirSync(dbDirectory, { recursive: true });
        }

        dbInstances[dbname] = new PouchDB(dbPath, { adapter: "leveldb" });
        await dbInstances[dbname].createIndex({
          index: { fields: ["updatedAt"] },
        });
      }
      await dbInstances[dbname].createIndex({
        index: { fields: ["updatedAt"] },
      });

      const dbinfo = await dbInstances[dbname].info();
      return dbinfo;
    } catch (error) {
      console.error("Error initializing database:", error);
      throw error;
    }
  });
}

//bulk insert data in local db get from the apis
// if (ipcMain.listenerCount("bulk-insert") === 0) {
//   ipcMain.handle("bulk-insert", async (event, dbname) => {
//     try {
//       const db = dbInstances[dbname.db];

//       db.createIndex({
//         index: { fields: ["updatedAt", "_id", "parentId"] },
//       })
//         .then(() => {
//           console.log("Index created on updatedAt");
//         })
//         .catch((err) => {
//           console.error("Error creating index:", err);
//         });

//       if (!db) {
//         throw new Error(
//           `Database ${dbname.db} is not initialized. Please initialize it first.`
//         );
//       }

//       const docsWithoutV = [];
//       const deleteDocs = [];

//       for (const row of dbname?.docs || []) {
//         try {
//           const existingDoc = await db.get(row._id);
//           if (existingDoc) {

//             const { __v, _rev, ...cleanDoc } = row;
//             cleanDoc._rev = existingDoc._rev;
//             cleanDoc.updatedAt = existingDoc.updatedAt;

//           const result=  await db.put(cleanDoc);

//           }
//         } catch (err) {
//           console.log(err);
//         }

//         const { __v, _rev, ...cleanDoc } = row;
//         cleanDoc.updatedAt = dbname.time;
//         docsWithoutV.push(cleanDoc);
//       }

//       //commeit this code for deletedocs here
//       // if (deleteDocs.length > 0 && !dbname.isDeleted) {
//       //   await db.bulkDocs(deleteDocs);
//       // }
//       if (docsWithoutV.length === 0) {
//         return null;
//       }

//       const response = await db.bulkDocs(docsWithoutV);

//       return response;
//     } catch (error) {
//       console.error("Error during bulk insert:", error);
//       throw error;
//     }
//   });
// }

if (ipcMain.listenerCount("bulk-insert") === 0) {
  ipcMain.handle("bulk-insert", async (event, dbname) => {
    try {
      const db = dbInstances[dbname.db];

      db.createIndex({
        index: { fields: ["updatedAt", "_id", "parentId"] },
      })
        .then(() => {
          console.log("Index created on updatedAt");
        })
        .catch((err) => {
          console.error("Error creating index:", err);
        });

      if (!db) {
        throw new Error(
          `Database ${dbname.db} is not initialized. Please initialize it first.`
        );
      }

      const docsWithoutV = [];
      const deleteDocs = [];

      for (const row of dbname?.docs || []) {
        try {
          const existingDoc = await db.get(row._id);
          if (existingDoc) {
            deleteDocs.push({
              _id: existingDoc._id,
              _rev: existingDoc._rev,
              _deleted: true,
            });
          }
        } catch (err) {
          console.log(err);
        }

        const { __v, _rev, ...cleanDoc } = row;
        cleanDoc.updatedAt = dbname.time;

        docsWithoutV.push(cleanDoc);
      }

      if (deleteDocs.length > 0 && !dbname.isDeleted) {
        await db.bulkDocs(deleteDocs);
      }
      if (docsWithoutV.length === 0) {
        return null;
      }

      const response = await db.bulkDocs(docsWithoutV);

      return response;
    } catch (error) {
      console.error("Error during bulk insert:", error);
      throw error;
    }
  });
}
// get data using by id by aayush its working on parent id right now its category id i am update to parent id its
if (ipcMain.listenerCount("getDataParentById") === 0) {
  ipcMain.handle("getDataParentById", async (event, data) => {
    try {
      const {
        dbName,
        catId,
        categoryId,
        type,
        page = 1,
        isDeletedNext,
        needSorting = false, // Include this flag
        needSearching = false,
        // Include this flag
      } = data;

      const skip = (page - 1) * 24;
      const db = dbInstances[dbName];

      if (!db) throw new Error(`Database ${dbName} is not initialized.`);

      // Always create index if sorting is needed
      if (needSorting) {
        await db.createIndex({
          index: {
            fields: [
              "lowercase_name",
              categoryId,
              "isDeleted",
              "updatedAt",
              "type",
            ],
          },
        });
      }
     
      // Build base selector

      const selector = {
        [categoryId]: catId,
      };

      if (needSorting) {
        selector.lowercase_name = { $gte: null };
        if (!type) {
          selector.isDeleted = isDeletedNext;
        }
      }

      // Build find options
      const findOptions = {
        selector,
      };

      if (needSorting) {
        findOptions.sort = [{ lowercase_name: "asc" }];
        if (!type && !needSearching) {
          findOptions.limit = 24;
          findOptions.skip = skip;
        }
        if (!type && needSearching) {
          findOptions.limit = 1000; // For searching, we might want more results
        }
      }

      // Perform query
      const result = await db.find(findOptions);

      console.log("result of the getDataParentById", result);

      if (type) {
        const formattedData = result.docs[0]?.data.filter(
          (item) => item.type === type && item.isDeleted === isDeletedNext
        );

        console.log(formattedData, "formattedData");
        return {
          data: formattedData,
          categoryId: result.docs[0]?.categoryId,
          consumableCount: result.docs[0]?.consumableCount,
          returnableCount: result.docs[0]?.returnableCount,
        };
      }

      return result.docs;
    } catch (error) {
      console.error("Error fetching tasks by category:", error);
      throw error;
    }
  });
}

// get subtaskdetail from localdb here start by rattandeep singh
if (ipcMain.listenerCount("getsubtaskDetailById") === 0) {
  ipcMain.handle("getsubtaskDetailById", async (event, data) => {
    try {
      const { dbName, id, towerId, taskId } = data;

      const db = dbInstances[dbName];

      if (!db) throw new Error(`Database ${dbName} is not initialized.`); // Ensure index is created
      await db.createIndex({
        index: {
          fields: ["_id", "subtaskId._id", "towerId", "taskId", "TaskmasterId"],
        },
      });

      // Step 2: Query with towerId and taskId first

      // If no result is found, fallback to querying with id

      let result = await db.find({
        selector: {
          $or: [{ _id: id }, { "subtaskId._id": id }],
        },
      });

      console.log("Result frtom tasjkid", result);
      return result.docs;
    } catch (error) {
      console.error("Error fetching subtasks", error);
      throw error;
    }
  });
}
// delete subtask from localdb here start by rattandeep singh
if (ipcMain.listenerCount("deleteDocById") === 0) {
  ipcMain.handle("deleteDocById", async (event, data) => {
    try {
      const { dbName, _id } = data;
      const db = dbInstances[dbName];

      if (!db) throw new Error(`Database ${dbName} is not initialized.`);

      // Ensure index exists (move to init phase ideally)
      await db.createIndex({
        index: { fields: ["_id"] },
      });

      // Find matching documents
      const result = await db.find({ _id: id });

      // Delete found documents
      const deleted = await Promise.all(
        result.docs.map((doc) => db.remove(doc))
      );

      console.log("Deleted documents:", deleted);
      return deleted;
    } catch (error) {
      console.error("Error deleting subtasks", error);
      throw error;
    }
  });
}
// delete tower by tower route by tower id and name

if (ipcMain.listenerCount("deleteTowerRouteByTowerIdAndNAme") === 0) {
  ipcMain.handle("deleteTowerRouteByTowerIdAndNAme", async (event, data) => {
    try {
      const { dbName, id, name } = data;
      const db = dbInstances[dbName];

      if (!db) throw new Error(`Database ${dbName} is not initialized.`);

      // Ensure index exists (move to init phase ideally)
      await db.createIndex({
        index: { fields: ["Tower_id", "name"] },
      });

      // Find matching documents
      const result = await db.find({
        selector: {
          $and: [{ Tower_id: id }, { name: name }],
        },
      });

      // Delete found documents
      const deleted = await Promise.all(
        result.docs.map((doc) => db.remove(doc))
      );

      console.log("Deleted documents:", deleted);
      return deleted;
    } catch (error) {
      console.error("Error deleting subtasks", error);
      throw error;
    }
  });
}

if (ipcMain.listenerCount("getDataById") === 0) {
  ipcMain.handle("getDataById", async (event, data) => {
    try {
      const { dbName, id } = data;
      const db = dbInstances[dbName];

      if (!db) throw new Error(`Database ${dbName} is not initialized.`);

      // Perform query
      const result = await db.find({
        selector: {
          _id: id,
        },
      });

      // console.log("DB Data:", await db.allDocs({ include_docs: true }));
      // console.log("Querying DB with ID:", id);
      // console.log("Result:", result.docs);

      return result.docs;
    } catch (error) {
      console.error("Error fetching tasks by category:", error);
      throw error;
    }
  });
}
if (ipcMain.listenerCount("deleteDocumentByid") === 0) {
  ipcMain.handle("deleteDocumentByid", async (event, data) => {
    try {
      const { dbName, idname, _id } = data;
      const db = dbInstances[dbName];
      if (!db) throw new Error(`Database ${dbName} is not initialized.`);

      // Ensure the plugin for Mango queries is available
      // You need to install `pouchdb-find` and set it up in your db initialization if not already

      // Find documents matching the custom key
      const result = await db.find({
        selector: {
          [idname]: _id,
        },
      });

      if (!result.docs.length) {
        throw new Error(`No document found with ${idname} = ${_id}`);
      }

      // Delete all matching documents
      const deleteResults = await Promise.all(
        result.docs.map((doc) => db.remove(doc))
      );

      return deleteResults;
    } catch (error) {
      console.error("Failed during delete document:", error);
      throw error;
    }
  });
}

// bulk get data from local db any table using dbName by aayush
if (ipcMain.listenerCount("bulk-get") === 0) {
  ipcMain.handle(
    "bulk-get",
    async (event, { dbName, deleted, page = 1, pageSize = 24, type }) => {
      try {
        if (!dbInstances[dbName]) {
          throw new Error(
            `Database ${dbName} is not initialized. Please initialize it first.`
          );
        }

        const db = dbInstances[dbName];

        await db.createIndex({
          index: { fields: ["lowercase_name"] },
        });

        const skip = (page - 1) * pageSize; // Calculate skip based on page

        const response = await db.find({
          selector: {
            lowercase_name: { $exists: true },
            isDeleted: deleted,
            ...(type ? { project_status: type } : {}),
          },
          sort: [{ lowercase_name: "asc" }],
          limit: pageSize,
          skip: skip,
        });
        return response;
      } catch (error) {
        console.error("Error during bulk get:", error);
        throw error;
      }
    }
  );
}
// bulk get all the data without pagination from localdb by rdev
if (ipcMain.listenerCount("all-bulk-get") === 0) {
  ipcMain.handle("all-bulk-get", async (event, { dbName }) => {
    try {
      if (!dbInstances[dbName]) {
        throw new Error(
          `Database ${dbName} is not initialized. Please initialize it first.`
        );
      }
      const db = dbInstances[dbName];
      const response = await db.find({
        selector: {
          updatedAt: { $exists: true },
        },
        sort: [{ updatedAt: "desc" }],
        limit: 1000,
      });
      return response;
    } catch (error) {
      console.error("Error during bulk get:", error);
      throw error;
    }
  });
}

// get single document by document id by aayush
if (ipcMain.listenerCount("get-document") === 0) {
  ipcMain.handle("get-document", async (event, { db, id }) => {
    try {
      const dbInstance = dbInstances[db];
      if (!dbInstance) {
        throw new Error(`Database ${db} is not initialized.`);
      }
      const doc = await dbInstance.get(id);
      return doc;
    } catch (error) {
      if (error.status === 404) {
        return null;
      }
    }
  });
}

if (ipcMain.listenerCount("search-data") === 0) {
  ipcMain.handle(
    "search-data",
    async (event, { path, key, value, deleted }) => {
      try {
        if (!path) throw new Error("Invalid path");

        const dbInstance = dbInstances[path];
        console.log(dbInstance, "check db instances bro");
        const result = await dbInstance.find({
          selector: {
            [key]: { $regex: new RegExp(value, "i") },
            ...(typeof deleted !== "undefined" ? { isDeleted: deleted } : {}),
          },
          limit: 1000,
        });

        return result.docs;
      } catch (err) {
        console.error("DB Search Error:", err);
        return { error: err.message };
      }
    }
  );
}

// put document and  i think modifie it in future by aayush
if (ipcMain.listenerCount("put-document") === 0) {
  ipcMain.handle("put-document", async (event, { db, doc }) => {
    try {
      const dbInstance = dbInstances[db];
      if (!dbInstance) {
        return "db not found";
      }

      const response = await dbInstance.put(doc);

      return response;
    } catch (err) {
      return err;
    }
  });
}

//create window when app is run
app.whenReady().then(() => {
  createWindow();

  const win = BrowserWindow.getAllWindows()[0];
});

//killl process when window closed
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") app.quit();
});

//get the cookies from cookies json file
const getCookies = () => {
  if (fs.existsSync(cookieFilePath)) {
    const rawData = fs.readFileSync(cookieFilePath, "utf-8");
    const parsedData = JSON.parse(rawData);

    return parsedData;
  }

  return null;
};

if (ipcMain.listenerCount("get-cookies") === 0) {
  // get cookies in react js
  ipcMain.handle("get-cookies", async () => {
    return getCookies();
  });
}
function clearAllCookies() {
  session.defaultSession
    .clearStorageData({
      storages: ["cookies"],
    })
    .then(() => console.log("All cookies cleared successfully!"))
    .catch((error) => console.error("Failed to clear cookies:", error));
}
//delete cookies function on un auth request
function replaceJsonFileData(newData) {
  try {
    const dataString = JSON.stringify(newData, null, 2);
    fs.writeFileSync(cookieFilePath, dataString, "utf8");

    clearAllCookies();
  } catch (error) {
    console.error("Error writing new data to JSON file:", error);
  }
}

// delete cookies connect to render process
ipcMain.handle("delete-cookies", async () => {
  replaceJsonFileData([]);
});

//change mac address to machine id
function getMacAddress() {
  const machineID = machineIdSync();

  if (machineID) {
    return machineID;
  }

  return null;
}
if (ipcMain.listenerCount("get-mac-address") === 0) {
  ipcMain.handle("get-mac-address", async () => {
    const macAddress = getMacAddress();

    return macAddress;
  });
}

if (ipcMain.listenerCount("get-zip-file") === 0) {
  ipcMain.handle("get-zip-file", async (event, data) => {
    return new Promise((resolve, reject) => {
      downloadZipFile(data, (err, filePath) => {
        if (err) {
          console.error("Download failed!", err);
          reject(false);
        } else {
          resolve(true);
        }
      });
    });
  });
}

//-----------------------------------------------------auto save code here for forms by aaysuh malviya -------------------------------------------------------------
// add material form functions by rattandeep singh start here
// required things categories
if (ipcMain.listenerCount("getCategoryData") === 0) {
  ipcMain.handle("getCategoryData", async (event, data) => {
    try {
      const { dbName } = data;
      const db = dbInstances[dbName];

      if (!db) throw new Error(`Database ${dbName} is not initialized.`);

      // OPTIONAL: Create index if needed (safe to call multiple times)
      await db.createIndex({
        index: { fields: ["_id"] }, // or any other field you might query in future
      });

      // Query all documents, but return only specific fields
      const result = await db.find({
        selector: {
          isDeleted: false,
        }, // Empty selector = match all docs
        fields: ["_id", "name"], // Replace with your actual field names,
        limit: 1000,
      });

      return result.docs;
    } catch (error) {
      console.error("Error fetching all docs using find:", error);
      throw error;
    }
  });
}
// requried things designation
if (ipcMain.listenerCount("getDesignationsData") === 0) {
  ipcMain.handle("getDesignationsData", async (event, data) => {
    try {
      const { dbName, id, idName, isTool = false } = data;
      const db = dbInstances[dbName];

      if (!db) throw new Error(`Database ${dbName} is not initialized.`);

      // OPTIONAL: Create index if needed (safe to call multiple times)
      await db.createIndex({
        index: { fields: [idName] }, // or any other field you might query in future
      });

      // Query all documents, but return only specific fields
      const result = await db.find({
        selector: {
          [idName]: id,
          ...(!isTool ? { isDeleted: false } : {}),
        }, // Empty selector = match all docs
        ...(!isTool ? { fields: ["_id", "name", idName, "unit"] } : {}),
        limit: 1000,
      });

      if (isTool) {
        const filteredData = result?.docs?.[0]?.data
          ?.filter((tools) => !tools?.isDeleted)
          ?.map((filterTools) => ({
            _id: filterTools?._id,
            categoryId: result?.docs?.[0]?.categoryId,
            name: filterTools?.name,
          }));

        return filteredData;
      }

      return result.docs;
    } catch (error) {
      console.error("Error fetching all docs using find:", error);
      throw error;
    }
  });
}
// for searching
if (ipcMain.listenerCount("searchRequierdThingsData") === 0) {
  ipcMain.handle("searchRequierdThingsData", async (event, data) => {
    try {
      const {
        searchTerm,
        dbName1,
        dbName2,
        categorykey,
        isTool,
        initialSelected,
      } = data;
      const db1 = dbInstances[dbName1];
      if (!db1) throw new Error(`Database ${dbName1} is not initialized.`);

      const db2 = dbInstances[dbName2];
      if (!db2) throw new Error(`Database ${dbName2} is not initialized.`);

      // Ensure indexes
      await Promise.all([
        db1.createIndex({ index: { fields: ["name"] } }),
        db2.createIndex({ index: { fields: ["name"] } }),
      ]);

      // Step 1: Find matching categories from db1 directly
      const categoryRes = await db1.find({
        selector: {
          name: { $regex: new RegExp(searchTerm, "i") },
          ...(!isTool ? { isDeleted: false } : {}),
        },
        fields: ["_id", "name"],
      });
      const directCategoryDocs = categoryRes.docs;

      // Step 2: Find matching items from db2
      let itemRes;
      if (!isTool) {
        itemRes = await db2.find({
          selector: {
            name: { $regex: new RegExp(searchTerm, "i") },
            isDeleted: false,
          },
        });
      } else {
        const allitems = await db2.find({
          selector: {},
        });
        const extractedItems = allitems?.docs?.flatMap((el) =>
          (el.data || [])
            ?.filter((item) => !item.isDeleted)
            ?.map((item) => ({
              ...item,
              categoryId: el.categoryId,
            }))
        );
        console.log(extractedItems, "these are extracted items");
        itemRes = extractedItems?.filter((el) =>
          el?.name?.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }

      console.log(itemRes, categorykey, "these are itmresfortoo");
      // Step 3: Extract category IDs from matched items
      let referencedCategoryIds;
      if (!isTool) {
        referencedCategoryIds = [
          ...new Set(itemRes?.docs?.map((item) => item[categorykey])),
        ];
      } else {
        if (itemRes && Array.isArray(itemRes)) {
          referencedCategoryIds = itemRes.map((item) => item[categorykey]);
        } else {
          console.error("itemRes is undefined or not an array:", itemRes);
          referencedCategoryIds = [];
        }
      }
      console.log(referencedCategoryIds, "tehse are refecnred categoried ids");
      // Step 4: Load the referenced categories from db1
      const referencedCategories = await db1.find({
        selector: {
          _id: { $in: referencedCategoryIds },
        },
        fields: ["_id", "name"], // Specify the fields you want to retrieve
      });
      console.log(referencedCategories, "tehse are refecrt5ed cate");
      // const referencedCategoryDocs = referencedCategories.rows
      //   .map((row) => row.doc)
      //   .filter(Boolean);

      // Step 5: Merge and deduplicate categories (based on _id)
      const categoryMap = new Map();
      for (const cat of [
        ...directCategoryDocs,
        ...referencedCategories?.docs,
      ]) {
        categoryMap.set(cat._id, cat);
      }

      return {
        categories: Array.from(categoryMap.values()),
      };
    } catch (error) {
      console.error("Error While Searching", error);
      throw error;
    }
  });
}

// add material form functions by rattandeep singh end here
// get allsubtasks using task id by rattandeep singh
if (ipcMain.listenerCount("getSubtasksByTaskId") === 0) {
  ipcMain.handle("getSubtasksByTaskId", async (event, data) => {
    try {
      console.log(data, "thisidatasdfasdf");
      const { dbName, taskId } = data;

      const db = dbInstances[dbName];

      if (!db) throw new Error(`Database ${dbName} is not initialized.`); // Ensure index is created

      await db.createIndex({
        index: {
          fields: ["TaskmasterId._id"],
        },
      });

      // Perform query
      const result = await db.find({
        selector: {
          "TaskmasterId._id": taskId,
          isDeleted: false, // Assuming you want only non-deleted subtasks
        },
      });

      console.log("result of the getSubtasksByTaskId", result);
      return result.docs;
    } catch (error) {
      console.error("Error fetching tasks by category:", error);
      throw error;
    }
  });
}
