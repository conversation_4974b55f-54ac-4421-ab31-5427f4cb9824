import React, { useEffect, useRef, useState } from "react";
import styles from "../Styles/BillingApproval.module.css";
import { DeleteIcon } from "../../../../../assets/icons";
import { MTTargetCardProps } from "../../../../../interfaces/Modules/Billing/BillingApproval/BillingApproval";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { setSelectedMaterialId } from "../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/projectPlanningSlice";
import { setCurrentCardId } from "../../../../../redux/features/Modules/Billing/BillingApproval/Slices/BillingApprovalSlice";
import { RootState } from "../../../../../redux/store";
import { openPopup } from "../../../../../redux/features/Modules/Reusble/popupSlice";
import { setToast } from "../../../../../redux/features/Modules/Reusble/ToastSlice";
import { useTowerCrud } from "../../../../../redux/api/Modules/Billing/DemoMonthlyTargetApi/useTowerCrud";

const MonthlyTargetCard: React.FC<MTTargetCardProps> = ({
  toggleEditFunc,
  onInputchange,
  edit,
  brand,
  _id,
  property,
  isAllowed = false,
  type,
  onDelete,
  quantity,
  title,
  towerName,
  floorNumber,
  taskIndex,
  isTower = true,
  onQuantityUpdated,
  showDelete,
  unit,
}) => {
  const [inputValue, setInputValue] = useState(
    quantity === 0 ? "" : quantity?.toString() || ""
  );
  const [originalValue, setOriginalValue] = useState(
    quantity === 0 ? "" : quantity?.toString() || ""
  );
  const [isEditValue, setIsEditValue] = useState(false);
  const [isChanged, setIsChanged] = useState(false);
  const [shake, setShake] = useState(false);
  const [stopshake, setStopShake] = useState(false);

  const inputRef = useRef<HTMLInputElement>(null);
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);
  const dispatch = useAppDispatch();

  const selectedMaterialId = useAppSelector(
    (state) => state.projectPlanning.selectedMaterialId
  );
  const currentEditId = useAppSelector(
    (state: RootState) => state.monthlyTarget.CurrentEditcardId
  );

  const { updateCardQuantity, deleteTask, loading } = useTowerCrud();

  const triggerInputChange = (val: string) => {
    if (onInputchange) {
      onInputchange({ target: { value: val } } as any);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = e.target.value;

    if (val === "0") {
      setInputValue("");
      return;
    }

    const regex = /^\d{0,10}$/;
    if (!regex.test(val)) return;

    setInputValue(val);
    setIsChanged(true);

    if (debounceTimeout.current) clearTimeout(debounceTimeout.current);

    debounceTimeout.current = setTimeout(() => {
      triggerInputChange(val);
    }, 500);
  };

  const handleBlur = async () => {
    if (debounceTimeout.current) clearTimeout(debounceTimeout.current);

    if (
      inputValue !== originalValue &&
      towerName &&
      floorNumber !== undefined &&
      taskIndex !== undefined
    ) {
      try {
        await updateCardQuantity(
          towerName,
          floorNumber,
          taskIndex,
          parseInt(inputValue) || 0,
          isTower
        );

        dispatch(
          setToast({
            isOpen: true,
            messageContent: "Quantity updated successfully!",
            type: "success",
          })
        );

        setOriginalValue(inputValue);

        if (onQuantityUpdated) {
          onQuantityUpdated(parseInt(inputValue) || 0);
        }
      } catch (error) {
        console.error("Error updating quantity:", error);
        dispatch(
          setToast({
            isOpen: true,
            messageContent: "Failed to update quantity",
            type: "danger",
          })
        );
        setInputValue(originalValue);
      }
    }

    setIsEditValue(false);
    triggerInputChange(inputValue); // Final sync
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();

    if (towerName && floorNumber !== undefined && taskIndex !== undefined) {
      dispatch(openPopup("DeleteCardConfirmation"));
      localStorage.setItem(
        "cardToDelete",
        JSON.stringify({ towerName, floorNumber, taskIndex, isTower })
      );
    } else if (onDelete) {
      onDelete();
    }
  };

  useEffect(() => {
    if (edit && Number(quantity) <= 0) {
      if (!shake && !stopshake) {
        setShake(true);
        const timeout = setTimeout(() => {
          setShake(false);
          setStopShake(true);
          toggleEditFunc?.();
        }, 10000);
        return () => clearTimeout(timeout);
      } else {
        setShake(true);
        setStopShake(false);
      }
    } else {
      setShake(false);
    }
  }, [edit, quantity]);

  useEffect(() => {
    if (isEditValue) {
      inputRef.current?.focus();
    }
  }, [isChanged]);

  useEffect(() => {
    if (!isEditValue) {
      if (quantity === undefined || quantity === null || quantity === 0) {
        setInputValue("");
      } else {
        setInputValue(quantity?.toString() || "");
      }
    }
  }, [quantity, isEditValue]);

  return (
    <div
      className={`${styles.mt_target_card} ${
        shake && currentEditId !== _id ? styles.shake : ""
      } ${loading ? styles.loading : ""}`}
    >
      <div
        onClick={(e) => {
          e.stopPropagation();
          setIsEditValue(true);
          setIsChanged(true);
          if (edit) {
            dispatch(setCurrentCardId(_id));
          }
        }}
        className={`${styles.mt_target_card_top}`}
      >
        <div
          className={`${styles.mt_target_card_inner_top} ${
            selectedMaterialId === _id && isAllowed ? styles.selected : ""
          }`}
          onClick={() => dispatch(setSelectedMaterialId(_id))}
        >
          {edit && showDelete && (
            <div
              onClick={handleDelete}
              className={`${styles.mt_target_card_delete}`}
            >
              <DeleteIcon />
            </div>
          )}
          <div className={`${styles.mt_target_card_inner_left}`}>
            <p className={`${styles.small_text_p}`}>{unit || "No's"}</p>
          </div>

          <div className={`${styles.mt_target_card_inner_right}`}>
            {title && (
              <div className={`${styles.mt_target_card_title}`}>
                <p>{title}</p>
              </div>
            )}
            <p
              style={{
                position: inputValue?.length === 0 ? "relative" : "static",
                top:
                  currentEditId == _id || inputValue?.length > 0
                    ? "0px"
                    : "10px",
              }}
              className={`${styles.small_text_p}`}
            >
              {type}
            </p>

            <input
              ref={inputRef}
              onChange={handleChange}
              onBlur={handleBlur}
              readOnly={!(edit && isEditValue)}
              value={inputValue}
              className={`${styles.mt_card_input}`}
              type="text"
              style={{ width: `${Math.max(50, inputValue?.length * 10)}px` }}
              maxLength={5}
            />
          </div>
        </div>
      </div>

      {/* Property + Brand Display */}
      {brand && property && (
        <>
          <div
            className={`${styles.mt_target_card_inner} ${styles.mt_target_card_bottom_11}`}
          >
            <p className="small_text_p">{property}</p>
          </div>
          <div
            className={`${styles.mt_target_card_inner} ${styles.mt_target_card_bottom_21}`}
          >
            <p className="small_text_p">{brand}</p>
          </div>
        </>
      )}
      {!brand && property && (
        <div
          className={`${styles.mt_target_card_inner} ${styles.mt_target_card_bottom_12}`}
        >
          <p className="small_text_p">{property}</p>
        </div>
      )}
      {brand && !property && (
        <div
          className={`${styles.mt_target_card_inner} ${styles.mt_target_card_bottom_22}`}
        >
          <p className="small_text_p">{brand}</p>
        </div>
      )}
    </div>
  );
};

export default MonthlyTargetCard;
