import React, { useEffect, useRef, useState } from "react";

import styles from "./Styles/MainMenu.module.css";

import Menuitem from "./MenuItem";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../redux/store";
import { mainMenuItems } from "../../../../utills/Routes/menuItems";
import { DoubleArrow } from "../../../../assets/icons";
import {
  currentActiveSubRouteIndex,
  setLabel,
  setSubRoutes,
} from "../../../../redux/features/Modules/Reusble/sidebarSlice";
import {
  emptyNavigate,
  removeNavigate,
  resetTaskName,
  spliceNavigate,
} from "../../../../redux/features/Modules/Reusble/navigationSlice";

const MainMenu: React.FC = () => {
  const [isMainMenuOpen, setMainMenuOpen] = useState<boolean>(false);
  const navigate = useNavigate();
  const currentActiveRotueIndex = useSelector(
    (state: RootState) => state.currentpaths.currentActiveRotueIndex
  ) as number;
  const label = useSelector((state: RootState) => state.currentpaths.label);

  const dispatch = useDispatch();
  const popupRef = useRef<HTMLDivElement | null>(null);
  const handleClickOutside = (event: MouseEvent) => {
    console.log("outside click detected");
    if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
      setMainMenuOpen(false);
      setTimeout(() => {
        // setIndex("0");
      }, 100);
    }
  };
  useEffect(() => {
    // Only add the listener when popup is open
    if (isMainMenuOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    // Clean up the event listener when the component unmounts or when isEyeOpen changes
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMainMenuOpen]);

  return (
    <>
      <div ref={popupRef} className={`${styles.main_menu_container}`}>
        <div
          className={`${styles.mainmenu_outer_container} ${
            isMainMenuOpen && styles.mainmenu_open
          }`}
        >
          <div
            className={styles.main_menu_upper_container}
            onClick={() => {
              setMainMenuOpen((prev) => !prev);
            }}
          >
            <h4 className="h4_18px">
              {isMainMenuOpen ? `Main Menu` : `${label}`}
            </h4>
            <div
              style={{
                transform: isMainMenuOpen ? "rotate(180deg)" : "none",
                display: "flex",
                cursor: "pointer",
              }}
            >
              <DoubleArrow color="var(--primary_color)" />
            </div>
          </div>
          <div
            className={styles.main_menu_lower_list}
            tabIndex={isMainMenuOpen ? 0 : -1}
            style={{ display: isMainMenuOpen ? "flex" : "none" }}
          >
            {mainMenuItems?.map((item, index) => (
              <Menuitem
                itemKey={JSON.stringify(`${item?.slug} + ${index}`)}
                // SvgElement={item?.icon}
                isSelected={
                  mainMenuItems.indexOf(item) === currentActiveRotueIndex
                }
                label={item?.label}
                onClick={() => {
                  console.log('item is here',item)
                  navigate(`/${item?.slug}`);
                  if (label !== item?.label) {
                    dispatch(emptyNavigate());
                  }
                  if(label === "Department") dispatch(spliceNavigate());
                  dispatch(setLabel(item?.label));
                  dispatch(resetTaskName({ title: item?.label }));
                  dispatch(setSubRoutes(item?.subRoute));
                  dispatch(currentActiveSubRouteIndex(0));
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default MainMenu;
