import { useEffect, useMemo, useRef, useState } from "react";
import {
  CloseIcon,
  AddCategoryIcon,
  DropDownArrowUpIcon,
  DropDownCategoryIcon,
  ReverseArrow,
  Attachment,
  DeleteIcon,
  RedCross,
  ImageIcon,
  VideoIcon,
  AudioIcon,
  CopyIcon,
  PasteIcon,
  Cross,
} from "../../../../../../assets/icons";
import Button from "../../../../../../components/Reusble/Global/Button";
import FloatingLabelInput from "../../../../../../components/Reusble/Global/FloatingLabel";
import styles from "../Styles/Materials.module.css";
import UnitPopup from "../../../../../../components/Reusble/Global/UnitPopup";
import DynamicGradeInput from "../../Subcomponents/DynamicGradeInput";
import { useAppDispatch } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { RootState, store } from "../../../../../../redux/store";
import { useSelector } from "react-redux";
import {
  resetDeletedGradeData,
  resetDeletedToolData,
  resetDeleteFormData,
  resetDeleteUnitData,
  setDeletedFormData,
  setDeletedGradeData,
  setDeletedToolData,
  setDeletedUnitData,
  setFormMaterialsData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import { useToast } from "../../../../../../hooks/ToastHook";
import {
  compressImage,
  // compressImage,
  isValidValue,
  slicedData,
} from "../../../../../../functions/functions";
import { useGetAllBrandsQuery } from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import {
  useAddMaterialDesignationMutation,
  useUpdateMaterialDesignationMutation,
} from "../../../../../../redux/api/Modules/Billing/Billingapi";
import { useParams } from "react-router-dom";
import Datafield from "../../../../../../components/Reusble/Billing/Masters/Datafield";
import InputNumber from "../../../../../../components/Reusble/Global/InputNumber";
import MaterialSummary from "./MaterialSummary";
import MaterialDiscard from "./MaterialDiscard";
import { SiCompilerexplorer } from "react-icons/si";
import { setIsLocalChange } from "../../../../../../redux/features/Modules/Reusble/backupSlice";

const AddMaterialsForm: React.FC<{
  isClosing?: boolean;
  handleClose: (targetForm: string) => void;
  setIsClosing?: React.Dispatch<React.SetStateAction<boolean>>;
}> = ({ isClosing = false, handleClose, setIsClosing }) => {
  const [isUnitPopUpVisible, setUnitPopUpVisible] = useState<{
    [key: number]: { [key: number]: boolean };
  } | null>({});
  const [isUnitPopUpVisible2, setUnitPopUpVisible2] = useState<{
    [key: number]: { [key: number]: boolean };
  } | null>({});
  const [fileLoader, setFileLoader] = useState(false);
  // const [selectedUnitId, setSelectedUnitId] = useState<number | null>(null);
  const [errors, setErrors] = useState<{
    Unit: boolean;
    Name: boolean;
    Brand: string[];
    Grade: string[];
  }>({
    Unit: false,
    Name: false,
    Brand: [],
    Grade: [],
  });
  const [emptyError, setEmptyError] = useState<{
    Photo: boolean;
    Name: boolean;
    Brand: boolean;
    Grade: boolean;
    Fuels: boolean;
  }>({
    Photo: false,
    Name: false,
    Brand: false,
    Grade: false,
    Fuels: false,
  });

  const contentRef = useRef<HTMLDivElement>(null);
  const formData = useSelector(
    (state: RootState) => state.masterForm.formMaterialsData
  );
  const initialFormData = useSelector(
    (state: RootState) => state.masterForm.initialFormMaterialsData
  );
  const formMode = useSelector((state: RootState) => state.masterForm.formMode);
  //state which keeps the track of deleted form data
  const deletedFormData = useSelector(
    (state: RootState) => state.masterForm.deleteFormData
  );
  //state which keeps the track of deleted form data
  const deletedGradeData = useSelector(
    (state: RootState) => state.masterForm.deleteGradeData
  );
  const deletedUnitData = useSelector(
    (state: RootState) => state.masterForm.deleteUnitData
  );
  const deletedUnitArray = useSelector(
    (state: RootState) => state.masterForm.deleteToolData
  );
  const [showSummary, setShowSummary] = useState(false);
  const [wasTrue, setWasTrue] = useState(false);
  const [file, setFile] = useState<{
    name: string;
    type: string;
    file: File;
  } | null>(formData?.Photo || null);

  //this is for managing suggestion popup
  const [isOpen, setIsOpen] = useState<{ [key: number]: boolean } | null>({});

  //this is for suggestion in brand name
  const [searchKey, setSearchKey] = useState<{ [key: number]: string } | null>(
    {}
  );

  const [discard, setDiscard] = useState(false);
  // const [fileUrl, setFileUrl] = useState("");
  const dispatch = useAppDispatch();
  const showToast = useToast();

  //api to fetch all the brands
  const { data: allBrands, refetch } = useGetAllBrandsQuery({});

  //api to add material
  const [addMaterialDesignation] = useAddMaterialDesignationMutation();

  //update api
  const [updateMaterialDesignation] = useUpdateMaterialDesignationMutation();

  //params
  const { materialsCategoryId } = useParams();

  const [copyGrades, setCopyGrades] = useState<string[]>([]);
  const currentFileState = useRef(fileLoader);
  //copy function
  const copyGradesfun = (grades: string[]) => {
    setCopyGrades(grades);
    showToast({
      messageContent: "Grades Copied!",
      type: "success",
    });
  };
  console.log("copyGrades", copyGrades);
  //paste function
  const pasteGradesfun = (id: string) => {
    console.log("copyGrades", copyGrades);
    dispatch(
      setFormMaterialsData({
        ...formData,
        Brands: formData?.Brands?.map((brand, _) => {
          if (brand?._id === id) {
            return {
              ...brand,
              Grade: copyGrades,
            };
          }
          return brand;
        }),
      })
    );

    //empty the copy grades after pasting
    setCopyGrades([]);
  };

  const toggleUnitPopUp = (index: number, indx: number) => {
    setUnitPopUpVisible((prev) => ({
      [index]: { [indx]: !prev?.[index]?.[indx] },
    }));
  };

  //first index for brand and second index for conversion rate
  const handleUnitSelect = (
    index: number,
    indx: number,
    unit: { id: number; label: string },
    type: string
  ) => {
    const updatedData = {
      ...formData,
      Brands: formData.Brands.map((brand, bIdx) =>
        bIdx === index
          ? {
              ...brand,
              ConversionRates: brand.ConversionRates?.map((rate, rIdx) =>
                rIdx === indx
                  ? {
                      ...rate,
                      ...(type === "fromUnit"
                        ? { fromUnit: unit.label }
                        : { toUnit: unit.label }),
                    }
                  : rate
              ),
            }
          : brand
      ),
    };

    dispatch(setFormMaterialsData(updatedData));

    if (type === "fromUnit") {
      setUnitPopUpVisible(() => ({
        [index]: { [indx]: false },
      }));
      return;
    }
    setUnitPopUpVisible2(() => ({ [index]: { [indx]: false } }));
  };

  const addBrandSection = () => {
    // console.log("brand section", formData.Brands);
    if (formData?.Brands?.[formData?.Brands?.length - 1]?.brand?.name === "") {
      showToast({
        messageContent: "Please Enter Brand Name!",
        type: "warning",
      });
      return;
    }

    dispatch(
      setFormMaterialsData({
        ...formData,
        Brands: Array.isArray(formData?.Brands)
          ? [
              ...formData.Brands,
              {
                _id: Date.now().toString(),
                brand: {
                  name: "",
                },
                Grade: [],
                ConversionRates: [],
              },
            ]
          : [
              {
                _id: Date.now().toString(),
                brand: {
                  name: "",
                },
                Grade: [],
                ConversionRates: [],
              },
            ],
      })
    );

    showToast({
      messageContent: "Brand Section Added!",
      type: "success",
    });
  };

  const addConversionRateSection = (brandIdx: number) => {
    if (!formData?.Unit?.[0]?.name) {
      showToast({
        messageContent: "Please Enter Unit!",
        type: "warning",
      });
      return;
    }

    if (formData?.Unit?.length === 1) {
      showToast({
        messageContent: "Please Add Another Unit!",
        type: "warning",
      });
      return;
    }

    if (
      formData?.Brands?.[brandIdx]?.ConversionRates?.[
        formData?.Brands?.[brandIdx]?.ConversionRates?.length - 1
      ]?.fromUnit === "" ||
      formData?.Brands?.[brandIdx]?.ConversionRates?.[
        formData?.Brands?.[brandIdx]?.ConversionRates?.length - 1
      ]?.toUnit === "" ||
      formData?.Brands?.[brandIdx]?.ConversionRates?.[
        formData?.Brands?.[brandIdx]?.ConversionRates?.length - 1
      ]?.rate === null
    ) {
      showToast({
        messageContent: "Please Enter Conversion Rate!",
        type: "warning",
      });
      return;
    }

    //combination formula applied for maximum conversion rates
    const units = formData?.Unit;
    const n = units.length;

    const totalPairs = (n * (n - 1)) / 2;

    if (
      (formData?.Brands?.[brandIdx]?.ConversionRates ?? []).length >= totalPairs
    ) {
      showToast({
        messageContent: "Maximum Conversion Rates reached!",
        type: "warning",
      });
      return;
    }

    //this code is for adding conversion rate section
    const updatedBrands = [...formData.Brands];
    const targetBrand = updatedBrands[brandIdx] || {};

    const updatedConversionRates = [
      ...(Array.isArray(targetBrand.ConversionRates)
        ? targetBrand.ConversionRates
        : []),
      { fromUnit: "", toUnit: "", rate: null },
    ];

    updatedBrands[brandIdx] = {
      ...targetBrand,
      ConversionRates: updatedConversionRates,
    };

    dispatch(
      setFormMaterialsData({
        ...formData,
        Brands: updatedBrands,
      })
    );
  };

  // console.log("form dataa", formData);

  // Handle Input Change
  const handleInputChange = (id: string, value: string) => {
    dispatch(
      setFormMaterialsData({
        ...formData,
        [id]: value,
      })
    );
  };

  // Next Button Click
  const handleNext = () => {
    if (
      !formData?.Name?.trim() ||
      formData?.Brands?.some(
        (brand) => !brand?.brand?.name?.trim() || !brand?.Grade?.[0]?.trim()
      ) ||
      !formData?.Unit?.[0]?.name
    ) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        Name: !formData?.Name?.trim() || prevErrors.Name,
        Brand: formData?.Brands?.map((item) =>
          !item?.brand?.name?.trim() ? item._id : undefined
        ).filter((id): id is string => Boolean(id)), // ensures only strings
        Grade: formData?.Brands?.map((item, index) => {
          if (!item?.Grade?.[0]?.trim()) {
            return item._id;
          }
          return undefined;
        }).filter((id): id is string => Boolean(id)),
        Unit: !formData?.Unit?.[0]?.name?.trim() || prevErrors.Unit,
      }));
      setEmptyError((prevErrors) => ({
        ...prevErrors,
        Brand: !formData.Brands[0]?.brand?.name?.trim() ? true : false,
        Grade: !formData.Brands[0]?.Grade.length ? true : false,
      }));
      showToast({
        messageContent: "Enter Required Fields!",
        type: "warning",
      });
      return;
    }

    const hasPartiallyFilledConversion = formData?.Brands?.some((brand) =>
      brand?.ConversionRates?.some((rate) => {
        console.log("converstion", rate);
        const fromUnitFilled = !!rate?.fromUnit?.trim();
        const toUnitFilled = !!rate?.toUnit?.trim();
        const rateFilled =
          rate?.rate !== null &&
          rate?.rate !== undefined &&
          String(rate?.rate) !== "";

        const filledFieldsCount = [
          fromUnitFilled,
          toUnitFilled,
          rateFilled,
        ].filter(Boolean).length;

        return filledFieldsCount >= 0 && filledFieldsCount < 3;
      })
    );

    console.log("partially filled", hasPartiallyFilledConversion);

    if (hasPartiallyFilledConversion) {
      showToast({
        messageContent: "Please fill all fields in a conversion rate!",
        type: "warning",
      });
      return;
    }

    // const hasInconsistentConversionRates = (() => {
    //   const brands = formData?.Brands || [];

    //   // Get brands with at least one fully filled conversion rate
    //   const brandsWithConversion = brands.filter((brand) =>
    //     brand?.ConversionRates?.some((rate) => {
    //       const fromUnitFilled = !!rate?.fromUnit?.trim();
    //       const toUnitFilled = !!rate?.toUnit?.trim();
    //       const rateFilled =
    //         rate?.rate !== null &&
    //         rate?.rate !== undefined &&
    //         String(rate?.rate) !== "";

    //       return fromUnitFilled && toUnitFilled && rateFilled;
    //     })
    //   );

    //   // If at least one brand has conversions and not all do, then give toast
    //   return (
    //     brandsWithConversion.length > 0 &&
    //     brandsWithConversion.length < brands.length
    //   );
    // })();

    // if (hasInconsistentConversionRates) {
    //   dispatch(setToast({
    //     isOpen: true,
    //     messageContent: "Please Define Converstion Rate For All Brands!",
    //     type: "warning"
    //   }))
    //   return;
    // }

    setShowSummary(true);
  };

  const handleUnitPopUpData = (
    brandIndex: number,
    rowIndex: number,
    mode: "from" | "to"
  ): { id: string; label: string }[] => {
    const brand = formData.Brands?.[brandIndex];
    const units = formData.Unit ?? [];
    const conversionRates = brand?.ConversionRates ?? [];

    //Only keep valid, complete pairs
    const filteredRates = conversionRates.filter((r) => r.fromUnit && r.toUnit);

    //Step 2: Build a map of which units are already paired with which
    const pairedMap = new Map<string, Set<string>>();
    filteredRates.forEach(({ fromUnit, toUnit }) => {
      if (!pairedMap.has(fromUnit)) pairedMap.set(fromUnit, new Set());
      if (!pairedMap.has(toUnit)) pairedMap.set(toUnit, new Set());
      pairedMap.get(fromUnit)!.add(toUnit);
      pairedMap.get(toUnit)!.add(fromUnit);
    });

    //Step 3: Get the unit on the opposite side of the row (if any)
    const currentRow = conversionRates[rowIndex] ?? {};
    const oppositeUnit =
      mode === "from" ? currentRow.toUnit : currentRow.fromUnit;
    const blockedUnits = pairedMap.get(oppositeUnit ?? "") ?? new Set();

    const allUnitNames = units.map((u) => u.name);

    //Step 4: Filter units that are still available to be paired
    const availableUnits: { id: string; label: string }[] = units
      .filter((u) => {
        const name = u.name;

        // Must not be already paired with selected opposite unit
        if (blockedUnits.has(name) || name === oppositeUnit) return false;

        const pairedWith = pairedMap.get(name) ?? new Set();
        if (pairedWith.size >= allUnitNames.length - 1) return false;

        return true;
      })
      .map((u) => ({ id: u._id, label: u.name }));

    return availableUnits;
  };
  // const handleCancelImageUplaod = () => {
  //   setFileLoader(false);
  // };
  // Back Button Click
  const handleBack = () => {
    setDiscard(false);
    setShowSummary(false);
  };

  //function to handle cover photo change
  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFile(null);
    const files = event.target.files;
    if (files && files.length > 0) {
      setFileLoader(() => true);

      const selectedFile = await compressImage(files[0], 0.2);
      if (currentFileState.current) {
        setFile({
          name: selectedFile.name,
          type: selectedFile.name.split(".").pop()?.toLowerCase() || "",
          file: selectedFile,
        });
        const latestFormData = store.getState().masterForm.formMaterialsData;

        dispatch(
          setFormMaterialsData({
            ...latestFormData,
            Photo: {
              name: selectedFile.name,
              type: selectedFile.name.split(".").pop()?.toLowerCase() || "",
              file: selectedFile,
            },
          })
        );
        updateClipPath("coverphoto", false, selectedFile.name);
      }
      setFileLoader(false);
    }
  };

  const areArraysDifferent = (arr1: {}[], arr2: {}[]) => {
    if (!arr1 || !arr2) return true;
    if (arr1.length !== arr2.length) return true;

    return arr1.some(
      (item, index) => JSON.stringify(item) !== JSON.stringify(arr2[index])
    );
  };

  // console.log("see the change", formData?.Brands, initialFormData?.Brands);
  const hasFormChanges = () => {
    if (formMode === "Add") {
      return (
        formData?.Name?.trim() ||
        formData?.Description?.trim() ||
        formData?.Photo?.name?.trim() ||
        formData?.Unit?.[0]?.name ||
        formData?.Brands?.[0]?.brand?.name?.trim() ||
        formData?.Brands?.[0]?.Grade?.[0]?.trim() ||
        formData?.Brands?.[0]?.ConversionRates?.[0]?.fromUnit?.trim() ||
        formData?.Brands?.[0]?.ConversionRates?.[0]?.toUnit?.trim() ||
        formData?.Brands?.[0]?.ConversionRates?.[0]?.rate
      );
    } else {
      return (
        formData?.Name?.trim() !== initialFormData?.Name?.trim() ||
        formData?.Description?.trim() !==
          initialFormData?.Description?.trim() ||
        formData?.Photo?.name?.trim() !==
          initialFormData?.Photo?.name?.trim() ||
        areArraysDifferent(formData?.Unit, initialFormData?.Unit) ||
        areArraysDifferent(formData?.Brands, initialFormData?.Brands)
      );
    }
  };
  const handleCancel = () => {
    const hasChanged = hasFormChanges();

    if (hasChanged) {
      setDiscard(true);
      return;
    }

    handleClose("AddMaterialsForm");
  };

  // console.log("form data", formData);

  //for submission api call
  const handleSubmit = async () => {
    try {
      //formatted the data into the form acceptable by backend so that in future minimum changes are required
      const formatedData = {
        ...(formMode === "Edit" && formData?._id ? { _id: formData._id } : {}),
        name: formData?.Name,
        Description: formData?.Description,
        Brand: formData?.Brands?.map((item) => ({
          ...(formMode === "Add"
            ? { Brandname: item?.brand.name }
            : item?.brand?._id && !/^\d{13}$/.test(item?.brand?._id)
            ? { BrandId: [item?.brand?._id] }
            : { name: item?.brand?.name }),
          Specs: item?.Grade,
          ...(item?.brand?._id && !/^\d{13}$/.test(item?.brand?._id)
            ? { _id: item?.brand?._id }
            : {}),
          conversionRate: item?.ConversionRates?.[0]?.toUnit
            ? item?.ConversionRates?.map((rate) => ({
                unit1: rate?.fromUnit,
                unit2: rate?.toUnit,
                value: Number(rate?.rate),
              }))
            : [],
        })),
        unit: formData?.Unit?.map((item) => item?.name),
        ...(formMode === "Add"
          ? { materialCategoryId: materialsCategoryId }
          : {}),
        images: formData?.Photo?.file,
      };

      if (formMode === "Edit") {
        const noChanges =
          formData?.Name?.trim() === initialFormData?.Name?.trim() &&
          formData?.Description?.trim() ===
            initialFormData?.Description?.trim() &&
          formData?.Photo?.name?.trim() ===
            initialFormData?.Photo?.name?.trim() &&
          !areArraysDifferent(formData?.Unit, initialFormData?.Unit) &&
          !areArraysDifferent(formData?.Brands, initialFormData?.Brands);
        if (noChanges) {
          setShowSummary(false);
          showToast({
            messageContent: "There Were no changes!",
            type: "warning",
          });
          return;
        }
      }
      if (formMode === "Add") {
        const response = await addMaterialDesignation(formatedData).unwrap();

        showToast({
          messageContent: "Material added successfully!",
          type: "success",
        });
      } else {
        //update api call
        const response = await updateMaterialDesignation(formatedData).unwrap();
        showToast({
          messageContent: "Material updated successfully!",
          type: "success",
        });
      }
      refetch();
      handleClose("AddMaterialsForm");
      dispatch(setIsLocalChange(true));
    } catch (error) {
      console.log("errprrrrrrr", error);
      showToast({
        messageContent:
          (error as { data?: { message?: string } })?.data?.message ||
          "Oops! Something went wrong",
        type: "danger",
      });
    }
  };

  function updateClipPath(
    id: string,
    resetBorder: boolean,
    value?: string | number
  ) {
    const inputWrapper =
      document.getElementById(id)?.parentElement?.parentElement;
    // console.log("selected input wrapper:", inputWrapper);
    if (!inputWrapper) return;

    const label = document.querySelector(".photo_tag");
    // console.log("label input wrapper:", label);
    const input = inputWrapper;
    // console.log("input2 input wrapper:", input);

    if (label && input) {
      if (!resetBorder || value) {
        const labelWidth = (label as HTMLElement).offsetWidth + 20;
        const inputWidth = input.offsetWidth;

        // Calculating clip-path based on label width
        const leftPercentage = (labelWidth / inputWidth) * 100;
        // input.style.clipPath = "none";
        input.style.clipPath = `polygon(0 0, ${leftPercentage}% 0, ${leftPercentage}% 2px, 0 2px, 0 100%, 100% 100%, 100% 0, 100% 0, 100% 0, 100% 2px, 100% 0)`;
      } else {
        input.style.clipPath = "none";
      }
    }
  }
  useEffect(() => {
    if (file?.name) {
      updateClipPath("coverphoto", false, file.name);
    } else {
      updateClipPath("coverphoto", true);
    }
  }, [discard, showSummary, file]);
  useEffect(() => {
    currentFileState.current = fileLoader;
  }, [fileLoader]);
  console.log("formData>>>>>>>>>>>>>", formData);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if(isClosing) return;
    if (e.key === "Enter" && e.shiftKey) return;

    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      if (showSummary) {
        handleSubmit();
        dispatch(resetDeleteUnitData());
        dispatch(resetDeleteFormData());
        dispatch(resetDeletedGradeData());
        dispatch(resetDeletedToolData());
      }
      if (!showSummary && !discard) {
        handleNext();
      }
      if (discard) {
        handleClose("AddMaterialsForm");
        dispatch(resetDeleteUnitData());
        dispatch(resetDeleteFormData());
        dispatch(resetDeletedGradeData());
        dispatch(resetDeletedToolData());
      }
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      if (!showSummary && !discard) {
        handleCancel();
      }

      if (showSummary) {
        handleBack();
      }
      if (discard) {
        if (discard && wasTrue) {
          setDiscard(false);
          setShowSummary(true);
          setWasTrue(false);
          return;
        }
        setDiscard(false);
      }
    }
  };
  const formRef = useRef(null);
  useEffect(() => {
    if (showSummary || discard) {
      if (formRef.current) {
        formRef.current.focus();
      }
    }
  }, [discard, showSummary]);

  console.log("is material form empty", formData);
  const [formEmpty, setFormEmpty] = useState(true);
  const isEmpty = (data): boolean => {
    if (
      data?.Name === "" &&
      data?.Description === "" &&
      data?.Photo === null &&
      data?.Brands?.[0]?.brand?.name === "" &&
      data?.Brands?.[0]?.ConversionRates?.length === 0 &&
      data?.Brands?.[0]?.Grade.length === 0 &&
      data?.Unit.length === 0
    ) {
      console.log("issempty>>: true");
      return true;
    } else {
      console.log("issempty>>: false");
      return false;
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // console.log('outisde click tools',inputValue)
      if (formRef.current && !formRef.current.contains(event.target as Node)) {
        const isEmp = isEmpty(formData);
        setFormEmpty(isEmp);
        if (isEmp) {
          handleClose("AddMaterialsForm");
          return;
        }
        if (!hasFormChanges() && !discard) {
          handleClose("AddMaterialsForm");
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [formData, dispatch]);

  useEffect(() => {
    requestAnimationFrame(() => {
      if (contentRef.current) {
        contentRef.current.scrollTop = 0;
      }
    });
  }, [showSummary, discard]);

  return (
    <div
      className={`${styles.addmaterialsform_container} ${
        isClosing ? styles.closing : ""
      }`}
      tabIndex={0}
      onKeyDown={handleKeyDown}
      ref={formRef}
    >
      <div
        className={styles.addmaterialsform_header}
        style={{ color: discard ? "var(--warning_color)" : "" }}
      >
        <h3>
          {showSummary
            ? `Are you sure you want to ${
                formMode === "Add" ? "add" : "update"
              } this Material?`
            : discard
            ? "Are you sure you want to discard these changes ?"
            : formMode === "Add"
            ? "Add Material"
            : "Edit Material"}
        </h3>
        <button
          onClick={() => {
            if (!hasFormChanges() && showSummary) {
              handleClose("AddMaterialsForm");
              return;
            }
            if (showSummary) {
              setDiscard(true);
              setWasTrue(true);
              setShowSummary(false);
              return;
            }
            if (discard && !wasTrue) {
              setDiscard(false);
              return;
            }
            if (discard && wasTrue) {
              setDiscard(false);
              setWasTrue(false);
              setShowSummary(true);
              return;
            }

            handleCancel();
          }}
          className={styles.closeButton}
        >
          <CloseIcon />
        </button>
      </div>

      <div className={styles.addmaterialsform_datainputs} ref={contentRef}>
        {showSummary ? (
          <MaterialSummary
            formData={formData}
            initialFormData={initialFormData}
            formMode={formMode}
            deletedFormData={deletedFormData}
            deletedGradeData={deletedGradeData}
            deletedUnitData={deletedUnitData}
            deletedUnitArray={deletedUnitArray}
          />
        ) : discard ? (
          // <MaterialDiscard
          //   formData={formData}
          //   initialFormData={initialFormData}
          //   formMode={formMode}
          //   // deletedFormData={deletedFormData}
          //   // deletedGradeData={deletedGradeData}
          // />
          <MaterialSummary
            formData={formData}
            initialFormData={initialFormData}
            formMode={formMode}
            deletedFormData={deletedFormData}
            deletedGradeData={deletedGradeData}
            deletedUnitData={deletedUnitData}
            deletedUnitArray={deletedUnitArray}
          />
        ) : (
          <div className={styles.addmaterialsform_datainputs_wrapper}>
            <div className={`${styles.addmaterialsform_datainputs_flexrow}`}>
              <FloatingLabelInput
                label="Name"
                id="name"
                focusOnInput={true}
                props="one_line"
                error={errors?.Name}
                placeholder="Name"
                value={formData?.Name}
                onInputChange={(value: any) => {
                  handleInputChange("Name", value);
                  setErrors({ ...errors, Name: false });
                }}
              />
              <div className={`${styles.photo_input_wrapper}`}>
                {
                  <label
                    className={`photo_tag `}
                    style={{
                      position: "absolute",
                      left: file ? 20 : 16,
                      color: file
                        ? "var(--text-black-87)"
                        : "var(--text-black-60)",
                      zIndex: 9999,
                      top: file ? "" : "30%",
                      borderRadius: "5px",
                      padding: "0.2rem",
                      fontSize: file ? "0.75rem" : "",
                      transform: file ? "translateY(40%)" : "translateY(40%)",
                      transition: "transform 0.3s ease-in-out",
                    }}
                  >
                    Cover Photo
                  </label>
                }
                <div
                  className={styles.cover_photo}
                  style={{
                    justifyContent: file ? "space-between" : "flex-end",
                    marginTop: "1.3rem",
                  }}
                >
                  {file && (
                    <div className={styles.tcr_fileNames_div}>
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          margin: "-0.5rem",
                          paddingInline: "0.5rem",
                          position: "relative",
                          maxWidth: "10rem",
                          overflow: "hidden",
                        }}
                        className={`${styles.tcr_fileNames} small_text_p_400`}
                      >
                        {file.type === "jpg" ||
                        file.type === "jpeg" ||
                        file.type === "png" ? (
                          <ImageIcon />
                        ) : null}
                        {file.type === "mp4" ? <VideoIcon /> : null}
                        {file.type === "mp3" ? <AudioIcon /> : null}

                        <p
                          style={{
                            marginLeft: "0.5rem",
                            width: "80%",
                            whiteSpace: "nowrap",
                            textOverflow: "ellipsis",
                            overflow: "hidden",
                          }}
                          className="small_text_p_400"
                        >
                          {file.name}
                        </p>
                      </div>
                    </div>
                  )}

                  <div
                    className={
                      !file ? styles.tcrpopup_header_attachmentbtn : ""
                    }
                    style={{
                      cursor: "pointer",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                    onClick={
                      fileLoader
                        ? () => {
                            setFileLoader(false);
                          }
                        : () => document.getElementById("coverphoto")?.click()
                    }
                  >
                    {file ? (
                      <ReverseArrow />
                    ) : fileLoader ? (
                      <>
                        <Cross />
                      </>
                    ) : (
                      <Attachment />
                    )}
                    <input
                      id="coverphoto"
                      type="file"
                      style={{ display: "none" }}
                      accept="image/jpeg,image/jpg,image/png"
                      onChange={handleFileChange}
                    />
                  </div>
                  {fileLoader && (
                    <div className={styles.progress_bar_container}>
                      <div className={styles.progress_bar}></div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <FloatingLabelInput
              label="Description"
              id="Description"
              placeholder="Description"
              props="description_prop"
              value={formData?.Description}
              onInputChange={(value: any) => {
                handleInputChange("Description", value);
              }}
            />

            <div style={{ marginTop: "1.5rem" }}>
              <h4 style={{ marginBottom: "1rem" }}>Unit</h4>
              <Datafield
                label="Add"
                isUnit={true}
                selectedValues={formData?.Unit}
                error={errors?.Unit}
                varient="AddMaterialsForm"
                callbackDelete={(id) => {
                  const isInInitial = formData?.Unit?.find(
                    (unit) => unit?.name === id
                  );

                  const deletedUnit =
                    !!formData?.Unit?.find((unit) => unit?.name === id) &&
                    !!initialFormData?.Unit?.find((unit) => unit?.name === id);
                  if (isInInitial && deletedUnit) {
                    dispatch(
                      setDeletedToolData([
                        ...deletedUnitArray,
                        initialFormData?.Unit?.find(
                          (unit) => unit?.name === id
                        ),
                      ])
                    );
                  }
                  dispatch(
                    setFormMaterialsData({
                      ...formData,
                      Unit: formData?.Unit?.filter((unit) => unit?.name !== id),
                      Brands: formData.Brands.map((brand) => ({
                        ...brand,
                        ConversionRates: brand.ConversionRates?.filter(
                          (rate) =>
                            rate?.fromUnit?.toLowerCase()?.trim() !==
                              id?.toLowerCase()?.trim() &&
                            rate?.toUnit?.toLowerCase()?.trim() !==
                              id?.toLowerCase()?.trim()
                        ),
                      })),
                    })
                  );
                }}
                setIsClosing={setIsClosing}
              />
            </div>

            {/* Brand Section Header with Add Icon */}
            <div
              className={styles.addmaterialsform_brandheader}
              style={{ marginTop: "1.5rem" }}
            >
              <h4>Brand</h4>
              <div
                onClick={addBrandSection}
                className={styles.addBrandsectionCategoryIcon}
              >
                <AddCategoryIcon />
              </div>
            </div>

            {/* Dynamic Brand Sections */}
            {formData &&
              formData?.Brands?.map((brandItem: any, index: any) => (
                <div
                  key={index}
                  className={styles.addmaterialsform_brandsection}
                  style={{
                    border:
                      errors?.Brand.find((val) => val == brandItem._id) ||
                      errors?.Grade.find((id) => id == brandItem._id) ||
                      (emptyError.Brand && !brandItem?.brand?.name) ||
                      (emptyError.Grade && !brandItem?.Grade[0])
                        ? "1px solid var(--warning_color) !important"
                        : "",
                  }}
                >
                  <div
                    className={styles.addmaterialsform_brandsectioninputs}
                    style={{ position: "relative" }}
                  >
                    <div style={{ position: "relative" }}>
                      <FloatingLabelInput
                        label="Name"
                        id={`brand-name-${index}`}
                        placeholder="Name"
                        props="one_line"
                        error={
                          errors?.Brand.find((val) => val == brandItem._id) ||
                          (emptyError.Brand && !brandItem?.brand?.name)
                            ? true
                            : false
                        }
                        marginTop="0rem"
                        value={
                          formData?.Brands?.find(
                            (item: any) => item?._id === brandItem?._id
                          )?.brand?.name ?? ""
                        }
                        onInputChange={(value: any) => {
                          setErrors((prevErrors) => ({
                            ...prevErrors,
                            Brand: prevErrors.Brand.filter(
                              (val, i) => val !== brandItem?._id
                            ),
                          }));
                          setEmptyError((prevErrors) => ({
                            ...prevErrors,
                            Brand: false,
                            Grade: false,
                          }));
                          dispatch(
                            setFormMaterialsData({
                              ...formData,
                              Brands: formData.Brands?.map(
                                (brand: any, i: number) =>
                                  brand?._id === brandItem?._id
                                    ? {
                                        ...brand,
                                        brand: { name: value },
                                        Grade: [...brand?.Grade],
                                        ...(brand?.updateId
                                          ? { updateId: brand?.updateId }
                                          : {}),
                                      }
                                    : brand
                              ),
                            })
                          );

                          //if value is empty no suggestion should be given
                          if (value?.trim() == "") {
                            setIsOpen(() => ({ [brandItem?._id]: false }));
                            () => ({
                              [brandItem?._id]: value,
                            });
                            return;
                          }

                          //if value is present give suggestion
                          setIsOpen(() => ({ [brandItem?._id]: true }));
                          setSearchKey(() => ({ [brandItem?._id]: value }));

                          //logic to close the isOpen if no match is found
                          const brands =
                            allBrands.data.length > 0
                              ? allBrands.data.filter((item: any) => {
                                  const safePattern = value?.replace(
                                    /[-\/\\^$*+?.()|[\]{}]/g,
                                    "\\$&"
                                  );
                                  return safePattern
                                    ? new RegExp(safePattern, "i").test(
                                        item?.Brandname
                                      )
                                    : false;
                                })
                              : [];

                          if (brands.length === 0) {
                            setIsOpen(() => ({ [brandItem?._id]: false })); // Correct way to update state
                          }
                        }}
                      />

                      {/* this is the dropdown in which suggestion will be shown */}
                      {isOpen?.[brandItem?._id] && (
                        <div
                          className={`${styles.unit_popup_mt_container} ${
                            isOpen?.[brandItem?._id]
                              ? `${styles.selected}`
                              : "notSelected"
                          }`}
                        >
                          <UnitPopup
                            property={"unit_popup_class"}
                            alignment="absolute"
                            left="0"
                            top="57px"
                            width="100%"
                            data={(() => {
                              const filtered =
                                (searchKey
                                  ? allBrands?.data?.filter((item: any) => {
                                      const safePattern = searchKey[
                                        brandItem?._id
                                      ]?.replace(
                                        /[-\/\\^$*+?.()|[\]{}]/g,
                                        "\\$&"
                                      );
                                      return new RegExp(safePattern, "i").test(
                                        item?.Brandname
                                      );
                                    })
                                  : allBrands?.data) || [];

                              const mapped = filtered
                                .map((item: any) => ({
                                  id: item?._id,
                                  label: item?.Brandname,
                                }))
                                .filter((currentItem: any) => {
                                  const isInFormData = formData?.Brands?.some(
                                    (b) => b.brand._id === currentItem?.id
                                  );
                                  return !isInFormData;
                                });

                              if (mapped.length === 0) {
                                setIsOpen?.(null);
                              }

                              return mapped;
                            })()}
                            onSelect={(item) => {
                              //setting brand name with the _id
                              dispatch(
                                setFormMaterialsData({
                                  ...formData,
                                  Brands: formData.Brands?.map(
                                    (brand: any, i: number) =>
                                      brand?._id === brandItem?._id
                                        ? {
                                            ...brand,
                                            brand: {
                                              _id: item?.id,
                                              name: item?.label,
                                            },
                                          }
                                        : brand
                                  ),
                                })
                              );
                              setSearchKey(null);
                              setIsOpen(null);
                            }}
                            selectedId={null} //we dont need this as of now, will see if there is any need
                          />
                        </div>
                      )}
                    </div>
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        marginTop: "1rem",
                      }}
                    >
                      <h4>Grade / Model</h4>
                      {formData?.Brands?.length > 1 &&
                        (!formData?.Brands[formData?.Brands?.length - 1]
                          ?.Grade?.[0] &&
                        index !== formData?.Brands?.length - 1 ? (
                          <div
                            onClick={() =>
                              copyGradesfun(
                                formData?.Brands?.find(
                                  (item) => item?._id === brandItem?._id
                                )?.Grade!
                              )
                            }
                            className={styles.copy_button}
                          >
                            <span style={{ marginInlineEnd: "0.5rem" }}>
                              Copy
                            </span>{" "}
                            <CopyIcon />
                          </div>
                        ) : (
                          copyGrades?.length > 0 &&
                          !formData?.Brands[formData?.Brands?.length - 1]?.Grade
                            ?.length && (
                            <div
                              onClick={() => pasteGradesfun(brandItem?._id)}
                              className={styles.copy_button}
                            >
                              <span style={{ marginInlineEnd: "0.5rem" }}>
                                Paste
                              </span>{" "}
                              <PasteIcon />
                            </div>
                          )
                        ))}
                    </div>
                    <div style={{ paddingTop: "0.5rem" }}>
                      {/* <GradeInputbox /> */}
                      <DynamicGradeInput
                        variant="grade"
                        label="Add"
                        callbackDelete={(deleteIndex) => {
                          const deletedGradeValue = formData?.Brands?.find(
                            (item: any) => item?._id === brandItem?._id
                          )?.Grade[deleteIndex];

                          const deletedGrade =
                            formData?.Brands?.find(
                              (item: any) => item?._id === brandItem?._id
                            )?.Grade?.includes(deletedGradeValue!) &&
                            initialFormData?.Brands?.find(
                              (item: any) => item?._id === brandItem?._id
                            )?.Grade.includes(deletedGradeValue!);

                          dispatch(
                            setFormMaterialsData({
                              ...formData,
                              Brands: formData?.Brands?.map(
                                (brand, brandIdx) =>
                                  brandIdx === index
                                    ? {
                                        ...brand,
                                        Grade: Array.isArray(brand?.Grade)
                                          ? brand.Grade.filter(
                                              (_, i) => i !== deleteIndex
                                            ) // Remove Grade at `index`
                                          : [],
                                      }
                                    : brand // Keep other brands unchanged
                              ),
                            })
                          );
                          if (formMode !== "Add" && deletedGrade) {
                            const existing = deletedGradeData?.find(
                              (item: any) => item[brandItem?._id] !== undefined
                            );

                            let updatedDeleteGradeData;

                            if (existing) {
                              updatedDeleteGradeData = deletedGradeData?.map(
                                (item: any) => {
                                  if (item[brandItem._id] !== undefined) {
                                    const updatedSet = new Set([
                                      ...item[brandItem._id],
                                      deletedGradeValue,
                                    ]);
                                    return {
                                      [brandItem._id]: Array.from(updatedSet),
                                    };
                                  }
                                  return item;
                                }
                              );
                            } else {
                              updatedDeleteGradeData = [
                                ...deletedGradeData,
                                { [brandItem._id]: [deletedGradeValue] },
                              ];
                            }

                            // Dispatch with the new data
                            dispatch(
                              setDeletedGradeData(updatedDeleteGradeData)
                            );
                          }
                        }}
                        error={
                          errors?.Grade.find((id) => id == brandItem._id) ||
                          (emptyError.Grade && !brandItem?.Grade[0])
                            ? true
                            : false
                        }
                        initialData={
                          formData?.Brands?.find(
                            (item: any) => item?._id === brandItem?._id
                          )?.Grade || []
                        }
                        onGradesUpdate={(grades) => {
                          setErrors((prevErrors) => ({
                            ...prevErrors,
                            Brand: prevErrors.Brand.filter(
                              (val, i) => val !== brandItem?._id
                            ),
                            Grade: prevErrors.Grade.filter(
                              (val, i) => val !== brandItem?._id
                            ),
                          }));
                          setEmptyError((prevErrors) => ({
                            ...prevErrors,
                            Brand: false,
                            Grade: false,
                          }));
                          dispatch(
                            setFormMaterialsData({
                              ...formData,
                              Brands: formData.Brands?.map(
                                (brand: any, i: any) =>
                                  i === index
                                    ? { ...brand, Grade: grades }
                                    : brand
                              ),
                            })
                          );
                        }}
                      />
                    </div>
                    {formData?.Brands?.length > 1 && (
                      <div
                        className={styles.delete_icon_tooltip}
                        onClick={() => {
                          const currentBrand = initialFormData?.Brands?.find(
                            (item) => item?._id === brandItem?._id
                          );

                          if (currentBrand && formMode !== "Add") {
                            const allConversionRatesToDelete =
                              currentBrand.ConversionRates || [];

                            // Check if this brand already has deleted entries
                            const existing = deletedUnitData?.find(
                              (item: any) => item[brandItem._id] !== undefined
                            );

                            let updatedDeleteUnitData;

                            if (existing) {
                              updatedDeleteUnitData = deletedUnitData.map(
                                (item: any) =>
                                  item[brandItem._id] !== undefined
                                    ? {
                                        [brandItem._id]: [
                                          ...allConversionRatesToDelete,
                                        ],
                                      }
                                    : item
                              );
                            } else {
                              updatedDeleteUnitData = [
                                ...deletedUnitData,
                                {
                                  [brandItem._id]: allConversionRatesToDelete,
                                },
                              ];
                            }

                            dispatch(setDeletedUnitData(updatedDeleteUnitData));
                          }
                          const isInInitial = initialFormData?.Brands?.find(
                            (item) => item?._id === brandItem?._id
                          )?._id;

                          if (isInInitial) {
                            dispatch(
                              setDeletedFormData([
                                ...deletedFormData,
                                initialFormData?.Brands?.find(
                                  (item) => item?._id === brandItem?._id
                                ),
                              ])
                            );
                          }
                          dispatch(
                            setFormMaterialsData({
                              ...formData,
                              Brands: formData?.Brands?.filter(
                                (brand, brandIdx) =>
                                  brand?._id !== brandItem?._id
                              ),
                            })
                          );
                        }}
                      >
                        <DeleteIcon />
                      </div>
                    )}
                    <div
                      style={{ marginTop: "1rem" }}
                      className={styles.addmaterialsform_brandheader}
                    >
                      <h4>Conversion Rate</h4>
                      <div
                        onClick={() => addConversionRateSection(index)}
                        className={styles.addBrandsectionCategoryIcon}
                      >
                        <AddCategoryIcon />
                      </div>
                    </div>
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "0.5rem",
                      }}
                    >
                      {formData?.Brands?.[index]?.ConversionRates &&
                        formData?.Brands?.[index]?.ConversionRates?.map(
                          (rate, indx) => (
                            <div
                              style={{
                                display: "grid",
                                position: "relative",
                                gridTemplateColumns: "repeat(3, 1fr)",
                                gap: "1rem",
                                border: "1px solid var(--text-black-28)",
                                borderRadius: "1rem",
                                padding: "0.5rem 0.5rem 0.25rem 0.5rem",
                                marginTop: "0.5rem",
                              }}
                            >
                              {/* Unit Selection */}
                              <div style={{ position: "relative" }}>
                                <FloatingLabelInput
                                  label="Unit 1"
                                  id={`unit1-${index}-${indx}`}
                                  width="0"
                                  placeholder="Unit 1"
                                  marginTop="0"
                                  isDisabled={true}
                                  onInputChange={(value: any) => {
                                    handleInputChange("Unit", value);
                                    setErrors({ ...errors, Unit: false });
                                  }}
                                  Icon={
                                    isUnitPopUpVisible?.[index]?.[indx]
                                      ? DropDownArrowUpIcon
                                      : DropDownCategoryIcon
                                  }
                                  iconClick={() => toggleUnitPopUp(index, indx)}
                                  value={
                                    formData?.Brands?.[index]
                                      ?.ConversionRates?.[indx]?.fromUnit
                                  }
                                  maxLength={8}
                                />

                                {isUnitPopUpVisible?.[index]?.[indx] && (
                                  <div
                                    style={{
                                      position: "relative",
                                      left: "0%",
                                      top: "0rem",
                                      zIndex: 10,
                                      width: "100%",
                                      height: "320%",
                                    }}
                                  >
                                    <UnitPopup
                                      height="11rem"
                                      alignment="absolute"
                                      width="100%"
                                      data={handleUnitPopUpData(
                                        index,
                                        indx,
                                        "from"
                                      )}
                                      onSelect={(item: any) =>
                                        handleUnitSelect(
                                          index,
                                          indx,
                                          item,
                                          "fromUnit"
                                        )
                                      }
                                      selectedId={null}
                                    />
                                  </div>
                                )}
                              </div>

                              <FloatingLabelInput
                                label="Value"
                                placeholder="Value"
                                props="one_line"
                                type="number"
                                maxlength={9}
                                width="0"
                                marginTop="0"
                                id={`value-${index}-${indx}`}
                                value={
                                  formData?.Brands?.[index]?.ConversionRates?.[
                                    indx
                                  ]?.rate ?? ""
                                }
                                onInputChange={(e) => {
                                  dispatch(
                                    setFormMaterialsData({
                                      ...formData,
                                      Brands: formData.Brands?.map(
                                        (brand: any, i: number) =>
                                          i === index
                                            ? {
                                                ...brand,
                                                ConversionRates:
                                                  brand.ConversionRates?.map(
                                                    (rate: any, j: number) =>
                                                      j === indx
                                                        ? {
                                                            ...rate,
                                                            rate: e,
                                                          }
                                                        : rate
                                                  ),
                                              }
                                            : brand
                                      ),
                                    })
                                  );
                                }}
                              />

                              {/* Unit Selection */}
                              <div style={{ position: "relative" }}>
                                <FloatingLabelInput
                                  label="Unit 2"
                                  id={`unit2-${index}-${indx}`}
                                  placeholder="Unit 2"
                                  width="0"
                                  marginTop="0"
                                  isDisabled={true}
                                  onInputChange={(value: any) => {}}
                                  Icon={
                                    isUnitPopUpVisible2?.[index]?.[indx]
                                      ? DropDownArrowUpIcon
                                      : DropDownCategoryIcon
                                  }
                                  iconClick={() => {
                                    setUnitPopUpVisible2((prev) => ({
                                      [index]: {
                                        [indx]: !prev?.[index]?.[indx],
                                      },
                                    }));
                                  }}
                                  value={
                                    formData?.Brands?.[index]
                                      ?.ConversionRates?.[indx]?.toUnit
                                  }
                                  maxLength={8}
                                />
                                {isUnitPopUpVisible2?.[index]?.[indx] && (
                                  <div
                                    style={{
                                      position: "relative",
                                      left: "0%",
                                      top: "0rem",
                                      zIndex: 10,
                                      width: "100%",
                                      height: "320%",
                                    }}
                                  >
                                    <UnitPopup
                                      height="11rem"
                                      width="100%"
                                      alignment="absolute"
                                      data={handleUnitPopUpData(
                                        index,
                                        indx,
                                        "to"
                                      )}
                                      onSelect={(item: any) =>
                                        handleUnitSelect(
                                          index,
                                          indx,
                                          item,
                                          "toUnit"
                                        )
                                      }
                                      selectedId={null}
                                    />
                                  </div>
                                )}
                              </div>

                              {formData?.Brands[index]?.ConversionRates &&
                                formData?.Brands[index]?.ConversionRates
                                  ?.length && (
                                  <div
                                    className={styles.delete_icon_tooltip}
                                    onClick={() => {
                                      const currentBrand =
                                        initialFormData?.Brands?.find(
                                          (item) => item?._id === brandItem?._id
                                        );

                                      if (currentBrand) {
                                        const conversionRateToDelete =
                                          currentBrand.ConversionRates?.find(
                                            (rateItem) =>
                                              rateItem._id === rate._id
                                          );
                                        if (
                                          formMode !== "Add" &&
                                          conversionRateToDelete
                                        ) {
                                          const existing =
                                            deletedUnitData?.find(
                                              (item: any) =>
                                                item[brandItem._id] !==
                                                undefined
                                            );

                                          let updatedDeleteUnitData;

                                          if (existing) {
                                            updatedDeleteUnitData =
                                              deletedUnitData.map((item: any) =>
                                                item[brandItem._id] !==
                                                undefined
                                                  ? {
                                                      [brandItem._id]: [
                                                        ...item[brandItem._id],
                                                        conversionRateToDelete,
                                                      ],
                                                    }
                                                  : item
                                              );
                                          } else {
                                            updatedDeleteUnitData = [
                                              ...deletedUnitData,
                                              {
                                                [brandItem._id]: [
                                                  conversionRateToDelete,
                                                ],
                                              },
                                            ];
                                          }

                                          console.log(
                                            "conversion rate data",
                                            updatedDeleteUnitData
                                          );

                                          dispatch(
                                            setDeletedUnitData(
                                              updatedDeleteUnitData
                                            )
                                          );
                                        }
                                      }

                                      dispatch(
                                        setFormMaterialsData({
                                          ...formData,
                                          Brands: formData.Brands.map(
                                            (brand, bIdx) =>
                                              bIdx === index
                                                ? {
                                                    ...brand,
                                                    ConversionRates:
                                                      brand.ConversionRates?.filter(
                                                        (_, grdInx) =>
                                                          grdInx !== indx
                                                      ),
                                                  }
                                                : brand
                                          ),
                                        })
                                      );
                                    }}
                                  >
                                    <DeleteIcon />
                                  </div>
                                )}
                            </div>
                          )
                        )}
                    </div>
                  </div>
                </div>
              ))}
          </div>
        )}
      </div>

      <div className={styles.addmaterialsform_btngroup}>
        {showSummary ? (
          <>
            <Button type="Cancel" Content="Back" Callback={handleBack} />
            <Button
              type="Next"
              Content="Submit"
              Callback={() => {
                handleSubmit();
                dispatch(resetDeleteUnitData());
                dispatch(resetDeleteFormData());
                dispatch(resetDeletedGradeData());
                dispatch(resetDeletedToolData());
              }}
            />
          </>
        ) : discard ? (
          <>
            <Button
              type="Cancel"
              Content="No"
              Callback={() => {
                if (discard && wasTrue) {
                  setDiscard(false);
                  setShowSummary(true);
                  setWasTrue(false);
                  return;
                }
                setDiscard(false);
              }}
            />
            <Button
              type="Next"
              Content="Yes"
              Callback={() => {
                handleClose("AddMaterialsForm");
                dispatch(resetDeleteUnitData());
                dispatch(resetDeleteFormData());
                dispatch(resetDeletedGradeData());
                dispatch(resetDeletedToolData());
              }}
            />
          </>
        ) : (
          <>
            <Button type="Cancel" Content="Cancel" Callback={handleCancel} />
            <Button
              type="Next"
              Content={formMode === "Add" ? "Add" : "Update"}
              Callback={handleNext}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default AddMaterialsForm;
