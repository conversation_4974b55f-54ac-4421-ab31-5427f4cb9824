import React, { useState } from "react";
import styles from "./Styles/Tooltip.module.css";
import { DeleteIcon } from "../../assets/icons";
import { TooltipProps } from "../GlobalInterfaces/GlobalInterface";

const Tooltip: React.FC<TooltipProps> = ({
  content,
  id,
  index,
  label = "",
  isEdit = false,
  handleClick,
  className="",
  quantity,
  selectedCategory,
  onCategoryChange,
}) => {
  // const [isCategoryToggled, setIsCategoryToggled] = useState(false);

  // const handleToggleCategory = () => {
  //   if (onCategoryChange) {
  //     const newCategory = selectedCategory === "Fixed" ? "Calculated" : "Fixed";
  //     onCategoryChange(newCategory);
  //     setIsCategoryToggled(!isCategoryToggled);
  //   }
  // };
// console.log("className>>", className);
  return (
    <>
      <div style={{ position: "relative" }}>
        {/* {quantity && selectedCategory ? (
        <div className={`${styles.tooltip_main_card}`}>
          <div className={`${styles.tooltip_top}`}>
            <p
              className="small_text_p"
              style={{ color: "var(--text-black-60)" }}
            >
              {content}
            </p>
            <p style={{ color: "var(--text-black-87)" }}>{quantity}</p>
          </div>

          {selectedCategory === "Fixed" && (
            <div
              className={`${styles.tooltip_bottom} ${styles.tooltip_bottom_1}`}
              onClick={handleToggleCategory}
            >
              <h4>Fixed</h4>
            </div>
          )}

          {selectedCategory === "Calculated" && (
            <div
              className={`${styles.tooltip_bottom_calculated} ${styles.tooltip_bottom_2}`}
              onClick={handleToggleCategory}
            >
              <h4>Calculated</h4>
            </div>
          )}
        </div>
      ) : (
        <div
          onClick={(e) => handleClick && handleClick(e)}
          className={styles.tooltip}
        >
          <p>{content}</p>
        </div>
      )} */}
        <div
          onClick={(e) => handleClick && handleClick(e)}
          className={`${styles.tooltip} ${styles[className]}`}
          style={{
            marginTop:
              index && index > 0 ? (label === "Subtasks" ? "10px" : "") : "",
          }}
        >
          <h4>{content}</h4>
        </div>
      </div>
    </>
  );
};

export default Tooltip;
