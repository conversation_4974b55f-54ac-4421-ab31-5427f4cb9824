/* .addtooltip_container {
    max-width: 1.7rem;
    min-width: 1.7rem;
    height: 0;
    min-height: 2rem;
    display: flex;
    gap: 1rem;
    padding: 0.1rem;
    align-items: center;
} */

.addtooltip_container {
    display: flex;
    flex-direction: column;
    width: fit-content;
}

.cover_extra_space > * {
    /* flex-grow: 1; // Allows buttons to take equal space */
}

.addtooltip_sub_container {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
    position: relative;
    width: fit-content;
    min-width: 10px;
    padding: 1rem;
}

.addtooltip_header {
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: space-between;
    width: 100%;
    color: var(--text-black-87);
}

.addtooltip_header h4{
    color: var(--text-black-87);
}

.addtooltip_data_container {
    min-width: 5px;
    width: auto;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
    max-width: 434px;
    margin-left: 0.8rem;
    border: 1px solid #00000047;
    border-radius: 24px;
    padding: 1rem;
}

.delete_icon_tooltip {
    position: absolute;
    top: -0.5rem;
    right: 0rem;
    cursor: pointer;
    height: 24px;
    width: 24px;
    background-color: var(--secondary_warning_background);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 100%;
  }

.width_100{
    width: 100%;
}