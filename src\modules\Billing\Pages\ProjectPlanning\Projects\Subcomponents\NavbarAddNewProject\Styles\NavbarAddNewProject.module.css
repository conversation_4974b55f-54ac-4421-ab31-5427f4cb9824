.add_new_project_navbar_container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--main_background);
}
@media (max-width: 1200px) {
  .add_new_project_navbar_container {

    width: 1790px;
  }

}


.CategoryView_DesignArrow_Rotate {
  transform: rotate(-90deg);
  display: flex;
  align-items: center;
  width: fit-content;
}

.add_new_project_navbar_leftbtns {
  margin-left: 0.4rem;
  display: flex;
  position: relative;
  top: 0.625rem;
  /* top: -0.625rem; */
}
.add_new_project_navbar_leftbtn_tableview_switch {
  position: relative;
  top: -0.625rem;
  margin-left: 1.5rem;
}

.add_new_project_navbar_rightbtns {
  display: flex;
  gap: 0.938rem;
  position: relative;
  right: 2.65rem;
  align-items: center;
}

/* Responsive: Reset position and allow horizontal scroll under 1200px */
@media (max-width: 1200px) {
  .add_new_project_navbar_rightbtns {

  }
}

.add_new_project_navbar_exportbtn {
  width: 7.1rem;
  height: 2.6rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  border-radius: 6.25rem;
  cursor: pointer;
  background-color: var(--main_background);
  box-shadow: var(--extra-shadow);
  border: none;
  color: var(--text-black-60);
  font-size: 1rem;
}

.add_new_project_navbar_addprojectbtn {
  min-width: 7.1rem;
  height: 2.6rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  border-radius: 6.25rem;
  cursor: pointer;
  background-color: var(--primary_color);
  color: var(--text-white-100);
  border: none;
  padding: 1rem;
  font-size: 1rem
}

.locationbtncorner {
  backdrop-filter: blur(100px);

  box-shadow: var(--extra-shdow-six);

  position: fixed;
  right: -3.5rem;
  width: 6.563rem;
  height: 3.25rem;
  text-align: left;
  padding-left: 1rem;
  background-color: var(--main_background);
  margin-top: -0.2rem;
  border: none;
  border-radius: 1.75rem;
  cursor: pointer;
  z-index: 2;
}
.billing_toggle_container {
  backdrop-filter: blur(40px);
  padding: 0.3rem;
  border-radius: 10rem;
  box-shadow: var(--extra-shdow-third);
}
/* styles for delete project summary start by Rattandeep singh */

.summaryDivData {
  display: flex;
  align-items: center;
}

.summaryDataContent {
  display: flex;
  flex-direction: column;
  background: var(--main_background);
  border-radius: 0.75rem;
  width: 30.8rem;
  min-height: 3rem;
  padding: 1rem;
  white-space: normal;
  margin: 0.6rem;
  line-height: 1.363rem;
  text-align: left;
}

.summaryItem {
  display: flex;
}
.summaryItems {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}
/* styles for delete project summary end by Rattandeep singh */
/* styles for navigation component in navbaraddproject start */
.navproject_navigation_component {
  display: flex;
  justify-content: space-between;
  margin-top: -1.3rem;
  align-items: center;
  /* margin-left: 1.3rem; */
}
/* styles for navigation component in navbaraddproject end */
