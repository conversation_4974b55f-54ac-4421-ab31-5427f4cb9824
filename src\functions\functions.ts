import {
  getImageUrl,
  getImageUrlForInitial,
} from "./../Backup/TaskMasterBackup/index";
import customDebounce from "lodash.debounce";

import { AppDispatch, store } from "../redux/store";
import { ProjectData } from "../modules/Billing/Pages/ProjectPlanning/Projects/AddProjectForm/Interfaces/interface";
// import { createFFmpeg } from "@ffmpeg/ffmpeg";
// import { fetchFile } from "@ffmpeg/util";
// import {
//   compressAndAppendImage,
//   imageCompressor,
// } from "@mbs-dev/react-image-compressor";
import { SetProjects } from "../redux/features/Modules/Billing/ProjectPlanning/Slices/ProjectSlice";
import { ActionCreatorWithPayload } from "@reduxjs/toolkit";
import {
  SetCategoryEditDelete,
  SetTaskEditDelete,
} from "../redux/features/Modules/TaskMaster/Slices/CategorySlice";
import axios from "axios";
import { setImageToggle } from "../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import { saveSyncDataForImageStack } from "../Backup/BackupFunctions/BackupFunctions";
import { url } from "../config/urls";
import { TaskMasterApi } from "../redux/api/Modules/TaskMaster/TaskMasterapi";
// import {
//   SetCategoryEditDelete,
//   SetTaskEditDelete,
// } from "../redux/features/Modules/TaskMaster/Slices/CategorySlice";

// var imageAspectRatio = require("image-aspect-ratio");
// const ffmpeg = createFFmpeg({ log: true });

const initializedDbs: Record<string, boolean> = {};

export const initializeDatabase = async (
  dbName: string,
  retryCount = 3,
  delay = 1000
): Promise<string | null> => {
  if (initializedDbs[dbName]) return dbName;

  for (let attempt = 1; attempt <= retryCount; attempt++) {
    try {
      const db = await window.electron.initDb(dbName);
      if (db) {
        initializedDbs[dbName] = true;
        return dbName;
      }
    } catch (error: any) {
      console.error(
        `Attempt ${attempt} - Failed to initialize ${dbName} DB:`,
        error
      );

      if (error?.message?.includes("LOCK") && attempt < retryCount) {
        await new Promise((res) => setTimeout(res, delay));
      } else {
        return null;
      }
    }
  }

  return null;
};

export const getTime = async (id: string) => {
  try {
    const db = await initializeDatabase("localtime");
    console.log("db haii>>", db);
    if (db) {
      const fetchedData = await window.electron.getDocument({
        db: db,
        id: id,
      });
      console.log("db haii>>ffff", fetchedData);
      return fetchedData || null;
    }
  } catch (error) {
    console.error(`Failed to get time:`, error);
    return null;
  }
};
export const generateSummaryData = (
  inputValues: any,
  fields: Array<{ label: string; value: string | number | null }>,
  catId: string | null = null,
  mode: string | null = null
) => {
  const summaryData = fields.map((field) => {
    return {
      label: field.label,
      value:
        inputValues[field.label] != null
          ? inputValues[field.label] || field.value
          : "",
      isChanged:
        catId || mode === "edit"
          ? inputValues[field.label] != field.value
          : false,
    };
  });

  return summaryData;
};

export function isValidValue(value: any) {
  if (value === undefined || value === null) {
    return false;
  }

  if (typeof value === "string") {
    return value.trim() !== "";
  }

  if (typeof value === "number" && value === 0) {
    return false;
  }

  if (typeof value === "number") {
    return !isNaN(value);
  }
  return false;
}

export function isValidValue2(value: any) {
  if (value === undefined || value === null) {
    return false;
  }
  console.log("helotype", value, typeof value);

  if (typeof value === "string") {
    return value.trim() !== "";
  }

  if (typeof value === "number" && value === 0) {
    return true;
  }

  if (typeof value === "number") {
    return !isNaN(value);
  }
  return false;
}

export const PAGE_SIZE = 1000;

export const truncatetext = (text: string, limit: number) => {
  return text.length > limit ? text.substring(0, limit) + "..." : text;
};

// Autho name: Charvi format date
export function formatDate(dateStr: string): string {
  const [day, month, year] = dateStr.split("/").map(Number);

  if (!day || !month || !year || day > 31 || month > 12) {
    throw new Error("Invalid date format. Expected format: dd/mm/yyyy");
  }

  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  const monthName = months[month - 1];
  if (!monthName) throw new Error("Invalid month in date.");

  return `${monthName} ${day},${year}`;
}
// Author name: Charvi here if function for format time
export function formatTime(time: string): string {
  const [hourStr, minute] = time.split(":");
  const hour = parseInt(hourStr, 10);

  if (isNaN(hour) || !minute) {
    throw new Error("Invalid time format. Expected format: HH:mm");
  }

  if (hour === 0 && minute === "00") {
    return `12:00 Midnight`;
  }
  if (hour === 12 && minute === "00") {
    return `12:00 Noon`;
  }

  const period = hour >= 12 ? "PM" : "AM";
  const formattedHour = hour % 12 === 0 ? 12 : hour % 12;

  return `${formattedHour}:${minute} ${period}`;
}

//AUthor name: Charvi here function for fomat date

export const formatDateMT = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

export const numberWithCommas = (x: any): string => {
  const parts = x.toString().split(".");
  const integerPart = parts[0];
  const decimalPart = parts.length > 1 ? "." + parts[1] : "";
  const lastThreeDigits = integerPart.slice(-3);
  const otherDigits = integerPart.slice(0, -3);
  const formattedOtherDigits = otherDigits.replace(
    /\B(?=(\d{2})+(?!\d))/g,
    ","
  );
  return otherDigits
    ? formattedOtherDigits + "," + lastThreeDigits + decimalPart
    : lastThreeDigits + decimalPart;
};

// debounce function for delay 30 sec
export const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout | null = null;
  let lastCallTime: number | null = null;

  return function (...args: any) {
    lastCallTime = Date.now();

    if (timeout) clearTimeout(timeout);

    timeout = setTimeout(() => {
      if (lastCallTime && Date.now() - lastCallTime >= wait) {
        func(...args);
      }
    }, wait);
  };
};

//helper function to convert base64 into file
export const base64ToFile = async (
  base64: string,
  filename: string
): Promise<File | string> => {
  if (!base64 || typeof base64 !== "string" || !base64.includes(",")) {
    return "Invalid base64 string";
  }

  const [metadata, encoded] = base64?.split(",");

  const mimeType = metadata?.split(":")[1]?.split(";")[0];
  const byteCharacters = atob(encoded);
  const byteArrays = [];

  for (let offset = 0; offset < byteCharacters.length; offset += 1024) {
    const byteArray = new Uint8Array(
      Math.min(byteCharacters.length - offset, 1024)
    );
    for (let i = 0; i < byteArray.length; i++) {
      byteArray[i] = byteCharacters.charCodeAt(offset + i);
    }
    byteArrays.push(byteArray);
  }

  const blob = new Blob(byteArrays, { type: mimeType });

  const file = new File([blob], filename, { type: mimeType });
  console.log("lllll");
  return file;
  //   const compressedFile = await compressImage(file!, 0.6);
  //   return compressedFile;
  //   const compressedFile = await compressImage(file!, 0.6);
  //   return compressedFile;
};

export const autoSaveFormData = async (formData: any) => {
  try {
    console.log("function called data ", formData);
    const dbName = await initializeDatabase("taskformdata");
    const result = await window.electron.autoSave({ dbName, formData });
  } catch (error) {
    console.log("Error During Auto Save:", error);
  }
};

type PhotoRef = {
  photoref: {
    photos: {
      photo: string[];
    };
  };
};

type DataItem = Array<PhotoRef>;

export const filterImages = async ({
  newImages,
  taskdata,
  dispatch,
}: {
  newImages: DataItem;
  taskdata: DataItem;
  dispatch: AppDispatch;
}) => {
  const newSet: Set<string> = new Set(
    newImages?.flatMap((item: any) =>
      item?.photoref?.photos.map((p: any) => p.photo)
    ) // Extract photo values
  );

  const existingSet: Set<string> = new Set(
    taskdata?.flatMap((item: any) =>
      item?.photoref?.photos
        .filter((p: any) => !isBase64(p.photo))
        .map((p: any) => p.photo)
    ) || []
  );

  console.log(existingSet, newSet, "existing set ");
  const toDelete: string[] = [...existingSet].filter((img) => !newSet.has(img));
  const toAdd: string[] = [...newSet].filter((img) => !existingSet.has(img));
  console.log(toAdd, "check download images here bro");

  const result = await getImageUrl({ toAdd, dispatch });
};
// to get date in "1 Aug 2001" format
export const extractDateParts = (dateString: string) =>
  ((d) => ({
    day: d.getUTCDate(),
    month: d.getUTCMonth() + 1,
    monthName: d.toLocaleString("en-US", { month: "long" }).slice(0, 3),
    year: d.getUTCFullYear(),
  }))(new Date(dateString));

export const getImagePath = async () => {
  const imgSrc = await window.electron.getImagePath();
  return imgSrc;
};

// export const deleteImage=async(toDelete:string[])=>{
//   const data=await window.electron.deleteImages(toDelete)
// }
//check for image type base64 or not function

export const isBase64 = (str: string) => {
  const result = /^data:image\/(png|jpeg|jpg|gif);base64,/.test(str);
  return result;
};
// to slice data
// function to show sliced data start  by rtn
export const slicedData = (data: string, slicevalue: number) => {
  return data?.length <= slicevalue
    ? data
    : `${data?.substring(0, slicevalue)}...`;
};
export const compressImage = (
  file: File,
  quality: number = 0.6
): Promise<File> => {
  return new Promise((resolve) => {
    requestIdleCallback(async () => {
      const rationImage = await compressImageSize(file, quality);
      console.log("rationImage", rationImage);
      // let blob: Blob;

      // if (file.size <= 1024 * 1024) {
      //   // No compression
      //   blob = file;
      // } else {
      //   // Compress the image
      //   blob = (await imageCompressor(file, quality)) as File;
      // }

      // const newFile = new File([blob], file.name.replace(/\.\w+$/, ".jpeg"), {
      //   type: "image/jpeg",
      //   lastModified: Date.now(),
      // });

      resolve(rationImage);
    });
  });
};

export const reencodeImage = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement("canvas");
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext("2d");
        if (!ctx) return reject(new Error("No canvas context"));

        ctx.drawImage(img, 0, 0);

        // Add a transparent pixel with a random alpha to ensure uniqueness
        const randomAlpha = Math.random() * 0.01; // Tiny invisible change
        ctx.fillStyle = `rgba(0, 0, 0, ${randomAlpha})`;
        ctx.fillRect(canvas.width - 1, canvas.height - 1, 1, 1);

        const dataUrl = canvas.toDataURL(file.type || "image/jpeg", 1.0);
        resolve(dataUrl);
      };

      img.onerror = reject;
      img.src = reader.result as string;
    };

    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

export const compressMultiImage = (
  files: File[],
  quality: number = 0.6
): Promise<File[]> => {
  return Promise.all(
    files.map(
      (file) =>
        new Promise<File>((resolve, reject) => {
          const reader = new FileReader();

          reader.readAsDataURL(file);
          reader.onload = (event) => {
            const img = new Image();
            img.src = event.target?.result as string;

            img.onload = () => {
              const canvas = document.createElement("canvas");
              const ctx = canvas.getContext("2d");

              if (!ctx) {
                return reject(new Error("Canvas context not supported"));
              }

              const maxWidth = 1000; // Set max width
              const maxHeight = 1000; // Set max height
              let { width, height } = img;

              // Scale down the image if necessary
              if (width > maxWidth || height > maxHeight) {
                const ratio = Math.min(maxWidth / width, maxHeight / height);
                width *= ratio;
                height *= ratio;
              }

              canvas.width = width;
              canvas.height = height;

              // Draw the image on the canvas
              ctx.drawImage(img, 0, 0, width, height);

              // Convert to Blob
              canvas.toBlob(
                (blob) => {
                  if (!blob) {
                    return reject(new Error("Image compression failed"));
                  }
                  resolve(new File([blob], file.name, { type: "image/jpeg" }));
                },
                "image/jpeg", // Change to 'image/webp' or 'image/png' if needed
                quality // Compression quality (0 to 1)
              );
            };

            img.onerror = () => reject(new Error("Failed to load image"));
          };

          reader.onerror = () => reject(new Error("Failed to read file"));
        })
    )
  );
};

export const projectFilterImages = async ({
  newdata,
  localDbData,
  dispatch,
}: {
  newdata: ProjectData[];
  localDbData: ProjectData[];
  dispatch: AppDispatch;
}) => {
  console.log("projectfilters image runs");
  console.log(newdata, localDbData, "these are dataaass");
  const newSet: Set<string> = new Set(
    newdata
      ?.map((item: ProjectData) => item?.photo)
      .filter((photo): photo is string => photo !== undefined)
  );
  const existingSet: Set<string> = new Set(
    localDbData
      ?.map((item: ProjectData) => item?.photo)
      .filter((photo): photo is string => photo !== undefined)
  );
  console.log(newSet, "newSet");
  console.log(existingSet, newSet, "existingSet");
  const toAdd: string[] = [...newSet].filter((img) => !existingSet.has(img));

  const result = await getImageUrl({ toAdd, dispatch });
};

//map for check wich page currently open

// pathTableMap.js
export const pathTableMap = {
  "/tools-master": "ToolCategory",
  "/manpower-master": "Manpowercategory",
  "/machinery-master": "MachinaryCategory",
  "/materials-master": "MaterialCategory",
  "/category": "TaskCategory",
  "/department": "Departmentmaster",
  "/billing": "projects",
  "/location": "Towerlocations",
} as const;

export type AppPath = keyof typeof pathTableMap;
export type TableName = (typeof pathTableMap)[AppPath];

// pathTableMap.js
export const pathTableMapNested = {
  "/category": "Taskmaster",
} as const;

export type AppPathNested = keyof typeof pathTableMapNested;
export type TableNameNested = (typeof pathTableMap)[AppPathNested];

export const masterFilterImages = async ({
  newdata,
  localDbData,
  dispatch,
}: {
  newdata: any;
  localDbData: any;
  dispatch: AppDispatch;
}) => {
  console.log(
    "new data",
    newdata
      ?.flatMap((item: any) => item?.images)
      .filter((photo: any): photo is string => photo !== undefined)
  );
  console.log(
    "new data",
    newdata
      ?.flatMap((item: any) => item?.images)
      .filter((photo: any): photo is string => photo !== undefined)
  );
  const newSet: Set<string> = new Set(
    newdata
      ?.flatMap((item: any) => item?.images)
      .filter((photo: any): photo is string => photo !== undefined)
  );
  const existingSet: Set<string> = new Set(
    localDbData
      ?.flatMap((item: any) => item?.images)
      .filter((photo: any): photo is string => photo !== undefined)
  );

  const toAdd: string[] = [...newSet].filter((img) => !existingSet.has(img));
  const result = await getImageUrl({ toAdd, dispatch });
};

export const getFileName = (
  filePath: string | null | undefined | any
): string | null => {
  if (!filePath) return null;
  return filePath?.split("/").pop()?.split("\\").pop() ?? null;
};

const getValueByPath = (obj: any, path: string): any => {
  return path
    .replace(/\?\./g, "") // remove optional chaining
    .replace(/\[(\d+)\]/g, ".$1") // convert [0] to .0
    .split(".")
    .reduce((acc, key) => acc?.[key], obj);
};

export const getFieldColor = (
  key: string,
  formData: any = {},
  initialFormData: any = {}
): string | undefined => {
  const newValue = getValueByPath(formData, key)?.trim?.();
  const oldValue = getValueByPath(initialFormData, key)?.trim?.();

  if (!newValue && oldValue) {
    return "var(--warning_color)";
  } else if (newValue !== oldValue) {
    return "var(--secondary_color)";
  }
  return "var(--text-black-87)";
};
export const compressImageSize = (
  file: File,
  quality: number = 0.6
): Promise<File> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.readAsDataURL(file);
    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target?.result as string;

      img.onload = () => {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        if (!ctx) {
          return reject(new Error("Canvas context not supported"));
        }

        const maxWidth = 800; // Set max width
        const maxHeight = 800; // Set max height
        let { width, height } = img;

        // Scale down the image if necessary
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }

        canvas.width = width;
        canvas.height = height;

        // Draw the image on the canvas
        ctx.drawImage(img, 0, 0, width, height);

        // Convert to Blob
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              return reject(new Error("Image compression failed"));
            }
            resolve(new File([blob], file.name, { type: "image/jpeg" }));
          },
          "image/jpeg", // Change to 'image/webp' or 'image/png' if needed
          quality // Compression quality (0 to 1)
        );
      };

      img.onerror = () => reject(new Error("Failed to load image"));
    };
    reader.onerror = () => reject(new Error("Failed to read file"));
  });
};

type EditDeleteMap = Record<string, ActionCreatorWithPayload<any[], string>>;

export const editDeleteMap: EditDeleteMap = {
  project: SetProjects,
  // TaskCategory:SetCategoryEditDelete,
  // Taskmaster:SetTaskEditDelete
};

// export const CapitalFirstLetter = (str: string): string => {
//   if (!str) return str;
//   return str.charAt(0).toUpperCase() + str.slice(1);
// };

//this is the image extraction logic for the initial backup image download
const extractImages = (item: any, tableName: string): string[] => {
  let result: string[] = [];

  console.log("result for images", item.data);
  //for directly images
  if (tableName === "project") {
    Array.isArray(item.photo)
      ? result.push(...item.photo)
      : result.push(item.photo);
  } else {
    if (Array.isArray(item.images)) {
      result.push(...item.images);
    }
  }

  //for tools which have nested images inside the data
  console.log("result for images1", item.data);
  if (item.consumableCount && Array.isArray(item.data)) {
    console.log("result for images2", item.data);
    for (const subItem of item.data) {
      if (Array.isArray(subItem.images)) {
        result.push(...subItem.images);
      }
    }
  }
  console.log("result for images3", result);
  return result;
};

//this is to download the images for initial backup only by jagroop
export const checkAndDownloadImages = async (
  tableName: string,
  data: any[],
  dispatch: AppDispatch
) => {
  console.log("lets see the data", data, tableName);
  const dbName = await initializeDatabase("ImageStack");
  const imageStackData = await window.electron.allbulkGet({ dbName });
  const imageStack = imageStackData?.docs[0];

  console.log("lets see the data3", imageStack, tableName);

  if (!imageStack?.[tableName]) return;

  const stack = new Set(imageStack[tableName]);

  console.log("lets see the data2", stack, tableName);

  const imagesToDownload: string[] = [];

  for (const item of data) {
    const images = extractImages(item, tableName);
    for (const img of images) {
      if (stack.has(img)) {
        imagesToDownload.push(img);
      }
    }
  }

  console.log("lets see the data4", imagesToDownload, tableName);

  const flushDispatch = customDebounce(() => {
    dispatch(setImageToggle());
  }, 2000);

  await getImageUrlForInitial({
    toAdd: imagesToDownload,
    dispatch,
    onProgress: ({ image, success }) => {
      if (success && stack.has(image)) {
        stack.delete(image);
        flushDispatch();
      }
    },
  });

  flushDispatch.flush();

  if (stack.size === 0) {
    delete imageStack[tableName];
  } else {
    imageStack[tableName] = Array.from(stack);
  }

  console.log("lets see the data5", imageStack);

  saveSyncDataForImageStack(imageStack, "time", "ImageStack");
};

//higher order function to that calls the function only if internet is available by jagroop
export function withInternetCheck<T extends (...args: any[]) => any>(
  fn: T
): (...args: Parameters<T>) => ReturnType<T> | void {
  return function (...args: Parameters<T>): ReturnType<T> | void {
    if (navigator.onLine) {
      return fn(...args);
    } else {
      console.warn("No internet connection. Function not executed.");
    }
  };
}
export const getOnlineURl = async (
  imgName: string,
  dispatch: any
): Promise<string> => {
  const [result] = await Promise.all([
    dispatch(
      TaskMasterApi.endpoints.getImageUrlApi.initiate(imgName, {
        forceRefetch: true,
      })
    ).unwrap(),
  ]);

  return result?.data;
};

export const fileTypeMapper = (file: { name: string; type: string } | null) => {
  const fileType = file?.type;
  console.log("required things delete fileType", file);
  switch (fileType) {
    case "pdf":
      return "Document";
    case "png":
    case "jpg":
    case "jpeg":
      return "Photo";
    case "mp4":
    case "mkv":
      return "Video";
    case "mp3":
      return "Audio";
    default:
      return "File";
  }
};

//download resources function
export const downloadZipFile = async () => {
  try {
    const macAddress = await window.electron.getMacAddress();

    const response = await axios({
      url: "https://www.suryacon.net/development/api/v1/downloaddata/resourcesdownload",
      method: "GET",
      withCredentials: true,
      headers: {
        "device-id": `${macAddress}`,
      },
    });

    const zipUrl = response?.data?.data;
    const newVersion = 2; // You can change or make this dynamic if needed
    const newRole = "hr";

    const storedVersion = localStorage.getItem("resource-version");
    const storedRole = localStorage.getItem("resource-role");

    const shouldDownload =
      storedVersion !== `${newVersion}` || storedRole !== newRole;

    if (shouldDownload) {
      //make ui in base app for downloading resources and navigate to ui of base app initially if resource download
      //starting render the main base app
      console.log("Downloading new resources...");

      // Download and store new zip
      const checkFile = await window.electron.getZipFile({
        url: zipUrl,
        versions: newVersion,
        role: newRole,
      });

      console.log(checkFile, "first check this is true or not");
      if (checkFile) {
        localStorage.setItem("resource-version", `${newVersion}`);
        localStorage.setItem("resource-role", newRole);
        console.log("Resources downloaded and saved.");
        //afte success same function use here to change the dist file to main role serve here
      } else {
        console.warn("Failed to download or verify the zip file.");
      }
    } else {
      //here need to update electron app if already download then here use function and change the dist serve
      console.log("Resources are up-to-date. No need to download.");
    }
  } catch (error) {
    console.error("Error downloading the file:", error);
  }
};

export function getValue<T extends Record<string, any>>(
  obj: T,
  key: string,
): any {
  if (!obj) return "";

  const updated = obj.updatedData?.[key];
  const original = obj[key];

  if (typeof updated === "object") {
    const isUpdatedValid = updated !== null && Object.keys(updated).length > 0;
    if (isUpdatedValid) return updated;

    const isOriginalValid =
      original &&
      typeof original === "object" &&
      Object.keys(original).length > 0;
    return isOriginalValid ? original : null;
  }

  // If updated is not an object, return it if it's not null/undefined
  return updated ?? original ?? "";
}
