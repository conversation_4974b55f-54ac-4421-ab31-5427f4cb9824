import React, { useMemo } from 'react';
import { useSelector } from "react-redux";
import { useParams } from 'react-router-dom';
import { BillingPageNav } from '../../../../../assets/icons';
import ReusableSidebar from '../../../../../components/Common/Sidebar_Sahil';
import { RootState } from '../../../../../redux/store';

interface SidebarProps {
  onRouteChange: (route: string) => void;
}

const Sidebar = ({ onRouteChange }: SidebarProps) => {
  const { projectId } = useParams();
  
  // Get the project information from Redux store
  const selectOpenedProject = useSelector(
    (state: RootState) => state.projectLocalDb?.openedProject
  );
  
  // Define your custom routes with simple identifiers
  const mainRoutes = useMemo(() => [
    { label: 'Summary', route: 'summary', icon: BillingPageNav, section: 'main' as const },
    { label: 'Planning', route: 'location', icon: BillingPageNav, section: 'main' as const },
    { label: 'Actual Work', route: 'actual-work', icon: BillingPageNav, section: 'main' as const },
    { label: 'Monthly Target', route: 'monthly-target', icon: BillingPageNav, section: 'main' as const },
    { label: 'Inventory', route: 'inventory', icon: BillingPageNav, section: 'main' as const },
    { label: 'Petty Contractors', route: 'petty-contractors', icon: BillingPageNav, section: 'main' as const },
    { label: 'Site Billing', route: 'site-billing', icon: BillingPageNav, section: 'main' as const },
  ], []);

  // Static menu items for "Others" section
  const otherRoutes = useMemo(() => [
    { label: 'Settings', icon: BillingPageNav, route: 'settings', section: 'other' as const },
    { label: 'Support', icon: BillingPageNav, route: 'support', section: 'other' as const }
  ], []);

  return (
    <ReusableSidebar
      onRouteChange={onRouteChange}
      mainRoutes={mainRoutes}
      otherRoutes={otherRoutes}
      defaultRoute="summary"
      projectId={projectId}
      baseUrl="/billing/main"
      projectTitle={selectOpenedProject || "Select Project"}
      showProjectSelector={true}
    />
  );
};

export default React.memo(Sidebar);
