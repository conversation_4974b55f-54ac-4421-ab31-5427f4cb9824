// Author Name <PERSON><PERSON><PERSON>

// ======================================= backup indicator and ui reflect to get data again from local db and render to ui ===========================================-====

import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { BackupInterface } from "../../../Interfaces/Modules/Reuseable/Reuseable";

const initialState: BackupInterface = {
  isOpen: false,
  isLocalChange:false,
};

const backupSlice = createSlice({
  name: "backupslice",
  initialState,
  reducers: {
    setBackupChange(state, action: PayloadAction<void>) {
      state.isOpen = !state.isOpen;
    },
    setIsLocalChange(state,action:PayloadAction<boolean>){
      state.isLocalChange=action.payload;
    }
    
  },
});

export const { setBackupChange ,setIsLocalChange} = backupSlice.actions;
export default backupSlice.reducer;
