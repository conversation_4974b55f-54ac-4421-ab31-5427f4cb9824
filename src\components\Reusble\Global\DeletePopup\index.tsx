import React, { useEffect, useRef, useState } from "react";
import Button from "../Button";
import { useAppDispatch } from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { resetInputValues } from "../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { CloseIcon } from "../../../../assets/icons";
import styles from "./Styles/Delete.module.css";
import { DeletePopupProps } from "../GlobalInterfaces/GlobalInterface";
import { useToast } from "../../../../hooks/ToastHook";

export function DeletePopup({
  onClose,
  callbackDelete,
  children,
  header,
  width = "34rem",
  height,
  heightupperlimit = "2rem",
}: DeletePopupProps) {
  const [isClosing, setIsClosing] = useState<boolean>(false);
  const dispatch = useAppDispatch();
  const showToast = useToast();
  console.log("payload-- popup rendered");
  const formHandler = async (e: any) => {
    try {
      e.stopPropagation();
      if (callbackDelete) {
        await callbackDelete(); // This can be a Redux delete action or API call
      }
      setIsClosing(true);
      setTimeout(() => {
        onClose();
        dispatch(resetInputValues());
      }, 400);
    } catch (error) {
      console.error("Oops! Something went wrong", error);
      showToast({
        messageContent: "Oops! Something went wrong!",
        type: "danger",
      });
    }
  };

  const handleClose = (e: any) => {
    e.stopPropagation();
    setIsClosing(true);
    setTimeout(() => {
      onClose();
    }, 400);
  };

  console.log(children, "children data check bro fully>>>>>>>>>>>>>>");

  const condition = window.innerWidth > 1800;
  console.log("heiight", height);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if(isClosing) return;
    if (e.key === "Enter" && e.shiftKey) return;
    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      formHandler(e);
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      handleClose(e);
    }
  };
  const formRef = useRef(null);
  const scrollRef = useRef(null);

  useEffect(() => {
    if (formRef.current) {
      formRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTop = 0; // Scroll to top
    }
  }, []);

  // const fo

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        formRef.current &&
        !(formRef.current as HTMLElement).contains(event.target as Node)
      ) {
        handleClose(event);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  return (
    <div className={styles.deletePopupOverlay}>
     <div
      ref={formRef}
      className={`${styles.DeletePopup_container} ${
        isClosing ? styles.closing : ""
      }`}
      onClick={(e) => e.stopPropagation()}
      style={{ width: width }}
      tabIndex={0}
      onKeyDown={handleKeyDown}
    >
      <div className={styles.deletePopup_header}>
        <h3 className={styles.delete_popup_header}>{header}</h3>
        <button className={styles.closeButton} onClick={(e) => handleClose(e)}>
          <CloseIcon />
        </button>
      </div>

      <div
        style={{
          height:
            height && condition
              ? height
              : height && !condition
              ? `calc(${height} + ${heightupperlimit})`
              : undefined,
        }}
        ref={scrollRef}
        className={styles.deletePopup_datainputs}
      >
        {children}
        <input
          autoFocus
          readOnly
          style={{ width: "0px", height: "0px", opacity: 0, display: "block" }}
        ></input>
      </div>

      <div className={styles.deletePopup_btngrp}>
        <>
          <Button
            type="Cancel"
            Content="Cancel"
            Callback={(e) => handleClose(e)}
          />
          <Button type="Delete" Content="Delete" Callback={formHandler} />
        </>
      </div>
     </div>
    </div>
  );
}
