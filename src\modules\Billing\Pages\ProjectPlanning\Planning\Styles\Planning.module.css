.projectPlanning_container {
  padding: 0rem;
  display: flex;
  width: 100%;
  overflow: hidden;
}

.planning_Table_OuterMostContainer {
  width: 100%;
  border-radius: 36px;
  margin-right: 3px;
}
.planning_Table_OuterMostContainer::-webkit-scrollbar {
  width: 4px;
}

.planning_Table_OuterMostContainer::-webkit-scrollbar-track {
  background-color: transparent;  
  border-radius: 10px;
}

.planning_Table_OuterMostContainer::-webkit-scrollbar-thumb {
  background-color: var(--primary_color);
  border-radius: 10px;
}




@media (max-width: 1200px) {
  .projectPlanning_container {
    /* overflow-x: auto; */
    flex-wrap: nowrap;
  }

  .planning_progress_bar,
  .planning_Table_OuterMostContainer {
    flex-shrink: 0;
    /* min-width: 300px; */
    width: 300px;
  }
}
