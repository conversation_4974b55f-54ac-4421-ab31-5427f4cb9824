import React from "react";
import styles from "./Styles/RadioBtns.module.css";
import { RadioGroupProps } from "../GlobalInterfaces/GlobalInterface";

const RadioBtns: React.FC<RadioGroupProps> = ({
  id,
  name, // Add this prop to make `name` unique
  options = [],
  errors = false,
  selectedValue,
  onValueChange,
  error = false,
}) => {
  return (
    <div id={id} className={`${styles.radioGroup}`}>
      {options.map((option) => (
        <div
          key={option.value}
          className={`${styles.radioButton} ${errors ? styles.error : ""}`}
          style={{
            border:
              selectedValue === option.value
                ? "1px solid var(--primary_color)"
                : error && !selectedValue
                ? "1px solid var(--warning_color)"
                : "1px solid #00000047",
          }}
        >
          <input
            type="radio"
            name={name} // Ensure unique name per instance
            id={`${id}-${option.value}`} // Ensure unique ID
            value={option.value}
            checked={selectedValue === option.value}
            className={styles.changeColor}
            onChange={() => onValueChange(option.value)}
          />
          <label htmlFor={`${id}-${option.value}`}>{option?.label}</label>
        </div>
      ))}
    </div>
  );
};

export default RadioBtns;
