import {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
  useRef,
} from "react";
import { io, Socket } from "socket.io-client";
import { useAuth } from "./AuthProvider";
import { env_type, ip_address } from "./config/urls";

interface ISocketContext {
  socket: Socket | null;
  isConnected: boolean;
  disconnect: () => void;
}

const SocketContext = createContext<ISocketContext | undefined>(undefined);

export const useSocket = (): ISocketContext => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error("useSocket must be used within a SocketProvider");
  }
  return context;
};

// ✅ GLOBAL SOCKET BLOCK FLAG
let GLOBAL_SOCKET_BLOCK = false;
export const setGlobalSocketBlock = (value: boolean) => {
  GLOBAL_SOCKET_BLOCK = value;
  console.log("🔌 GLOBAL_SOCKET_BLOCK:", GLOBAL_SOCKET_BLOCK);
};

interface SocketProviderProps {
  children: ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const { isAuthenticated } = useAuth();

  const socketRef = useRef<Socket | null>(null);

  const connectSocket = () => {
    if (GLOBAL_SOCKET_BLOCK) {
      console.log("❌ Socket blocked globally. Not connecting.");
      return;
    }

    const instance = io(
      env_type === "main"
        ? `https://www.ayrusnoc.com`
        : env_type === "local"
        ? `http://192.168.1.${ip_address}:3000`
        : `https://www.suryacon.net`,
      {
        transports: ["websocket"],
        reconnection: true,
        reconnectionAttempts: 10,
        reconnectionDelay: 1000,
        withCredentials: true,
        ...(env_type !== "main" && env_type !== "local"
          ? { path: `/${env_type}socket` }
          : {}),
      }
    );

    instance.on("connect", () => {
      setIsConnected(true);
      console.log("✅ Socket connected");
    });

    instance.on("disconnect", () => {
      setIsConnected(false);
      console.log("🛑 Socket disconnected");
    });

    socketRef.current = instance;
    setSocket(instance);
  };

  const disconnectSocket = () => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
      setSocket(null);
      setIsConnected(false);
      console.log("🧹 Socket manually disconnected");
    }
  };

  useEffect(() => {
    if (!isAuthenticated || GLOBAL_SOCKET_BLOCK) {
      disconnectSocket();
      return;
    }

    if (navigator.onLine) {
      connectSocket();
    }

    const handleOnline = () => {
      if (isAuthenticated && !GLOBAL_SOCKET_BLOCK) connectSocket();
    };

    window.addEventListener("online", handleOnline);
    return () => window.removeEventListener("online", handleOnline);
  }, [isAuthenticated]);

  useEffect(() => {
    const interval = setInterval(() => {
      if (
        isAuthenticated &&
        navigator.onLine &&
        socketRef.current &&
        !socketRef.current.connected &&
        !GLOBAL_SOCKET_BLOCK
      ) {
        console.log("🔁 Retrying socket connect...");
        socketRef.current.connect();
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [isAuthenticated]);

  return (
    <SocketContext.Provider
      value={{
        socket,
        isConnected,
        disconnect: disconnectSocket,
      }}
    >
      {children}
    </SocketContext.Provider>
  );
};
