// add project from made by rat<PERSON><PERSON><PERSON> singh from start

import {
  Attachment,
  AttachmentIcon,
  AudioIcon,
  CloseIcon,
  DateIcon,
  DeleteIcon,
  DropDownArrowUpIcon,
  DropDownCategoryIcon,
  ImageIcon,
  RedCross,
  ReverseArrow,
  Cross,
  VideoIcon,
} from "../../../../../../assets/icons";
import Button from "../../../../../../components/Reusble/Global/Button";
import FloatingLabelInput from "../../../../../../components/Reusble/Global/FloatingLabel";
import styles from "../AddProjectForm/Styles/AddProjectForm.module.css";
import { FC, useCallback, useEffect, useRef, useState } from "react";
import { ProjectData } from "./Interfaces/interface";
import UnitPopup from "../../../../../../components/Reusble/Global/UnitPopup";
import CalendarComp from "../../../../../../components/Reusble/Global/Calender";
import { useDispatch, useSelector } from "react-redux";
import { closePopup } from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import { resetInputValues } from "../../../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { RootState } from "../../../../../../redux/store";
import {
  compressImage,
  extractDateParts,
  getFileName,
  slicedData,
} from "../../../../../../functions/functions";
import { useToast } from "../../../../../../hooks/ToastHook";
import { project } from "../../../../../../Backup/BillingBackup";

interface AddProjectFormProps {
  onsubmit: (projectData: ProjectData) => void;
  onClose: () => void;
}

export const AddProjectForm: FC<AddProjectFormProps> = ({
  onsubmit,
  onClose,
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const selectedProjectData = useSelector(
    (state: RootState) => state?.projectLocalDb?.selectedProjectIddata
  );
  console.log(selectedProjectData, "this is selectedproject for editing");
  const [fileLoader, setFileLoader] = useState(false);
  const [drawingsLoader, setDrawingsLoader] = useState(false);

  const [projectData, setProjectData] = useState<ProjectData>(
    selectedProjectData?._id
      ? {
          ...selectedProjectData,
          // clientName: selectedProjectData?.client_id?.clientName,
          // ClientPhoneNumber: selectedProjectData?.client_id?.ClientPhoneNumber,
          // photo: selectedProjectData?.photo,
          // client_id: selectedProjectData?.client_id?._id,
        }
      : {
          name: "",
          photo: undefined,
          Address: "",
          project_type: "",
          estimate_budget: "",
          project_area: "",
          rate_type: "",
          rate: "",
          project_duration: "",
          project_drawing: [],

          clientName: "",
          client: {
            _id: "",
            clientName: "",
            ClientPhoneNumber: "",
          },
          ClientPhoneNumber: "",
          Remarks: "",
          project_start_date: "",
          project_completes: "",
        }
  );
  console.log(projectData, "this is selectedproject for editing>>");
  const [file, setFile] = useState<{ name: string; type: string } | null>(
    projectData?.photo
      ? {
          name:
            projectData?.photo instanceof File
              ? projectData?.photo.name
              : projectData?.photo.split("/").pop() || "",
          type:
            projectData?.photo instanceof File
              ? projectData?.photo.type.split("/").pop() || ""
              : "",
        }
      : null
  );

  console.log("file>>", projectData?.photo);

  console.log(
    "photo>>",
    projectData?.photo,
    "type",
    projectData?.photo instanceof File
  );
  const [drawings, setDrawings] = useState<{ name: string; type: string }[]>(
    projectData?.project_drawing instanceof Array
      ? projectData?.project_drawing.map((drawing) => ({
          name: drawing instanceof File ? drawing.name : String(drawing),
          type:
            drawing instanceof File
              ? drawing.type.split("/").pop()?.toLowerCase() || ""
              : "unknown",
        }))
      : []
  );
  // handleinput for change for change values
  // const handleInputChange = (key: string, data: any) => {
  //   const trimmedData = typeof data === "string" ? data.trim() : data;

  //   setProjectData({
  //     ...projectData,
  //     [key]: trimmedData,
  //   });
  //   setinvalidErrors(invalidErrors?.filter((e) => e !== key));
  //   setpopups((prev) => prev.filter((e) => e !== "RateType"));
  // };

  const handleInputChange = (key: string, data: any) => {
    // Block inputs that start with space
    if (typeof data === "string" && data.startsWith(" ")) return;
    // Block inputs that start with space
    if (typeof data === "string" && data.startsWith(" ")) return;

    const trimmedData = typeof data === "string" ? data.trim() : data;

    setProjectData({
      ...projectData,
      [key]: trimmedData,
    });

    setinvalidErrors(invalidErrors?.filter((e) => e !== key));
    setpopups((prev) => prev.filter((e) => e !== "RateType"));
  };

  const [popups, setpopups] = useState<string[]>([]);
  const formRef = useRef<HTMLDivElement>(null);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const drawingsInputRef = useRef<HTMLInputElement>(null);
  const handleIconClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }

 
  };

  console.log(currentStep, "this is selected project id data");
  const dispatch = useDispatch();

  const handleDrawingClick = () => {
    if (drawingsInputRef.current) {
      drawingsInputRef.current.click();
    }
  };

  dispatch(resetInputValues());

  const currentFileState = useRef(true);

const handleFileChange = async (
  event: React.ChangeEvent<HTMLInputElement>
) => {
  const fileInput = event.target;
  const file = fileInput.files ? fileInput.files[0] : null;

  setFile(null);

  if (file) {
    try {
      setFileLoader(true);
      const selectedFile = await compressImage(file, 0.2);
      console.log(selectedFile, "this is compressed file");

      if (currentFileState.current) {
        setFile({
          name: selectedFile.name,
          type: selectedFile.type.split("/").pop() || "",
        });
        handleInputChange("photo", selectedFile);
        setinvalidErrors((prev) => prev.filter((e) => e !== "photo"));
      }

      fileInput.value = "";
    } catch (error) {
      console.error("Error compressing image:", error);
      showToast({
        messageContent: "Error processing image",
        type: "error",
      });
    } finally {
      setFileLoader(false);
    }
  }
};


const handleDrawingsChange = async (
  event: React.ChangeEvent<HTMLInputElement>
) => {
  const fileInput = event.target;
  const files = fileInput.files ? Array.from(fileInput.files) : [];
  if (files.length === 0) return;

  try {
    setDrawingsLoader(true);

    const processedFiles: File[] = [];

    for (const file of files) {
      if (file.type.startsWith("image/")) {
        const compressedFile = await compressImage(file, 0.2);
        processedFiles.push(compressedFile);
      } else {
        processedFiles.push(file);
      }
    }

    handleInputChange("project_drawing", [
      ...(projectData?.project_drawing || []),
      ...processedFiles,
    ]);

    if (drawingsInputRef.current) {
      drawingsInputRef.current.value = "";
    }
    fileInput.value = "";
  } catch (error) {
    console.error("Error processing drawings:", error);
    showToast({
      messageContent: "Error processing some files",
      type: "error",
    });
  } finally {
    setDrawingsLoader(false);
  }
};


  const [invalidErrors, setinvalidErrors] = useState<string[]>([]);
  const [showNotAvailable, setShowNotAvailable] = useState(false);
  console.log(projectData, "project data is here jagga bro");
  console.log("invalid error:::", invalidErrors);
  const handleDeleteFile = () => {
    setFile(null);
    handleInputChange("photo", null);
  };
  const handleCancelOrClose = () => {
    // Helper to check if a value is filled
    const isValueFilled = (value: any): boolean => {
      if (typeof value === "string") return value.trim() !== "";
      if (Array.isArray(value)) return value.length > 0;
      if (value instanceof File) return true;
      if (typeof value === "object" && value !== null) {
        return Object.values(value).some(isValueFilled);
      }
      return value !== undefined && value !== null;
    };

    // Only consider fields that are not meta fields
    const hasUnsavedChanges = Object.entries(projectData)
      .filter(
        ([key]) => !["_id", "isDeleted", "createdAt", "updatedAt"].includes(key)
      )
      .some(([_, value]) => isValueFilled(value));

    // If all fields are empty, close directly
    if (!hasUnsavedChanges) {
      handleClose();
      return;
    }
    // If editing and no changes, close directly
    if (
      projectData?._id &&
      JSON.stringify(selectedProjectData) === JSON.stringify(projectData)
    ) {
      handleClose();
      return;
    }
    // Otherwise, show discard/summary page
    setCurrentStep(3);
  };

  const showToast = useToast();
  const calendarDropdownRef = useRef<HTMLDivElement>(null);
  const rateDropdownRef = useRef<HTMLDivElement>(null);
  const projectTypeDropdownRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      console.log("outside click detected");
      const target = event.target as Node;
      if (
        calendarDropdownRef.current &&
        !calendarDropdownRef.current.contains(target)
      ) {
        setpopups((prev) => prev.filter((e) => e !== "calender"));
      }
      if (
        rateDropdownRef.current &&
        !rateDropdownRef.current.contains(target)
      ) {
        setpopups((prev) => prev.filter((e) => e !== "RateType"));
      }
      if (
        projectTypeDropdownRef.current &&
        !projectTypeDropdownRef.current.contains(target)
      ) {
        setpopups((prev) => prev.filter((e) => e !== "projectType"));
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  const [isClosing, setIsClosing] = useState<boolean>(false);
  console.log("iscloseing", isClosing);
  const handleClose = useCallback(() => {
    setIsClosing(true);
    setTimeout(() => dispatch(closePopup("AddProjectForm")), 400);
  }, [isClosing, onClose]);

  const formContainerRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (formRef.current && !formRef.current.contains(event.target as Node)) {
        // Only act if not on discard confirmation step
        if (currentStep !== 3) {
          // Helper to check if a value is filled
          const isValueFilled = (value: any): boolean => {
            if (typeof value === "string") return value.trim() !== "";
            if (Array.isArray(value)) return value.length > 0;
            if (value instanceof File) return true;
            if (typeof value === "object" && value !== null) {
              return Object.values(value).some(isValueFilled);
            }
            return value !== undefined && value !== null;
          };

          // Only consider fields that are not meta fields
          const hasUnsavedChanges = Object.entries(projectData)
            .filter(
              ([key]) =>
                !["_id", "isDeleted", "createdAt", "updatedAt"].includes(key)
            )
            .some(([_, value]) => isValueFilled(value));

          // If all fields are empty, close directly
          if (!hasUnsavedChanges) {
            handleClose();
            return;
          }
          // If editing and no changes, close directly
          if (
            projectData?._id &&
            JSON.stringify(selectedProjectData) === JSON.stringify(projectData)
          ) {
            handleClose();
            return;
          }
          // If there is data, do nothing
          return;
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [currentStep, projectData, selectedProjectData, handleClose]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isClosing) return;
    if (e.key === "Enter" && e.shiftKey) return;

    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();

      if (currentStep == 1) {
        if (projectData.name && projectData.name.length < 5) {
          showToast({
            messageContent: "Project name must be at least 5 characters",
            type: "warning",
          });
          setinvalidErrors((prev) => [
            ...prev.filter((e) => e !== "name"),
            "name",
          ]);
          return;
        }
        if (!projectData.photo) {
          showToast({
            messageContent: "Please upload a project photo",
            type: "warning",
          });
          setinvalidErrors((prev) => [
            ...prev.filter((e) => e !== "photo"),
            "photo",
          ]);
          return;
        }
        const emptyKeys = Object.keys(projectData).filter(
          (key) =>
            key !== "Remarks" &&
            key !== "AssignedEngg" &&
            key !== "client_id" &&
            key !== "project_completes" &&
            key !== "updatedAt" &&
            (projectData[key as keyof ProjectData] === "" ||
              (Array.isArray(projectData[key as keyof ProjectData]) &&
                (typeof projectData[key as keyof ProjectData] === "string" ||
                  Array.isArray(projectData[key as keyof ProjectData])) &&
                (Array.isArray(projectData[key as keyof ProjectData]) ||
                  typeof projectData[key as keyof ProjectData] === "string") &&
                (typeof projectData[key as keyof ProjectData] === "string" ||
                  Array.isArray(projectData[key as keyof ProjectData]) ||
                  typeof projectData[key as keyof ProjectData] === "object") &&
                projectData[key as keyof ProjectData] !== undefined &&
                typeof projectData[key as keyof ProjectData] === "object" &&
                "length" in (projectData[key as keyof ProjectData] as object) &&
                (Array.isArray(projectData[key as keyof ProjectData]) ||
                  typeof projectData[key as keyof ProjectData] === "string") &&
                (projectData[key as keyof ProjectData] as string | any[])
                  ?.length === 0) ||
              projectData[key as keyof ProjectData] === undefined)
        );
        if (
          projectData.ClientPhoneNumber &&
          projectData.ClientPhoneNumber.length > 0 &&
          projectData.ClientPhoneNumber.length < 10
        ) {
          setinvalidErrors([...emptyKeys, "ClientPhoneNumber"]);
          showToast({
            messageContent: "Enter a valid mobile number",
            type: "warning",
          });
          return;
        }
        console.log(emptyKeys, "this is empty keys");
        if (emptyKeys.length > 0) {
          setinvalidErrors(emptyKeys);
          showToast({
            messageContent: "Enter required fields!",
            type: "warning",
          });
        } else {
          setCurrentStep(2);
        }
      }

      if (currentStep == 2) {
        onsubmit(projectData);
      }
      if (currentStep == 3) {
        handleClose();
      }
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      if (currentStep == 2) {
        setCurrentStep(1);
      }
      if (currentStep == 1) {
        handleCancelOrClose();
      }
      if (currentStep == 3) {
        setCurrentStep(1);
      }
    }
  };

  useEffect(() => {
    if (currentStep == 1 || currentStep == 2 || currentStep == 3) {
      if (formRef.current) {
        formRef.current.focus();
      }
    }
  }, [currentStep]);

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = 0;
    }
  }, [currentStep]);

  useEffect(() => {
    if (currentStep === 1) {
      setTimeout(() => {
        const projectNameInput = document.getElementById("ProjectName");
        if (projectNameInput) {
          (projectNameInput as HTMLTextAreaElement).focus();
          projectNameInput.dispatchEvent(new Event("focus"));
        }
      }, 0);
    }
  }, [currentStep]);

  return (
    <>
    <div className={styles.addProject_overlay}>
      <div
        ref={formRef}
        className={`${styles.add_new_projectForm_container}  ${
          isClosing && styles.closing
        } }`}
        onKeyDown={handleKeyDown}
        tabIndex={0}
      >
        <div className={styles.add_new_projectForm_header}>
          <button
            className={styles.closeButton}
            onClick={() => {
              if (currentStep !== 3) {
                handleCancelOrClose();
              } else {
                setCurrentStep(1);
              }
            }}
          >
            <CloseIcon />
          </button>

          <h3
            style={{
              color:
                currentStep == 3
                  ? "var(--warning_color)"
                  : "var(--primary_color)",
            }}
          >
            {currentStep == 1
              ? projectData?._id
                ? "Edit Project"
                : "Add Project"
              : currentStep == 2
              ? projectData?._id
                ? "Are you sure you want to update this project"
                : "Are you sure you want to add this project"
              : ""}
            {currentStep == 3 &&
              "Are you sure you want to discard these changes?"}
          </h3>
        </div>
        {currentStep == 1 && (
          <div ref={containerRef} className={styles.add_new_projectForm_inpts}>
            <h4 style={{ fontSize: "16px", fontFamily: "Nunito" }}>
              Project Details
            </h4>
            <div className={styles.add_new_projectForm_rowOneInputs}>
            <FloatingLabelInput
              width="14rem"
              enterAllowed={false}
              label="Project Name"
              id="ProjectName"
              focusOnInput={true}
              autoFocus={true}
              props="one_line"
              value={projectData?.name}
              placeholder="Project Name"
              isInvalid={invalidErrors?.includes("name") ? true : false}
              onInputChange={(data) => {
                handleInputChange("name", data);
              }}
              onMount={() => {
                const input = document.getElementById("ProjectName");
                if (input) {
                  (input as HTMLTextAreaElement).focus();
                  input.dispatchEvent(new Event("focus"));
                }
              }}
            />
            <div
              style={{
                position: "relative",
                width: "48%",
                marginTop: "0rem",
              }}
            >

    
                <FloatingLabelInput
                  width="14rem"
                  enterAllowed={false}
                  label="Cover Photo"
                  isDisabled={true}
                  id="CoverPhoto"
                  additionalCaseforClippath={true}
                  handledelete={(e) => {
                    e.preventDefault();
                    handleDeleteFile();
                  }}
                  placeholder="Cover Photo"
                  isInvalid={invalidErrors?.includes("photo") ? true : false}
                  onInputChange={() => {}}
                  itemLabels={
                    projectData?.photo
                      ? [
                          projectData?.photo instanceof File
                            ? slicedData(projectData?.photo?.name, 12)
                            : slicedData(projectData?.photo?.split("/")[2], 12),
                        ]
                      : []
                  }
                  iconClick={
                    fileLoader
                      ? () => setFileLoader(false)
                      : () => handleIconClick()
                  }
                  Icon={
                    projectData?.photo instanceof File || projectData.photo
                      ? ReverseArrow : fileLoader ? Cross
                      : AttachmentIcon
                  }
               />

                <input
                  type="file"
                  ref={fileInputRef}
                  style={{ display: "none" }}
                  onChange={handleFileChange}
                  accept="image/jpeg,image/jpg,image/png"
                />
                </div>
              {fileLoader && (
                <div className={styles.progress_bar_container}>
                    <div className={styles.progress_bar}></div>
                  </div>  
                )}


            </div>
            <FloatingLabelInput
              width="14rem"
              enterAllowed={true}
              label="Address"
              id="Address"
              placeholder="Address"
              // props="description_prop"
              props="description_prop"
                props2="description_prop2"

              isInvalid={invalidErrors?.includes("Address") ? true : false}
              value={projectData?.Address}
              onInputChange={(data) => {
                handleInputChange("Address", data);
              }}
            />

            <div ref={projectTypeDropdownRef} style={{ position: "relative" }}>
              <FloatingLabelInput
                width="14rem"
                enterAllowed={false}
                label="Project Type"
                id="Projectype"
                placeholder="Unit"
                isDisabled={true}
                isInvalid={
                  invalidErrors?.includes("project_type") ? true : false
                }
                onInputChange={() => {}}
                // onInputChange={clearUnitError}
                Icon={
                  popups?.includes("projectType")
                    ? DropDownArrowUpIcon
                    : DropDownCategoryIcon
                }
                iconClick={() => {
                  setpopups((prev) =>
                    prev?.includes("projectType")
                      ? prev.filter((e) => e !== "projectType")
                      : [...prev, "projectType"]
                  );
                }}
                value={projectData?.project_type}
              />
              <div style={{ position: "relative" }}>
                {popups?.includes("projectType") && (
                  <UnitPopup
                    alignment="absolute"
                    left="2px"
                    top="-5px"
                    width="99%"
                    property="addprojecttypePopup"
                    data={[
                      { id: "Residential", label: "Residential" },
                      { id: "Commercial", label: "Commercial" },
                      { id: "Industrial", label: "Industrial" },
                      { id: "Hotel", label: "Hotel" },
                      { id: "Hospital", label: "Hospital" },
                      { id: "Development", label: "Development" },
                      { id: "Institutional", label: "Institutional" },
                    ]}
                    onSelect={(unit) => {
                      setpopups((prev) =>
                        prev.filter((e) => e !== "projectType")
                      );
                      handleInputChange("project_type", unit?.label);
                      // handleUnitSelect(unit as { id: number; label: string });
                    }}
                    selectedId={projectData?.project_type}
                  />
                )}
              </div>
            </div>

            <div className={styles.add_new_projectForm_rowOneInputs}>
              <FloatingLabelInput
                width="14rem"
                enterAllowed={false}
                label="Estimated Budget"
                id="EstimatedBudget"
                maxlength={15}
                placeholder="Estimated Budget"
                value={projectData?.estimate_budget}
                isInvalid={
                  invalidErrors?.includes("estimate_budget") ? true : false
                }
                preventEnter={true}
                props="one_line"
                type="numberWithoutDecimal"
                onPaste={(e) => e.preventDefault()}
                onInputChange={(value) => {
                

                  setinvalidErrors((prev) =>
                    prev.filter((e) => e !== "estimate_budget")
                  );
                  handleInputChange("estimate_budget", value);
                }}
              />
              <FloatingLabelInput
                width="14rem"
                enterAllowed={false}
                label="Area"
                id="Area"
                maxlength={15}
                placeholder="Area"
                value={projectData?.project_area}
                isInvalid={
                  invalidErrors?.includes("project_area") ? true : false
                }
                preventEnter={true}
                props="one_line"
                type="twoDigitAfterDecimal"
                onPaste={(e) => e.preventDefault()}
                onInputChange={(value) => {
                  

                  setinvalidErrors((prev) =>
                    prev.filter((e) => e !== "project_area")
                  );
                  handleInputChange("project_area", value);
                }}
              />
            </div>
            <div className={styles.add_new_projectForm_rowOneInputs}>
              <div ref={rateDropdownRef} style={{ position: "relative" }}>
                <FloatingLabelInput
                  width="14rem"
                  enterAllowed={false}
                  label="Rate Type"
                  id="RateType"
                  placeholder="Unit"
                  isDisabled={true}
                  isInvalid={
                    invalidErrors?.includes("rate_type") ? true : false
                  }
                  onInputChange={() => {}}
                  // onInputChange={clearUnitError}
                  Icon={
                    popups?.includes("RateType")
                      ? DropDownArrowUpIcon
                      : DropDownCategoryIcon
                  }
                  iconClick={() => {
                    setpopups((prev) =>
                      prev?.includes("RateType")
                        ? prev.filter((e) => e !== "RateType")
                        : [...prev, "RateType"]
                    );
                  }}
                  value={projectData?.rate_type}
                />
              </div>
              <FloatingLabelInput
                width="14rem"
                enterAllowed={false}
                label="Rate"
                id="Rate"
                maxlength={12}
                placeholder="Rate"
                value={projectData?.rate}
                isInvalid={invalidErrors?.includes("rate") ? true : false}
                preventEnter={true}
                props="one_line"
                type="twoDigitAfterDecimal"
                onPaste={(e) => e.preventDefault()}
                onInputChange={(value) => {
                  setinvalidErrors((prev) => prev.filter((e) => e !== "rate"));
                  handleInputChange("rate", value);
                }}
              />
              <>
                {popups?.includes("RateType") && (
                  <>
                    <div
                      ref={rateDropdownRef}
                      className={`${styles.ratePopup}`}
                    >
                      <div>
                        <div className={`${styles.ratePopupinner}`}>
                          <div>
                            <input
                              onChange={(
                                e: React.ChangeEvent<HTMLInputElement>
                              ) => {
                                // e.preventDefault()
                                // e.stopPropagation()
                                handleInputChange("rate_type", e.target.value);
                              }}
                              type="radio"
                              name="rate"
                              value="Sq.ft"
                              defaultChecked={projectData?.rate_type == "Sq.ft"}
                              id="1"
                            />
                            <label htmlFor="1"> Sq.ft</label>
                          </div>
                          <div
                            className={`${styles.itemRatecontainer}`}
                            onMouseEnter={() => setShowNotAvailable(true)}
                            onMouseLeave={() => setShowNotAvailable(false)}
                          >
                            <input disabled type="radio" name="rate" id="2" />
                            <label htmlFor="2"> Item-rate</label>
                          </div>
                        </div>

                        {showNotAvailable && (
                          <p className={styles.notAvailable}>
                            Currently not available
                          </p>
                        )}
                      </div>
                    </div>
                  </>
                )}
              </>
            </div>
            <div
              className={styles.add_new_projectForm_rowOneInputs}
              style={{ position: "relative" }}
            >
              <div ref={calendarDropdownRef} style={{ position: "relative" }}>
                <FloatingLabelInput
                  width="14rem"
                  enterAllowed={false}
                  label="Start Date"
                  id="StartDate"
                  placeholder="Start Date"
                  isInvalid={
                    invalidErrors?.includes("project_start_date") ? true : false
                  }
                  value={
                    projectData?.project_start_date != ""
                      ? ((date) =>
                          `${date?.day} ${date?.monthName} ${date?.year}`)(
                          extractDateParts(projectData?.project_start_date)
                        )
                      : ""
                  }
                  onInputChange={() => {}}
                  iconClick={() => setpopups([...popups, "calender"])}
                  isDisabled={true}
                  Icon={DateIcon}
                />
                {popups.includes("calender") && (
                  <CalendarComp
                    initialDate={projectData?.project_start_date}
                    handleselect={(data) => {
                      if (data) {
                        console.log(data, "this is selecteddqate");
                        handleInputChange("project_start_date", data);
                        setpopups((prev) =>
                          prev.filter((e) => e !== "calender")
                        );
                      }
                    }}
                  />
                )}
              </div>

              <div style={{ position: "relative", width: "14.5rem" }}>
                <FloatingLabelInput
                  width="14rem"
                  enterAllowed={false}
                  label="Project Duration"
                  id="ProjectDuration"
                  maxlength={10}
                  placeholder="Project Duration"
                  value={projectData?.project_duration}
                  isInvalid={
                    invalidErrors?.includes("project_duration") ? true : false
                  }
                  preventEnter={true}
                  props="one_line"
                  type="oneDigitAfterDecimal"
                  onPaste={(e) => e.preventDefault()}
                  onInputChange={(value) => {

                    setinvalidErrors((prev) =>
                      prev.filter((e) => e !== "project_duration")
                    );
                    handleInputChange("project_duration", value);
                  }}
                />
                <p className={styles.taskcard_header_leftpara}>Months</p>
              </div>
            </div>
            {/* <FloatingLabelInput
              width="14rem"
              enterAllowed={false}
              label="Drawings"
              id="Drawings"
              placeholder="Drawings"
              isInvalid={
                invalidErrors?.includes("project_drawing") ? true : false
                }
                handledelete={(e, index) => {
                e.preventDefault();
                const allDrawings = projectData?.project_drawing;
                const filteredDrawings = allDrawings?.filter(
                  (_, i) => i !== index
                );

                handleInputChange("project_drawing", filteredDrawings);
              }}
              isDisabled={true}
              onInputChange={() => {}}
              value={undefined}
              iconClick={() => {
                handleDrawingClick();
              }}
              itemLabels={projectData?.project_drawing
                ?.map((e) => {
                  if (typeof e === "object") {
                    return e instanceof File ? e?.name : undefined;
                  }
                  return typeof e === "string"
                    ? (e as string).split("/")[2]
                    : undefined;
                })
                .filter(
                  (e): e is string => e !== undefined && typeof e === "string"
                )}
              Icon={AttachmentIcon}
              /> */}
              <div className={styles.drawings_Wrapper_addproject} >
              <FloatingLabelInput
                // width="14rem"
                enterAllowed={false}
                label="Drawings"
                id="Drawings"
                placeholder="Drawings"
                isInvalid={
                  invalidErrors?.includes("project_drawing") ? true : false
                }
                handledelete={(e, index) => {
                  e.preventDefault();
                  const allDrawings = projectData?.project_drawing;
                  const filteredDrawings = allDrawings?.filter(
                    (_, i) => i !== index
                  );

                  handleInputChange("project_drawing", filteredDrawings);
                }}
                isDisabled={true}
                onInputChange={() => {}}
                value={undefined}
                iconClick={() => {
                  handleDrawingClick();
                }}
                itemLabels={projectData?.project_drawing
                  ?.map((e) => {
                    if (typeof e === "object") {
                      return e instanceof File
                        ? slicedData(e?.name, 10)
                        : undefined;
                    }
                    return typeof e === "string"
                      ? typeof e === "string"
                        ? typeof e === "string"
                          ? typeof e === "string"
                            ? typeof e === "string"
                              ? slicedData(
                                  (e as string).split("/")[2]?.toString() || "",
                                  10
                                )
                              : ""
                            : ""
                          : ""
                        : ""
                      : undefined;
                  })
                  .filter(
                    (e): e is string => e !== undefined && typeof e === "string"
                  )}
                Icon={AttachmentIcon}
              />
              <input
                className={styles.cover_photo}
                type="file"
                ref={drawingsInputRef}
                style={{ display: "none" }}
                onChange={handleDrawingsChange}
                accept="image/*" // Only allows image files 
              />
              {drawingsLoader && (
                <div className={styles.progress_bar_container_drawing}>
                  <div className={styles.progress_bar_drawing}></div>
                </div>
              )}
            </div>
            <h4
              style={{
                marginTop: "24px",
                fontSize: "16px",
                fontFamily: "Nunito",
              }}
            >
              Client Details
            </h4>
            <div className={styles.add_new_projectForm_rowOneInputs}>
              <FloatingLabelInput
                width="14rem"
                enterAllowed={false}
                props="one_line"
                label="Client Name"
                id="clientname"
                placeholder="Rate"
                isInvalid={invalidErrors?.includes("clientName") ? true : false}
                value={projectData?.clientName}
                onInputChange={(data) => {
                  handleInputChange("clientName", data);
                }}
              />

              <FloatingLabelInput
                width="14rem"
                enterAllowed={false}
                label="Mobile Number"
                id="clientnumber"
                maxlength={10}
                placeholder="Enter Mobile Number"
                value={projectData?.ClientPhoneNumber}
                isInvalid={
                  invalidErrors?.includes("ClientPhoneNumber") ? true : false
                }
                preventEnter={true}
                props="one_line"
                type="numberWithoutDecimal"
                onPaste={(e) => e.preventDefault()}
                onInputChange={(value) => {
                

                  setinvalidErrors((prev) =>
                    prev.filter((e) => e !== "ClientPhoneNumber")
                  );
                  handleInputChange("ClientPhoneNumber", value);
                }}
              />
            </div>
            <FloatingLabelInput
              width="14rem"
              enterAllowed={true}
              label="Remarks"
              id="Remarks"
              placeholder="Remarks"
              isInvalid={invalidErrors?.includes("Remarks") ? true : false}
              value={projectData?.Remarks}
              onInputChange={(data) => {
                handleInputChange("Remarks", data);
              }}
            />
          </div>
        )}
        {currentStep == 2 && (
          <div className={styles.add_new_projectForm_inpts}>
            <CategorySummary
              previousdata={selectedProjectData ?? undefined}
              selectedItems={projectData}
            />
          </div>
        )}
        {currentStep == 3 && (
          <div className={styles.add_new_projectForm_inpts}>
            <CategorySummary
              previousdata={selectedProjectData ?? undefined}
              selectedItems={projectData}
            />
          </div>
        )}
        {currentStep == 1 && (
          <div className={styles.add_new_projectForm_btngrp}>
            <Button
              type="Cancel"
              Content="Cancel"
              Callback={handleCancelOrClose}
            />
            <Button
              type="Next"
              Content={projectData?._id ? "Update" : "Add"}
              Callback={() => {
                if (projectData.name && projectData.name.length < 5) {
                  showToast({
                    messageContent:
                      "Project name must be at least 5 characters",
                    type: "warning",
                  });
                  setinvalidErrors((prev) => [
                    ...prev.filter((e) => e !== "name"),
                    "name",
                  ]);
                  return;
                }
                if (!projectData.photo) {
                  showToast({
                    messageContent: "Please upload a project photo",
                    type: "warning",
                  });
                  setinvalidErrors((prev) => [
                    ...prev.filter((e) => e !== "photo"),
                    "photo",
                  ]);
                  return;
                }
                const emptyKeys = Object.keys(projectData).filter(
                  (key) =>
                    key !== "Remarks" &&
                    key !== "AssignedEngg" &&
                    key !== "client_id" &&
                    key !== "project_completes" &&
                    key !== "updatedAt" &&
                    (projectData[key as keyof ProjectData] === "" ||
                      (Array.isArray(projectData[key as keyof ProjectData]) &&
                        (typeof projectData[key as keyof ProjectData] ===
                          "string" ||
                          Array.isArray(
                            projectData[key as keyof ProjectData]
                          )) &&
                        (Array.isArray(projectData[key as keyof ProjectData]) ||
                          typeof projectData[key as keyof ProjectData] ===
                            "string") &&
                        (typeof projectData[key as keyof ProjectData] ===
                          "string" ||
                          Array.isArray(
                            projectData[key as keyof ProjectData]
                          ) ||
                          typeof projectData[key as keyof ProjectData] ===
                            "object") &&
                        projectData[key as keyof ProjectData] !== undefined &&
                        typeof projectData[key as keyof ProjectData] ===
                          "object" &&
                        "length" in
                          (projectData[key as keyof ProjectData] as object) &&
                        (Array.isArray(projectData[key as keyof ProjectData]) ||
                          typeof projectData[key as keyof ProjectData] ===
                            "string") &&
                        (
                          projectData[key as keyof ProjectData] as
                            | string
                            | any[]
                        )?.length === 0) ||
                      projectData[key as keyof ProjectData] === undefined)
                );
                if (
                  projectData.ClientPhoneNumber &&
                  projectData.ClientPhoneNumber.length > 0 &&
                  projectData.ClientPhoneNumber.length < 10
                ) {
                  setinvalidErrors([...emptyKeys, "ClientPhoneNumber"]);
                  showToast({
                    messageContent: "Enter a valid mobile number",
                    type: "warning",
                  });
                  return;
                }

                console.log(emptyKeys, "this is empty keys");
                if (emptyKeys.length > 0) {
                  setinvalidErrors(emptyKeys);

                  showToast({
                    messageContent: "Enter required fields!",
                    type: "warning",
                  });
                } else {
                  setCurrentStep(2);
                }
              }}
            />
          </div>
        )}
        {currentStep == 2 && (
          <div className={styles.add_new_projectForm_btngrp}>
            <Button
              type="Cancel"
              Content="Back"
              Callback={() => {
                setCurrentStep(1);
              }}
            />
            <Button
              type="Next"
              Content="Submit"
              Callback={() => {
                onsubmit(projectData);
              }}
            />
          </div>
        )}
        {currentStep == 3 && (
          <div className={styles.add_new_projectForm_btngrp}>
            <Button
              type="Cancel"
              Content="No"
              Callback={() => {
                setCurrentStep(1);
              }}
            />
            <Button
              type="Next"
              Content="Yes"
              Callback={() => {
                handleClose();
              }}
            />
          </div>
        )}
      </div>
    </div>
    </>
  );
};

const CategorySummary: FC<{
  previousdata?: ProjectData;
  selectedItems: ProjectData;
}> = ({ selectedItems, previousdata }) => {
  console.log(previousdata, selectedItems, "previous data");

  // Check if any client details are filled
  const hasClientDetails =
    selectedItems?.clientName ||
    selectedItems?.ClientPhoneNumber ||
    selectedItems?.Remarks;

  // Check if any project details are filled
  const hasProjectDetails =
    selectedItems?.name ||
    selectedItems?.photo ||
    selectedItems?.Address ||
    selectedItems?.project_type ||
    selectedItems?.estimate_budget ||
    selectedItems?.project_area ||
    selectedItems?.rate_type ||
    selectedItems?.rate ||
    selectedItems?.project_start_date ||
    selectedItems?.project_duration ||
    (selectedItems?.project_drawing &&
      selectedItems?.project_drawing?.length > 0);

  return (
    <>
      {/* Only show Project Details section if any project details are filled */}
      {hasProjectDetails && (
        <>
          <h4>Project Details</h4>
          <div className={styles.summaryDivData}>
            {selectedItems?.name && (
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Project Name
                </p>
                <div
                  className={styles.summaryItems}
                  style={{ color: "var(--text-black-87)" }}
                >
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color:
                        previousdata?.name &&
                        previousdata?.name !== selectedItems?.name
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {slicedData(selectedItems?.name, 12)}
                  </h4>
                </div>
              </div>
            )}
            {(selectedItems?.photo instanceof File
              ? selectedItems?.photo.name
              : selectedItems?.photo) && (
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Cover Photo
                </p>
                <div
                  className={styles.summaryItems}
                  style={{ color: "var(--text-black-87)" }}
                >
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color:
                        previousdata?.photo &&
                        previousdata?.photo !==
                          (selectedItems?.photo instanceof File
                            ? selectedItems?.photo?.name
                            : selectedItems.photo)
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {selectedItems?.photo instanceof File
                      ? slicedData(selectedItems?.photo?.name, 12)
                      : slicedData(
                          selectedItems?.photo?.split("/")[2] ?? "",
                          12
                        )}
                  </h4>
                </div>
              </div>
            )}
          </div>
          <div className={styles.summaryDivData}>
            {selectedItems?.Address && (
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Address
                </p>
                <div
                  className={styles.summaryItems}
                  style={{ color: "var(--text-black-87)" }}
                >
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color:
                        previousdata?.Address &&
                        previousdata?.Address !== selectedItems?.Address
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {slicedData(selectedItems?.Address as string, 50)}
                  </h4>
                </div>
              </div>
            )}
          </div>

          <div className={styles.summaryDivData}>
            {selectedItems?.project_type && (
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Project Type
                </p>
                <div
                  className={styles.summaryItems}
                  style={{ color: "var(--text-black-87)" }}
                >
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color:
                        previousdata?.project_type &&
                        previousdata?.project_type !==
                          selectedItems?.project_type
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {selectedItems?.project_type}
                  </h4>
                </div>
              </div>
            )}
          </div>
          <div className={styles.summaryDivData}>
            {selectedItems?.estimate_budget && (
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Estimated Budget
                </p>
                <div
                  className={styles.summaryItems}
                  style={{ color: "var(--text-black-87)" }}
                >
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color:
                        previousdata?.estimate_budget &&
                        previousdata?.estimate_budget !==
                          selectedItems?.estimate_budget
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {selectedItems?.estimate_budget}
                  </h4>
                </div>
              </div>
            )}
            {selectedItems?.project_area && (
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Area
                </p>
                <div
                  className={styles.summaryItems}
                  style={{ color: "var(--text-black-87)" }}
                >
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color:
                        previousdata?.project_area &&
                        previousdata?.project_area !==
                          selectedItems?.project_area
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {selectedItems?.project_area}
                  </h4>
                </div>
              </div>
            )}
          </div>
          <div className={styles.summaryDivData}>
            {selectedItems?.rate_type && (
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Rate Type
                </p>
                <div
                  className={styles.summaryItems}
                  style={{ color: "var(--text-black-87)" }}
                >
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color:
                        previousdata?.rate_type &&
                        previousdata?.rate_type !== selectedItems?.rate_type
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {selectedItems?.rate_type}
                  </h4>
                </div>
              </div>
            )}
            {selectedItems?.rate && (
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Rate
                </p>
                <div
                  className={styles.summaryItems}
                  style={{ color: "var(--text-black-87)" }}
                >
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color:
                        previousdata?.rate &&
                        previousdata?.rate !== selectedItems?.rate
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {selectedItems?.rate}
                  </h4>
                </div>
              </div>
            )}
          </div>
          <div className={styles.summaryDivData}>
            {selectedItems?.project_start_date && (
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Start date
                </p>
                <div
                  className={styles.summaryItems}
                  style={{ color: "var(--text-black-87)" }}
                >
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color:
                        previousdata?.project_start_date &&
                        previousdata?.project_start_date !==
                          selectedItems?.project_start_date
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {((date) =>
                      `${date?.day} ${date?.monthName} ${date?.year}`)(
                      extractDateParts(selectedItems?.project_start_date)
                    )}
                  </h4>
                </div>
              </div>
            )}
            {selectedItems?.project_duration && (
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Duration
                </p>
                <div
                  className={styles.summaryItems}
                  style={{ color: "var(--text-black-87)" }}
                >
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color:
                        previousdata?.project_duration &&
                        previousdata?.project_duration !==
                          selectedItems?.project_duration
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {selectedItems?.project_duration}
                  </h4>
                </div>
              </div>
            )}
          </div>
          <div className={styles.summaryDivData}>
            {selectedItems?.project_drawing &&
              selectedItems?.project_drawing?.length > 0 && (
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Drawings
                  </p>
                  <div
                    className={styles.summaryItems}
                    style={{ color: "var(--text-black-87)" }}
                  >
                    {/* Render selected project drawings and highlight changes */}
                    {selectedItems?.project_drawing?.map((e, index) => {
                      const currentName = getFileName(
                        e instanceof File ? e.name : e
                      );
                      const previousRaw =
                        previousdata?.project_drawing?.[index];
                      const previousName = getFileName(
                        previousRaw instanceof File
                          ? previousRaw.name
                          : previousRaw
                      );

                      return (
                        <h4
                          key={`selected-project-${index}`}
                          className={styles.summaryItem}
                          style={{
                            paddingRight: "16px",
                            color:
                              previousdata &&
                              previousdata?.project_drawing?.length > 0 &&
                              currentName !== previousName
                                ? "var(--secondary_color)"
                                : "var(--text-black-87)",
                          }}
                        >
                          {slicedData(currentName ?? "", 12)}
                        </h4>
                      );
                    })}

                    {/* Show removed items from previous data in warning color */}
                    {previousdata?.project_drawing
                      ?.filter((prev) => {
                        const prevName = getFileName(
                          prev instanceof File ? prev.name : prev
                        );
                        return !selectedItems?.project_drawing?.some((sel) => {
                          const selName = getFileName(
                            sel instanceof File ? sel.name : sel
                          );
                          return selName === prevName;
                        });
                      })
                      .map((missing, idx) => {
                        const name = getFileName(
                          missing instanceof File ? missing.name : missing
                        );

                        return (
                          <h4
                            key={`missing-project-${idx}`}
                            className={styles.summaryItem}
                            style={{
                              paddingRight: "16px",
                              color: "var(--warning_color)",
                            }}
                          >
                            {slicedData(name ?? "", 12)}
                          </h4>
                        );
                      })}
                  </div>
                </div>
              )}

          </div>
        </>
      )}

      {/* Only show Client Details section if any client details are filled */}
      {hasClientDetails && (
        <>
          <h4>Client Details</h4>
          <div className={styles.summaryDivData}>
            {selectedItems?.clientName && (
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Client Name
                </p>
                <div
                  className={styles.summaryItems}
                  style={{
                    color: "var(--text-black-87)",
                    marginTop: "0.3rem",
                    textTransform: "capitalize",
                  }}
                >
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color:
                        previousdata?.clientName &&
                        selectedItems?.clientName !== previousdata?.clientName
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {slicedData(selectedItems?.clientName as string, 12)}
                  </h4>
                </div>
              </div>
            )}
            {selectedItems?.ClientPhoneNumber && (
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  {"Mobile Number"}
                </p>
                <div
                  className={styles.summaryItems}
                  style={{ color: "var(--text-black-87)" }}
                >
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color:
                        previousdata?.ClientPhoneNumber &&
                        selectedItems?.ClientPhoneNumber !==
                          previousdata?.ClientPhoneNumber
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {selectedItems?.ClientPhoneNumber}
                  </h4>
                </div>
              </div>
            )}
          </div>
          <div className={styles.summaryDivData}>
            {selectedItems?.Remarks && (
              <div className={styles.summaryDataContent}>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Remarks
                </p>
                <div
                  className={styles.summaryItems}
                  style={{ color: "var(--text-black-87)" }}
                >
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color:
                        previousdata?.Remarks === null ||
                        previousdata?.Remarks === undefined ||
                        previousdata?.Remarks?.trim() === "" ||
                        selectedItems?.Remarks !== previousdata?.Remarks
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {selectedItems?.Remarks}
                  </h4>
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </>
  );
};
