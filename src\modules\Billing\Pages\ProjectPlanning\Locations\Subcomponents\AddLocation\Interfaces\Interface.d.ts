export interface TowerLocationData {
  category?: string;
  _id?: string;
  name: string;
  location_drawing: string[] | File[];
  project_id: string;
  area: string;
  number_of_floors: string;
  number_of_basements: string;
  location_duration: string;
  structure_type: string;
  conventionals: { _id: string; name: string }[]; // the actual type of conventional is array of string we are using this type because we are opening another form for selecting conventioanl floors that where we need id
  mivan: { _id: string; name: string }[]; //the actual type of miavn is array of string we are using this type because we are opening another form for selecting conventioanl floors that where we need id
  remarks: string;
}
