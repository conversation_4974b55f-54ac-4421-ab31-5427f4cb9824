/* Author name: CHARVI */

.notification_container {
  border: 1px solid gray;
  width: 26.625rem;
  min-height: 18.125rem;
  border-radius: 28px;
  backdrop-filter: blur(100px);
  position: absolute;
  right: 0;
}

.notification_container h3 {
  text-align: center;
  padding-top: 2rem;
  color: var(--primary_color);
}

.notification_toggleswitch {
  width: 20rem;
  position: relative;
  display: flex;
  padding: 0;
  background: var(--main_background);
  line-height: 2.5rem;
  border-radius: 24px;
  margin-top: 1.2rem;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0px 0px 6px 0px #00000026;
}

.notification_toggleswitch input {
  visibility: hidden;
  position: absolute;
  top: 0;
}

.notification_toggleswitch label {
  width: 50%;
  padding: 0;
  margin: 0;
  text-align: center;
  cursor: pointer;
  color: var(--text-black-87);

}

.notification_switch_wrapper {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 50%;
  z-index: 3;
  transition: transform 0.5s cubic-bezier(0.77, 0, 0.175, 1);
}

.notification_switch {
  border-radius: 3rem;
  background: var(--primary_color);
  height: 100%;
  box-shadow: 0px 0px 6px 0px #00000026;
}

.notification_switch div {
  width: 100%;
  text-align: center;
  opacity: 0;
  display: block;

  transition: opacity 0.2s cubic-bezier(0.77, 0, 0.175, 1) 0.125s;
  will-change: opacity;
  position: absolute;
  top: 0;
  left: 0;
  color: var(--text-white-100);
}

.notification_toggleswitch input:nth-of-type(1):checked~.notification_switch_wrapper {
  transform: translateX(0%);
}

.notification_toggleswitch input:nth-of-type(2):checked~.notification_switch_wrapper {
  transform: translateX(100%);
}

.notification_toggleswitch input:nth-of-type(1):checked~.notification_switch_wrapper .notification_switch div:nth-of-type(1) {
  opacity: 1;
}

.notification_toggleswitch input:nth-of-type(2):checked~.notification_switch_wrapper .notification_switch div:nth-of-type(2) {
  opacity: 1;
}

.notification_toasts {
  display: flex;
  justify-content: space-between;
  gap: 1rem;

  margin: 1.5rem 1rem 1rem 1rem;
  border-radius: 20px;
  box-shadow: 0px 0px 6px 0px #00000026;
  padding: 1.2rem;
  background-color: #fff;
  width: 100%;
  max-width: 24.5rem;
  min-height: 7rem;
  position: relative;
}

.notification_toastIcon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background-color: #ededed;
  border-radius: 50%;
}

.notification_toastDetails {
  flex-grow: 1;
  min-width: 0;
}

.notification_toastMessage {
  color: var(--text-black-60);
  margin: 0.3rem 2rem 0 0;
  word-wrap: break-word;
  line-height: 1.1rem;
}

.notification_statusDiv {
  min-width: 8px;
  max-height: 8px;
  border-radius: 20px;
  background-color: var(--primary_color);
}

.notification_time {}

.notification_timestamp {
  position: absolute;
  bottom: 5px;
  right: 1rem;

  color: var(--text-black-60);
}