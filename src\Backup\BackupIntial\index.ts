import axios from "axios";
import { url } from "../../config/urls";
import { initializeDatabase, masterFilterImages } from "../../functions/functions";
import { saveSyncData } from "../BackupFunctions/BackupFunctions";
import { TriggerEventData } from "../../redux/features/Modules/TaskMaster/Slices/TriggerEventSlice";

// export const syncTableWithProgress = async (table: string,dispatch:any) => {
//   const tableName = table || [];

//   for (const keyName of table) {
//     const res = await fetch(
//       `${url}/downloaddata/getlatestsyncbackupdata?tablename=${keyName}`
//     );
//     const reader = res.body.getReader();
//     const decoder = new TextDecoder("utf-8");

//     let buffer = "";
//     let total = 0;
//     let received = 0;

//     while (true) {
//       const { done, value } = await reader.read();
//       if (done) break;

//       buffer += decoder.decode(value, { stream: true });
//       const lines = buffer.split("\n");
//       buffer = lines.pop();

//       for (let line of lines) {
//         if (!line.trim()) continue;

//         const parsed = JSON.parse(line);

//         if (parsed.meta?.total) {
//           total = parsed.meta.total;
//           //   onProgress(0);
//           continue;
//         }

//         if (parsed.data) {
//             console.log(parsed,'parsed')
//           saveSyncData(parsed.data, parsed.time, keyName, false,dispatch);
//           received += parsed.data.length;
//           //   if (total > 0) onProgress(Math.min(100, Math.floor((received / total) * 100)));
//         }
//       }
//     }

//     const index = tableName.indexOf(keyName);
//     if (index !== -1) {
//       tableName.splice(index, 1);
//       localStorage.setItem("backuptable", JSON.stringify(tableName));
//       const newEvent=new Event("backuptable");
//       dispatchEvent(newEvent)
//     }
//     // onProgress(100); // complete
//   }
// };

const setHeadersWithDeviceId = async () => {
  const headers = new Headers();
  const macAddress = await window.electron.getMacAddress();

  if (macAddress) {
    headers.set("device-id", macAddress);
  }

  return headers;
};

// export const syncTableWithProgress = async (dispatch: any) => {
//   const tableList = JSON.parse(
//     localStorage.getItem("backuptable") || "[]"
//   ) as string[];
//   const headers = await setHeadersWithDeviceId();
//   for (const keyName of tableList) {
//     const res1 = await axios.get(
//       `${url}/downloaddata/getlatestsyncbackupdata?tablename=${keyName}`,
//       {
//         headers: {
//           "device-id": headers.get("device-id"),
//           "Content-Type": "application/json",
//         },
//         withCredentials: true, // This includes cookies and auth credentials
//       }
//     );

//     if (!res1.data.signedUrl) {
//       return;
//     }

//     const res = await fetch(res1.data.signedUrl, {
//       method: "GET",
//       headers,
//       credentials: "include",
//     });

//     //majboori me client ko agr dikhana pda to
// const res = await fetch(
//   `${url}/downloaddata/getlatestsyncbackupdata?tablename=${keyName}`,
//   {
//     method: "GET",
//     headers,
//     credentials: "include",
//   }
// );

//     console.log("response sign-url", res, res.body);
//     const reader = res?.body.getReader();
//     const decoder = new TextDecoder("utf-8");

//     let buffer = "";
//     let total = 0;
//     let received = 0;

//     while (true) {
//       const { done, value } = await reader.read();
//       if (done) break;

//       buffer += decoder.decode(value, { stream: true });
//       const lines = buffer.split("\n");
//       buffer = lines.pop() || "";

//       for (let line of lines) {
//         if (!line.trim()) continue;
//         const parsed = JSON.parse(line);

//         if (parsed.meta?.total) {
//           total = parsed.meta.total;
//           continue;
//         }

//         if (parsed.data) {
//           if (parsed.data[0].images) {
//             await masterFilterImages({
//               newdata: parsed.data,
//               localDbData: [],
//               dispatch: dispatch,
//             });
//           }

//           if (parsed.data[0].consumableCount) {
//             await masterFilterImages({
//               newdata: parsed.data?.flatMap((item: any) => item?.data),
//               localDbData: [],
//               dispatch: dispatch,
//             });
//           }

//           saveSyncData(parsed.data, parsed.time, keyName, false, dispatch);
//           received += parsed.data.length;
//         }
//       }
//     }

//     const updatedTables = JSON.parse(
//       localStorage.getItem("backuptable") || "[]"
//     ) as string[];
//     const index = updatedTables.indexOf(keyName);
//     if (index !== -1) {
//       updatedTables.splice(index, 1);
//       localStorage.setItem("backuptable", JSON.stringify(updatedTables));
//       dispatchEvent(new Event("backuptable"));
//     }
//   }
// };

export const syncTableWithProgress = async (dispatch: any) => {
  const tableList = JSON.parse(
    localStorage.getItem("backuptable") || "[]"
  ) as string[];
  const headers = await setHeadersWithDeviceId();

  const imageStack = new Map<string, string[]>();

  for (const keyName of tableList) {
    //ONLY FOR MAIN PRODUCTION
    // const res1 = await axios.get(
    //   `${url}/downloaddata/getlatestsyncbackupdata?tablename=${keyName}`,
    //   {
    //     headers: {
    //       "device-id": headers.get("device-id"),
    //       "Content-Type": "application/json",
    //     },
    //     withCredentials: true, // This includes cookies and auth credentials
    //   }
    // );

    // console.log("response>>>>>> ", res1);
    // if (!res1.data.data.signedUrl) {
    //   return;
    // }

    // const res = await fetch(res1.data.data.signedUrl, {
    //   method: "GET",
    //   headers,
    //   credentials: "include",
    // });

    //LOGIC FOR DEVELOPMENT / TESTING AND TRAINING ONLY
    const res = await fetch(
      `${url}/downloaddata/getlatestsyncbackupdata?tablename=${keyName}`,
      {
        method: "GET",
        headers,
        credentials: "include",
      }
    );

    console.log("response>>>>>>>>2", res);

    const reader = res?.body.getReader();
    const decoder = new TextDecoder("utf-8");

    let buffer = "";
    let total = 0;
    let received = 0;

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split("\n");
      buffer = lines.pop() || "";

      for (let line of lines) {
        if (!line.trim()) continue;
        const parsed = JSON.parse(line);

        if (parsed.meta?.total) {
          total = parsed.meta.total;
          continue;
        }

        if (parsed.data) {
          if (!imageStack.has(keyName)) {
            imageStack.set(keyName, []);
          }

          const stack = imageStack.get(keyName)!;

          for (const item of parsed.data) {
            console.log(
              "parsed data>>>>>>>>>>>>>>",
              item,
              item.counts,
              keyName
            );

            if (keyName === "project") {
              item?.projectDetails?.forEach((pitem: any) => {
                Array.isArray(pitem.photo)
                  ? stack.unshift(...pitem.photo)
                  : stack.unshift(pitem.photo);
              });
            } else {
              if (Array.isArray(item.images)) {
                stack.unshift(...item.images);
              }
            }

            if (item.consumableCount && Array.isArray(item.data)) {
              for (const subItem of item.data) {
                if (Array.isArray(subItem.images)) {
                  stack.unshift(...subItem.images);
                }
              }
            }
          }

          let data: any = [];

          for (const item of parsed.data) {
            console.log("itemmm to he", item);
            if (keyName === "Taskmaster_Form") {
              const formattedTaskData: any = {
                id: item?._id || "",
                _id: item?._id || "",
                name: item?.name || "",
                Unit: item?.Unit || "",
                Description: item?.Description || "",
                subtaskWeighatages: item?.Quantity || 0,
                Tracking: item?.isCompleted ? "Completed" : "Pending",
                DepartmentId: item?.departmentId || [],
                DesignationId: item?.designationId || [],
                MaterialId: item?.materialId || [],
                ToolId: item?.toolId || [],
                MachinaryId: item?.machinaryId || [],
                ManpowerId: item?.manpowerId || [],
                Adminid: item?.Adminid,
                ReporterId: {
                  Reporter:
                    item?.ReporterId?.Reporter?.[0]?.designationId?.length > 0
                      ? item?.ReporterId?.Reporter
                      : [],
                },
                AssigneeId: item.Assignee || [],
                Subtaskdetails: item?.SubtaskId,
                MethodId: {
                  ...item?.MethodId,
                  work_instruction_id:
                    item?.MethodId?.work_instruction_id
                      ?.filter(
                        (methodItem: any) =>
                          methodItem?.Description &&
                          methodItem?.optionselected &&
                          methodItem.Description.trim() !== "" &&
                          methodItem.optionselected.trim() !== ""
                      )
                      ?.map((methodItem: any) => ({
                        photoref: {
                          photos: !Array.isArray(methodItem.photoRef)
                            ? []
                            : (methodItem.photoRef || []).map((photo: any) => ({
                                photo: photo?.photos,
                                details: photo?.Decription || "",
                              })),
                        },
                        _id: methodItem._id || "",
                        Description: methodItem.Description || "",
                        optionselected: methodItem.optionselected || "",
                        materialId: methodItem.materialId || [],
                        manpowerId: methodItem.manpowerId || [],
                        toolsId: methodItem.toolsId || [],
                        machinaryId: methodItem.machinaryId || [],
                      })) || [],
                  task_closing_requirement:
                    item?.MethodId?.task_closing_requirement?.map(
                      (taskItem: any) => ({
                        photoref: {
                          photos:
                            typeof taskItem.photoRef === "object"
                              ? []
                              : (taskItem.photoRef || []).map((photo: any) => ({
                                  photo: photo?.photos,
                                  details: photo?.Decription || "",
                                })),
                        },
                        _id: taskItem._id || "",
                        Description: taskItem.Description || "",
                        optionselected: taskItem.optionselected || "",
                      })
                    ) || [],
                },
              };
              data.push(formattedTaskData);
            } else if (keyName === "Subtaskmaster") {
              const triggerResponseName =
                item.AutoId?.TriggerResponse?.name ?? "";

              //Generating the action data from the trigger response name (backend)
              const triggerActions =
                TriggerEventData(triggerResponseName).action;

              const matchingAction = triggerActions?.find(
                (itemAction) =>
                  itemAction.name === item.AutoId?.TriggerAction?.ActionName
              );

              const formattedSubtaskData = {
                ...item,
                ReporterId: {
                  Reporter:
                    item?.ReporterId?.Reporter?.[0]?.designationId?.length > 0
                      ? item?.ReporterId?.Reporter
                      : [],
                },
                MethodId: {
                  ...item?.MethodId,
                  work_instruction_id:
                    item?.MethodId?.work_instruction_id
                      ?.filter(
                        (methodItem: any) =>
                          methodItem?.Description &&
                          methodItem?.optionselected &&
                          methodItem.Description.trim() !== "" &&
                          methodItem.optionselected.trim() !== ""
                      )
                      ?.map((methodItem: any) => ({
                        photoref: {
                          photos: !Array.isArray(methodItem.photoRef)
                            ? []
                            : (methodItem.photoRef || []).map((photo: any) => ({
                                photo: photo?.photos,
                                details: photo?.Decription || "",
                              })),
                        },
                        _id: methodItem._id || "",
                        Description: methodItem.Description || "",
                        optionselected: methodItem.optionselected || "",
                        materialId: methodItem.materialId || [],
                        manpowerId: methodItem.manpowerId || [],
                        toolsId: methodItem.toolsId || [],
                        machinaryId: methodItem.machinaryId || [],
                      })) || [],
                  task_closing_requirement:
                    item?.MethodId?.task_closing_requirement?.map(
                      (taskItem: any) => ({
                        photoref: {
                          photos:
                            typeof taskItem.photoRef === "object"
                              ? []
                              : (taskItem.photoRef || []).map((photo: any) => ({
                                  photo: photo?.photos,
                                  details: photo?.Decription || "",
                                })),
                        },
                        _id: taskItem._id || "",
                        Description: taskItem.Description || "",
                        optionselected: taskItem.optionselected || "",
                      })
                    ) || [],
                },
                AutoId: {
                  TriggerResponse: {
                    _id: item?.AutoId?.isFirst
                      ? ""
                      : item?.AutoId?.TriggerResponse?._id ?? "",
                    name: item?.AutoId?.isFirst
                      ? "This is a First Subtask"
                      : item?.AutoId?.TriggerResponse?.name ?? "",
                    isFirst: item?.AutoId?.isFirst ?? false,
                  },
                  TriggerAction: {
                    ActionName: {
                      id: matchingAction?.id ?? null,
                      name: item?.AutoId?.TriggerAction?.ActionName ?? "",
                    },
                    ActionTime: item?.AutoId?.TriggerAction?.ActionTime ?? "",
                  },
                  ResponseTime: item?.AutoId?.ResponseTime ?? "",
                },
              };
              data.push(formattedSubtaskData);
            } else if (keyName === "project") {
              item?.projectDetails?.map((item: any) => {
                const formattedItem = {
                  ...item,
                  lowercase_name: item.name.toLowerCase(),
                };

                data.push(formattedItem);
              });

              const counts = [
                {
                  OngoingProjects: item?.onGoingCount,
                  _id: "OngoingProjects",
                },
                {
                  completedProjects: item.completedCount,
                  _id: "completedProjects",
                },
              ];
              saveSyncData(counts, "time", "counts");
            } else if (keyName === "Towerlocations") {
              item?.towerDetail?.map((titem: any) => {
                const formattedItem = {
                  ...titem,
                  lowercase_name: titem.name?.toLowerCase(),
                };

                data.push(formattedItem);

                saveSyncData(
                  item?.counts?.map((countItem: any) => ({
                    ...countItem,
                    _id: countItem?.project_id,
                  })),
                  "time",
                  "countsTN"
                );
              });
            } else if (keyName === "TowerlocationsDetails_Tasks") {
              const formattedData = {
                ...item,
                _id: `${item?.taskid}-${item?._id}`,
              };

              data.push(formattedData);
            } else if (keyName === "SubtasklocDetail") {
              console.log("inside here");
              const materialCategoryMap: Record<
                string,
                {
                  categoryId: string;
                  categoryName: string;
                  materials: any[];
                  taskId: string;
                }
              > = {};

              data.push(item);

              const task_Id = item?.taskId;
              const materials = item?.materialId || [];

              console.log("taskId and materials", task_Id, materials);

              for (const material of materials) {
                const catId =
                  material?.Mastermaterial_id?.materialCategoryId?._id;
                const catName =
                  material?.Mastermaterial_id?.materialCategoryId?.name || "";
                const qty = Number(material.quantity) || 0;

                if (!catId) continue;

                if (!materialCategoryMap[catId]) {
                  materialCategoryMap[catId] = {
                    categoryId: catId,
                    categoryName: catName,
                    materials: [],
                    taskId: task_Id || "",
                  };
                }

                const existingMaterial = materialCategoryMap[
                  catId
                ].materials.find(
                  (existing) =>
                    existing.Masterbrand_id === material?.Masterbrand_id?._id &&
                    existing.Mastermaterial_id ===
                      material?.Mastermaterial_id?._id
                );

                console.log("existingMaterial", existingMaterial);

                if (existingMaterial) {
                  existingMaterial.quantity += qty;

                  existingMaterial.subtasks.push({
                    name: item?.subtaskId?.name,
                    id: item?._id,
                    quantity: qty,
                  });
                } else {
                  materialCategoryMap[catId].materials.push({
                    Masterbrand_id: material?.Masterbrand_id?._id || "",
                    Brandname: material?.Masterbrand_id?.Brandname || "",
                    Mastermaterial_id: material?.Mastermaterial_id?._id || "",
                    name: material?.Mastermaterial_id?.name || "",
                    unit: material?.Mastermaterial_id?.unit || [],
                    quantity: qty,
                    spec: material?.spec || "",
                    subtasks: [
                      {
                        name: item?.subtaskId?.name,
                        id: item?._id,
                        quantity: qty,
                      },
                    ],
                    _id: material?._id || "",
                  });
                }
              }

              const dbName = await initializeDatabase("materialtable");
              const existingDocs = await window.electron.allbulkGet({ dbName });

              const summaryArray = Object.values(materialCategoryMap).map(
                (category) => {
                  const existingDoc = existingDocs?.docs?.find(
                    (doc: any) =>
                      doc.taskId === category.taskId &&
                      doc.categoryId === category.categoryId
                  );

                  if (existingDoc) {
                    const mergedMaterials = [
                      ...category.materials.map((newMat) => {
                        const oldMat = (existingDoc.materials || []).find(
                          (m: any) =>
                            m.Masterbrand_id === newMat.Masterbrand_id &&
                            m.Mastermaterial_id === newMat.Mastermaterial_id
                        );
                        if (oldMat) {
                          const oldSubtasks = oldMat.subtasks || [];
                          const newSubtasks = newMat.subtasks || [];
                          const mergedSubtasks = [
                            ...oldSubtasks.filter(
                              (oldSub: any) =>
                                !newSubtasks.some(
                                  (newSub: any) => newSub.id === oldSub.id
                                )
                            ),
                            ...newSubtasks,
                          ];
                          return {
                            ...oldMat,
                            ...newMat,
                            subtasks: mergedSubtasks,
                          };
                        }

                        return newMat;
                      }),

                      ...(existingDoc.materials || []).filter(
                        (oldMat: any) =>
                          !category.materials.some(
                            (newMat) =>
                              newMat.Masterbrand_id === oldMat.Masterbrand_id &&
                              newMat.Mastermaterial_id ===
                                oldMat.Mastermaterial_id
                          )
                      ),
                    ];
                    return {
                      ...existingDoc,
                      ...category,
                      materials: mergedMaterials,
                    };
                  }
                  return category;
                }
              );

              await saveSyncData(
                summaryArray,
                "",
                "materialtable",
                false,
                dispatch
              );
            } else {
              const formattedData = {
                ...item,
                ...(keyName === "Designationmaster"
                  ? { DepartmentId: item?.DepartmentId?._id }
                  : {}),
                ...(item?.consumableCount ? { _id: item?.categoryId } : {}),
                lowercase_name: item.taskname
                  ? item.taskname.toLowerCase()
                  : item.categoryName
                  ? item.categoryName.toLowerCase()
                  : item.name
                  ? item.name.toLowerCase()
                  : "",
              };
              data.push(formattedData);
            }
          }

          console.log("here at least");
          await saveSyncData(
            data,
            parsed.time,
            keyName === "Taskmaster_Form"
              ? "TaskForm"
              : keyName === "Subtaskmaster"
              ? "SubTaskForm"
              : keyName === "project"
              ? "projects"
              : keyName === "TowerlocationsDetails_Routes"
              ? "TowerRoutes"
              : keyName === "TowerlocationsDetails_Tasks"
              ? "TaskBasicDetails"
              : keyName === "TowerlocationsDetails_Subtask"
              ? "SubTasksBasicDetails"
              : keyName,
            false,
            dispatch
          );
          received += parsed.data.length;
        }
      }
    }

    const updatedTables = JSON.parse(
      localStorage.getItem("backuptable") || "[]"
    ) as string[];
    const index = updatedTables.indexOf(keyName);
    if (index !== -1) {
      updatedTables.splice(index, 1);
      localStorage.setItem("backuptable", JSON.stringify(updatedTables));
      dispatchEvent(new Event("backuptable"));
    }
  }

  const flatImageObject = Object.fromEntries(imageStack.entries());
  console.log(flatImageObject, "Final image stack object");

  saveSyncData(flatImageObject, "time", "ImageStack", false, dispatch);
};
