import React, { FC, useEffect, useLayoutEffect, useState } from "react";
import styles from "./Styles/NavbarAddNewProject.module.css";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../../../../redux/store";
import {
  closePopup,
  togglePopup,
} from "../../../../../../../redux/features/Modules/Reusble/popupSlice";
import MainMenu from "../../../../../../../components/Common/Sidebar/SubComponents/MainMenu";
import { ToggleTowerSwitch } from "../../../../BillingApproval/Subcomponents/ToggleTowerSwitch";
import { DashboardSvg } from "../../../../../../../assets/SidebarAssets/SVGs";
import {
  AddIcon,
  LocationIcon,
  TableViewBilling,
  Uploadicon,
} from "../../../../../../../assets/icons";
import { AddProjectForm } from "../../AddProjectForm";
import { NavAddNewProjectProps } from "../../../../../../../interfaces/Modules/Billing/ProjectPlanning/ProjectPlanning";
import { useAddProjectApi } from "../../../../../../../redux/hooks/Modules/Billing/billinghooks";
import { DeletePopup } from "../../../../../../../components/Reusble/Global/DeletePopup";
import {
  setprojectDeleted,
  setSelectedProjectCategory,
  setSelectedProjectIddata,
  setTowerLocationDeleted,
} from "../../../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/ProjectSlice";
import { ToggleSwitch } from "../../../../../../../components/Reusble/Billing/ToggleSwitch/ToggleSwitch";
import {
  extractDateParts,
  initializeDatabase,
  slicedData,
} from "../../../../../../../functions/functions";
import { setBackupChange } from "../../../../../../../redux/features/Modules/Reusble/backupSlice";
import {
  useDeleteProjectByprojectIdMutation,
  useUpdateProjectByProjectIdMutation,
} from "../../../../../../../redux/api/Modules/Billing/Billingapi";
import { FetchBaseQueryError } from "@reduxjs/toolkit/query";
import { ProjectData } from "../../AddProjectForm/Interfaces/interface";
import NavigationComponent from "../../../../../../../components/Reusble/Global/navigationComponents/commonHeaderComponent";
import { useToast } from "../../../../../../../hooks/ToastHook";
import { initializeBreadcrumb } from "../../../../../../../redux/features/Modules/Reusble/navigationSlice";

const NavAddNewProject: React.FC<NavAddNewProjectProps> = ({
  setIsTableView,
  isTableView,
}) => {
  const dispatch = useDispatch();
  const currentOpenPopup = useSelector(
    (state: RootState) => state.popup.popups
  );
  const showToast = useToast();
  // Local state for the "Ongoing/Completed" toggle

  // Handler for opening the Add Project Form
  const handleOpenAddProjectForm = () => {
    if (!navigator.onLine) {
      showToast({
        messageContent: "Oops! no internet connection!",
        type: "danger",
      });
      return;
    }
    
    dispatch(
      setSelectedProjectIddata({
        name: "",
        photo: undefined,
        Address: "",
        project_type: "",
        estimate_budget: "",
        project_area: "",
        rate_type: "",
        rate: "",
        project_duration: "",
        project_drawing: [],
        clientName: "",
        ClientPhoneNumber: "",
        Remarks: "",
        project_start_date: "",
        project_completes: "",
      })
    );
    dispatch(togglePopup("AddProjectForm"));
  };
  const isProjectDeleted = useSelector(
    (state: RootState) => state.projectLocalDb.isProjectDeleted
  );
  // Toggle handler for TableView
  const handleTableViewToggle = (isChecked: boolean) => {
    setIsTableView(!isChecked);
  };
  const selectedProjectData = useSelector(
    (state: RootState) => state.projectLocalDb.selectedProjectIddata
  );

  const getLocalDbData = async () => {
    try {
      const dbName = await initializeDatabase("counts");
      const onGoingprojects = await window.electron.getDataById({
        dbName,
        id: "OngoingProjects",
      });
      const completedProjects = await window.electron.getDataById({
        dbName,
        id: "completedProjects",
      });
      console.log(completedProjects, onGoingprojects, "thease are counts");

      return {
        OngoingProjects: onGoingprojects[0]?.OngoingProjects || 0,
        completedProjects: completedProjects[0]?.completedProjects || 0,
      };
    } catch (error) {
      console.error("Error fetching project data", error);
      return { OngoingProjects: 0, completedProjects: 0 };
    }
  };

  const allProjects = useSelector(
    (state: RootState) => state?.projectLocalDb?.projects
  );

  const backupChanged = useSelector(
    (state: RootState) => state?.backupSlice?.isOpen
  );

  const selectedProjectCategory = useSelector(
    (state: RootState) => state?.projectLocalDb?.selectedProjectCategory
  );

  const [ongoingProjects, setOngoingProjects] = useState<number>(0);
  const [completedProjects, setCompletedProjects] = useState<number>(0);

  // Combined useEffect to update counts from both Redux state and local database
  useLayoutEffect(() => {
    // Function to calculate counts from Redux state
    const calculateCountsFromRedux = () => {
      if (allProjects && allProjects.length > 0) {
        const ongoingCount = allProjects.filter(
          (project: ProjectData) => project.project_status === "Ongoing"
        ).length;
        const completedCount = allProjects.filter(
          (project: ProjectData) => project.project_status === "Completed"
        ).length;
        
        setOngoingProjects(ongoingCount);
        setCompletedProjects(completedCount);
      }
    };


    // Function to fetch counts from local database
    const fetchCountsFromLocalDB = async () => {
      try {
        const { OngoingProjects, completedProjects } = await getLocalDbData();
        setOngoingProjects(OngoingProjects);
        setCompletedProjects(completedProjects);
      } catch (error) {
        console.error("Error fetching counts from local DB:", error);
        calculateCountsFromRedux();
      }
    };

    if (allProjects && allProjects.length > 0) {
      calculateCountsFromRedux();
    } else {
      fetchCountsFromLocalDB();
    }
  }, [allProjects, selectedProjectCategory, backupChanged]);

  //  Use these anywhere
  const totalOngoingprojects = ongoingProjects;
  const totalcompletedProjects = completedProjects;

  // api to add project
  const [AddProjectApi] = useAddProjectApi();
  const [updateProjectapi] = useUpdateProjectByProjectIdMutation();
  // api to delete project
  const [DeleteProject] = useDeleteProjectByprojectIdMutation();

  return (
    <div className={styles.add_new_project_navbar_container}>
      <div className={styles.add_new_project_navbar_leftbtns}>
        <MainMenu />
        <div className={styles.navproject_navigation_component}>
          {isProjectDeleted ? (
            <NavigationComponent
              handleOutsideNavigation={(_title: string, _route: string) => {
                dispatch(setprojectDeleted(false));
                dispatch(setTowerLocationDeleted(false));
              }}
              route={[
                { route: "/billing", title: "Projects" },
                { route: "#", title: "Deleted" },
              ]}
            />
          ) : (
            <NavigationComponent
              route={[{ route: "/billing", title: "Projects" }]}
            />
          )}
        </div>

        <div className={styles.add_new_project_navbar_leftbtn_tableview_switch}>
          <ToggleTowerSwitch
            toggleClassName="toggleProjectviewSwitch"
            leftLabel={
              <DashboardSvg
                color={isTableView ? "var(--text-black-28)" : "white"}
              />
            }
            rightLabel={
              <TableViewBilling
                color={isTableView ? "white" : "var(--text-black-28)"}
              />
            }
            onToggle={handleTableViewToggle}
            width="90px"
            id="toggleproject-table-view"
          />
        </div>
      </div>

      <div>
        <ToggleSwitch
          leftLabel={"Ongoing"}
          rightLabel={"Completed"}
          countLeft={totalOngoingprojects}
          countRight={totalcompletedProjects}
          onToggle={(e) => {
            if (e == true) {
              dispatch(setSelectedProjectCategory("Ongoing"));
            } else {
              dispatch(setSelectedProjectCategory("Completed"));
            }
          }}
          width={"320px"}
          id="toggleSwitchtower"
        />
      </div>

      <div className={styles.add_new_project_navbar_rightbtns}>
        {/* {!isProjectDeleted && (
          <button
            onClick={() => {
              dispatch(setprojectDeleted(true));
              dispatch(setTowerLocationDeleted(true));
            }}
            className={styles.add_new_project_navbar_exportbtn}
          >
            Deleted
            <DeleteIcon />
          </button>
        )} */}

        <button className={styles.add_new_project_navbar_exportbtn}>
          Export <Uploadicon />
        </button>
        <button
          className={styles.add_new_project_navbar_addprojectbtn}
          onClick={handleOpenAddProjectForm}
        >
          Project <AddIcon />
        </button>
        <button className={styles.locationbtncorner}>
          <LocationIcon />
        </button>
      </div>

      {currentOpenPopup["AddProjectForm"] && (
        <AddProjectForm
          onClose={() => {}}
          onsubmit={async (data: ProjectData) => {
            console.log(data, "this is form asdfdata");
            const formData = new FormData();

            Object.keys(data).forEach((key) => {
              if (key === "photo") {
                // Handle single photo upload
                if (Array.isArray(data.photo)) {
                  formData.append("photo", data.photo); // Assuming one file is uploaded
                } else {
                  formData.append("photo", data.photo as File);
                }
              } else if (key === "project_drawing") {
                // Handle multiple project drawings
                if (Array.isArray(data.project_drawing)) {
                  const projectDrawingStrings: string[] = [];
                  data.project_drawing.forEach((file) => {
                    if (typeof file === "string") {
                      projectDrawingStrings.push(file); // Collect strings in an array
                    } else {
                      formData.append("project_drawing", file as File); // Append file
                    }
                  });
                  formData.append(
                    "project_drawing",
                    JSON.stringify(projectDrawingStrings)
                  ); // Append array of strings as JSON
                }
              } else {
                formData.append(key, (data as any)[key]);
              }
            });
            try {
              const formdataObject = Object.fromEntries(formData.entries());
              console.log(formdataObject, "this is form data object");
              // check if the formdataObject contains _id it means it is an existing project
              if (formdataObject?._id) {
                // Check for no changes before making API call
                if (
                  JSON.stringify(selectedProjectData) === JSON.stringify(data)
                ) {
                  showToast({
                    messageContent: "There Were No changes!",
                    type: "warning",
                  });
                  return;
                }
                const res = await updateProjectapi({
                  projectId: formdataObject?._id,
                  data: formData,
                });

                if (res?.data?.success) {
                  dispatch(closePopup("AddProjectForm"));
                  showToast({
                    messageContent: `Project updated successfully`,
                    type: "success",
                  });
                  dispatch(setBackupChange());
                } else {
                  console.log(res, "this is responseeeeee");
                  showToast({
                    messageContent:
                      (
                        (res.error as FetchBaseQueryError)?.data as {
                          message?: string;
                        }
                      )?.message || "An error occurred",
                    type: "warning",
                  });
                }
              } else {
                // if the form data doest not contain _id it means it is a new project
                const res = await AddProjectApi(formData);

                if (res?.data?.success) {
                  dispatch(closePopup("AddProjectForm"));
                  showToast({
                    messageContent: `Project added successfully`,
                    type: "success",
                  });
                } else {
                  console.log(res, "this is responseeeeee");
                  showToast({
                    messageContent:
                      (
                        (res.error as FetchBaseQueryError)?.data as {
                          message?: string;
                        }
                      )?.message || "An error occurred",
                    type: "warning",
                  });
                }
              }
            } catch (error) {
              showToast({
                messageContent: `Something went wrong`,
                type: "warning",
              });
            }
          }}
        />
      )}
      {currentOpenPopup["deleteproject"] && (
        <DeletePopup
          header="Are you sure you want to delete this Project"
          callbackDelete={async () => {
            console.log(selectedProjectData?._id, "this is selectedprojectid");

            const response = await DeleteProject({
              projectId: selectedProjectData?._id,
            });
            console.log(response, "this is responseeeee");
            if (response.data.success) {
              showToast({
                messageContent: `Project deleted successfully`,
                type: "success",
              });
              // HEREEE
              dispatch(setBackupChange());
            } else {
              showToast({
                messageContent: `${
                  (
                    (response?.error as FetchBaseQueryError)?.data as {
                      message?: string;
                    }
                  )?.message || "An error occurred"
                }`,
                type: "success",
              });
            }

            dispatch(setBackupChange());
          }}
          children={
            <>
              <DeleteProjectSummary />
            </>
          }
          // onsubmit={async (data: ProjectData) => {
          //   console.log(data, "this is form data");
          //   const formData = new FormData();

          //   Object.keys(data).forEach((key) => {
          //     if (key === "photo") {
          //       // Handle single photo upload
          //       if (Array.isArray(data.photo)) {
          //         formData.append("photo", data.photo[0]); // Assuming one file is uploaded
          //       } else {
          //         formData.append("photo", data.photo as File);
          //       }
          //     } else if (key === "project_drawing") {
          //       // Handle multiple project drawings
          //       if (Array.isArray(data.project_drawing)) {
          //         data.project_drawing.forEach((file) => {
          //           formData.append("project_drawing", file); // Append each file separately
          //         });
          //       }
          //     } else {
          //       formData.append(key, (data as any)[key]);
          //     }
          //   });

          //   const res = await AddProjectApi(formData);
          //   if (res?.data?.success) {
          //     dispatch(closePopup("AddProjectForm"));
          //   }
          //   console.log(res, "this is response4");
          // }}
          onClose={() => {
            dispatch(closePopup("deleteproject"));
          }}
        />
      )}
    </div>
  );
};

export default NavAddNewProject;

const DeleteProjectSummary: FC<{}> = () => {
  const selectedItems = useSelector(
    (state: RootState) => state?.projectLocalDb?.selectedProjectIddata
  );
  console.log(selectedItems, "projectdataaa");
  return (
    <>
      <div className={styles.summaryDivData}>
        {selectedItems?.name && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Project Name
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                }}
              >
                {selectedItems?.name}
              </h4>
            </div>
          </div>
        )}
        {(selectedItems?.photo instanceof File
          ? selectedItems?.photo.name
          : selectedItems?.photo) && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Cover Photo
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                  color: "var(--text-black-87)",
                }}
              >
                {selectedItems?.photo instanceof File
                  ? slicedData(selectedItems?.photo?.name, 12)
                  : slicedData(selectedItems?.photo?.split("/")[2] ?? "", 12)}
              </h4>
            </div>
          </div>
        )}
      </div>
      <div className={styles.summaryDivData}>
        {selectedItems?.Address && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Address
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                }}
              >
                {selectedItems?.Address}
              </h4>
            </div>
          </div>
        )}
      </div>

      <div className={styles.summaryDivData}>
        {selectedItems?.project_type && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Project Type
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                }}
              >
                {selectedItems?.project_type}
              </h4>
            </div>
          </div>
        )}
      </div>
      <div className={styles.summaryDivData}>
        {selectedItems?.estimate_budget && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Estimated Budget
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                }}
              >
                {selectedItems?.estimate_budget}
              </h4>
            </div>
          </div>
        )}
        {selectedItems?.project_area && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Area
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                }}
              >
                {selectedItems?.project_area}
              </h4>
            </div>
          </div>
        )}
      </div>
      <div className={styles.summaryDivData}>
        {selectedItems?.rate_type && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Rate Type
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                }}
              >
                {selectedItems?.rate_type}
              </h4>
            </div>
          </div>
        )}
        {selectedItems?.rate && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Rate
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                }}
              >
                {selectedItems?.rate}
              </h4>
            </div>
          </div>
        )}
      </div>
      <div className={styles.summaryDivData}>
        {selectedItems?.project_start_date && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Start date
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                }}
              >
                {((date) => `${date?.day} ${date?.monthName} ${date?.year}`)(
                  extractDateParts(selectedItems?.project_start_date)
                )}
              </h4>
            </div>
          </div>
        )}
        {selectedItems?.project_duration && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Duration
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                }}
              >
                {selectedItems?.project_duration}
              </h4>
            </div>
          </div>
        )}
      </div>
      <div className={styles.summaryDivData}>
        {selectedItems?.project_drawing &&
          selectedItems?.project_drawing?.length > 0 && (
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Drawings
              </p>
              <div
                className={styles.summaryItems}
                style={{ color: "var(--text-black-87)" }}
              >
                {selectedItems?.project_drawing?.map((e) => (
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color: "var(--text-black-87)",
                    }}
                  >
                    {`${
                      e instanceof File
                        ? slicedData(e?.name, 12)
                        : slicedData(e?.split("/")[2] as string, 12)
                    }`}
                  </h4>
                ))}
              </div>
            </div>
          )}
      </div>
      <div className={styles.summaryDivData}>
        {selectedItems?.clientName && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Client Name
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
          className={styles.summaryItem}
          style={{
            paddingRight: "16px",
          }}
              >
          {slicedData(selectedItems?.clientName, 20)}
              </h4>
            </div>
          </div>
        )}
        {selectedItems?.ClientPhoneNumber && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              {"Mobile Number"}
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                }}
              >
                {selectedItems?.ClientPhoneNumber}
              </h4>
            </div>
          </div>
        )}
      </div>
      <div className={styles.summaryDivData}>
        {selectedItems?.Remarks && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Remarks
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                }}
              >
                {selectedItems?.Remarks}
              </h4>
            </div>
          </div>
        )}
      </div>
    </>
  );
};
