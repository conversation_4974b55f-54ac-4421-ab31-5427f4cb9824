import { AppDispatch, store } from "./../../redux/store";

import { saveSyncData, saveSyncTime } from "../BackupFunctions/BackupFunctions";
import PQueue from "p-queue";
import { filterImages, getTime } from "../../functions/functions";
import { TaskMasterApi } from "../../redux/api/Modules/TaskMaster/TaskMasterapi";
import { settaskWorkInstructionDeleteEmpty } from "../../redux/features/Modules/TaskMaster/Slices/WorkInstructionSlice";
import {
  setcurrentSubtaskData,
  setcurrentTaskData,
  setNavigateToTask,
  setNavigateToTaskView,
  updateTaskData,
} from "../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import { TriggerEventData } from "../../redux/features/Modules/TaskMaster/Slices/TriggerEventSlice";
import {
  removeNavigate,
  resetTaskHeader,
  setRemoveNavigationKey,
} from "../../redux/features/Modules/Reusble/navigationSlice";

// remove lazy query and set data directly to api in redux temporaray also timing setupo bro not a good way of custom hook its not h good approach delete this approach or not follow this approach

export const TaskCategory = async (
  dispatch: AppDispatch,
  categoryIds: string[]
): Promise<void> => {
  console.log(categoryIds, "Processing category IDs");

  const date = await getTime("TaskCategory");

  let time = "";
  if (date) {
    time = date.date;
  }

  const result = await dispatch(
    TaskMasterApi.endpoints.getTaskCategories.initiate(time as string, {
      forceRefetch: true,
    })
  );
  console.log("task category data>>>", result.data);
  if (result.data && result.data.data && result.data.data.date) {
    saveSyncTime(result.data.data.date, "TaskCategory");
  }
  if (result.data && result.data.data.responseData.length > 0) {
    saveSyncData(
      result.data.data.responseData?.map((el: any) => {
        return {
          ...el,
          lowercase_name: el.categoryName
            ? el.categoryName.toLowerCase()
            : el.name
            ? el.name.toLowerCase()
            : "",
        };
      }),

      time,
      "TaskCategory",
      false,
      dispatch
    );
  }
};

export const Taskmaster = async (
  dispatch: AppDispatch,
  Ids: string[],
  parentIds: string[],
  useSelector,
  isParent: boolean
): Promise<string | void> => {
  console.log(Ids, parentIds, "these are the ids in taskmaster");
  const taskQueue = new PQueue({ concurrency: 1 });

  if (Ids.length === 0 || (parentIds && parentIds.length === 0)) {
    return "No Category Id found";
  }

  if (!isParent) {
    for (const data of parentIds) {
      taskQueue.add(async () => {
        if (!data) {
          return null;
        }
        try {
          const dates = await getTime(data);
          console.log(dates, "check for dates here");
          const [result] = await Promise.all([
            dispatch(
              TaskMasterApi.endpoints.getAllTaskByCategoryId.initiate(
                { id: data, time: dates ? dates.date : "" },
                {
                  forceRefetch: true,
                }
              )
            ).unwrap(),
          ]);
          const date = await getTime(data);

          let time = "";
          if (date) {
            time = date.date;
          }

          if (result.data && result.data.response.length > 0) {
            saveSyncTime((result.data as any).date, data);
            saveSyncData(
              result.data.response?.map((el: any) => {
                return {
                  ...el,
                  lowercase_name: el.taskname
                    ? el.taskname.toLowerCase()
                    : el.taskName
                    ? el.taskName.toLowerCase()
                    : el.name
                    ? el.name.toLowerCase()
                    : "",
                };
              }),

              time,
              "Taskmaster",
              false,
              dispatch
            );
          }
        } catch (error) {
          console.error(`Error  task backup[]`, error);
        }
      });
    }
  }

  if (!isParent) {
    for (const data of Ids) {
      taskQueue.add(async () => {
        if (!data) {
          return null;
        }
        try {
          const [taskformdata]: any = await Promise.all([
            dispatch(
              TaskMasterApi.endpoints.getTaskDetailsByTaskId.initiate(data, {
                forceRefetch: true,
              })
            ),
          ]);

          console.log("task form data234", taskformdata);

          const formattedTaskData: any = {
            id: taskformdata.data?.data?._id || "",
            _id: taskformdata.data?.data?._id || "",
            name: taskformdata.data?.data?.name || "",
            Unit: taskformdata.data?.data?.Unit || "",
            Description: taskformdata.data?.data?.Description || "",
            subtaskWeighatages: taskformdata.data?.data?.Quantity || 0,
            isDeleted: taskformdata?.data?.data?.isDeleted,
            Tracking: taskformdata.data?.data?.isCompleted
              ? "Completed"
              : "Pending",
            DepartmentId: taskformdata.data?.data?.departmentId || [],
            DesignationId: taskformdata.data?.data?.designationId || [],
            MaterialId: taskformdata.data?.data?.materialId || [],
            ToolId: taskformdata.data?.data?.toolId || [],
            MachinaryId: taskformdata.data?.data?.machinaryId || [],
            ManpowerId: taskformdata.data?.data?.manpowerId || [],
            Adminid: taskformdata.data?.data?.Adminid,
            ReporterId: {
              Reporter: taskformdata.data?.data?.ReporterId?.Reporter || [],
            },
            AssigneeId: taskformdata.data?.data?.Assignee || [],
            Subtaskdetails: taskformdata.data?.data?.SubtaskId,
            MethodId: {
              ...taskformdata?.data?.data?.MethodId,
              work_instruction_id:
                taskformdata.data.data?.MethodId?.work_instruction_id?.map(
                  (item: any) => ({
                    photoref: {
                      photos: (item.photoRef || []).map((photo: any) => ({
                        photo: photo?.photos,
                        details: photo?.Decription || "",
                      })),
                    },
                    _id: item._id || "",
                    Description: item.Description || "",
                    optionselected: item.optionselected || "",
                    materialId: item.materialId || [],
                    manpowerId: item.manpowerId || [],
                    toolsId: item.toolsId || [],
                    machinaryId: item.machinaryId || [],
                  })
                ) || [],
              task_closing_requirement:
                taskformdata?.data?.data?.MethodId?.task_closing_requirement?.map(
                  (item: any) => ({
                    photoref: {
                      photos: (item.photoRef || []).map((photo: any) => ({
                        photo: photo?.photos,
                        details: photo?.Decription || "",
                      })),
                    },
                    _id: item._id || "",
                    Description: item.Description || "",
                    optionselected: item.optionselected || "",
                  })
                ) || [],
            },
          };

          const state = store.getState();
          // let taskdata =
          //   state?.taskForm?.currentSubtaskData?.MethodId?.work_instruction_id;
          const currentTaskId = state?.taskForm?.currentSubtaskData?._id;
          const navigationArray = state?.navigateData?.navigateArray;

          // const result = filterImages({
          //   newImages: formattedTaskData?.MethodId?.work_instruction_id,
          //   taskdata: taskdata,
          //   dispatch,
          // });

          // let taskdata2 =
          //   state?.taskForm?.currentSubtaskData?.MethodId
          //     ?.task_closing_requirement;

          // const resultss = filterImages({
          //   newImages: formattedTaskData?.MethodId?.task_closing_requirement,
          //   taskdata: taskdata2,
          //   dispatch,
          // });

          console.log(formattedTaskData, "check for task form data ");

          if (currentTaskId === formattedTaskData._id) {
            if (!formattedTaskData?.isDeleted) {
              dispatch(settaskWorkInstructionDeleteEmpty());
              dispatch(setcurrentTaskData(formattedTaskData));
            } else {
              const route = navigationArray[navigationArray?.length - 1]?.route;
              console.log(route, "routee");
              if (route.includes("task")) {
                dispatch(setNavigateToTaskView(true));
                dispatch(removeNavigate());
              }
            }
          }

          saveSyncData(formattedTaskData, "time", "TaskForm", false, dispatch);
        } catch (error) {
          console.error(`Error task backup[]`, error);
        }
      });
    }
  }

  // if (isParent) {
  //   for (const data of parentIds) {
  //     taskQueue.add(async () => {
  //       if (!data) {
  //         return null;
  //       }
  //       try {
  //         const [taskformdata]: any = await Promise.all([
  //           dispatch(
  //             TaskMasterApi.endpoints.getTaskDetailsByTaskId.initiate(data, {
  //               forceRefetch: true,
  //             })
  //           ),
  //         ]);

  //         console.log("task form data234", taskformdata);

  //         const formattedTaskData: any = {
  //           id: taskformdata.data?.data?._id || "",
  //           _id: taskformdata.data?.data?._id || "",
  //           name: taskformdata.data?.data?.name || "",
  //           Unit: taskformdata.data?.data?.Unit || "",
  //           Description: taskformdata.data?.data?.Description || "",
  //           subtaskWeighatages: taskformdata.data?.data?.Quantity || 0,
  //           Tracking: taskformdata.data?.data?.isCompleted
  //             ? "Completed"
  //             : "Pending",
  //           DepartmentId: taskformdata.data?.data?.departmentId || [],
  //           DesignationId: taskformdata.data?.data?.designationId || [],
  //           MaterialId: taskformdata.data?.data?.materialId || [],
  //           ToolId: taskformdata.data?.data?.toolId || [],
  //           MachinaryId: taskformdata.data?.data?.machinaryId || [],
  //           ManpowerId: taskformdata.data?.data?.manpowerId || [],
  //           Adminid: taskformdata.data?.data?.Adminid,
  //           ReporterId: {
  //             Reporter: taskformdata.data?.data?.ReporterId?.Reporter || [],
  //           },
  //           AssigneeId: taskformdata.data?.data?.Assignee || [],
  //           Subtaskdetails: taskformdata.data?.data?.SubtaskId,
  //           MethodId: {
  //             ...taskformdata?.data?.data?.MethodId,
  //             work_instruction_id:
  //               taskformdata.data.data?.MethodId?.work_instruction_id?.map(
  //                 (item: any) => ({
  //                   photoref: {
  //                     photos: (item.photoRef || []).map((photo: any) => ({
  //                       photo: photo?.photos,
  //                       details: photo?.Decription,
  //                     })),
  //                   },
  //                   _id: item._id || "",
  //                   Description: item.Description || "",
  //                   optionselected: item.optionselected || "",
  //                   materialId: item.materialId || [],
  //                   manpowerId: item.manpowerId || [],
  //                   toolsId: item.toolsId || [],
  //                   machinaryId: item.machinaryId || [],
  //                 })
  //               ) || [],
  //             task_closing_requirement:
  //               taskformdata?.data?.data?.MethodId?.task_closing_requirement?.map(
  //                 (item: any) => ({
  //                   photoref: {
  //                     photos: (item.photoRef || []).map((photo: any) => ({
  //                       photo: photo?.photos,
  //                       details: photo?.Decription || "",
  //                     })),
  //                   },
  //                   _id: item._id || "",
  //                   Description: item.Description || "",
  //                   optionselected: item.optionselected || "",
  //                 })
  //               ) || [],
  //           },
  //         };

  //         const state = store.getState();
  //         // let taskdata =
  //         //   state?.taskForm?.currentSubtaskData?.MethodId?.work_instruction_id;
  //         const currentTaskId = state?.taskForm?.currentSubtaskData?._id;

  //         // const result = filterImages({
  //         //   newImages: formattedTaskData?.MethodId?.work_instruction_id,
  //         //   taskdata: taskdata,
  //         //   dispatch,
  //         // });

  //         // let taskdata2 =
  //         //   state?.taskForm?.currentSubtaskData?.MethodId
  //         //     ?.task_closing_requirement;

  //         // const resultss = filterImages({
  //         //   newImages: formattedTaskData?.MethodId?.task_closing_requirement,
  //         //   taskdata: taskdata2,
  //         //   dispatch,
  //         // });

  //         if (currentTaskId === formattedTaskData._id) {
  //           dispatch(settaskWorkInstructionDeleteEmpty());
  //           dispatch(setcurrentTaskData(formattedTaskData));
  //         }

  //         saveSyncData(formattedTaskData, "time", "TaskForm", false, dispatch);
  //       } catch (error) {
  //         console.error(`Error task backup[]`, error);
  //       }
  //     });
  //   }
  // }

  await taskQueue.onIdle();
};

export const Subtaskmaster = async (
  dispatch: AppDispatch,
  Ids: string[],
  parentIds: string[]
): Promise<void> => {
  const taskQueue = new PQueue({ concurrency: 1 });

  for (const data of Ids) {
    console.log("check for data", data);
    taskQueue.add(async () => {
      if (!data) {
        return null;
      }
      try {
        const [result] = await Promise.all([
          dispatch(
            TaskMasterApi.endpoints.getsubTaskDetails.initiate(data, {
              forceRefetch: true,
            })
          ).unwrap(),
        ]);

        console.log(
          result.data.response,
          "check result subtask socket>>>>>>>>>>>>>"
        );

        if (result.data && result.data.response) {
          const triggerResponseName =
            result.data.response.AutoId?.TriggerResponse?.name ?? "";

          //Generating the action data from the trigger response name (backend)
          const triggerActions = TriggerEventData(triggerResponseName).action;

          const matchingAction = triggerActions?.find(
            (item) =>
              item.name ===
              result.data.response.AutoId?.TriggerAction?.ActionName
          );

          console.log("before formattt", result.data.response);
          const formattedData = {
            ...result.data.response,
            MethodId: {
              ...result.data.response?.MethodId,
              work_instruction_id:
                result.data.response?.MethodId?.work_instruction_id?.map(
                  (workItem: any) => ({
                    photoref: {
                      photos: (workItem.photoRef || []).map((photo: any) => ({
                        photo: photo?.photos,
                        details: photo?.Decription || "",
                      })),
                    },
                    _id: workItem._id || "",
                    Description: workItem.Description || "",
                    optionselected: workItem.optionselected || "",
                    materialId: workItem.materialId || [],
                    manpowerId: workItem.manpowerId || [],
                    toolsId: workItem.toolsId || [],
                    machinaryId: workItem.machinaryId || [],
                  })
                ) || [],
              task_closing_requirement:
                result.data.response?.MethodId?.task_closing_requirement?.map(
                  (item: any) => ({
                    photoref: {
                      photos: (item.photoRef || []).map((photo: any) => ({
                        photo: photo?.photos,
                        details: photo?.Decription || "",
                      })),
                    },
                    _id: item._id || "",
                    Description: item.Description || "",
                    optionselected: item.optionselected || "",
                  })
                ) || [],
            },
            AutoId: {
              TriggerResponse: {
                _id: result.data.response?.AutoId?.isFirst
                  ? ""
                  : result.data.response?.AutoId?.TriggerResponse?._id ?? "",
                name: result.data.response?.AutoId?.isFirst
                  ? "This is a First Subtask"
                  : result.data.response?.AutoId?.TriggerResponse?.name ?? "",
                isFirst: result.data.response?.AutoId?.isFirst ?? false,
              },
              TriggerAction: {
                ActionName: {
                  id: matchingAction?.id ?? null,
                  name:
                    result.data.response?.AutoId?.TriggerAction?.ActionName ??
                    "",
                },
                ActionTime:
                  result.data.response?.AutoId?.TriggerAction?.ActionTime ?? "",
              },
              ResponseTime: result.data.response?.AutoId?.ResponseTime ?? "",
            },
          };

          console.log("before formattt 2", formattedData);
          const state = store.getState();
          // let taskdata =
          //   state?.taskMaster?.currentSubtaskData?.MethodId
          //     ?.work_instruction_id;
          let currentTaskId = state?.taskMaster?.currentSubtaskData?._id;
          const taskData = state?.taskForm?.currentSubtaskData;
          const navigationArray = state?.navigateData?.navigateArray;
          const removeNavigationKey = state.navigateData.removeNavigationKey;

          console.log("before formattt 3", currentTaskId);
          // const result2 = filterImages({
          //   newImages: formattedData?.MethodId?.work_instruction_id,
          //   taskdata: taskdata,
          //   dispatch,
          // });

          // let taskdata2 =
          //   state?.taskMaster?.currentSubtaskData?.MethodId
          //     ?.task_closing_requirement;

          // const results2 = filterImages({
          //   newImages: formattedData?.MethodId?.task_closing_requirement,
          //   taskdata: taskdata2,
          //   dispatch,
          // });

          console.log("this is called from initial backup", navigationArray);
          if (currentTaskId === formattedData._id) {
            if (!formattedData?.isDeleted) {
              dispatch(resetTaskHeader({ title: formattedData?.name }));

              dispatch(
                setcurrentTaskData({
                  ...taskData,
                  Subtaskdetails: taskData?.Subtaskdetails?.map((item: any) =>
                    item?._id === formattedData?._id
                      ? {
                          ...item,
                          name: formattedData.name,
                          Description: formattedData.Description,
                          Unit: formattedData.Unit,
                          subtaskWeighatages: Number(
                            formattedData.subtaskWeighatages
                          ),
                          Tracking: formattedData.Tracking,
                        }
                      : item
                  ),
                })
              );
              dispatch(setcurrentSubtaskData(formattedData));
            } else {
              const route = navigationArray[navigationArray?.length - 1]?.route;
              console.log(route, "routee");
              if (route.includes("subtask") && !removeNavigationKey) {
                dispatch(
                  updateTaskData({
                    ...taskData,
                    Subtaskdetails: taskData?.Subtaskdetails?.filter(
                      (item: any) => item._id !== formattedData?._id
                    ),
                  })
                );
                dispatch(setNavigateToTask(true));
                dispatch(removeNavigate());
              }
              dispatch(setRemoveNavigationKey(false));
            }
          }

          saveSyncData(
            {
              ...formattedData,
              lowercase_name: formattedData.name
                ? formattedData.name.toLowerCase()
                : "",
            },
            "time",
            "SubTaskForm",
            false,
            dispatch
          );
          console.log(formattedData,"subtask local db data")
        }
      } catch (error) {
        console.error(`Error  task backup[]`, error);
      }
    });
  }

  // if (result.data && result.data.data.responseData.length > 0) {
  //   saveSyncTime(result.data.data.date, "TaskCategory");
  //   saveSyncData(result.data.data.responseData, time, "TaskCategory", dispatch);
  // }
};

export const getImageUrl = async ({
  toAdd,
  dispatch,
}: {
  toAdd: string[];
  dispatch: AppDispatch;
}): Promise<void> => {
  const taskQueue = new PQueue({ concurrency: 1 });

  for (const image of toAdd) {
    taskQueue.add(async () => {
      if (!image) {
        return null;
      }
      try {
        const [result] = await Promise.all([
          dispatch(
            TaskMasterApi.endpoints.getImageUrlApi.initiate(image, {
              forceRefetch: true,
            })
          ).unwrap(),
        ]);

        //download  images from here
        const resultaddimage = await window.electron.addNewImages({
          url: result?.data,
          name: image,
        });
      } catch (error) {
        console.error(`Error  task backup[]`, error);
      }
    });
  }

  await taskQueue.onIdle(); // Ensure all tasks are completed
};

type ImageDownloadResult = {
  image: string;
  success: boolean;
  error?: any;
};

//this is for downloading images for initial backup
export const getImageUrlForInitial = async ({
  toAdd,
  dispatch,
  onProgress,
}: {
  toAdd: string[];
  dispatch: AppDispatch;
  onProgress?: (result: ImageDownloadResult) => void;
}): Promise<void> => {
  if (!toAdd?.length) return;

  const taskQueue = new PQueue({ concurrency: 1 });

  for (const image of toAdd) {
    taskQueue.add(async () => {
      if (!image) {
        const result = { image, success: false, error: "Empty image name" };
        onProgress?.(result);
        return;
      }

      try {
        const response = await dispatch(
          TaskMasterApi.endpoints.getImageUrlApi.initiate(image, {
            forceRefetch: true,
          })
        ).unwrap();

        if (!response?.data) {
          throw new Error("No image URL returned");
        }

        await window.electron.addNewImages({
          url: response.data,
          name: image,
        });

        onProgress?.({ image, success: true });
      } catch (error) {
        console.error(`Error downloading image "${image}":`, error);
        onProgress?.({ image, success: false, error });
      }
    });
  }

  await taskQueue.onIdle();
};
