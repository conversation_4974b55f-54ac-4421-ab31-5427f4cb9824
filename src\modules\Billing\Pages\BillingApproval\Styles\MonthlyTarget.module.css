/* Add these styles to your existing CSS file */
.monthly_target_container {
  position: relative;
  max-width: 440px;
  margin: 0 auto;
  background-color: var(--background_color);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.monthly_target_creation_header {
  display: flex;
  justify-content: initial;
  align-items: center;
  padding: 0 1rem;
  margin: initial;
  gap: 30rem;
  position: relative;
}

.monthly_target_creation_header_buttons_rhs {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
  justify-self: end;
}

.monthly_target_creation_form_header {
  display: flex;
  /* gap: 7rem; */
  justify-content: center;
  align-items: center;
  padding: 0 1rem;
  margin: 0 auto;
}

.form_overlay {
  justify-content: center;
  align-items: center;
  z-index: 9;
}

/* Monthly Target Creation Form Styles */
.monthly_target_creation_form {
  position: fixed;
  top: 7rem;
  right: 1.5rem;
  transform: translate(0%, 0%);
  background: var(--blur-background);
  padding: 1.25rem;
  backdrop-filter: blur(60px);
  box-shadow: var(--extra-shadow-five);
  border-radius: 2.6rem;
  z-index: 3;
  width: 33rem;
  height: calc(100% - 8.5rem);
  animation: slideIn 0.5s ease-out;
  display: flex;
  flex-direction: column;
}

.monthly_target_creation_form.closing {
  animation: slideOut 0.5s ease-out;
}

/* Header Styles */
.form_header {
  position: relative;
  text-align: center;

  border-bottom: 1px solid var(--border_color);
}

.form_header h3 {
  margin: 0;
  font-weight: 500;
  color: var(--primary_color);
}

.closeButton {
  position: absolute;
  right: 16px;
  top: 16px;
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Form Body */
.form_body {
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: scroll;
}

.category_section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.section_label {
  font-size: 14px;
  font-weight: 500;
  color: var(--text_color);
  margin: 0 0 4px 0;
}

/* Tower Selection Section */
.tower_selection_section {
  position: relative;
  width: 100%;
  margin-bottom: 1.5rem;
}

.searchable_dropdown_container {
  position: relative;
  width: 100%;
}

/* Dropdown List Styles */
.floating_dropdown_list {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  background: #ffffff;
  border: 1px solid var(--border_color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-height: 240px;
  overflow-y: auto;
  z-index: 1000;
}

/* Dropdown Items */
.floating_dropdown_item {
  padding: 12px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #333333;
  background-color: #ffffff;
}

.floating_dropdown_item:hover {
  background-color: var(--primary_color);
  color: #ffffff;
  border-radius: 6px;
}

/* Selected Item */
.floating_dropdown_item.selected {
  background-color: var(--primary_color);
  color: #ffffff;
  font-weight: 500;
  border-radius: 6px;
}

/* Disabled/Loading States */
.floating_dropdown_item_disabled {
  padding: 12px 16px;
  color: #999999;
  font-style: italic;
  text-align: center;
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.loading_indicator {
  padding: 16px;
  text-align: center;
  color: #666666;
  font-size: 14px;
}

/* Scrollbar Styling */
.floating_dropdown_list::-webkit-scrollbar {
  width: 4px;
}

.floating_dropdown_list::-webkit-scrollbar-track {
  background: transparent;
}

.floating_dropdown_list::-webkit-scrollbar-thumb {
  background: var(--primary_color);
  border-radius: 2px;
}

.floating_dropdown_list::-webkit-scrollbar-thumb:hover {
  background: var(--primary_color);
}

/* Section Header with Collapsible */
.section_header {
  display: flex;
  align-items: center;
  gap: 8px;

  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0.5rem;
}

.section_icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: var(--primary_color);
  color: white;
  border-radius: 50%;
  font-size: 14px;
  font-weight: 500;
}

.section_header h4 {
  margin: 0;
  font-weight: 500;
}

.expand_icon {
  transition: transform 0.2s ease;
  color: var(--text-black-60);
}

.expand_icon.expanded {
  transform: rotate(180deg);
}

/* Content Sections */
.details_content {
  display: flex;
  flex-direction: column;
  background-color: var(--background_color_secondary);
  border-radius: 8px;
}

.subtasks_content {
  margin-top: 1.5rem;
  margin-bottom: 16px;
}

/* Input Rows */
.date_row {
  display: flex;
  gap: 16px;
  justify-content: space-between;
  position: relative;
}

.input_row {
  display: flex;
  gap: 16px;
  justify-content: space-between;
  position: relative;
}

.input_row > * {
  flex: 1;
  min-width: 0;
}

/* Two column layout for smaller inputs */
.date_row > * {
  flex: 1;
  max-width: 14rem;
  margin-top: 4.8rem;
}

/* Footer Buttons */
.footer_buttons {
  display: flex;
  justify-content: space-between;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
  margin-top: auto;
  background: var(--main_background);
  border-radius: 0 0 2.5rem 2.5rem;
}

.save_continue_container {
  display: flex;
  justify-content: flex-start;
}

/* Animations */
@keyframes slideIn {
  from {
    transform: translate(100%, 0%);
    opacity: 0;
  }
  to {
    transform: translate(0%, 0%);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translate(0%, 0%);
    opacity: 1;
  }
  to {
    transform: translate(100%, 0%);
    opacity: 0;
  }
}

.closing {
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Radio Buttons Container (if needed) */
.radio_buttons_container {
  display: flex;
  gap: 1rem;
  margin: 0.5rem 0;
}

/* File Input Styling */
.file_input_wrapper {
  position: relative;
  width: 100%;
}

.file_input_hidden {
  display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .monthly_target_creation_form {
    width: calc(100% - 2rem);
    left: 1rem;
    right: 1rem;
    top: 2rem;
    height: calc(100% - 4rem);
  }

  .date_row,
  .input_row {
    flex-direction: column;
  }

  .date_row > *,
  .input_row > * {
    min-width: 100%;
  }
}

/* Custom Scrollbar */
.form_body::-webkit-scrollbar {
  width: 6px;
}

.form_body::-webkit-scrollbar-track {
  background: transparent;
}

.form_body::-webkit-scrollbar-thumb {
  background: var(--text-black-38);
  border-radius: 3px;
}

.form_body::-webkit-scrollbar-thumb:hover {
  background: var(--text-black-60);
}

.floating_dropdown_list::-webkit-scrollbar {
  width: 4px;
}

.floating_dropdown_list::-webkit-scrollbar-track {
  background: transparent;
}

.floating_dropdown_list::-webkit-scrollbar-thumb {
  background: var(--text-black-28);
  border-radius: 2px;
}

/* Focus States */
.section_header:focus,
.floating_dropdown_item:focus {
  outline: 2px solid var(--primary_color);
  outline-offset: 2px;
}

/* Loading and Error States */
.error_message {
  color: var(--error_color);
  font-size: 0.8rem;
  margin-top: 0.25rem;
  margin-left: 0.5rem;
}

.success_message {
  color: var(--success_color);
  font-size: 0.8rem;
  margin-top: 0.25rem;
  margin-left: 0.5rem;
}

/* Spacing Utilities */
.mb-1 {
  margin-bottom: 0.5rem;
}
.mb-2 {
  margin-bottom: 1rem;
}
.mb-3 {
  margin-bottom: 1.5rem;
}
.mt-1 {
  margin-top: 0.5rem;
}
.mt-2 {
  margin-top: 1rem;
}
.mt-3 {
  margin-top: 1.5rem;
}

.separator_line_container {
  width: 100%;
  height: 0.5rem;
  display: flex;
  align-items: center;
  margin-block: 1rem;
  justify-content: center;
  position: relative;
}

.dottedline_wrapper {
  width: 45%;
  border-bottom: 1px dashed var(--line-color);
  display: inline-block;
}

.separator_icon {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 1rem;
  color: var(--line-color);
  font-weight: 500;
}

/* Update the surycon_logo class if you want to keep it for other uses */
.surycon_logo {
  width: 3rem;
  height: auto;
  margin: 0 1rem;
}

.tasks_badges_container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
  margin-left: 1px;
  padding: 0.8rem;
}

.selected_tasks_display {
  width: 100%;
  overflow-x: auto;
  padding-bottom: 1px;
  margin-bottom: 16px;
}

.description_container {
  margin-bottom: 8px;
  width: 100%;
}

.description_label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-black-87);
}

.description_box {
  border: 1px solid var(--border_color);
  border-radius: 12px;
  padding: 8px;
  background-color: #0059681a;
  color: var(--text-black-87);
  font-size: 1rem;
  line-height: 1.5;
  overflow-wrap: break-word;
}

.final_details_content {
  padding: 16px;
  background-color: var(--background_color_secondary);
  border-radius: 8px;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.final_tasks_container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
}

.no_final_tasks {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px;
}

.no_final_tasks p {
  color: var(--text_color_light);
  font-style: italic;
}

.monthly_target_badge_final {
  background-color: var(--primary_color);
  color: white;
}

.monthly_target_badge_text_final {
  color: white;
  font-weight: 500;
}

/* Add these styles to your MonthlyTarget.module.css file */
.available_subtasks_section {
  margin-top: 1rem;
}

.section_label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--primary_color);
}

.available_subtasks_list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  max-height: 200px;
  overflow-y: auto;
  padding: 0.5rem;
  border: 1px solid var(--border_color);
  border-radius: 4px;
}

.available_subtask_item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  background-color: var(--light_bg);
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.9rem;
  gap: 0.5rem;
}

.available_subtask_item:hover {
  background-color: var(--hover_bg);
}

.available_subtask_item_selected {
  background-color: rgba(var(--primary_color_rgb), 0.1);
  border: 1px solid var(--primary_color);
  cursor: not-allowed;
  opacity: 0.7;
}

.add_subtask_button {
  background-color: var(--primary_color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  transition: transform 0.2s;
}

.add_subtask_button:hover {
  transform: scale(1.1);
}

.fetch_subtasks_button_container {
  margin: 1rem 0;
  display: flex;
  justify-content: flex-end;
}
