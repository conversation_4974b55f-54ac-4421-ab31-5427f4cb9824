import styles from "../Styles/VersionHistoryContainers.module.css";
import { <PERSON>, <PERSON><PERSON>, Clock, openVersion } from "../../../assets/icons";
import VersionHistoryIcons from "./VersionHistoryIcons";
import SubVersionContainers from "./SubVersionContainers";
import { FC, useState } from "react";
import UserVersionHistory from "./UserVersionHistory";
interface versionHistoryProps {
  versionIndex: number;
  versionData: { version: string; employees: [] };
}
const VersionHistoryContainers: FC<versionHistoryProps> = ({
  versionData,
  versionIndex,
}) => {
  const [showDetail, setShowDetail] = useState(false);
  const toggleDetail = () => {
    setShowDetail(!showDetail);
  };

  return (
    <div className={`${styles.versionHistory_accordian_container} `}>
      <div className={`${styles.versionHistory_accordian_parent} `}>
        <div
          onClick={versionIndex === 0 ? toggleDetail : () => console.log("l")}
          style={showDetail ? { transform: "rotate(-90deg)" } : {}}
        >
          <VersionHistoryIcons
            backgroundColor={
              versionIndex == 0
                ? "var(--primary_color)"
                : "var(--text-white-100)"
            }
            icon={versionIndex == 0 ? Arrow : openVersion}
            padding={versionIndex === 0 ? "0.7rem 0.9rem" : "0.5rem"}
          />
        </div>

        <div className={`${styles.versionHistory_accordian_parent} `}>
          <SubVersionContainers
            showDetail={showDetail}
            versionData={versionData}
            versionIndex={versionIndex}
          />
        </div>
      </div>
    </div>
  );
};

export default VersionHistoryContainers;
