.login_headings {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.login_headings h2 {}

.login_headings h3 {}

.login_inputs_container {
    width: 70%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 0.5rem;
}

.login_inputs_error {
    color: var(--warning_color);
    margin-left: 1rem;
}

.login_inputs {
    position: relative;
    background-color: var(--main_background);
    border-radius: 100px;
    display: flex;
    padding: 1rem;
    align-items: center;
    justify-content: flex-start;
}

.login_password_field input {
    width: calc(100% - 25px)
}

.login_input {
    width: 100%;
    border: none;
}

.login_input:focus {
    outline: none;
}

.login_input_password {
    width: 100%;
    border: none;
}

.login_hidepassword {
    position: absolute;
    right: 12px;
    top: 15px;
}

.login_utilties {
    display: flex;
    justify-content: space-between;
    margin-top: 0.3rem;
}

.login_utilties p {
    cursor: pointer;
}

.login_with_OTP {
    color: var(--secondary_color);
}

.login_button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login_footer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

.login_policies {
    text-align: center;
}

.login_policies p {


}


/* 1024 */
/* 1280 */