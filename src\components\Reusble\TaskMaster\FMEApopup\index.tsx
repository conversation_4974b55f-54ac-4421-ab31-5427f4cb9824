import { useState, useEffect, useRef } from "react";
import styles from "./Styles/FMEApopup.module.css";
import { useAppDispatch } from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { AddIcon, CloseIcon, MinusIcon } from "../../../../assets/icons";
import DiscardPopup from "../../Global/DiscardPopup";
import { closePopup } from "../../../../redux/features/Modules/Reusble/popupSlice";
import FloatingLabelInput from "../../Global/FloatingLabel";
import Button from "../../Global/Button";
import { useToast } from "../../../../hooks/ToastHook";
import { FmeaPopupProps } from "../TaskMasterInterfaces/TaskMasterInterface";
import { isValidValue } from "../../../../functions/functions";
import { settaskChangeAPiFlag } from "../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";

function FMEApopup({ onCancel, onSubmit, initialData }: FmeaPopupProps) {
  const [isSummaryPage, setIsSummaryPage] = useState<boolean>(false);
  const [isClosing, setIsClosing] = useState<boolean>(false);
  const dispatch = useAppDispatch();
  const [showDiscardConfirmation, setShowDiscardConfirmation] =
    useState<boolean>(false);
  const [errors, setErrors] = useState<{
    Description: boolean;
    solution: boolean;
    severity: boolean;
  }>({
    Description: false,
    solution: false,
    severity: false,
  });

  const [counter, setCounter] = useState<number | string>(
    initialData?.severity || ""
  );
  const [Description, setDescription] = useState<string>(
    initialData?.Description || ""
  );
  const [solution, setSolution] = useState<string>(initialData?.solution || "");

  const showToast = useToast();

  const isEmpty = () => {
    // console.log('outisde click tools inside if>>val:', value);
    if (Description === "" && solution === "" && counter === "") {
      return true;
    } else {
      return false;
    }
  };

  console.log(isSummaryPage, " this is summary page");
  const taskFormRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // console.log('outisde click tools',inputValue)
      if (formRef.current && !formRef.current.contains(event.target as Node)) {
        console.log("outisde click tools inside if");
        const isEmp = isEmpty();
        console.log("outisde click tools inside if >>isEmp:", isEmp);
        if (isEmp) {
          // setIsClosing(true);
          // setTimeout(onClose, 400);

          console.log("outisde click tools inside inner if");

          handleClose();
        }

        if (!hasChanges() && !showDiscardConfirmation && !isSummaryPage) {
          handleClose();
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [Description, solution, counter, dispatch]);
  useEffect(() => {
    if (initialData) {
      setDescription(initialData.Description);
      setSolution(initialData.solution);
      setCounter(initialData.severity);
    }
  }, [initialData]);

  console.log("counterrrrrr", counter, initialData?.severity);
  // Check if the form has unsaved changes
  const hasChanges = () => {
    if (initialData) {
      console.log("kivee oooo");
      return (
        Description?.trim() !== (initialData?.Description?.trim() ?? "") ||
        solution?.trim() !== (initialData?.solution?.trim() ?? "") ||
        Number(counter) !== (Number(initialData?.severity) ?? "")
      );
    } else {
      console.log(
        "thikkkkk heee",
        Description?.trim(),
        solution?.trim(),
        counter
      );
      return (
        Description?.trim() !== "" || solution?.trim() !== "" || counter !== ""
      );
    }
  };

  // Handle Discard Click
  const handleDiscard = () => {
    if (hasChanges()) {
      setShowDiscardConfirmation(true);
    } else {
      handleClose();
    }
  };

  const incrementCounter = (): void => {
    setCounter((prev) => {
      const value = Number(prev) || 0;
      return value < 10 ? value + 1 : value;
    });
  };

  const decrementCounter = (): void => {
    setCounter((prev) => {
      const value = Number(prev) || 1;
      return value > 1 ? value - 1 : value;
    });
  };

  const handleCounterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (
      value === "" ||
      (/^\d+$/.test(value) && +value > 0 && +value <= 10 && value !== "01")
    ) {
      setCounter(value);
    }
  };

  useEffect(() => {
    setErrors({ ...errors, severity: false });
  }, [counter]);

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      onCancel();
    }, 400);
  };

  const handleFormSubmit = () => {
    onSubmit({
      _id: initialData ? initialData?._id : Date.now(),
      Description: Description.trim(),
      solution: solution.trim(),
      severity: counter === "" ? "(1-10)" : counter.toString(),
    });
    showToast({
      messageContent: "Quality Ensuring Measures Added Successfully!",
      type: "success",
    });
    dispatch(settaskChangeAPiFlag(true));
    handleClose();
    // onCancel();
  };
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if(isClosing) return;
    if (e.key === "Enter" && e.shiftKey) return;

    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      if (!isSummaryPage && !showDiscardConfirmation) {
        if (!Description.trim() || !solution.trim() || !counter) {
          setErrors({
            Description: !Description,
            severity: !counter,
            solution: !solution,
          });
          showToast({
            messageContent: "Enter Required Fields!",
            type: "warning",
          });
          return;
        }
        setIsSummaryPage(true);
      }
      if (isSummaryPage) {
        handleFormSubmit();
      }
      if (showDiscardConfirmation) {
        handleClose();
      }
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      if (!isSummaryPage && !showDiscardConfirmation) {
        handleDiscard();
      }
      if (isSummaryPage) {
        setIsSummaryPage(false);
      }
      if (showDiscardConfirmation) {
        setShowDiscardConfirmation(false);
      }
    }
  };
  const formRef = useRef(null);
  useEffect(() => {
    console.log(isSummaryPage, "summarypageee");
    if (isSummaryPage || showDiscardConfirmation) {
      if (formRef.current) {
        formRef.current.focus();
      }
    }
  }, [isSummaryPage, showDiscardConfirmation]);

  return (
    <div
      className={`${styles.fmea_container} ${isClosing ? styles.closing : ""}`}
      tabIndex={0}
      onKeyDown={handleKeyDown}
      ref={formRef}
    >
      <div
        className={styles.fmea_header}
        style={{ color: showDiscardConfirmation ? "var(--warning_color)" : "" }}
      >
        <h3 style={{ maxWidth: "80%" }}>
          {showDiscardConfirmation
            ? "Are you sure you want to discard these changes?"
            : isSummaryPage
            ? initialData
              ? "Are you sure you want to update this Quality Ensuring Measures?"
              : "Are you sure you want to add this Quality Ensuring Measures?"
            : initialData
            ? "Edit Quality Ensuring Measures"
            : "Add Quality Ensuring Measures"}
        </h3>
        <button
          className={styles.closeButton}
          onClick={
            showDiscardConfirmation
              ? () => setShowDiscardConfirmation(false)
              : handleDiscard
          }
        >
          <CloseIcon />
        </button>
      </div>

      {showDiscardConfirmation ? (
        <div className={styles.summary_main_content}>
          {(isValidValue(Description) || initialData?.Description) && (
            <div className={styles.summaryDivData}>
              <div className={styles.summaryDataContent}>
                <p
                  className="p_tag_14px"
                  style={{ color: "var(--text-black-60)" }}
                >
                  Description
                </p>
                <h4
                  style={{
                    color:
                      initialData && Description !== initialData?.Description
                        ? "var(--secondary_color)"
                        : "var(--text-black-87)",
                  }}
                >
                  {Description || initialData?.Description}
                </h4>
              </div>
            </div>
          )}
          {(solution || initialData?.solution) && (
            <div className={styles.summaryDivData}>
              <div className={styles.summaryDataContent}>
                <p
                  className="p_tag_14px"
                  style={{ color: "var(--text-black-60)" }}
                >
                  Solution
                </p>
                <h4
                  style={{
                    color:
                      initialData && solution !== initialData?.solution
                        ? "var(--secondary_color)"
                        : "var(--text-black-87)",
                  }}
                >
                  {solution || initialData?.solution}
                </h4>
              </div>
            </div>
          )}
          {(isValidValue(counter) || initialData?.severity) && (
            <div className={styles.summaryDivData}>
              <div className={styles.summaryDataContent}>
                <p
                  className="p_tag_14px"
                  style={{ color: "var(--text-black-60)" }}
                >
                  Severity
                </p>
                <h4
                  style={{
                    color:
                      initialData &&
                      String(counter) !== String(initialData?.severity)
                        ? "var(--secondary_color)"
                        : "var(--text-black-87)",
                  }}
                >
                  {counter === "" ? "(1-10)" : counter || initialData?.severity}
                </h4>
              </div>
            </div>
          )}
        </div>
      ) : isSummaryPage ? (
        <div className={styles.summary_main_content}>
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                className="p_tag_14px"
                style={{ color: "var(--text-black-60)" }}
              >
                Description
              </p>
              <h4
                style={{
                  color:
                    initialData && Description !== initialData?.Description
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                }}
              >
                {Description}
              </h4>
            </div>
          </div>
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                className="p_tag_14px"
                style={{ color: "var(--text-black-60)" }}
              >
                Solution
              </p>
              <h4
                style={{
                  color:
                    initialData && solution !== initialData?.solution
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                }}
              >
                {solution}
              </h4>
            </div>
          </div>
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                className="p_tag_14px"
                style={{ color: "var(--text-black-60)" }}
              >
                Severity
              </p>
              <h4
                style={{
                  color:
                    initialData &&
                    String(counter) !== String(initialData?.severity)
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                }}
              >
                {counter === "" ? "(1-10)" : counter}
              </h4>
            </div>
          </div>
        </div>
      ) : (
        <div className={styles.input_tags_wrapper}>
          <FloatingLabelInput
            label="Description"
            id="Description"
            focusOnInput={true}
            placeholder="Description"
            isInvalid={false}
            error={errors?.Description}
            value={Description}
            props="description_prop"
            onInputChange={(value: string) => {
              setErrors({ ...errors, Description: false });
              setDescription(value.replace(/^\s+/, ""));
            }}
          />
          <FloatingLabelInput
            label="Solution"
            id="Solution"
            placeholder="Solution"
            props="description_prop"
            isInvalid={false}
            error={errors?.solution}
            value={solution}
            onInputChange={(value: string) => {
              setErrors({ ...errors, solution: false });
              setSolution(value.replace(/^\s+/, ""));
            }}
          />
          <div
            className={`${styles.fmea_severity_div} ${
              errors?.severity ? styles.error : ""
            }`}
          >
            <p>Severity:</p>
            <div className={styles.fmea_severity_counter}>
              <div
                className={styles.severity_minus}
                onClick={decrementCounter}
                style={{ cursor: "pointer" }}
              >
                <MinusIcon />
              </div>
              <input
                type="text"
                value={counter}
                onChange={handleCounterChange}
                style={{
                  width: "40px",
                  textAlign: "center",
                  border: "none",
                  fontWeight: "600",
                  outline: "none",
                  color: "var(--text-black-87)",
                }}
                placeholder="(1-10)"
              />
              <div
                className={styles.severity_plus}
                onClick={incrementCounter}
                style={{ cursor: "pointer" }}
              >
                <AddIcon />
              </div>
            </div>
          </div>
        </div>
      )}

      <div className={styles.fmea_btngrp}>
        {isSummaryPage && !showDiscardConfirmation ? (
          <>
            <Button
              type="Cancel"
              Content="Back"
              Callback={() => setIsSummaryPage(false)}
            />
            <Button type="Next" Content="Submit" Callback={handleFormSubmit} />
          </>
        ) : showDiscardConfirmation ? (
          <>
            <Button
              type="Cancel"
              Content="No"
              Callback={() => setShowDiscardConfirmation(false)}
            />
            <Button type="Next" Content="Yes" Callback={() => handleClose()} />
          </>
        ) : (
          <>
            <Button type="Cancel" Content="Cancel" Callback={handleDiscard} />
            <Button
              type="Next"
              Content={initialData ? "Update" : "Add"}
              Callback={() => {
                if (!Description || !solution || !counter) {
                  setErrors({
                    Description: !Description.trim(),
                    severity: !counter,
                    solution: !solution.trim(),
                  });

                  showToast({
                    messageContent: "Enter Required Fields!",
                    type: "warning",
                  });

                  return;
                }
                setIsSummaryPage(true);
              }}
            />
          </>
        )}
      </div>
    </div>
  );
}

export default FMEApopup;
