/*  AUTHOR NAME : CHARVI */
.add_category_type_maindiv {
  position: fixed;
  top: 48%;
  right: 0;
  transform: translate(-5%, -43%);
  background-color: var(--main_background);
  padding: 1.25rem;
  box-shadow: var(--extra-shadow-five);
  border-radius: 2.6rem;
  z-index: 9999;
  width: 23rem;
  background: var(--white-50-background);
  backdrop-filter: blur(150px);
  border: 1px solid;
  border-image-source: var(--primary-bg-gradient);
  min-height: 85vh;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideOut {
  from {
    transform: translate(-5%, -43%);
  }
  to {
    transform: translate(100%, -43%);
  }
}

.add_category_type_maindiv.closing {
  animation: slideOut 0.5s ease-out;
}

@keyframes slideIn {
  from {
    transform: translate(100%, -43%);
  }
  to {
    transform: translate(-5%, -43%);
  }
}

@keyframes ios-shake {
  0%,
  100% {
    transform: translateX(-53%) rotate(0deg);
  }
  15% {
    transform: translateX(-53%) rotate(-5deg);
  }
  30% {
    transform: translateX(-53%) rotate(5deg);
  }
  45% {
    transform: translateX(-53%) rotate(-3deg);
  }
  60% {
    transform: translateX(-53%) rotate(3deg);
  }
  75% {
    transform: translateX(-53%) rotate(-1deg);
  }
  85% {
    transform: translateX(-53%) rotate(1deg);
  }
}

.shake {
  transform-origin: center center;
  animation: ios-shake 0.8s ease-in-out;
}

.add_category_type_innerdiv {
  margin: 0.5rem;
  padding: 0.2rem;
  max-height: 60vh;
  overflow-y: auto;
}

.add_category_type_innerdiv li:hover {
  color: var(--primary_color) !important;
}

.category {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 90%;
  padding: 0.6rem 1rem;
  margin: 0.5rem;
  border-radius: 1.5rem;
  background: var(--white-70-background);
  box-shadow: var(--extra-shadow-four);
  position: relative;
  left: 50%;
  transform: translateX(-53%);
  cursor: pointer;
  list-style-type: none;
  color: var(--text-black-60);
  border: 1px solid;
  border-image-source: var(--primary-bg-gradient);
  backdrop-filter: blur(100px);
}

.category:hover {
  background: var(--main_background);
  border: 1px solid;
  border-image-source: var(--primary-bg-gradient);
  backdrop-filter: blur(100px);
  color: var(--primary_color);
  box-shadow: var(--extra-shadow-four);
}

.add_category_type_button_div {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  padding: 1rem;
  display: flex;
  justify-content: center;
  gap: 1rem;
  border-radius: 2.6rem;
  backdrop-filter: blur(60px);
  border: 1px solid;
  border-image-source: var(--primary-bg-gradient);
  background: var(--main_background);
}

.popup_close_icon {
  position: absolute;
  top: 1.4rem;
  right: 0.2rem;
  background-color: none;
  padding: 0.5rem;
}

.closebtn_category {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
}

.categorypopupheading {
  color: var(--primary_color);
  display: flex;
  justify-content: center;
  padding: 0.6rem 0.6rem 0.4rem 0.6rem;
}

.summaryDivData {
  display: flex;
  align-items: center;
}

.summaryDataContent {
  display: flex;
  flex-direction: column;
  background: var(--main_background);
  border-radius: 0.75rem;
  width: 30.8rem;
  min-height: 3rem;
  padding: 1rem;
  white-space: normal;
  margin: 0.6rem;
  line-height: 1.363rem;
  text-align: left;
}

.summaryItem {
  display: flex;
}

/* brand and grade code start from here by aayush malviya */
.brand_circle {
  height: 15px;
  width: 15px;
  border-radius: 50px;
  border: 1.5px solid var(--primary_color);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.brand_circle_gray {
  height: 15px;
  width: 15px;
  border-radius: 50px;
  border: 1.5px solid var(--extra-gray-28);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.brand_dottedline {
  width: 40px;
  height: 5px;
  border-bottom: 1.5px dashed var(--primary_color);
  margin-bottom: 0.3rem;
}

.brand_dottedline_gray {
  border-bottom: 1.5px dashed var(--extra-gray-28);
  margin-bottom: 0.3rem;
  width: 40px;
  height: 5px;
}

.brand_top_container {
  width: 100%;
  display: flex;
  gap: 0.3rem;
  justify-content: center;
  align-items: center;
}

.brand_grade_text {
  display: flex;
  gap: 0.2rem;
  margin-top: 0.3rem;
}

.brand_grade_text p:nth-child(1) {
  color: var(--text-black-60);
}

.brand_grade_text p:nth-child(2) {
  color: var(--text-black-87);
}
.summaryItems{
  display: flex;
  flex-wrap: wrap;
}
/* brand and grade code end from here by aayush malviya */
