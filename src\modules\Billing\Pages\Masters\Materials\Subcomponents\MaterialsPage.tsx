import styles from "../Styles/Materials.module.css";
import { useEffect, useRef, useState } from "react";
import { Loader } from "../../../../../../assets/loader";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import {
  useDeleteMaterialByToolIdMutation,
  useLazyGetMaterialDesiginationDetailByIdQuery,
} from "../../../../../../redux/api/Modules/Billing/Billingapi";
import TMMMNav from "../../../../../../components/Reusble/TMMMNav";
import MasterCard from "../../../../../../components/Reusble/Billing/Masters/MasterCard";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import { useToast } from "../../../../../../hooks/ToastHook";
import { closePopup } from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import { resetFormMaterialsData } from "../../../../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import {
  checkAndDownloadImages,
  initializeDatabase,
  isValidValue,
  slicedData,
  withInternetCheck,
} from "../../../../../../functions/functions";
import { useParams } from "react-router-dom";
import { useNestedPouchSearch } from "../../../../../../functions/useNestedLocalSearch";
import MaterialDiscard from "./MaterialDiscard";
import {
  clearFetchedMasters,
  SetCategoryId,
  setFetchedMastersDesignation,
  setSearchData,
} from "../../../../../../redux/features/Modules/Masters";
import { useSelector } from "react-redux";
import { RootState, store } from "../../../../../../redux/store";
const MaterialsPage = () => {
  const currentOpenPopup = useAppSelector((state) => state.popup.popups);
  const formData = useAppSelector(
    (state) => state.masterForm.formMaterialsData
  );
  const [materialCardData, setMaterialCardData] = useState<any>([]);
  const [searchmaterialCardData, setSearchMaterialCardData] = useState<any>([]);
  const [editToolData, setEditToolData] = useState<any>({});
  const [page, setPage] = useState(1);
  const dispatch = useAppDispatch();
  const { materialsCategoryId } = useParams();
  //to detect changes in the localdb
  const detectChanges = useAppSelector((state) => state.backupSlice.isOpen);
  const searchKey = useAppSelector((state) => state.taskMaster.searchKey);
  const [searchLocalKey, setSearchLocalKey] = useState("");
  const [deleteMaterialDesignation] = useDeleteMaterialByToolIdMutation();
  const localChange = useAppSelector(
    (state) => state.backupSlice.isLocalChange
  );
    const searchedData = useSelector((state: RootState) => state.masterReduxSlice.searchedData)

  const showToast = useToast();
  // If you have a RootState type defined in your Redux setup, use it here:

  // ...

  const data = useSelector(
    (state: RootState) => state.masterReduxSlice.fetchedData
  );
  console.log(data, "this is the data");
  console.log("material category id", materialsCategoryId);

  //get tool's edit data
  const [getMaterialDesiginationDetails] =
    useLazyGetMaterialDesiginationDetailByIdQuery();

  const getDatafromDb = async (p: any, id: string) => {
    const dbName = await initializeDatabase("MaterialDesignation");
    const fetchedData = await window.electron.getDocumentByParentId({
      dbName,
      page: p,
      catId: id,
      categoryId: "materialCategoryId",
      isDeletedNext: false,
      needSorting: true,
    });
    console.log(fetchedData, "daataata>");
    if (p === 1) {
      dispatch(setFetchedMastersDesignation({ data: fetchedData, page: p }));
    } else {
      console.log("newww dataaa2", data);
      const previousData = store.getState().masterReduxSlice.fetchedData;
      const newData = [...previousData, ...fetchedData];

      console.log("newww dataaa", newData);
      dispatch(setFetchedMastersDesignation({ data: newData, page: p }));
    }

    const imageDownload = withInternetCheck(() =>
      checkAndDownloadImages("MaterialDesignation", fetchedData, dispatch)
    );
    imageDownload();
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;

    if (target) {
      const { scrollHeight, clientHeight, scrollTop } = target;

      if (scrollTop + clientHeight >= scrollHeight - 1) {
        console.log("page changed", page);
        setPage((prev) => prev + 1);
      }
    }
  };

  // useEffect(() => {
  //   if (localChange) {
  //     setPage(1);
  //   }
  // }, [detectChanges]);

  useEffect(() => {
    if (searchedData?.length > 0) {
      const imageDownload = withInternetCheck(() =>
        checkAndDownloadImages(
          "MaterialDesignation",
          searchedData,
          dispatch
        )
      );
      imageDownload();
    }
  }, [searchedData]);

  useEffect(() => {
    if (page && materialsCategoryId) {
      getDatafromDb(page, materialsCategoryId);
    }
  }, [page]);

  useEffect(() => {
    setSearchLocalKey(searchKey);
  }, [searchKey]);

  useNestedPouchSearch({
    pathRecord: "MaterialDesignation",
    searchKey: searchLocalKey,
    setData: setSearchData,
    setPage,
    key: "name",
    extraSearchParams: {
      catId: materialsCategoryId,
      categoryId: "materialCategoryId",
      isDeletedNext: false,
    },
  });

  const navRef = useRef<HTMLDivElement>(null);
  const mainContentRef = useRef<HTMLDivElement>(null);
  // const [width, setWidth] = useState<null | number>(null);
  const clientFunction = () => {
    // const cardViewcontainer = document.querySelector(`.${styles.progress_card_view_container}`) as HTMLDivElement;
    // console.log( 'details of card view container in useEffect div',cardViewcontainer);
    // const details = cardViewcontainer?.getBoundingClientRect();
    // console.log('details of card view container in useEffect details',details,);
    // setWidth(details?.width);
    const mainContentWidth =
      mainContentRef?.current?.getBoundingClientRect()?.width;
    console.log("inner width");
    // if (window.innerWidth < 1200) {
    navRef.current?.style.setProperty("width", `${mainContentWidth}px`);
    // }
  };
  const condition =
    mainContentRef.current &&
    mainContentRef.current.getBoundingClientRect().width < 1200;
  useEffect(() => {
    clientFunction();
    const handleResize = () => clientFunction();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [window.innerWidth, condition]);

  useEffect(() => {
    return () => {
      dispatch(clearFetchedMasters());
    };
  }, []);

  useEffect(() => {
    dispatch(SetCategoryId(materialsCategoryId as string));
  }, [materialsCategoryId]);

  return (
    <>
      <div ref={navRef}>
        <TMMMNav Label={"Materials"} TargetForm={"AddMaterialsForm"} />
      </div>
      <div
        ref={mainContentRef}
        className={styles.main_content_wrapper}
        style={{ marginTop: "1.5rem" }}
      >
        <div className={styles.cardview} onScroll={(e) => handleScroll(e)}>
          <div className={`${styles.inner_cardview} ${styles.inner_cardview2}`}>
            {data && data.length > 0 ? (
              (searchedData?.length > 0
                ? searchedData
                : data
              ).map((item: any) => (
                <>
                  <MasterCard
                    variant="material"
                    callbackEditData={async () => {
                      const response = await getMaterialDesiginationDetails({
                        materialId: item?._id,
                      });
                      setEditToolData(response?.data?.data);
                      return response;
                    }}
                    editData={editToolData}
                    // cardBlockSize="171px"
                    data={{
                      _id: item?._id,
                      title: item?.name,
                      images: item?.images?.[0],
                      unit: item?.unit,
                      items: [
                        { title: "Brands", name: item?.BrandDetails?.length },
                        { title: "Grades", name: item?.Grades },
                      ],
                      brandDetails: item?.BrandDetails,
                    }}
                  />
                </>
              ))
            ) : (
              <div className={styles.loader_loading}>
                <img
                  src={Loader.suryaconLogo}
                  alt="Loading..."
                  className={styles.loader_loading_image}
                />
              </div>
            )}
          </div>
        </div>
      </div>
      {currentOpenPopup["deleteMaterial"] && (
        <DeletePopup
          header="Are you sure you want to delete this Material?"
          height="calc(100% - 7.25rem)"
          heightupperlimit="0"
          callbackDelete={async () => {
            await deleteMaterialDesignation({
              materialId: formData?._id,
            }).unwrap();
            showToast({
              messageContent: `Material Deleted Successfully!`,
              type: "success",
            });
            dispatch(closePopup("deleteMaterial"));
            dispatch(resetFormMaterialsData());
          }}
          onClose={() => {
            dispatch(closePopup("deleteMaterial"));
            dispatch(resetFormMaterialsData());
          }}
        >
          <MaterialDiscard
            formData={formData}
            // initialFormData={initialFormData}
            // formMode={formMode}
            // deletedFormData={deletedFormData}
            // deletedGradeData={deletedGradeData}
          />
        </DeletePopup>
      )}
    </>
  );
};

export default MaterialsPage;
