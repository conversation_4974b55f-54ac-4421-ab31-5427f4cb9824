import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  AuthState,
  User,
} from "../../../Interfaces/Modules/Auth/AuthInterface";

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  isWeb: true,
  cookies: null,
  isOfflineMode: false, // ✅ added this line
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setCredentials: (state, action: PayloadAction<any>) => {
      console.log("state is working");
      state.isAuthenticated = action.payload.isAuthenticated;
      state.user = action.payload.user;
    },
    setIsWeb: (state, action: PayloadAction<boolean>) => {
      state.isWeb = action.payload;
    },
    setCookies: (state, action: PayloadAction<Object | null>) => {
      state.cookies = action.payload;
    },
    setOfflineMode: (state, action: PayloadAction<boolean>) => {
      state.isOfflineMode = action.payload;
      console.log("🛑 Offline mode:", action.payload);
    },
  },
});

export const {
  setCredentials,
  setIsWeb,
  setCookies,
  setOfflineMode, // ✅ export this
} = authSlice.actions;

export default authSlice.reducer;
