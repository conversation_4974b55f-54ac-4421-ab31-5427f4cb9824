/* AUTHOR NAME : CHARVI */

import { FC, useState, useEffect } from "react";
import styles from "./Styles/UnitPopup.module.css";
import { UnitPopupPropType } from "../GlobalInterfaces/GlobalInterface";

const UnitPopup: FC<UnitPopupPropType> = ({
  data,
  left,
  right,
  height,
  width,
  alignment,
  top,
  onSelect,
  selectedId,
  property,
}) => {
  useEffect(() => {
    setSelectedItem(selectedId);
  }, [selectedId]);

  const [selectedItem, setSelectedItem] = useState<number | string | null>(
    selectedId
  );

  const toggleSelection = (itemId: number | string) => {
    const selectedItemData = data.find((item) => item.id === itemId);
    if (selectedItemData) {
      onSelect(selectedItemData);
    }
    setSelectedItem(itemId);
  };

  return (
    <div
      className={`${styles.unitpopup_container} ${styles[property || ""]}`}
      style={{
        left: left ?? "",
        right: right ?? "",
        top: top ?? "",
        height: height ?? "",
        maxHeight: height ?? "",
        position: alignment as React.CSSProperties["position"],
        width: width ?? "",
        minWidth: width ?? "",
      }}
    >
      <ul
        className={`${styles.unitpopup_list} ${styles[property || ""]}`}
        style={{
          height: height ? "9.5rem" : "",
          maxHeight: height ? "9.5rem" : "",
        }}
      >
        {data && data.length > 0 ? (
          data?.map((item, index) => (
            <li
              key={index}
              className={`${styles.unit} ${
                selectedItem === item.id ? styles.unitselected : ""
              }`}
              onClick={(e) => {
                e.stopPropagation();
                toggleSelection(item.id);
              }}
            >
              {item.label}
            </li>
          ))
        ) : (
          <li className={`${styles.unit}`}>No Data..</li>
        )}
      </ul>
    </div>
  );
};

export default UnitPopup;
