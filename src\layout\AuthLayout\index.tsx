import { Outlet, useNavigate } from "react-router-dom";
import styles from "./Styles/AuthLayout.module.css";
import { useCheckAuthQuery } from "../../redux/api/Modules/Auth/Authapi";
import { useAuth } from "../../AuthProvider";
import { useEffect } from "react";
import SuryconLoogo from '../../../public/SuryaconLogo.png'
import { SuryaconMainLogo } from "../../assets/TopbarAssets/SVGs";

const AuthLayout = () => {
  const { data, isLoading } = useCheckAuthQuery();
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();
  useEffect(() => {
    if (isAuthenticated) {
      navigate("/category");
    }
  }, [isAuthenticated]);
  return (
    !isLoading && (
      <div className={`${styles.AuthLayout_container}`}>
        <div className={`${styles.AuthLayout_outerParent}`}>
          <div className={`${styles.AuthLayout_gradient}`}>
            <div className={`${styles.AuthLayout_innerParent}`}>
              <div>
                <SuryaconMainLogo />
              </div>
              <Outlet />
            </div>
          </div>
        </div>
      </div>
    )
  );
};

export default AuthLayout;
