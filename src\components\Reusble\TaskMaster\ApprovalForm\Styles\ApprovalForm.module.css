.approvalform_container {
  position: fixed;
  top: 7rem;
  right: 1.5rem;
  transform: translate(0%, 0%);
  padding-inline: 24px;
  padding-top: 32px;
  padding-bottom: 24px;
  z-index: 20;
  width: 34rem;
  animation: slideIn 0.5s ease-out;
  border-radius: 2.6rem;
  border: 1px solid;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -22.43%,
    rgba(251, 251, 251, 0.05) 75.66%
  );
  backdrop-filter: blur(150px);
  box-shadow: 0 0.25rem 2.5rem 0 rgba(0, 0, 0, 0.5); /* 4px 40px 0px => 0.25rem 2.5rem 0 */
  height: calc(100% - 8.5rem);
}

@keyframes slideIn {
  from {
    transform: translate(100%, 0%);
  }

  to {
    transform: translate(0%, 0%);
  }
}

@keyframes slideOut {
  from {
    transform: translate(0%, 0%);
  }

  to {
    transform: translate(100%, 0%);
  }
}

.approvalform_container.closing {
  animation: slideOut 0.5s ease-out;
}

@media only screen and (max-width: 1280px) {
  .approvalform_container {
    width: 32rem;
  }
}

.approvalform_closeButton {
  position: absolute;
  top: 2rem; 
  right: 0.625rem; 
  padding: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
}

.approvalform_header {
  color: var(--primary_color);
  display: flex;
  justify-content: center;
  align-items: center;
  height: 3rem;
  margin-bottom: 24px;
}
.approvalform_title{
  font-size: 21px;
  font-weight: 600;
  font-family: "Nunito";
  text-align: center;
  padding-inline: 40px;
  line-height: 24px;
}
.approvalform_header_decline {
  color: #A80000;
}

.approvalform_datainputs {
  height: calc(100% - 8.5rem);
  overflow-y: scroll;
  margin-bottom: 24px;
}

.approvalform_btngrp {
  display: flex;
  justify-content: center;
  gap: 1rem;
}



.approvalform_horizaontalData{
  display: flex;
  flex-direction: row;
  column-gap: 24px;
  flex-wrap: wrap;
  font-family: "Nunito";
  font-size: 16px;
  font-weight: 400;
  color: var(--text-black-87);
}


.approvalform_summaryDataContent p {
  font-size: 14px; 
  font-family: "Nunito";
  color: var(--text-black-60);
  font-weight: 500;
}

.approvalform_summaryDataContent {
  display: flex;
  flex-direction: column;
  border-radius: 0.75rem;
  background-color: var( --main_background);
  width:100%;
  min-height: 77px;
  padding: 1rem;
  white-space: normal;
  margin-bottom: 12px;
  text-align: left;
  gap: 4px;
  /* background-color: blue; */
}



.approvalform_reasonInput::placeholder {
  color: rgba(0, 0, 0, 0.6);
  font-weight: 400;
  font-size: 1rem; /* 16px */
  font-family: "Nunito";
}

.approvalform_reasonInput {
  width: 100%;
  height: 52px;
  padding: 0.75rem 1rem;
  border-radius: 3.125rem; /* 50px */
  border: 1px solid rgba(0, 0, 0, 0.28);
  font-size: 1rem;
  outline: none;
  margin-bottom: 12px;
}
  

.approvalform_line_container {
  width: 100%;
  height: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
  margin-top: 4px;
  margin-bottom: 24px;

}

.approvalform_dottedline_wrapper {
  width: 49%;
  border-bottom: 1px dashed rgba(176, 204, 209, 1)
}

.approvalform_reporter p{

  font-size: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.87);
}

.approvalform_sectionHeading{
  font-size: 1rem;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.87);
  font-family: "Nunito";
  margin-bottom: 1rem;
}