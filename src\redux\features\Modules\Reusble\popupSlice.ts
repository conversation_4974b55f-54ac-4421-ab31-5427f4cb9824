import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { PopupSliceState } from "../../../Interfaces/Modules/Reuseable/Reuseable";
import { Console } from "console";

const initialState: PopupSliceState = {
  popups: {},
  openDocData: [],
  categoryPopup: null,
  approvalFormStep: "initial", // <-- NEW
};

const popupSlice = createSlice({
  name: "popup",
  initialState,
  reducers: {
    openPopup(state, action: PayloadAction<string>) {
      state.popups = {};
      console.log("payload--", action);
      state.popups[action.payload] = true;
    },
      closePopup(state, action: PayloadAction<string>) {
      delete state.popups[action.payload];
      state.approvalFormStep = "initial"; // reset when closing
    },

    togglePopup(state, action: PayloadAction<string>) {
      state.popups[action.payload] = !state.popups[action.payload];
    },

    openCategoryPopup(state, action: PayloadAction<string>) {
      state.categoryPopup = action.payload;
    },
    clearCategoryPopup(state) {
      state.categoryPopup = null;
    },
    setApprovalFormStep(state, action: PayloadAction<"initial" | "approve" | "decline" | "reason">) {
      state.approvalFormStep = action.payload;
    },
    clearPopups(state) {
      state.popups = {};
    },
    setOpenDocData(state, action: PayloadAction<any>) {
      // state.openDocData = action.payload;

      // Remove existing records where openedbyemployee is null
      // let filteredArray = state.openDocData.filter(
      //   (item: any) => item.openedbyemployee !== null
      // );

      // Convert filtered array to a Map for quick lookup by id
      const recordMap = new Map(
        state?.openDocData &&
          state?.openDocData?.map((item: any) => [item.id, item])
      );

      // Process each new record
      action.payload.forEach((record: any) => {
        // if (record.openedbyemployee !== null) {
        recordMap && recordMap?.set(record.id, record); // Adds new or updates existing record
        // }
      });

      // Convert the map back to an array
      let result = Array.from(recordMap.values()) as any;
      state.openDocData = result.filter(
        (item: any) => item.openedbyemployee !== null
      );
    },
  },
});

export const {
  openPopup,
  closePopup,
  togglePopup,
  openCategoryPopup,
  clearCategoryPopup,
  clearPopups,
  setOpenDocData,
  setApprovalFormStep, // <-- new
} = popupSlice.actions;
export default popupSlice.reducer;
