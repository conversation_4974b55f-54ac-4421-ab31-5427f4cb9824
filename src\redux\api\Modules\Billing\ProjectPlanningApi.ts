import { billing, taskmaster } from "../../../../config/urls";
import { baseApi } from "../../index";
import {
  GetTowerResponse,
  GetSubTaskResponse,
} from "./Interfaces/BillingInterfaces";

export const BillingMasterApi = baseApi.injectEndpoints({
  endpoints: (builder) => ({
    getTowerRoutes: builder.query<
      GetTowerResponse,
      { locationId: string; time: string }
    >({
      query: ({ locationId, time }) => ({
        url: `${billing}/ProjectData/locations/getPlanningrouteForPlanning/${locationId}?sincetime=${time}`,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
      providesTags: ["NewTask", "DeleteTask", "AddTaskPlanning"],
    }),
    // get task basic details by id start
    getTaskBasicDetailByTowerId: builder.query<
      GetSubTaskResponse,
      { locationId: string; taskId: string }
    >({
      query: ({ locationId, taskId }) => ({
        url: `${billing}/ProjectData/locations/getBasicDetailsOfTowerForPlanning?location_id=${locationId}&task_id=${taskId}`,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
    }),

    // get task basic details by id end

    getSubTaskOfTaskRoute: builder.query<
      GetSubTaskResponse,
      { locationId: string; taskId: string }
    >({
      query: ({ locationId, taskId }) => ({
        url: `${billing}/ProjectData/locations/getTaskRouteForPlanning?location_id=${locationId}&task_id=${taskId}`,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
      providesTags: ["SubTasks"],
    }),
    getSubTaskDetailById: builder.query<
      GetSubTaskResponse,
      { SubTaskId: string }
    >({
      query: ({ SubTaskId }) => ({
        url: `${billing}/ProjectData/locations/getSubtaskDetailsById?Subtask_id=${SubTaskId}`,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
      providesTags: ["SubTasks"],
    }),
    getAllSubtaskForTaskRouteForPlanning: builder.query<
      GetTowerResponse,
      { locationId: string; taskId: string; time: string}
    >({
      query: ({ locationId, taskId ,time}) => ({
        url: `${billing}/ProjectData/locations/getAllSubtaskForTaskRouteForPlanning?location_id=${locationId}&task_id=${taskId}&sincetime=${time}`,
        method: "GET",
      }), 
      keepUnusedDataFor: 0,
      providesTags: ["NewTask", "DeleteTask"],
    }),
    updateLocationDetails: builder.mutation<void, { data: any }>({
      query: ({ data }) => ({
        url: `${billing}/ProjectData/locations/addandupdateprojectlocationdetails`,
        method: "POST",
        body: data, // Pass the data to the request body
      }),

      // invalidatesTags: ["NewTask", "SubTasks"],
    }),
    getMaterialDetailsForPlanning: builder.query<GetTowerResponse, any>({
      query: ({ locationId, taskId }) => ({
        url: `${billing}/ProjectData/locations/getMaterialDetailsForPlanning?location_id=${locationId}&task_id=${taskId}`,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
    }),
    getMaterialDetailsForSubtask: builder.query<GetTowerResponse, any>({
      query: ({ locationId, materialId }) => ({
        url: `${billing}/ProjectData/locations/getMaterialDetailsForCorrespondingSubtasksInPlanning?location_id=${locationId}&material_id=${materialId}`,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
    }),
    getAllTasksFromTaskMaster: builder.query<GetTowerResponse, any>({
      query: () => ({
        url: `${taskmaster}/getAllTasksFromTaskmaster`,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
    }),
    getDataOfParticularTower: builder.query<GetTowerResponse, any>({
      query: ({ towerId }) => ({
        url: `${billing}/ProjectData/locations/getperticulartowerlocationdetailsbyId?towerId=${towerId}`,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
    }),
    getSubtasksRouteToList: builder.query<GetTowerResponse, any>({
      query: ({ taskId }) => ({
        url: `${taskmaster}/getSubtasksRoute/${taskId}`,
        method: "GET",
      }),
      keepUnusedDataFor: 0,
    }),
    AddTaskInPlanningRoute: builder.mutation<
      void,
      { taskName: string; taskId: string; location_id: any }
    >({
      query: (data) => ({
        url: `${billing}/ProjectData/locations/addTaskInPlanningRoute`,
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["AddTaskPlanning"],
    }),
    sendApprovalByhod: builder.mutation<any, { location_id: string }>({
      query: ({ location_id }) => ({
        url: `/billingmaster/ProjectData/locations/sendApprovalByhod`,
        method: "POST",
        params: { location_id },
      }),
    }),
  }),
});

export const {
  useGetTowerRoutesQuery,
  useGetSubTaskOfTaskRouteQuery,
  useGetSubTaskDetailByIdQuery,
  useLazyGetSubTaskDetailByIdQuery,
  useGetAllSubtaskForTaskRouteForPlanningQuery,
  useUpdateLocationDetailsMutation,
  useGetMaterialDetailsForPlanningQuery,
  useGetMaterialDetailsForSubtaskQuery,
  useGetAllTasksFromTaskMasterQuery,
  useAddTaskInPlanningRouteMutation,
  useGetSubtasksRouteToListQuery,
  useGetTaskBasicDetailByTowerIdQuery,
  useGetDataOfParticularTowerQuery,
  useSendApprovalByhodMutation,
} = BillingMasterApi;
