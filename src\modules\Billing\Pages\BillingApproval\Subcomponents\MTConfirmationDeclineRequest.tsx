import React, { FC } from 'react'
import styles from '../Styles/BillingApprovalSubcomponent.module.css'
import Button from '../../../../../components/Reusble/Global/Button'
import FloatingLabelInput from '../../../../../components/Reusble/Global/FloatingLabel'
import { MTSubTaskConfirmationDeclineRequestProps } from '../../../../../interfaces/Modules/Billing/BillingApproval/BillingApproval'

const MTConfirmationDeclineRequest: FC<MTSubTaskConfirmationDeclineRequestProps>  = ({onClose} ) => {
  return (
    <div className={`${styles.mt_confirmation_decline_dialog_form_container}`}>
      <h3 className={`${styles.mt_confirmation_decline_dialog_form_top_text}`}>
        Are you sure you want to decline this request?
      </h3>
      <div className={`${styles.mt_confirmation_decline_form_main}`}>
        <FloatingLabelInput onInputChange={()=>{}}  id='101'  props='mt_confirmation_decline_reason_text_area' label='Reason'/>

        <div className={`${styles.mt_confirmation_decline_button_div} `}>
              <Button Callback={onClose} type="Cancel" Content="Cancel" property="mtc_decline_form_cancel_button"/>
              <Button type="Decline" Content="Decline" property="mtc_decline_form_button_decline"/>
        </div>
      </div>
    </div>
  )
}

export default MTConfirmationDeclineRequest
