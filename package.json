{"name": "surycon-frontend", "private": true, "version": "0.0.0", "type": "module", "main": "src/electron/main.js", "scripts": {"dev": "cross-env HTTPS=True vite", "electron:dev": "cross-env HTTPS=True  NODE_ENV=development electron --no-sandbox src/electron/main.js", "build:mac": "electron-builder --mac", "electron:build": "electron-builder", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "echo \"No tests specified\""}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@ffmpeg/ffmpeg": "^0.11.6", "@ffmpeg/util": "^0.12.2", "@material/floating-label": "^14.0.0", "@mbs-dev/react-image-compressor": "^1.0.5", "@reduxjs/toolkit": "^2.3.0", "@types/crypto-js": "^4.2.2", "axios": "^1.9.0", "cross-env": "^7.0.3", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "electron-is-dev": "^3.0.1", "events": "^3.3.0", "express": "^4.21.2", "ffmpeg": "^0.0.4", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "node-machine-id": "^1.1.12", "p-queue": "^8.1.0", "pouchdb": "^9.0.0", "pouchdb-adapter-websql": "^7.0.0", "pouchdb-find": "^9.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-loading-skeleton": "^3.5.0", "react-redux": "^9.1.2", "react-router-dom": "^6.27.0", "socket.io-client": "^4.8.1", "styled-components": "^6.1.19", "unzipper": "^0.12.3"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/levelup": "^5.1.5", "@types/lodash.debounce": "^4.0.9", "@types/pouchdb": "^6.4.2", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/react-window": "^1.8.8", "@types/styled-components": "^5.1.34", "@vitejs/plugin-react": "^4.3.2", "electron": "^33.2.1", "electron-builder": "^25.1.8", "eslint": "^9.27.0", "vite": "^5.4.8", "webpack": "^5.97.1", "webpack-cli": "^5.1.4"}, "build": {"appId": "com.yourapp.id", "productName": "SURYACON", "asar": true, "asarUnpack": ["src/electron/cookies.json"], "files": ["dist/**/*", "src/electron/**/*"], "mac": {"target": "dmg", "artifactName": "${productName}-${version}-mac.dmg"}, "win": {"target": "nsis", "artifactName": "${productName}-Setup-${version}.exe", "icon": "/public/SuryaconLogo.ico"}}}