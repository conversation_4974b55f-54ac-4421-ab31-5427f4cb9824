import React, { useState } from "react";
import styles from "./Styles/AddToolTip.module.css";

import { AddCategoryIcon, DeleteIcon } from "../../../../assets/icons";
import Tooltip from "../Tooltip";
import ValuePopup from "../ValuePopup";
import { AddToolTipProps } from "../GlobalInterfaces/GlobalInterface";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { RootState } from "../../../../redux/store";

interface TooltipState {
  quantity: string;
  selectedCategory: string;
}

const AddToolTip: React.FC<AddToolTipProps> = ({
  label,
  className = "",
  className2 = "",
  additionalClass = "",
  onClick,
  onTooltipClick,
  setReporterAdded,
  reporterAdded = false,
  isReporter = false,
  data = [],
  icon,
  isPllaning = true,
  isActive = true,
  handleDelete,
  isEdit = false,
  showAddIcon = true,
}) => {
  const [showValuePopup, setShowValuePopup] = useState(false);
  const [activeTooltipId, setActiveTooltipId] = useState<string | number>("");
  const isDeletedNext = useAppSelector(
    (state) => state.isDeletedSLice.isDeletedNext
  );
  const deleted = useAppSelector((state) => state.isDeletedSLice.isDeleted);
  const [tooltipStates, setTooltipStates] = useState<
    Record<string, TooltipState>
  >({});

  const handlePopupClose = () => {
    setShowValuePopup(false);
  };

  const handleSubmitPopup = (category: string, qty: string) => {
    setTooltipStates((prev) => ({
      ...prev,
      [activeTooltipId]: {
        quantity: qty,
        selectedCategory: category,
      },
    }));
    setShowValuePopup(false);
  };

  const handleCategoryChange = (tooltipId: string, category: string) => {
    setTooltipStates((prev) => ({
      ...prev,
      [tooltipId]: {
        ...prev[tooltipId],
        selectedCategory: category,
      },
    }));
  };

  return (
    <div className={`${styles.addtooltip_container} ${className}`}>
      <div
        className={`${styles.addtooltip_sub_container} ${styles[className2]}`}
      >
        <div className={styles.addtooltip_header}>
          <div style={{ display: "flex", gap: isPllaning ? "0.8rem" : "0" }}>
            <span>{icon && icon}</span>
            <h4 style={{ color: isActive ? "black" : "gray" }}>{label}</h4>
          </div>

          {isPllaning ? (
            isActive &&
            isEdit &&
            !isDeletedNext &&
            !deleted &&
            !reporterAdded &&
            showAddIcon && (
              <span className="cursor_pointer" onClick={onClick}>
                <AddCategoryIcon marginTop={0.25} />
              </span>
            )
          ) : (
            <circle
              nameclass="material_bubble"
              icon={
                <p
                  className="small_text_p"
                  style={{ color: "var(--main_background" }}
                >
                  33
                </p>
              }
            />
          )}
        </div>

        {data && data.length > 0 && (
          <div
            className={
              label === "Quality Control Plan"
                ? `${styles.addtooltip_data_conatiner_column}  ${
                    styles.addtooltip_data_container
                  } ${data.length == 1 ? styles.cover_extra_space : ""}`
                : `${styles.addtooltip_data_container} ${
                    data.length == 1 ? styles.cover_extra_space : ""
                  }`
            }
            style={{
              display: label === "Subtasks" ? "block" : "flex",
              minWidth: label === "Subtasks" ? "176px" : "",
            }}
          >
            {data.map((item: any, index: any) => {
              const itemId = typeof item === "string" ? item : item._id;
              const itemName =
                typeof item === "string" ? item : (item as any).name;

              return (
                <>
                  <div className={styles.tooltiplusbubble}>
                    <Tooltip
                      isEdit={isEdit}
                      key={index}
                      index={index}
                      label={label}
                      content={itemName}
                      className={additionalClass}
                      handleClick={() => {
                        console.log(item, "ddd");
                        setActiveTooltipId(itemId);
                        onTooltipClick && onTooltipClick(item);
                        // : setShowValuePopup(true);
                      }}
                      id={String(itemId)}
                      quantity={tooltipStates[String(itemId)]?.quantity}
                      selectedCategory={
                        tooltipStates[String(itemId)]?.selectedCategory
                      }
                      onCategoryChange={(category) =>
                        handleCategoryChange(String(itemId), category)
                      }
                    />

                    {isEdit && handleDelete && (
                      <div
                        className={styles.delete_icon_tooltip}
                        onClick={() => handleDelete(item)}
                      >
                        <DeleteIcon />
                      </div>
                    )}
                  </div>
                </>
              );
            })}
          </div>
        )}
      </div>

      {showValuePopup && (
        <ValuePopup onClose={handlePopupClose} onSubmit={handleSubmitPopup} />
      )}
    </div>
  );
};

export default AddToolTip;
