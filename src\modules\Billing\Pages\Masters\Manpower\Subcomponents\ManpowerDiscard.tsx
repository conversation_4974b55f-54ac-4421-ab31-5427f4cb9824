import React, { <PERSON> } from "react";
import styles from "../Styles/Manpower.module.css";
import { isValidValue } from "../../../../../../functions/functions";

interface ManpowerDiscardProps {
  formData: any;
  initialFormData?: any;
  formMode?: any;
  //   deletedGradeData: Array<Record<number, any[]>>;
  //   deletedFormData: any;
}

const ManpowerDiscard: FC<ManpowerDiscardProps> = ({
  formData,
  initialFormData,
  formMode,
  //   deletedGradeData,
  //   deletedFormData,
}) => {
  return (
    <div style={{ display: "flex", flexDirection:"column"  }}>
      {formData?.type && isValidValue(formData?.type) && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Type
            </p>
            <h4
              style={{
                color: "var(--text-black-87)",
                marginTop: "0.3rem",
              }}
            >
              {formData?.type?.charAt(0).toUpperCase() +
                formData?.type?.slice(1)}
            </h4>
          </div>
        </div>
      )}
      {formData?.Name && isValidValue(formData?.Name) && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Name
            </p>
            <h4
              style={{
                color: "var(--text-black-87)",
                marginTop: "0.3rem",
              }}
            >
              {formData?.Name}
            </h4>
          </div>
        </div>
      )}
      {formData?.Description && isValidValue(formData?.Description) && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <div>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Description
              </p>
              <h4
                style={{
                  color: "var(--text-black-87)",
                  marginTop: "0.3rem",
                }}
              >
                {formData?.Description}
              </h4>
            </div>
          </div>
        </div>
      )}
      {formData?.Skills && formData?.Skills?.length > 0 && (
        <>
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Skills
              </p>
              <div
                style={{
                  display: "flex",
                  marginTop: "0.3rem",
                  gap: "0.3rem 2rem",
                  flexWrap: "wrap",
                }}
              >
                {formData?.Skills?.map((item: any, index: number) => (
                  <h4
                    style={{
                      color: "var(--text-black-87)",
                    }}
                  >
                    {item}
                  </h4>
                ))}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ManpowerDiscard;
