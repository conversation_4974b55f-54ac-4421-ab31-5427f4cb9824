// c:\PSQUARE\SURYACON_FRONTEND_TASKMASTER\src\modules\Billing\Pages\ProjectPlanning\Planning\SendAprovalHeader\PlanningTableHeader.tsx
import React from "react";
import { useSelector } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import styles from "./Styles/PlanningTableHeader.module.css";
import TargetBadge from "../../../../../../components/Reusble/Global/TargetBadge/TargetBadge";
import { SendApproval, SiteDrawingIcon } from "../../../../../../assets/icons";
import { RootState } from "../../../../../../redux/store";
import { selectIsApprovalEnabled } from "../../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/projectPlanningSlice";
import { useSendApprovalByhod } from "../../../../../../redux/hooks/Modules/Billing/billinghooks";
import { useToast } from "../../../../../../hooks/ToastHook"; 
// Import the new confirmation form
import SendApprovalConfirmationForm from "./SendApprovalConfirmationForm";

// Example task interface - adjust according to your actual task data structure
interface Task {
  id: string;
  name: string;
  description: string;
}

const PlanningTableHeader: React.FC = () => {
  const { towerLocationId } = useParams<{ towerLocationId: string }>();
  const navigate = useNavigate();
  const isApprovalEnabled = useSelector(selectIsApprovalEnabled);
  const [sendApprovalByhod] = useSendApprovalByhod();
  const showToast = useToast();
  const [isConfirmationOpen, setIsConfirmationOpen] = React.useState(false);

  // 1. Redux se actual tasks lao
  const tasks = useSelector((state: RootState) => state.projectPlanning.tasks);

const progressTowerData = useSelector(
  (state: RootState) => state.projectPlanning.progressTowerData || []
);

const tasksForApproval = React.useMemo(
  () =>
    (progressTowerData || []).map((task: any) => ({
      id: task._id,
      name: task.name,
      description: task.description || "",
      progressPercentage: task.progressPercentage || 0,
    })),
  [progressTowerData]
);

  const handleApprovalClick = async () => {
    if (!isApprovalEnabled) return;
    setIsConfirmationOpen(true);
  };

  const handleConfirmApproval = async () => {
    if (!towerLocationId) return;
    try {
      await sendApprovalByhod({ location_id: towerLocationId }).unwrap();
      showToast({ messageContent: "Approval sent successfully!", type: "success" });
      setIsConfirmationOpen(false);
    } catch (error: any) {
      showToast({ messageContent: "Failed to send approval.", type: "error" });
    }
  };

  const handleSiteDrawingsClick = () => {
    console.log("Site Drawings clicked");
  };

  const handleVersionHistoryClick = () => {
    console.log("Version History clicked");
    navigate("/version");
  };

  return (
    <div className={styles.planning_table_header}>
      <div className={styles.planning_table_header_rhs}>
        <div className={styles.approval_button_container}>
          <TargetBadge
            backgroundColor="var(--secondary-color,rgb(255, 255, 255))"
            outerContainerClassName="planning_table_actions"
            valueTextClassName="action_button_text"
            value="Site Drawings"
            icon={<SiteDrawingIcon />}
            onClick={handleSiteDrawingsClick}
          />

          <TargetBadge
            backgroundColor="var(--info-color,rgb(255, 255, 255))"
            outerContainerClassName="planning_table_actions"
            valueTextClassName="action_button_text"
            value="Version History"
            onClick={handleVersionHistoryClick}
          />

          <TargetBadge
            backgroundColor="var(--primary_color)"
            outerContainerClassName="planning_table_actions"
            valueTextClassName="approval_button_text"
            value="Send Approval"
            icon={<SendApproval />}
            onClick={handleApprovalClick}
            disabled={!isApprovalEnabled}
            style={{
              filter: isApprovalEnabled ? "none" : "blur(1.5px)",
              opacity: isApprovalEnabled ? 1 : 0.6,
              pointerEvents: isApprovalEnabled ? "auto" : "none",
            }}
          />
        </div>
      </div>
      
      {/* Confirmation Form */}
      <SendApprovalConfirmationForm
        isOpen={isConfirmationOpen}
        onClose={() => setIsConfirmationOpen(false)}
        onConfirm={handleConfirmApproval}
        // 3. Actual tasks bhejo form me
        tasks={tasksForApproval}
      />
    </div>
  );
};

export default PlanningTableHeader;