import React, { useState, useEffect, useRef } from "react";
import { useParams } from "react-router-dom";
import styles from "../Styles/MonthlyTarget.module.css";
import RadioBtns from "../../../../../components/Reusble/Global/RadioBtns";
import FloatingLabelInput from "../../../../../components/Reusble/Global/FloatingLabel";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { RootState } from "../../../../../redux/store";
import {
  Cross,
  DropDownArrowUpIcon,
  DropDownArrowDownIcon,
  DateIcon,
  AttachmentIcon,
  SuryconLogo,
} from "../../../../../assets/icons";
import { initializeDatabase } from "../../../../../functions/functions";
import Button from "../../../../../components/Reusble/Global/Button";
import Datafield from "../../../../../components/Reusble/Billing/Masters/Datafield";
import CalendarComp from "../../../../../components/Reusble/Global/Calender";
import { setToast } from "../../../../../redux/features/Modules/Reusble/ToastSlice";
import { setBackupChange } from "../../../../../redux/features/Modules/Reusble/backupSlice";
import { saveSyncData } from "../../../../../Backup/BackupFunctions/BackupFunctions";
import {
  TargetGenerationFormProps,
  MonthlyTargetData,
  Task,
  Subtask,
} from "../Interface/interface";
import {
  openPopup,
  closePopup,
} from "../../../../../redux/features/Modules/Reusble/popupSlice";
import TargetBadge from "../../../../../components/Reusble/Global/TargetBadge/TargetBadge";

// Import Redux actions
import {
  setCategory,
  setSelectedTower,
  setSelectedTasks,
  removeTask,
  setActiveTaskDetails,
  updateTaskDetails,
  setAvailableTasks,
  setAvailableSubtasks,
  addDrawing,
  updateFinalDetails,
  validateForm as validateReduxForm,
  resetForm,
} from "../../../../../redux/features/Modules/Billing/BillingApproval/Slices/MonthlyTargetSlice";

interface TowerOption {
  value: string;
  label: string;
  category: string;
}

const MonthlyTargetCreationForm: React.FC<TargetGenerationFormProps> = ({
  onClose,
  projectId: propProjectId,
  editData = null,
  mode = "create",
  onSubmitSuccess,
}) => {
  const dispatch = useAppDispatch();
  const { projectId: paramProjectId } = useParams<{ projectId: string }>();
  const projectId = propProjectId || paramProjectId;

  // Refs
  const modalRef = useRef<HTMLDivElement>(null);
  const drawingsInputRef = useRef<HTMLInputElement>(null);

  // Redux state
  const formState = useAppSelector((state: RootState) => state.monthlyTargetForm);

  // Local state
  const [isClosing, setIsClosing] = useState<boolean>(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [towerOptions, setTowerOptions] = useState<TowerOption[]>([]);
  const [filteredTowers, setFilteredTowers] = useState<TowerOption[]>([]);
  const [isLoadingTowers, setIsLoadingTowers] = useState<boolean>(false);
  const [isLoadingTasks, setIsLoadingTasks] = useState<boolean>(false);
  const [calendarPopups, setCalendarPopups] = useState<string[]>([]);
  const [isDetailsExpanded, setIsDetailsExpanded] = useState<boolean>(true);
  const [isSubtasksExpanded, setIsSubtasksExpanded] = useState<boolean>(true);
  const [isDatafieldClosing, setIsDatafieldClosing] = useState<boolean>(false);

  // Utility functions
  const showToast = (
    messageContent: string,
    type: "success" | "error" | "warning" | "info"
  ) => {
    dispatch(setToast({ isOpen: true, messageContent, type }));
  };

  const extractDateParts = (dateString?: string) => {
    if (!dateString) return null;
    const date = new Date(dateString);
    const months = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    return {
      day: date.getDate().toString().padStart(2, "0"),
      monthName: months[date.getMonth()],
      year: date.getFullYear().toString(),
    };
  };

  const getCurrentTaskDetails = () => {
    if (!formState.activeTaskDetails) return null;
    const details = formState.taskDetails[formState.activeTaskDetails];
    return {
      quantity: "",
      weightage: "",
      unit: "",
      ...details,
    };
  };

  const getActiveTaskSubtasks = () => {
    const currentDetails = getCurrentTaskDetails();
    return currentDetails?.subtasks || [];
  };

  const getDrawingsDisplayText = () => {
    if (!formState.activeTaskDetails) return "";
    const currentDetails = formState.taskDetails[formState.activeTaskDetails];
    if (!currentDetails?.drawings?.length) return "";

    const drawings = currentDetails.drawings;
    return drawings.length === 1
      ? drawings[0].name
      : `${drawings.length} drawings attached`;
  };

  // Database operations
  const fetchFromDatabase = async (dbName: string, config: any) => {
    const database = await initializeDatabase(dbName);
    return await window.electron.getDocumentByParentId({
      dbName: database,
      isDeletedNext: false,
      ...config,
    });
  };

  const fetchTowers = async () => {
    if (!projectId) return;

    setIsLoadingTowers(true);
    try {
      const response = await fetchFromDatabase("Towerlocations", {
        catId: projectId,
        categoryId: "project_id",
      });

      if (response?.length > 0) {
        const formattedTowers: TowerOption[] = response.map((tower: any) => ({
          value: tower._id || tower.id,
          label: tower.name || tower.towerName || tower.tower_name,
          category: tower.category || "Tower",
        }));

        setTowerOptions(formattedTowers);
        const categoryFiltered = formattedTowers.filter(
          (tower) =>
            tower.category === formState.category ||
            (formState.category === "Tower" && !tower.category)
        );
        setFilteredTowers(categoryFiltered);
      } else {
        setTowerOptions([]);
        setFilteredTowers([]);
      }
    } catch (error) {
      console.error("Error fetching towers:", error);
      setTowerOptions([]);
      setFilteredTowers([]);
    } finally {
      setIsLoadingTowers(false);
    }
  };

  const fetchTasks = async (towerId: string) => {
    if (!towerId) return;

    setIsLoadingTasks(true);
    try {
      const towerRoutes = await fetchFromDatabase("TowerRoutes", {
        categoryId: "Tower_id",
        catId: towerId,
      });

      if (towerRoutes?.length > 0) {
        dispatch(setAvailableTasks(towerRoutes));
      } else {
        dispatch(setAvailableTasks([]));
        showToast("No tasks found for this tower", "info");
      }
    } catch (error) {
      console.error("Error fetching tasks:", error);
      dispatch(setAvailableTasks([]));
    } finally {
      setIsLoadingTasks(false);
    }
  };

  const fetchTaskDetails = async (taskId: string) => {
    if (!taskId) return;

    try {
      const dbName = await initializeDatabase("TaskBasicDetails");

      // Try multiple ID fields
      const idFields = ["taskId", "_id"];
      let fetchedData = null;

      for (const field of idFields) {
        fetchedData = await window.electron.getDocumentByParentId({
          dbName,
          categoryId: field,
          catId: taskId,
        });
        if (fetchedData?.length > 0) break;
      }

      if (fetchedData?.[0]) {
        const taskDetails = fetchedData[0];
        dispatch(
          updateTaskDetails({
            taskId,
            details: {
              description: taskDetails.description || "",
              startDate: taskDetails.start_date || "",
              endDate: taskDetails.end_date || "",
              area: taskDetails.area?.toString() || "",
              duration: taskDetails.duration?.toString() || "",
              quantity: taskDetails.quantity?.toString() || "",
              weightage: taskDetails.weightage?.toString() || "",
              unit: taskDetails.unit || "",
              remarks: taskDetails.remarks || "",
            },
          })
        );
      }
    } catch (error) {
      console.error("Error fetching task details:", error);
    }
  };

  const fetchSubtasksForTask = async (taskId: string) => {
    if (!taskId) return [];

    try {
      const dbname = await initializeDatabase("SubTasksBasicDetails");
      const idFields = ["task_id", "taskId", "towerRouteId"];

      for (const field of idFields) {
        const subtaskBasicDetails = await window.electron.getDocumentByParentId(
          {
            categoryId: field,
            dbName: dbname,
            catId: taskId,
            isDeletedNext: false,
          }
        );

        if (subtaskBasicDetails?.length > 0) {
          return subtaskBasicDetails.map((subtask) => ({
            ...subtask,
            task_id: taskId,
            taskId: taskId,
            towerRouteId: subtask.towerRouteId || taskId,
          }));
        }
      }
      return [];
    } catch (error) {
      console.error("Error fetching subtasks:", error);
      return [];
    }
  };

  // Event handlers
  const handleCategoryChange = (
    category: "Tower" | "Non-Tower" | "Miscellaneous"
  ) => {
    dispatch(setCategory(category));
    dispatch(setSelectedTower({ id: "", name: "" }));
    dispatch(setSelectedTasks([]));
    dispatch(setAvailableTasks([]));
    setIsDropdownOpen(false);
  };

  const handleTowerSelect = async (tower: TowerOption) => {
    dispatch(setSelectedTower({ id: tower.value, name: tower.label }));

    if (mode === "create" || !editData) {
      dispatch(setSelectedTasks([]));
      dispatch(setAvailableTasks([]));
    }

    setIsDropdownOpen(false);
    await fetchTasks(tower.value);
  };

  const handleTaskSelect = async (taskId: string) => {
    if (!taskId) return;

    dispatch(setActiveTaskDetails(taskId));

    if (!formState.taskDetails[taskId]) {
      await fetchTaskDetails(taskId);
    }

    setIsDetailsExpanded(true);
    dispatch(setAvailableSubtasks([]));

    // Auto-fetch subtasks
    showToast("Fetching subtasks...", "info");
    try {
      const subtasks = await fetchSubtasksForTask(taskId);

      if (subtasks.length === 0) {
        showToast("No subtasks found for this task", "info");
        return;
      }

      dispatch(setAvailableSubtasks(subtasks));
      setIsSubtasksExpanded(true);
      showToast(`${subtasks.length} subtasks found`, "success");
    } catch (error) {
      console.error("Error fetching subtasks:", error);
      showToast("Failed to fetch subtasks", "error");
    }
  };

  const handleTaskAddition = () => {
    if (!formState.selectedTower.id) {
      showToast("Please select a tower first", "warning");
      return;
    }

    dispatch(closePopup("MonthlyTargetCreationForm"));
    dispatch(openPopup("AddSubForm"));
  };

  const handleSubtaskSelect = (subtask: Subtask) => {
    if (!formState.activeTaskDetails) return;

    const currentDetails = getCurrentTaskDetails() || {
      description: "",
      startDate: "",
      endDate: "",
      area: "",
      duration: "",
      drawings: [],
      remarks: "",
      subtasks: [],
    };

    const isAlreadySelected = currentDetails.subtasks?.some(
      (s) => s.id === subtask._id
    );

    if (isAlreadySelected) {
      showToast("This subtask is already added", "info");
      return;
    }

    const updatedSubtasks = [
      ...(currentDetails.subtasks || []),
      {
        id: subtask._id,
        name: subtask.name || subtask.task_name,
        isSelected: true,
      },
    ];

    dispatch(
      updateTaskDetails({
        taskId: formState.activeTaskDetails,
        details: { ...currentDetails, subtasks: updatedSubtasks },
      })
    );

    showToast(
      `Subtask "${subtask.name || subtask.task_name}" added successfully`,
      "success"
    );
  };

  const handleTaskDelete = (taskId: string) => {
    dispatch(removeTask(taskId));
  };

  const handleSubtaskDelete = (subtaskId: string) => {
    if (!formState.activeTaskDetails) return;

    const currentDetails = getCurrentTaskDetails();
    if (!currentDetails?.subtasks) return;

    const updatedSubtasks = currentDetails.subtasks.filter(
      (subtask) => subtask.id !== subtaskId
    );

    dispatch(
      updateTaskDetails({
        taskId: formState.activeTaskDetails,
        details: { ...currentDetails, subtasks: updatedSubtasks },
      })
    );

    showToast("Subtask removed successfully", "success");
  };

  const handleTaskDetailChange = (field: string, value: any) => {
    if (!formState.activeTaskDetails) return;

    const currentDetails = getCurrentTaskDetails() || {
      description: "",
      startDate: "",
      endDate: "",
      area: "",
      duration: "",
      drawings: [],
      remarks: "",
      subtasks: [],
    };

    let updatedDetails = { ...currentDetails, [field]: value };

    // Auto-calculate end date
    if (
      (field === "startDate" || field === "duration") &&
      updatedDetails.startDate &&
      updatedDetails.duration
    ) {
      const durationDays = parseInt(updatedDetails.duration);
      if (!isNaN(durationDays)) {
        const startDate = new Date(updatedDetails.startDate);
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + durationDays);
        updatedDetails.endDate = endDate.toISOString();
      }
    }

    dispatch(
      updateTaskDetails({
        taskId: formState.activeTaskDetails,
        details: updatedDetails,
      })
    );
  };

  const handleDrawingUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!formState.activeTaskDetails) {
      showToast("Please select a task first", "warning");
      return;
    }

    const file = event.target.files?.[0];
    if (file) {
      dispatch(
        addDrawing({
          taskId: formState.activeTaskDetails,
          drawing: { name: file.name, file: file },
        })
      );
      showToast(`Drawing "${file.name}" uploaded successfully`, "success");
    }
  };

  const validateFormData = (): boolean => {
    dispatch(validateReduxForm());
    return formState.isFormValid;
  };

  const handleSubmit = async () => {
    if (!validateFormData()) {
      showToast("Please fill all required fields", "warning");
      return;
    }

    try {
      const monthlyTarget: Partial<MonthlyTargetData> = {
        id: editData?.id || Date.now().toString(),
        projectId: projectId || "",
        towerId: formState.selectedTower.id,
        towerName: formState.selectedTower.name,
        tower_description: formState.selectedTower.description || "", // Add tower description
        location_type: formState.category,
        tasks: formState.selectedTasks.map((task) => {
          const taskDetails = formState.taskDetails[task.id];
          return {
            _id: task.id,
            name: task.name,
            task_description: taskDetails?.description || "",
            start_date: taskDetails?.startDate || "",
            end_date: taskDetails?.endDate || "",
            area: taskDetails?.area || "",
            duration: taskDetails?.duration || "",
            remark: taskDetails?.remarks || "",
            quantity: taskDetails?.quantity || "",
            weightage: taskDetails?.weightage || "",
            unit: taskDetails?.unit || "",
            is_selected: task.isSelected,
            status: "pending",
            subtask_ids:
              taskDetails?.subtasks?.map((subtask) => subtask.id) || [],
          } as Task;
        }),
        status: "pending",
        is_deleted: false,
        created_at: editData?.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      await initializeDatabase("MonthlyTargets");
      await saveSyncData(
        [monthlyTarget],
        new Date().toISOString(),
        "MonthlyTargets",
        false,
        dispatch
      );

      dispatch(setBackupChange());
      showToast(
        `Monthly target ${
          mode === "edit" ? "updated" : "created"
        } successfully`,
        "success"
      );

      if (onSubmitSuccess) {
        onSubmitSuccess();
      }

      handleClose();
    } catch (error) {
      console.error(
        `Error ${mode === "edit" ? "updating" : "creating"} monthly target:`,
        error
      );
      showToast(
        `Failed to ${mode === "edit" ? "update" : "create"} monthly target`,
        "error"
      );
    }
  };

  const handleClose = () => {
    setIsClosing(true);
    dispatch(resetForm());
    setTimeout(() => onClose(), 300);
  };

  const handleSaveAndContinue = () => {
    if (!formState.activeTaskDetails) {
      showToast("Please select a task first", "warning");
      return;
    }

    const currentTask = formState.selectedTasks.find(
      (task) => task.id === formState.activeTaskDetails
    );

    if (!currentTask) {
      showToast("Task not found", "error");
      return;
    }

    // Check if task is already in final details
    const currentFinalDetails = formState.finalDetails.selectedTaskIds;
    if (currentFinalDetails.includes(formState.activeTaskDetails)) {
      showToast("This task is already in Final Details", "info");
      return;
    }

    // Add to final details
    dispatch(
      updateFinalDetails({
        selectedTaskIds: [
          ...currentFinalDetails,
          formState.activeTaskDetails,
        ],
      })
    );

    // Remove from selected tasks and available tasks
    const updatedSelectedTasks = formState.selectedTasks.filter(
      (task) => task.id !== formState.activeTaskDetails
    );
    dispatch(setSelectedTasks(updatedSelectedTasks));

    // Clear active task details
    dispatch(setActiveTaskDetails(""));

    // Clear available subtasks since we're no longer viewing any task
    dispatch(setAvailableSubtasks([]));

    showToast(`${currentTask.name} moved to Final Details`, "success");

    // If there are remaining tasks, auto-select the first one
    if (updatedSelectedTasks.length > 0) {
      const nextTaskId = updatedSelectedTasks[0].id;
      setTimeout(() => {
        handleTaskSelect(nextTaskId);
      }, 100);
    }
  };

  // Effects
  useEffect(() => {
    if (editData) {
      dispatch(
        setCategory(
          (editData.location_type as "Tower" | "Non-Tower" | "Miscellaneous") ||
            "Tower"
        )
      );
      dispatch(
        setSelectedTower({
          id: editData.towerId || editData.tower_id || "",
          name: editData.towerName || editData.tower_name || "",
        })
      );

      if (editData.tasks?.length > 0) {
        const formattedTasks = editData.tasks.map((task) => ({
          id: task._id,
          name: task.name || task.task_name || "",
          isSelected: task.is_selected,
        }));
        dispatch(setSelectedTasks(formattedTasks));
      }
    }
  }, [editData, dispatch]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        handleClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    fetchTowers();
  }, [projectId, formState.category]);

  useEffect(() => {
    const filtered = towerOptions.filter(
      (tower) =>
        (tower.category === formState.category ||
          (formState.category === "Tower" && !tower.category)) &&
        tower.label.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredTowers(filtered);
  }, [searchTerm, towerOptions, formState.category]);

  return (
    <div
      className={`${styles.monthly_target_creation_form} ${
        isClosing ? styles.closing : ""
      }`}
      tabIndex={0}
      ref={modalRef}
    >
      {/* Form Header */}
      <div className={styles.form_header}>
        <h3>{mode === "edit" ? "Edit" : "Genrate"} Target</h3>
        <button
          type="button"
          aria-label="Close"
          className={styles.closeButton}
          onClick={handleClose}
        >
          <Cross />
        </button>
      </div>

      <div className={styles.form_body}>
        {/* Category Selection */}
        <div className={styles.category_section}>
          <p className={styles.section_label}>Category</p>
          <RadioBtns
            options={[
              { label: "Tower", value: "Tower" },
              { label: "Non-Tower", value: "Non-Tower" },
              { label: "Miscellaneous", value: "Miscellaneous" },
            ]}
            selectedValue={formState.category}
            onValueChange={handleCategoryChange}
          />
        </div>

        {/* Tower Selection */}
        <div className={styles.tower_selection_section}>
          <div className={styles.searchable_dropdown_container}>
            <FloatingLabelInput
              id="towerSelection"
              label={formState.category}
              value={formState.selectedTower.name}
              onInputChange={setSearchTerm}
              iconClick={() => setIsDropdownOpen(!isDropdownOpen)}
              isInvalid={!!formState.errors.tower}
              Icon={
                isDropdownOpen ? DropDownArrowUpIcon : DropDownArrowDownIcon
              }
              placeholder={`Select ${formState.category}`}
            />

            {isDropdownOpen && (
              <div className={styles.floating_dropdown_list}>
                {isLoadingTowers ? (
                  <div className={styles.loading_indicator}>Loading...</div>
                ) : filteredTowers.length === 0 ? (
                  <div className={styles.floating_dropdown_item_disabled}>
                    No {formState.category.toLowerCase()} found
                  </div>
                ) : (
                  filteredTowers.map((tower) => (
                    <div
                      key={tower.value}
                      className={`${styles.floating_dropdown_item} ${
                        formState.selectedTower.id === tower.value
                          ? styles.selected
                          : ""
                      }`}
                      onClick={() => handleTowerSelect(tower)}
                    >
                      {tower.label}
                    </div>
                  ))
                )}
              </div>
            )}
          </div>
        </div>

        {/* Tasks Section */}
        <Datafield
          label="Tasks"
          varient="AddToolsForm"
          selectedValues={formState.selectedTasks.map((task) => ({
            _id: task.id,
            name: task.name,
            is_selected: task.isSelected,
          }))}
          callbackDelete={handleTaskDelete}
          setIsClosing={setIsDatafieldClosing}
          handlePlusIcon={handleTaskAddition}
          onItemClick={handleTaskSelect}
        />

        <div className={styles.separator_line_container}>
          <span className={styles.dottedline_wrapper}></span>
          <div className={styles.separator_icon}>
            <span>
              <SuryconLogo />
            </span>
          </div>
          <span className={styles.dottedline_wrapper}></span>
        </div>

        {/* Details Section */}
        <div>
          <div className={styles.section_header}>
            <div>
              <SuryconLogo />
            </div>
            <h4>Details</h4>
          </div>

          {/* Selected Task Display */}
          {formState.selectedTasks.length > 0 && (
            <div className={styles.selected_tasks_display}>
              <div className={styles.tasks_badges_container}>
                {formState.selectedTasks.map((task) => (
                  <TargetBadge
                    key={task.id}
                    value={task.name}
                    backgroundColor={
                      formState.activeTaskDetails === task.id
                        ? "var(--primary_color)"
                        : "var(--secondary_color)"
                    }
                    outerContainerClassName={
                      formState.activeTaskDetails === task.id
                        ? "monthly_target_badge_selected"
                        : "monthly_target_badge"
                    }
                    valueTextClassName={
                      formState.activeTaskDetails === task.id
                        ? "monthly_target_badge_text_selected"
                        : "monthly_target_badge_text"
                    }
                    onClick={() => handleTaskSelect(task.id)}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Task Details Form */}
          {isDetailsExpanded && formState.activeTaskDetails && (
            <div className={styles.details_content}>
              {/* Only show details if the active task is not in final details */}
              {!formState.finalDetails.selectedTaskIds.includes(formState.activeTaskDetails) ? (
                <>
                  {/* Description */}
                  <div className={styles.description_container}>
                    <div className={styles.description_box}>
                      <label className={styles.description_label}>
                        Description
                      </label>
                      <div className={styles.description_content}>
                        {getCurrentTaskDetails()?.description ||
                          "Loading description..."}
                      </div>
                    </div>
                  </div>

                  {/* Date Fields */}
                  <div className={styles.date_row}>
                    <FloatingLabelInput
                      width="13rem"
                      enterAllowed={false}
                      label="Start Date"
                      id="startDate"
                      placeholder="Start Date"
                      isInvalid={!!formState.errors.startDate}
                      value={
                        getCurrentTaskDetails()?.startDate
                          ? ((date) =>
                              `${date?.day} ${date?.monthName} ${date?.year}`)(
                              extractDateParts(getCurrentTaskDetails()?.startDate)
                            )
                          : ""
                      }
                      onInputChange={() => {}}
                      iconClick={() =>
                        setCalendarPopups([...calendarPopups, "startCalendar"])
                      }
                      isDisabled={true}
                      Icon={DateIcon}
                    />

                    {calendarPopups.includes("startCalendar") && (
                      <CalendarComp
                        initialDate={getCurrentTaskDetails()?.startDate}
                        handleselect={(data) => {
                          if (data) {
                            handleTaskDetailChange("startDate", data);
                            setCalendarPopups((prev) =>
                              prev.filter((e) => e !== "startCalendar")
                            );
                          }
                        }}
                      />
                    )}

                    <FloatingLabelInput
                      width="13rem"
                      enterAllowed={false}
                      label="End Date"
                      id="endDate"
                      placeholder="End Date"
                      isInvalid={!!formState.errors.endDate}
                      value={
                        getCurrentTaskDetails()?.endDate
                          ? ((date) =>
                              `${date?.day} ${date?.monthName} ${date?.year}`)(
                              extractDateParts(getCurrentTaskDetails()?.endDate)
                            )
                          : ""
                      }
                      onInputChange={() => {}}
                      iconClick={() =>
                        setCalendarPopups([...calendarPopups, "endCalendar"])
                      }
                      isDisabled={true}
                      Icon={DateIcon}
                      backgroundColor="#0059681a"
                    />

                    {calendarPopups.includes("endCalendar") && (
                      <CalendarComp
                        initialDate={() => {}}
                        handleselect={() => {}} // Empty handler
                      />
                    )}
                  </div>

                  {/* Area and Duration */}
                  <div className={styles.input_row}>
                    <FloatingLabelInput
                      width="13rem"
                      enterAllowed={false}
                      label="Area"
                      id="Area"
                      placeholder="Area"
                      isInvalid={!!formState.errors.area}
                      value={getCurrentTaskDetails()?.area || ""}
                      onInputChange={(data) => {
                        if (!isNaN(Number(data))) {
                          handleTaskDetailChange("area", data);
                        }
                      }}
                    />

                    <FloatingLabelInput
                      width="13rem"
                      enterAllowed={false}
                      label="Project Duration"
                      id="ProjectDuration"
                      placeholder="Duration"
                      isInvalid={!!formState.errors.duration}
                      value={getCurrentTaskDetails()?.duration || ""}
                      onInputChange={(data) => {
                        if (!isNaN(Number(data))) {
                          handleTaskDetailChange("duration", data);
                        }
                      }}
                    />
                  </div>

                  {/* Quantity, Weightage, Unit */}
                  <div className={styles.input_row}>
                    <FloatingLabelInput
                      width="8.5rem"
                      enterAllowed={false}
                      label="Quantity"
                      id="Quantity"
                      placeholder="Quantity"
                      isInvalid={!!formState.errors.quantity}
                      value={getCurrentTaskDetails()?.quantity || ""}
                      onInputChange={(data) => {
                        if (!isNaN(Number(data))) {
                          handleTaskDetailChange("quantity", data);
                        }
                      }}
                    />

                    <FloatingLabelInput
                      width="8.5rem"
                      enterAllowed={false}
                      label="Weightage"
                      id="Weightage"
                      placeholder="Weightage"
                      isInvalid={!!formState.errors.weightage}
                      value={getCurrentTaskDetails()?.weightage || ""}
                      onInputChange={(data) => {
                        if (!isNaN(Number(data))) {
                          handleTaskDetailChange("weightage", data);
                        }
                      }}
                    />

                    <FloatingLabelInput
                      width="8.5rem"
                      enterAllowed={false}
                      label="Unit"
                      id="Unit"
                      placeholder="Unit"
                      isInvalid={!!formState.errors.unit}
                      value={getCurrentTaskDetails()?.unit || ""}
                      onInputChange={(data) => {
                        handleTaskDetailChange("unit", data);
                      }}
                    />
                  </div>

                  {/* Drawings and Remarks */}
                  <FloatingLabelInput
                    enterAllowed={false}
                    label="Drawings"
                    id="Drawings"
                    placeholder="Drawings"
                    isInvalid={!!formState.errors.drawings}
                    isDisabled={!formState.activeTaskDetails}
                    Icon={AttachmentIcon}
                    value={getDrawingsDisplayText()}
                    iconClick={() =>
                      formState.activeTaskDetails &&
                      drawingsInputRef.current?.click()
                    }
                  />

                  <FloatingLabelInput
                    width="14rem"
                    enterAllowed={true}
                    label="Remarks"
                    id="Remarks"
                    placeholder="Remarks"
                    isInvalid={!!formState.errors.remarks}
                    value={getCurrentTaskDetails()?.remarks || ""}
                    onInputChange={(data) =>
                      handleTaskDetailChange("remarks", data)
                    }
                  />

                  {/* Subtasks Section */}
                  <div>
                    {isSubtasksExpanded && (
                      <div className={styles.subtasks_content}>
                        <div className={styles.selected_subtasks_section}>
                          <Datafield
                            label="Selected Subtasks"
                            varient="AddToolsForm"
                            selectedValues={getActiveTaskSubtasks().map(
                              (subtask) => ({
                                _id: subtask.id,
                                name: subtask.name,
                                is_selected: subtask.isSelected,
                                parent_task_id: formState.activeTaskDetails || "",
                              })
                            )}
                            callbackDelete={handleSubtaskDelete}
                            setIsClosing={setIsDatafieldClosing}
                            disabled={!formState.activeTaskDetails}
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Save & Continue Button */}
                  <div className={styles.save_continue_container}>
                    <Button
                      type="Next"
                      Content="Save & Continue"
                      Callback={handleSaveAndContinue}
                    />
                  </div>
                </>
              ) : (
                <div className={styles.task_moved_message}>
                  <p>This task has been moved to Final Details</p>
                </div>
              )}
            </div>
          )}
        </div>

        <div className={styles.separator_line_container}>
          <span className={styles.dottedline_wrapper}></span>
          <div className={styles.separator_icon}>
            <span>
              <SuryconLogo />
            </span>
          </div>
          <span className={styles.dottedline_wrapper}></span>
        </div>

        
        {/* Final Details Section */}
        <div>
          <div className={styles.section_header}>
            <div className={styles.section_icon}>2</div>
            <h4>Final Details</h4>
          </div>

          <div className={styles.final_details_content}>
            {formState.finalDetails.selectedTaskIds.length > 0 ? (
              <div className={styles.final_tasks_container}>
                {formState.finalDetails.selectedTaskIds.map((taskId) => {
                  const task = formState.selectedTasks.find(
                    (t) => t.id === taskId
                  ) || 
                  // If task is not in selectedTasks anymore, get it from taskDetails
                  {
                    id: taskId,
                    name: formState.taskDetails[taskId]?.description || `Task ${taskId}`,
                    isSelected: true
                  };

                  return (
                    <TargetBadge
                      key={`final-${task.id}`}
                      value={task.name}
                      backgroundColor="var(--primary_color)"
                      outerContainerClassName="monthly_target_badge_final"
                      valueTextClassName="monthly_target_badge_text_final"
                    />
                  );
                })}
              </div>
            ) : (
              <div className={styles.no_final_tasks}>
                <p>No tasks added to final details yet</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer Buttons */}
        <div className={styles.footer_buttons}>
          <Button type="Cancel" Content="Cancel" Callback={handleClose} />
          <Button
            type="Next"
            Content={mode === "edit" ? "Update" : "Create"}
            Callback={handleSubmit}
          />
        </div>
      </div>

      {/* Hidden file input for drawings */}
      <input
        type="file"
        ref={drawingsInputRef}
        style={{ display: "none" }}
        onChange={handleDrawingUpload}
        accept=".pdf,.jpg,.jpeg,.png,.dwg"
      />
    </div>
  );
};

export default MonthlyTargetCreationForm;
