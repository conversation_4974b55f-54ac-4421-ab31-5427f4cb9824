.planning_table_header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px 24px;
  background-color: var(--background-color, #ffffff);
 
  min-height: 60px;
}

.planning_table_header_lhs {
  display: flex;
  align-items: center;
  gap: 16px;
}

.planning_table_title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary, #1a1a1a);
  margin: 0;
}

.planning_table_subtitle {
  font-size: 14px;
  color: var(--text-secondary, #666666);
  margin: 0;
}

.planning_table_header_rhs {
  display: flex;
  align-items: center;
  gap: 12px;
}

.approval_button_container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.planning_table_actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Site Drawings button styling - matching LocationHeader */
.site_drawings_btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid var(--border-color, #e0e0e0);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.site_drawings_btn:hover {
  background-color: var(--hover-color, #f5f5f5);
}

.site_drawings_btn h4 {
  margin: 0;
  font-size: 14px;
  color: var(--text-primary, #ffffff);
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
  .planning_table_header {
    padding: 12px 16px;
  }
  
  .approval_button_container {
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .approval_button_container {
    flex-wrap: wrap;
    gap: 6px;
  }
  
  .site_drawings_btn h4 {
    font-size: 12px;
  }
}

/* Additional utility classes */
.planning_table_info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.planning_table_status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--text-secondary, #666666);
}