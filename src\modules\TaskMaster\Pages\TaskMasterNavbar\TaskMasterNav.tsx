// Author Name <PERSON><PERSON><PERSON> catergory and task header in taskmaset

import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../redux/store";

import styles from "./TaskMaster.module.css";
import {
  AddIcon,
  DeleteIcon,
  LocationIcon,
  Uploadicon,
} from "../../../../assets/icons/index";
import { useNavigate, useParams } from "react-router-dom";
import {
  useDeleteTaskCategoryMutation,
  useDeleteTaskMutation,
} from "../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import {
  closePopup,
  togglePopup,
} from "../../../../redux/features/Modules/Reusble/popupSlice";
import {
  initializeBreadcrumb,
  setNavigate,
} from "../../../../redux/features/Modules/Reusble/navigationSlice";
import { useAppSelector } from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { resetInputValues } from "../../../../redux/features/Modules/Reusble/floatinglabelslice";
import {
  setIsDeleted,
  setisDeletedNext,
} from "../../../../redux/features/Modules/Reusble/deletedSlice";
import MainMenu from "../../../../components/Common/Sidebar/SubComponents/MainMenu";
import NavigationComponent from "../../../../components/Reusble/Global/navigationComponents/commonHeaderComponent";
import { AddCategoryForm } from "../Category/Subcomponents/AddCategoryForm/AddcategoryForm";
import { TaskForm } from "../Tasks/Subcomponents/AddTaskForm";
import { setIsDeleteTask } from "../../../../redux/features/Modules/TaskMaster/Slices/CategorySlice";
import { useToast } from "../../../../hooks/ToastHook";
import { ApprovalForm } from "../../../../components/Reusble/TaskMaster/ApprovalForm";
import { useAuth } from "../../../../AuthProvider";

const CategoryView: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const navigateArray = useSelector(
    (state: RootState) => state.navigateData.navigateArray
  );
  // Close the popup when navigation happens (using navigate hook)

  // Close the popup when navigation happens (using navigate hook) by charvi
  useEffect(() => {
    // Dispatch action to close any open popup when navigating to another page by charvi
    dispatch(closePopup("AddCategoryForm"));
    dispatch(closePopup("AddTaskForm"));
  }, [navigate, dispatch]);
  const showToast = useToast();
  // deleted true or false by aayush for hide and show deleted and add button for both form
  const deleted = useAppSelector((state) => state.isDeletedSLice.isDeleted);
  const isdeletednext = useAppSelector(
    (state) => state.isDeletedSLice.isDeletedNext
  );

  // setup the input value on redux by charvi
  const inputValues = useAppSelector(
    (state) => state.floatingLabel.inputValues
  );

  // check for current popu open category and task popup by charvi
  const currentOpenPopup = useAppSelector((state) => state.popup.popups);
  const { catId } = useParams();

  // taoggle currently open popup by charvi
  const handleToggleForm = () => {
    const targetForm = catId ? "AddTaskForm" : "AddCategoryForm";

    if (!navigator.onLine) {
      showToast({
        messageContent: "Oops! no internet connection",
        type: "danger",
      });

      return;
    }

    dispatch(togglePopup(targetForm));
  };

  // close currently open popup by charvi
  const handleCloseForm = () => {
    const targetForm = catId ? "AddTaskForm" : "AddCategoryForm";
    dispatch(resetInputValues());
    if (currentOpenPopup["DeletePopUp"]) {
      dispatch(closePopup("DeletePopUp"));
    } else {
      dispatch(closePopup(targetForm));
    }
  };

  const handleFilterData = () => {
    if (catId) {
      console.log(catId, "thisiscatid");
      dispatch(setisDeletedNext({ isDelete: true }));
      dispatch(setIsDeleteTask(true));
      dispatch(
        setNavigate({
          route: `/category/${catId}`,
          title: "Deleted",
        })
      );
    } else {
      dispatch(setIsDeleted(true));
      dispatch(setisDeletedNext({ isDelete: true }));
      dispatch(setIsDeleteTask(true));
      dispatch(
        setNavigate({
          route: `/category/#`,
          title: "Deleted",
        })
      );
    }
  };

  const { user } = useAuth();
  const isMD = user?.designationId?.name === "MD";

  return (
    <div className={styles.task_header}>
      <div className={styles.tasknav_conatiner}>
        <div className={styles.tasknav_left}>
          <MainMenu />
          {/* <Button type="Navigate" Content="Categories" /> */}
          {navigateArray && <NavigationComponent route={navigateArray} />}
        </div>
        {!deleted && !isdeletednext && (
          <div className={styles.tasknav_rightbtns}>
            {/* deleted button show and hide on the basis of conditions by aayush  */}
            <button className={styles.taskdltbtn} onClick={handleFilterData}>
              <h4>Deleted</h4>
              <DeleteIcon />
            </button>

              {!isMD && (
              <button className={styles.taskdexportbtn}>
                <h4> Export </h4>
                <Uploadicon />
              </button>
            )}
            <button
              className={styles.taskaddcategorybtn}
              onClick={handleToggleForm}
            >
              <h4>{catId ? "Task" : "Category"}</h4>
              <AddIcon />
            </button>
            <button className={styles.locationbtncorner}>
              <LocationIcon />
            </button>
          </div>
        )}

        {currentOpenPopup["AddCategoryForm"] && (
          <AddCategoryForm
            catId={inputValues?.catId ? inputValues?.catId : null}
            onClose={handleCloseForm}
          />
        )}
        {currentOpenPopup["AddTaskForm"] && (
          <TaskForm
            mode={inputValues?.tskId ? "edit" : "add"}
            onClose={handleCloseForm}
          />
        )}
        {currentOpenPopup["ApprovalForm"] && <ApprovalForm />}
      </div>
    </div>
  );
};

export default CategoryView;
