//author name jagroop

import React, { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

import styles from "./Styles/TMMMNav.module.css";
import { useLocation, useNavigate } from "react-router-dom";
import { RootState } from "../../../redux/store";
import {
  closePopup,
  openPopup,
  togglePopup,
} from "../../../redux/features/Modules/Reusble/popupSlice";
import { useAppSelector } from "../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { resetInputValues } from "../../../redux/features/Modules/Reusble/floatinglabelslice";
import { setNavigate } from "../../../redux/features/Modules/Reusble/navigationSlice";
import MainMenu from "../../Common/Sidebar/SubComponents/MainMenu";
import NavigationComponent from "../Global/navigationComponents/commonHeaderComponent";
import { AddIcon, DeleteIcon, Uploadicon } from "../../../assets/icons";
import AddToolsForm from "../../../modules/Billing/Pages/Masters/Tools/Subcomponents/AddToolsForm";
import { ToggleSwitch } from "../Billing/ToggleSwitch/ToggleSwitch";
import AddMaterialsForm from "../../../modules/Billing/Pages/Masters/Materials/Subcomponents/AddMaterialsForm";
import AddManpowerForm from "../../../modules/Billing/Pages/Masters/Manpower/Subcomponents/AddManpowerForm";
import AddMachineryForm from "../../../modules/Billing/Pages/Masters/Machinery/Subcomponents/AddMachineryForm";
import AddCategoryType from "../Global/AddCategoryType";
import {
  resetDeletedGradeData,
  resetDeletedToolData,
  resetDeleteFormData,
  resetDeleteUnitData,
  resetDepartmentFormData,
  resetFormData,
  resetFormMachineryData,
  resetFormManpowerData,
  resetFormMaterialsData,
  resetInitialDepartmentFormData,
  resetInitialFormData,
  resetInitialFormMachineryData,
  resetInitialFormManpowerData,
  resetInitialFormMaterialsData,
  setDeletedToolData,
  setDesignationFormData,
  setDepartmentFormData,
  setFormData,
  setFormMachineryData,
  setFormMaterialsData,
  setFormMode,
  resetInitialDesignationFormData,
  resetDesignationFormData,
  resetMdStateData,
} from "../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import { AddCategoryForm } from "../../../modules/TaskMaster/Pages/Category/Subcomponents/AddCategoryForm/AddcategoryForm";
import { useGetTaskBuildingBlocksQuery } from "../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import { TaskDataType } from "../Global/GlobalInterfaces/GlobalInterface";
import { initializeDatabase } from "../../../functions/functions";
import AddDesignationForm from "../../../modules/Billing/Pages/Masters/Department/Subcomponents/AddDesignationForm";
import DepartmentForm from "../../../modules/Billing/Pages/Masters/Department/Subcomponents/DepartmentForm";
import { useToast } from "../../../hooks/ToastHook";
import { mode } from "crypto-js";
import { DepartmentApprovalForm } from "../../../modules/Billing/Pages/Masters/Department/Subcomponents/DepartmentApprovalForm";
import { useAuth } from "../../../AuthProvider";
import { ApprovalForm } from "../TaskMaster/ApprovalForm";

const TMMMNav: React.FC<{
  isLeftCallback?: (isLeft: boolean) => void;
  Label: string;
  crumbTitle?: string;
  variant?: string;
  countLeft?: number;
  countRight?: number;
  TargetForm: string;
  isHide?: boolean;
  deletedNeeded?: boolean;
  handleDelete?: () => void;
}> = ({
  Label,
  TargetForm,
  isLeftCallback,
  countLeft,
  countRight,
  variant,
  isHide = true,
  deletedNeeded,
  handleDelete,
}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const showToast = useToast();
  //redux state to get the breadcrumbs data
  const navigateArray = useSelector(
    (state: RootState) => state.navigateData.navigateArray
  );

  //states that store the data of the forms of the masters
  const formData =
    TargetForm === "AddMachineryForm"
      ? useSelector((state: RootState) => state.masterForm.formMachineryData)
      : TargetForm === "AddMaterialsForm"
      ? useSelector((state: RootState) => state.masterForm.formMaterialsData)
      : TargetForm === "AddDesignationForm"
      ? useSelector((state: RootState) => state.masterForm.designationFormData)
      : TargetForm === "AddDepartmentForm"
      ? useSelector((state: RootState) => state.masterForm.departmentFormData)
      : useSelector((state: RootState) => state.masterForm.formToolsData);

  console.log("navigate>>", navigateArray);

  //redux state to know which form to open
  const currentOpenPopup = useAppSelector((state) => state.popup.popups);
  const deletedUnitArray = useSelector(
    (state: RootState) => state.masterForm.deleteToolData
  );
  //this state is for applying logic of closing key frames in css
  const [isClosing, setIsClosing] = useState<boolean>(false);
  const [selectedOption, setSelectedOption] = useState("");
  const [selectedOptionApidata, setSelectedOptionApidata] = useState([]);
  const [primaryLabelForAddCategoryType, SetprimaryLabelForAddCategoryType] =
    useState("");
  const { user } = useAuth();
  // const role = user?.designationId?.roleId === "SURYAMD";

  console.log("selected option//", selectedOption);
  const getCategories = async (tablename: string) => {
    console.log("fetheced category called");

    let response;
    let dbName;
    switch (tablename) {
      case "MaterialCategory":
        dbName = await initializeDatabase("MaterialCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "Manpowercategory":
        dbName = await initializeDatabase("ManpowerCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "ToolCategory":
        dbName = await initializeDatabase("ToolCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "machinaryCategory":
        dbName = await initializeDatabase("MachinaryCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "departmentdetails":
        dbName = await initializeDatabase("Departmentmaster");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "designationdetails":
        dbName = await initializeDatabase("Designationmaster");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "alldepartments":
        dbName = await initializeDatabase("Designationmaster");
        const departmentData = await window.electron.allbulkGet({
          dbName,
        });
        console.log("data isss", departmentData);
        response = departmentData.docs;
        break;
      case "allDesignations":
        dbName = await initializeDatabase("Departmentmaster");
        const designationData = await window.electron.allbulkGet({
          dbName,
        });
        console.log("data isss>>", designationData);
        response = designationData.docs;
        break;
      default:
        console.log("Invalid table name");
        response = null;
    }
    console.log(response, "this is response for selecte option");
    return (
      response?.map((e: { _id: string; name: string }) => ({
        id: e._id,
        category: e.name,
      })) || []
    );
  };
  //for getting the data of building blocks
  // const { data, refetch } = useGetTaskBuildingBlocksQuery();

  const handleToggleDropdown = async (modelname?: string) => {
    console.log("funccalleddddd", modelname);
    if (modelname) {
      const data = await getCategories(modelname);
      console.log("all data categories??:", data);
      setSelectedOptionApidata(data);
      console.log(data, "dataiscallingyouhere");
      (() => {
        switch (modelname) {
          case "MaterialCategory":
            SetprimaryLabelForAddCategoryType("Add Materials Category");
            return;
          case "machinaryCategory":
            SetprimaryLabelForAddCategoryType("Add Machinery Category");
            return;
          case "ToolCategory":
            SetprimaryLabelForAddCategoryType("Add Tool Category");
            return;
          case "Manpowercategory":
            SetprimaryLabelForAddCategoryType("Add Manpower Category");
            return;
          case "departmentdetails":
            SetprimaryLabelForAddCategoryType("Add Department");
            return;
          case "alldepartments":
            SetprimaryLabelForAddCategoryType("Designation");
            return;
          case "allDesignations":
            SetprimaryLabelForAddCategoryType("Department");
            return;
          default:
            return <>Unknown </>;
        }
      })();
    }

    // dispatch(openPopup(name));
  };
  useEffect(() => {
    const fetchData = async () => {
      if (TargetForm == "AddMachineryForm") {
        console.log("this is called");
        await handleToggleDropdown("ToolCategory");
        setSelectedOption("ToolCategory");
      }

      if (TargetForm == "AddToolsForm") {
        console.log("this is called");
        await handleToggleDropdown("departmentdetails");
        setSelectedOption("designationdetails");
      }
      if (TargetForm == "AddDepartmentForm") {
        console.log("this is called>>");
        await handleToggleDropdown("alldepartments");
        setSelectedOption("alldepartments");
      }
      if (TargetForm == "AddDesignationForm") {
        console.log("this is called");
        await handleToggleDropdown("allDesignations");
        setSelectedOption("allDesignations");
      }
    };
    fetchData();
  }, [TargetForm]);

  const unitData = [
    { id: 1, category: "Bag" },
    { id: 2, category: "Box" },
    { id: 3, category: "Cft" },
    { id: 4, category: "Cum" },
    { id: 5, category: "Feet" },
    { id: 6, category: "Kgs" },
    { id: 7, category: "Length" },
    { id: 8, category: "Ltrs" },
    { id: 9, category: "Month" },
    { id: 10, category: "Mtr" },
    { id: 11, category: "Nos" },
    { id: 12, category: "Pair" },
    { id: 13, category: "Pkts" },
    { id: 14, category: "Rft" },
    { id: 15, category: "Roll" },
    { id: 16, category: "Set" },
    { id: 17, category: "Sqft" },
    { id: 18, category: "Sqmt" },
    { id: 19, category: "YDS" },
  ];
  // const qualificationsData = [
  //   { id: "673b2d01c580ca75890acf4e", category: "B.E/B.Tech" },
  //   { id: "673b2d01c580ca75890acf4c", category: "B.Sc" },
  //   { id: 3, category: "B.Com" },
  //   { id: 4, category: "M.E/M.Tech" },
  //   { id: 5, category: "M.Sc" },
  //   { id: 6, category: "M.Com" },
  //   { id: 7, category: "MBA" },
  //   { id: 8, category: "Diploma" },
  //   { id: 9, category: "ITI" },
  //   { id: 10, category: "PhD" },
  //   { id: 11, category: "Other" },
  // ];

  // const tempDepartmentData = [
  //   { id: 1, category: "Developer" },
  //   { id: 2, category: "HR" },
  //   { id: 3, category: "Tester" }, // this for temprarory and delete it.
  //   { id: 4, category: "BDE" },
  //   { id: 5, category: "Marketing" },
  //   { id: 6, category: "Digital Sales" },
  // ];

  //this is for the data to show inside the addcategorytype form
  // const designationBuildingBlock =
  //   TargetForm === "AddMachineryForm"
  //     ? data?.data?.response?.tools.map((item: any) => ({
  //         id: item._id,
  //         category: item.name,
  //       })) || []
  //     : TargetForm === "AddMaterialsForm"
  //     ? unitData
  //     : TargetForm === "AddToolsForm"
  //     ? data?.data?.response?.designation.map((item: any) => ({
  //         id: item._id,
  //         category: item.name,
  //       })) || []
  //     : [];

  //useeffect to reset form data and close any open forms whenever the user navigates
  useEffect(() => {
    dispatch(closePopup(TargetForm));
    dispatch(closePopup("AddSubForm"));
    if (
      Label !== "Tools" &&
      Label !== "Machinery" &&
      Label !== "Materials" &&
      Label !== "Manpower" &&
      Label !== "Designation"
    ) {
      dispatch(
        setNavigate({
          route: location.pathname,
          title: Label,
        })
      );
    }
    dispatch(resetFormData());
    dispatch(resetFormMachineryData());
    dispatch(resetFormManpowerData());
    dispatch(resetFormMaterialsData());
    dispatch(resetInputValues());
    dispatch(resetDeleteFormData());
    dispatch(resetDeletedGradeData());
    dispatch(resetDeletedToolData());
    dispatch(resetDepartmentFormData());
    dispatch(resetInitialDepartmentFormData());
    dispatch(resetDesignationFormData());
    dispatch(resetInitialDesignationFormData());
  }, [navigate, dispatch]);

  //this function is for desciding which form to open
  const handleToggleForm = (targetForm: string) => {
    if (!navigator.onLine) {
      showToast({
        messageContent: "Oops! no internet connection!",
        type: "danger",
      });
      return;
    }
    dispatch(setFormMode("Add"));
    dispatch(togglePopup(targetForm));
  };

  //this is function is for closing the forms of masters
  const handleClose = (targetForm: string) => {
    setIsClosing(true);
    setTimeout(() => {
      dispatch(closePopup(targetForm));
      setIsClosing(false);
      if (targetForm === "AddToolsForm") {
        dispatch(resetFormData());
        dispatch(resetInitialFormData());
      }
      if (targetForm === "AddMaterialsForm") {
        dispatch(resetFormMaterialsData());
        dispatch(resetInitialFormMaterialsData());
      }
      if (targetForm === "AddManpowerForm") {
        dispatch(resetFormManpowerData());
        dispatch(resetInitialFormManpowerData());
      }
      if (targetForm === "AddMachineryForm") {
        dispatch(resetFormMachineryData());
        dispatch(resetInitialFormMachineryData());
      }
      if (targetForm === "AddDepartmentForm") {
        // new add for departement
        dispatch(resetInitialDepartmentFormData());
        dispatch(resetDepartmentFormData());
      }
      if (targetForm === "AddDesignationForm") {
        // new add for departement
        dispatch(resetInitialDesignationFormData());
        dispatch(resetDesignationFormData());
      }

      dispatch(resetDeleteFormData());
      dispatch(resetDeletedGradeData());
      dispatch(resetInputValues());
      dispatch(resetDeleteUnitData());
    }, 400);
  };

  //this is for the designation and tools in tool master and machinery master's from
  const handleSelect = useCallback(
    (selectedItems: any, label?: string) => {
      const newselectedItems = selectedItems.map((item: any) => ({
        _id: item.id,
        name: item.category,
      }));
      // const designationSelectedItem = [
      //   {
      //     _id: selectedItems[0].id,
      //     name: selectedItems[0].category,
      //   },
      // ];

      console.log("new selected items", newselectedItems);

      if (selectedItems.length > 0) {
        //this is for machinery master
        if (TargetForm === "AddMachineryForm") {
          const updateUnit = deletedUnitArray?.filter(
            (unit: any) =>
              !newselectedItems?.some(
                (selected: any) => selected.name === unit.name
              )
          );
          dispatch(setDeletedToolData(updateUnit));
          console.log("newselectedItems", newselectedItems);
          dispatch(
            setFormMachineryData({
              ...formData,
              Tools: [
                ...(formData as { Tools: { _id: string; name: string }[] })
                  ?.Tools,
                ...newselectedItems,
              ],
            })
          );
        }

        //this is for tool master
        if (TargetForm === "AddToolsForm") {
          const updateUnit = deletedUnitArray?.filter(
            (unit: any) =>
              !newselectedItems?.some(
                (selected: any) => selected.name === unit.name
              )
          );
          dispatch(setDeletedToolData(updateUnit));
          // console.log("newselectedItems", newselectedItems);
          dispatch(
            setFormData({
              ...formData,
              Users: [
                ...(formData as { Users: { _id: string; name: string }[] })
                  ?.Users,
                ...newselectedItems,
              ],
            })
          );
        }

        if (TargetForm === "AddMaterialsForm") {
          const updateUnit = deletedUnitArray?.filter(
            (unit: any) =>
              !newselectedItems?.some(
                (selected: any) => selected.name === unit.name
              )
          );
          dispatch(setDeletedToolData(updateUnit));
          dispatch(
            setFormMaterialsData({
              ...formData,
              Unit: [
                ...(formData as { Unit: { _id: string; name: string }[] })
                  ?.Unit,
                ...newselectedItems,
              ],
            })
          );
        }

        if (TargetForm === "AddDesignationForm") {
          console.log("new selected items updateUnit>>>", selectedItems);
          console.log("formdata in designation form1", formData);
          dispatch(
            setDesignationFormData({
              ...formData,
              supervisor: {
                _id: selectedItems[0].id,
                name: selectedItems[0].category,
              },
            })
          );
          console.log("formdata in designation form2", formData);
          // dispatch(
          //   setFormData({
          //     ...formData,
          //     qualifications:[
          //       ...(formData as { qualifications: { _id: string; name: string }[] })
          //         ?.qualifications,
          //       ...newselectedItems,
          //     ]
          //   })
          // );
        }

        if (TargetForm === "AddDepartmentForm") {
          const selectedValue = newselectedItems ? newselectedItems[0] : null;
          dispatch(
            setDepartmentFormData({
              ...formData,
              departmentHead: selectedValue,
            })
          );
        }
        showToast({
          messageContent: `${
            selectedItems.length > 1 ? label : selectedItems[0]?.category
          } added Successfully!`,
          type: "success",
        });

        dispatch(closePopup("AddSubForm"));

        //for again opening the form after selection of designation or tools
        if (TargetForm === "AddToolsForm") {
          dispatch(openPopup("AddToolsForm"));
        }

        if (TargetForm === "AddMachineryForm") {
          dispatch(openPopup("AddMachineryForm"));
        }
      }
    },
    [dispatch, formData]
  );

  // const handleRefetchData = () => {
  //   console.log("called this one is here gpt");
  //   refetch(); // make sure `refetch` is defined and callable
  // };

  //function to decide which form will open
  const renderForm = () => {
    if (!currentOpenPopup[TargetForm]) return null; //if popup isnt open return null

    //this is tools master form
    if (TargetForm === "AddToolsForm") {
      return (
        <AddToolsForm
          isClosing={isClosing}
          handleClose={handleClose}
          setIsClosing={setIsClosing}
          // refetchData={handleRefetchData}
        />
      );
    }

    //this is materials master form
    if (TargetForm === "AddMaterialsForm") {
      return (
        <AddMaterialsForm
          isClosing={isClosing}
          setIsClosing={setIsClosing}
          handleClose={handleClose}
        />
      );
    }

    //this is manpower master form
    if (TargetForm === "AddManpowerForm") {
      return (
        <AddManpowerForm isClosing={isClosing} handleClose={handleClose} />
      );
    }

    //this is machinery master form
    if (TargetForm === "AddMachineryForm") {
      return (
        <AddMachineryForm
          isClosing={isClosing}
          handleClose={handleClose}
          setIsClosing={setIsClosing}
        />
      );
    }

    //this form is for categories of all four masters
    if (TargetForm === "AddCategoryForm") {
      return (
        <AddCategoryForm
          variant={variant!}
          onClose={() => {
            dispatch(closePopup("AddCategoryForm"));
            dispatch(resetInputValues());
          }}
        />
      );
    }
    if (TargetForm === "AddDesignationForm") {
      return (
        <AddDesignationForm
          isClosing={isClosing}
          handleClose={handleClose}
          setIsClosing={setIsClosing}
        />
      );
      // return <AddDesignationForm />;
    }

    if (TargetForm === "AddDepartmentForm") {
      return (
        <DepartmentForm
          isClosing={isClosing}
          handleClose={() => {
            handleClose("AddDepartmentForm");
          }}
        />
      );
    }

    if (TargetForm === "ApprovalForm") {
      return (
        <DepartmentApprovalForm
          isClosing={isClosing}
          handleClose={() => handleClose("ApprovalForm")}
        />
      );
    }

    return null;
  };

  return (
    <div className={styles.tmmm_header}>
      <div className={styles.tmmmnav_conatiner}>
        <div className={styles.tmmmnav_left}>
          <MainMenu />
          {navigateArray && <NavigationComponent route={navigateArray} />}
        </div>
        {/* this should only work if the label is tools  */}
        {Label === "Tools" && (
          <div className={styles.tmmmnav_center}>
            <ToggleSwitch
              leftLabel={"Consumables"}
              rightLabel={"Returnable"}
              countLeft={countLeft}
              countRight={countRight}
              onToggle={(left) => {
                // this is for filtering based on consumables and returnables
                isLeftCallback && isLeftCallback(left);
              }}
              width={"320px"}
              id="toggleSwitch"
            />
          </div>
        )}
        {isHide && Label !== "Billing" && (
          <div className={styles.tmmmnav_rightbtns}>
            {deletedNeeded && user?.designationId?.roleId === "SURYAHOD" && (
              <button className={styles.tmmmdexportbtn} onClick={handleDelete}>
                <h4>Deleted</h4>
                <DeleteIcon />
              </button>
            )}

            <button className={styles.tmmmdexportbtn}>
              <h4> Export </h4>
              <Uploadicon />
            </button>
            {(Label === "Department" || Label === "Designation"
              ? user?.designationId?.roleId === "SURYAHOD"
              : true) && (
              <button
                className={styles.tmmmaddcategorybtn}
                onClick={() => handleToggleForm(TargetForm)}
              >
                <h4>{Label}</h4>
                <AddIcon />
              </button>
            )}
            {/* {isMD && (Label !== "Department" || Label !== "Designation") && (
              <button
                className={styles.tmmmaddcategorybtn}
                onClick={() => handleToggleForm(TargetForm)}
              >
                <h4>{Label}</h4>
                <AddIcon />
              </button>
            )} */}

            {/* Show add button for all except Department/Designation for MD, otherwise show for all or for HOD */}
            {/* {(Label === "Department" || Label === "Designation"
              ? user?.designationId?.roleId === "SURYAHOD"
              : true) && (
              <button
                className={styles.tmmmaddcategorybtn}
                onClick={() => handleToggleForm(TargetForm)}
              >
                <h4>{Label}</h4>
                <AddIcon />
              </button>
            )} */}
          </div>
        )}
      </div>
      {/* will improve this currentOpenPopup logic */}
      {currentOpenPopup[TargetForm] && renderForm()}
      {currentOpenPopup["ApprovalForm"] && (
        <DepartmentApprovalForm
          isClosing={isClosing}
          handleClose={() => handleClose("ApprovalForm")}
        />
      )}

      {currentOpenPopup["AddSubForm"] && (
        <AddCategoryType
          singleSelected={
            TargetForm === "AddDesignationForm" ||
            TargetForm === "AddDepartmentForm"
              ? true
              : false
          }
          // singleSelected={TargetForm === "AddDepartmentForm" ? true : false}
          title={`${
            TargetForm === "AddMachineryForm"
              ? "Add Tools"
              : TargetForm === "AddMaterialsForm"
              ? "Add Units"
              : TargetForm === "AddDesignationForm"
              ? "Add Department"
              : TargetForm === "AddDepartmentForm"
              ? "Add Department Head"
              : "Add Designations"
          }`}
          primaryLabel2={
            TargetForm === "AddMachineryForm"
              ? "Add Tools"
              : TargetForm === "AddMaterialsForm"
              ? "Add Units"
              : TargetForm === "AddDesignationForm"
              ? "Add Designations"
              : "Add Designations"
          }
          primaryLabel={primaryLabelForAddCategoryType}
          modelname={selectedOption}
          isStepForm={
            TargetForm === "AddMachineryForm" ||
            TargetForm === "AddToolsForm" ||
            TargetForm === "AddDesignationForm"
              ? true
              : false
          }
          data={
            TargetForm === "AddMaterialsForm"
              ? (unitData as any)
              : TargetForm === "AddDesignationForm"
              ? (selectedOptionApidata as any)
              : TargetForm === "AddDepartmentForm"
              ? (selectedOptionApidata as any)
              : (selectedOptionApidata as TaskDataType[])
          }
          isUnit={
            TargetForm === "AddMaterialsForm" || "AddDesignationForm"
              ? true
              : false
          }
          initialSelected={
            TargetForm === "AddMachineryForm"
              ? (formData as { Tools: { _id: string; name: string }[] })?.Tools
              : TargetForm === "AddMaterialsForm" ||
                "AddDesignationForm" ||
                "AddDepartmentForm"
              ? (formData as { Unit: { _id: string; name: string }[] })?.Unit
              : TargetForm === "AddDesignationForm"
              ? [
                  (formData as { supervisor: { _id: string; name: string } })
                    .supervisor,
                ]
              : (formData as { Users: { _id: string; name: string }[] })?.Users
          }
          label={`${
            TargetForm === "AddMachineryForm"
              ? "Tool"
              : TargetForm === "AddMaterialsForm"
              ? "Unit"
              : TargetForm === "AddDepartmentForm"
              ? "Department Head"
              : TargetForm === "AddDesignationForm"
              ? "Department"
              : "Designation"
          }`}
          textWidth={`${
            (TargetForm === "AddDesignationForm" ||
              TargetForm === "AddDesignationForm") &&
            "100%"
          }`}
          placeholder="Search"
          buttonLabel="Add Category"
          onSelect={(item: any) => {
            handleSelect(
              item,
              `${
                TargetForm === "AddMachineryForm"
                  ? "Tools"
                  : TargetForm === "AddMaterialsForm"
                  ? "Units"
                  : TargetForm === "AddDepartmentForm"
                  ? "Department"
                  : TargetForm === "AddDesignationForm"
                  ? "Designation"
                  : "Designations"
              }`
            );
          }}
          onClose={() => {
            dispatch(closePopup("AddSubForm"));
            dispatch(openPopup(TargetForm));
          }}
        />
      )}
    </div>
  );
};

export default TMMMNav;
