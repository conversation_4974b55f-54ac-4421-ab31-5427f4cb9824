import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import {
  taskWorkInstructionInterfaceName,
  WorkInstructionInterfaceName,
} from "../../../../Interfaces/Modules/TaskMaster/TaskMasterInterface";

const workInstructionIntialState: WorkInstructionInterfaceName = {
  requiredThingsDeleteName: "",
  workinstruction: [],
  currentPopupId:null,
};

const WorkInstructionSlice = createSlice({
  name: "WorkInstructionSlice",
  initialState: workInstructionIntialState,
  reducers: {
    setRequiredThings: (state, action: PayloadAction<string>) => {
      state.requiredThingsDeleteName = action.payload;
    },
    setCurrentPopupId: (state, action: PayloadAction<string>) => {
      state.currentPopupId = action.payload;
    },
    setWorkInstructionDelete: (state, action: PayloadAction<string>) => {
      const isTimestampId = /^\d{13}$/.test(String(action.payload));
      console.log(isTimestampId, "check for tru or false");
      if (
        isTimestampId ||
        state.workinstruction.some((item) => item === action.payload)
      ) {
        console.log("yaha a raha huuuu2", action.payload);
        return state;
      }

      console.log("yaha a raha huuuu2", action.payload);
      state.workinstruction.push(action.payload);
    },
    setWorkInstructionDeleteEmpty: (state, action: PayloadAction<void>) => {
      state.workinstruction = [];
    },
  },
});
const taskworkInstructionIntialState: taskWorkInstructionInterfaceName = {
  taskrequiredThingsDeleteName: "",
  taskworkinstruction: [],
};

const taskWorkInstructionSlice = createSlice({
  name: "WorkInstructionSlice",
  initialState: taskworkInstructionIntialState,
  reducers: {
    settaskRequiredThings: (state, action: PayloadAction<string>) => {
      console.log(action.payload, "checked action payload bro>>>>>>>>>>>");
      state.taskrequiredThingsDeleteName = action.payload;
    },
    settaskWorkInstructionDelete: (state, action: PayloadAction<string>) => {
      const isTimestampId = /^\d{13}$/.test(String(action.payload));
      console.log("action paylod", action.payload);
      console.log(isTimestampId);
      if (isTimestampId) {
        return state;
      }
      state.taskworkinstruction = [
        ...state.taskworkinstruction,
        action.payload,
      ];
    },
    settaskWorkInstructionDeleteEmpty: (state, action: PayloadAction<void>) => {
      state.taskworkinstruction = [];
    },
  },
});
export const {
  settaskRequiredThings,
  settaskWorkInstructionDelete,
  settaskWorkInstructionDeleteEmpty,
} = taskWorkInstructionSlice.actions;
export const taskworkinstrucitonReducer = taskWorkInstructionSlice.reducer;

export const {
  setRequiredThings,
  setCurrentPopupId,
  setWorkInstructionDelete,
  setWorkInstructionDeleteEmpty,
} = WorkInstructionSlice.actions;
export const WorkInstructionReducer = WorkInstructionSlice.reducer;
