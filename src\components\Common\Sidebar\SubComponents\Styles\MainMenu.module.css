.mainmenu_outer_container {
  padding: 0.9rem 0.9rem 0.9rem 1rem;
  position: absolute;
  overflow: hidden;
  margin-top: -0.8rem;
  width: 13.5rem;
  height: 3.525rem;
  gap: 1rem;
  backdrop-filter: blur(40px);
  box-shadow: var(--extra-shdow-six);
  border-radius: 30px;
  background: var(--main_background);
  border: 1px solid;
  transition: all 0.5s ease;
  /* Added separate transition for border-radius */
  z-index: 99;
  color: var(--primary_color) !important;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -16.06%,
    rgba(251, 251, 251, 0.05) 82.03%
  );
  display: flex;
  flex-direction: column;
}

.main_menu_lower_list {
  max-height: calc(70vh - 4rem);
  overflow: scroll;
  /* scrollbar-width: none; */
  display: flex;
  margin-right: -14px;
  flex-direction: column;
  gap: 2rem;
}

.main_menu_lower_list::-webkit-scrollbar {
  width: 3px;
}

.main_menu_lower_list::-webkit-scrollbar-track {
  border-radius: 10px;
}

.main_menu_lower_list::-webkit-scrollbar-thumb {
  background: var(--primary_color);
  border-radius: 10px;
}

.mainmenu_open {
  height: 70vh;
  border-radius: 30px;
  z-index: 9999;
  transition: all 0.5s ease;
  /* Added separate transition for border-radius */
  backdrop-filter: blur(40px);

  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -16.06%,
    rgba(251, 251, 251, 0.05) 82.03%
  );
  background: var(--blur-background);
  scrollbar-width: none;
}

.main_menu_upper_container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.main_menu_container {
  position: relative;
  width: 15rem;
  min-width: 14rem;
}

@media screen and (max-width: 768px) {
  /* .mainmenu_outer_container {
    height: 3.125rem;
  } */
  .main_menu_upper_container {
    align-items: unset;
  }
}
