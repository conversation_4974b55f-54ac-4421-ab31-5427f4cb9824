import React, { useCallback, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Datafield from "../../../../../../components/Reusble/Billing/Masters/Datafield";
import FloatingLabelInput from "../../../../../../components/Reusble/Global/FloatingLabel";
import { IDepartMentformData } from "../../../../../../interfaces/Modules/Billing/DepartmentInterfaces/DepartmentInterfaces";
import {
  useAddDepartementsMutation,
  useDeleteDepartmentByIdMutation,
  useUpdateDepartmentMutation,
} from "../../../../../../redux/api/Modules/Billing/Billingapi";
import { setToast } from "../../../../../../redux/features/Modules/Reusble/ToastSlice";
import {
  resetDepartmentFormData,
  resetInitialDepartmentFormData,
  setDepartmentFormData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import { RootState } from "../../../../../../redux/store";
import AddFormWrapper from "./AddFormWrapper";
import FormChildTemplate from "./FormChildTemplate";

// Form Error InitialState
const initialErrorsState = {
  departmentHead: {
    error: false,
    message: "",
  },
  email: {
    error: false,
    message: "",
  },
  description: {
    error: false,
    message: "",
  },
  departmentName: {
    error: false,
    message: "",
  },
};

// DepartmentForm component handles Add/Edit/Delete/Reason modes for department data.
// It manages form state, validation, API calls, and renders the form UI and summary views.
const DepartmentForm = ({
  isClosing = true,
  handleClose,
}: {
  isClosing: boolean;
  handleClose: () => void;
  setIsClosing?: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [isSummary, setIsSummary] = useState(false);
  const [isDiscard, setIsDiscard] = useState(false);
  const [errors, setErrors] = React.useState(initialErrorsState); // Error state with message
  const formMode = useSelector((state: RootState) => state.masterForm.formMode); // form Mode -> ADD, EDIT, DELETED, REASON
  // Get initial department form data from Redux store (used for comparison/reset)
  const initialFormData = useSelector(
    (state: RootState) => state.masterForm.initialDepartmentFormData
  );
  // Get current department form data from Redux store
  const formData = useSelector(
    (state: RootState) => state.masterForm.departmentFormData
  );
  const dispatch = useDispatch();

  const [addDepartment] = useAddDepartementsMutation(); // updating a Department
  const [updateDepartment] = useUpdateDepartmentMutation(); // deleting a Department by ID
  const [deleteDepartmentById] = useDeleteDepartmentByIdMutation(); // // adding a department

  // console.log("form data?// ", formData);

  // Updates Redux store with new field value and clears error for that field if set.
  const handleInputChange = (id: string, value: string | number) => {
    dispatch(setDepartmentFormData({ ...formData, [id]: String(value) }));

    // If there was an error for this field, clear it on change
    if (errors[id].error) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [id]: {
          error: false,
          message: "",
        },
      }));
    }
  };

  // Resets form state, errors, and Redux data to their initial values.
  const setInitialDepartmentState = () => {
    if (isSummary) setIsSummary(false);
    if (isDiscard) setIsDiscard(false);
    if (errors) setErrors(initialErrorsState);
    if (!validateIsEmpty()) dispatch(resetDepartmentFormData());
    if (formMode === "Edit" && Object.keys(initialFormData).length !== 0)
      dispatch(resetInitialDepartmentFormData());
  };

  // Determines if a field was updated, discarded, or unchanged compared to initial data (for Edit mode).
  const formUpdatedField = React.useCallback(
    (
      field: string,
      value: string = ""
    ): "default" | "updated" | "discard" | undefined => {
      if (formMode !== "Edit") return;

      const key = field as keyof IDepartMentformData;
      const currentValue = formData?.[key];
      const originalValue = initialFormData?.[key];
      const trimmedValue = value.trim();

      // Handle Arrays: checks if value was added or removed from array fields.
      if (Array.isArray(currentValue)) {
        const originalArray = Array.isArray(originalValue) ? originalValue : [];

        if (!trimmedValue) return "default"; // empty input, no intent

        if (!originalArray.includes(trimmedValue)) {
          return originalArray.length && !currentValue.length
            ? "discard"
            : "updated";
        }

        return "default";
      }

      // Handle Objects: checks if object _id changed or was removed.
      if (
        typeof currentValue === "object" &&
        currentValue !== null &&
        "_id" in currentValue
      ) {
        const currentId = currentValue?._id;
        const originalId = (originalValue as any)?._id;

        if (!currentId && originalId) return "discard";
        if (currentId !== originalId) return "updated";
        return "default";
      }

      // Handle Primitives (string, number, etc): checks if value changed or was removed.
      const current = String(currentValue ?? "").trim();
      const original = String(originalValue ?? "").trim();

      if (current === original) return "default";
      if (!current && original) return "discard";
      return "updated";
    },
    [formData, initialFormData, formMode]
  );

  // Validates if the form is empty (all fields are blank).
  const validateIsEmpty = () => {
    let isEmpty = true;
    if (!isDiscard && !isSummary) {
      isEmpty = Object.values(formData ?? {}).some(
        (value) =>
          (typeof value === "string" && value.trim() !== "") ||
          (typeof value === "object" &&
            value !== null &&
            Object.keys(value).length > 0)
      )
        ? false
        : true;
    }
    return isEmpty;
  };

  // Handles closing the form and resets state after a short delay.
  const onClose = () => {
    handleClose();
    setTimeout(setInitialDepartmentState, 400);
  };

  // Handles form submission for Add, Edit, and Delete modes, dispatches API calls and shows toast notifications.
  const submitForm = async () => {
    const formattedData = {
      _id: formData?._id,
      name: formData.departmentName,
      Description: formData.description,
      Email: formData.email,
      DepartmentHead: formData?.departmentHead?._id || null,
    };

    const showToast = (
      messageContent: string,
      type: "success" | "info" | "danger"
    ) => {
      dispatch(
        setToast({
          isOpen: true,
          messageContent,
          type,
        })
      );
    };

    // Handles API response for department actions and shows toast, then closes form if successful.
    const handleApiResponse = (response: any, successMsg: string) => {
      if (response?.success) {
        showToast(successMsg, "success");
        onClose();
      }
    };

    try {
      if (formMode === "Edit") {
        // check if any update in form edit state and edit Api
        if (!hasFormChanged()) {
          showToast("No changes made to the department", "info");
          setIsSummary(false);
          return;
        }
        // Edit API
        const response = await updateDepartment(formattedData).unwrap();
        // console.log("Submit Response Update :", response)
        handleApiResponse(
          response,
          response?.data || "Department updated successfully"
        );
      } else if (formMode === "Deleted") {
        // Delete API
        const response = await deleteDepartmentById(formattedData._id).unwrap();
        handleApiResponse(
          response,
          response?.data || "Department deleted successfully"
        );
      } else {
        // Add NEW API
        const response = await addDepartment(formattedData).unwrap();
        handleApiResponse(
          response,
          response?.data || "Department added successfully!"
        );
      }
    } catch (error) {
      console.error("Form submission error:", error);
      showToast(error?.data?.message, "danger");
    }
  };

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9-]+(\.[a-zA-Z]{2,})+$/;
    return emailRegex.test(email);
  };

  // Validates form fields and sets error state, returns true if any required field is empty.
  const validateError = useCallback(() => {
    const newErrors = { ...errors };
    let hasErrors = false;
    let validateEmail = true;

    Object.entries(formData ?? {}).forEach(([key, value]) => {
      let isEmpty = false;
      if (key === "_id") return;
      if (key === "departmentHead") return;
      if (typeof value === "object") {
        isEmpty = !value || Object.keys(value).length < 0;
      } else if (key === "email") {
        isEmpty = !value || value.toString().trim() === "";
        if (!isEmpty && !isValidEmail(value)) {
          validateEmail = false;
        }
      } else {
        isEmpty = !value || value.toString().trim() === "";
      }

      newErrors[key] = {
        error: isEmpty || !validateEmail,
        message: isEmpty ? `${key} is required.` : validateEmail ? "" : "Email is not Valid!",
      };

      if (isEmpty) {
        hasErrors = true;
      }
    });

    setErrors(newErrors);

    if (!hasErrors && !validateEmail) {
      dispatch(
        setToast({
          isOpen: true,
          messageContent: "Please enter a valid email",
          type: "warning",
        })
      );

      return true;
    }

    if (hasErrors) {
      dispatch(
        setToast({
          isOpen: true,
          messageContent: "Please fill the required fields",
          type: "warning",
        })
      );
    }

    return hasErrors;
  }, [formData, errors]);

  // Checks if any field in the form has changed compared to the initial data.
  function hasFormChanged(): boolean {
    if (initialFormData.departmentName !== formData.departmentName) return true;
    if (initialFormData.description!== formData.description) return true;
    if (initialFormData.email !== formData.email) return true;

    const originalHead = initialFormData.departmentHead;
    const currentHead = formData.departmentHead;

    if (originalHead === null && currentHead !== null) return true;
    if (originalHead !== null && currentHead === null) return true;
    if (
      originalHead !== null &&
      currentHead !== null &&
      (originalHead._id !== currentHead._id ||
        originalHead.name !== currentHead.name)
    ) {
      return true;
    }

    return false;
  }

  // console.log("DepartmentForm rendered with formMode:", formMode, formData);

  return (
    <AddFormWrapper
      label="Department"
      key="department-form"
      isOpen={!isClosing}
      isSummary={isSummary}
      isDiscard={isDiscard}
      isDeleted={formMode === "Deleted"}
      isEdited={formMode === "Edit"}
      setSummary={setIsSummary}
      setDiscard={setIsDiscard}
      onClose={onClose}
      headingLabel={formMode === "Add" ? "Add Department" : "Edit Department"}
      isRightButtonVisible={true}
      btn1Label="Cancel"
      // btn1Handler={}
      btn2Label="Add"
      btn2Handler={isSummary || formMode === "Deleted" ? submitForm : () => {}}
      status="default"
      validateIsEmpty={validateIsEmpty}
      validateError={validateError}
      validateFormUpdated={hasFormChanged}
    >
      {/* Renders the department form fields for Add/Edit mode, including Department Name, Department Head, Description, and Email. */}
      {!isSummary &&
        !isDiscard &&
        (formMode === "Add" || formMode === "Edit") && (
          <form>
            <FloatingLabelInput
              label="Department Name"
              type="text"
              placeholder="Enter Department Name"
              value={formData?.departmentName}
              id="department-name"
              onInputChange={(value) =>
                handleInputChange("departmentName", value)
              }
              props="one_line"
              error={errors.departmentName.error}
              focusOnInput={true}
            />

            <div style={{ marginTop: "1.25rem" }}>
              <Datafield
                key={"Depatment-head"}
                label="Department Head"
                heading="Department Head"
                error={errors.departmentHead.error}
                selectedValues={
                  formData.departmentHead
                    ? [formData.departmentHead]
                    : undefined
                }
                callbackDelete={() => {
                  dispatch(
                    setDepartmentFormData({ ...formData, departmentHead: null })
                  );
                }}
              />
            </div>

            <FloatingLabelInput
              label="Description"
              type="text"
              placeholder="Enter description"
              value={formData?.description}
              id="description"
              props="description_prop"
              onInputChange={(value) => handleInputChange("description", value)}
              error={errors.description.error}
              focusOnInput={false}
            />
            <FloatingLabelInput
              label="Email"
              type="email"
              placeholder="Enter contact email"
              value={formData?.email}
              id="contact-email"
              maxlength={50}
              onInputChange={(value) => handleInputChange("email", value)}
              error={errors.email.error}
              focusOnInput={false}
              props="one_line"
            />
          </form>
        )}

      {/* Renders summary/discard/deleted view using FormChildTemplate with department fields. */}
      {formMode !== "Reason" &&
        (isDiscard || isSummary || formMode === "Deleted") && (
          <>
            <FormChildTemplate
              formData={formData || {}}
              formDataLabels={{
                departmentName: "Department Name",
                departmentHead: "Department Head",
                description: "Description",
                email: "Email",
              }}
              formUpdatedField={formUpdatedField}
              initialFormData={initialFormData}
            />
          </>
        )}

      {/* Renders reason view using FormChildTemplate with only the reason field. */}
      {formMode === "Reason" && (
        <FormChildTemplate
          formData={formData || {}}
          formDataLabels={{
            reason: "Reason",
          }}
        />
      )}
    </AddFormWrapper>
  );
};

export default DepartmentForm;
