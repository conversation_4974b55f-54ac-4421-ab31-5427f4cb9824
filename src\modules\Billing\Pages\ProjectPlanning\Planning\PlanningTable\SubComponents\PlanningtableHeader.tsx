import { useNavigate, useParams } from "react-router-dom";
import { planningHeaderProps } from "../Interfaces/Interfaces";
import styles from "../Styles/PlanningTable.module.css";
import {
  DurationIcon,
  MenuIcon,
  RedCross,
  TaskEditPencil,
} from "../../../../../../../assets/icons";
import Circle from "../../../../../../../components/Reusble/Billing/Circle";
import TargetBadge from "../../../../../../../components/Reusble/Global/TargetBadge/TargetBadge";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../../../../redux/store";
import React, { useEffect, useCallback, useState } from "react";
import {
  setEditMode,
  setTowerBasicDetails,
} from "../../../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/projectPlanningSlice";
import HeaderCard from "./HeaderCard";
import { initializeDatabase } from "../../../../../../../functions/functions";
import {
  PaymentIcon,
  RateIcon,
  ShutteringIcon,
  UnitSvgs,
} from "../../../../../../../assets/TopbarAssets/SVGs";
import { saveSyncData } from "../../../../../../../Backup/BackupFunctions/BackupFunctions";
import SummaryPopup from "../../../../../../../components/Reusble/Global/SummaryPopup";
import { useToast } from "../../../../../../../hooks/ToastHook";

const PlanningtableHeader: React.FC<planningHeaderProps> = ({
  edit,
  handleToggleEdit,
  isPlanning,
  totalItems,
  handleUpdateLocation,

  // onHeaderFieldChange
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const { projectId, towerLocationId } = useParams<{
    projectId: string;
    towerLocationId: string;
  }>();
  const seletectedTask = useSelector(
    (state: RootState) => state.projectPlanning.selectedLocationTaskId
  );
  const currentTaskDetails = useSelector(
    (state: RootState) => state.projectPlanning.currentTaskbasicDetails
  );
  console.log(currentTaskDetails, "this is selectedtaskdastasdfa");

  const editMode = useSelector(
    (state: RootState) => state.projectPlanning.editMode
  );

  const currentSelectedData = useSelector(
    (state: RootState) => state.projectPlanning.curretSelectedData
  );
  const showToast = useToast();

  console.log(currentSelectedData, "this is current selecteddata");

  const fetchData = async () => {
    // dispatch(selectLocationTaskId(""));
    console.log("function running");
    if (!seletectedTask) return;
    console.log(seletectedTask, "data of selected task");
    const dbName = await initializeDatabase("TaskBasicDetails");
    let fetchedData;

    console.log(seletectedTask, "this si current asdfasdf");
    const formatedTaskId = seletectedTask;
    fetchedData = await window.electron.getDocumentByParentId({
      dbName,
      categoryId: "taskid",
      catId: `${formatedTaskId}`,
    });

    console.log(fetchedData?.[0], "this if fetched data bro checkout her");
    if (Array.isArray(fetchedData) && fetchedData.length > 0 && 
      fetchedData[0]?._id?.substring(0, 24) === seletectedTask) {
      dispatch(setTowerBasicDetails(fetchedData?.[0]));
    } else {
      dispatch(setTowerBasicDetails({}));
    }
  };

  useEffect(() => {
    if (!seletectedTask) {
      dispatch(setTowerBasicDetails({}));
      return;
    }
    fetchData();
  }, [seletectedTask]);

  const selectedProjectRate = useSelector(
    (state: RootState) => state.projectPlanning.selectedprojectRate
  );
  const selectedProjectEstimate = useSelector(
    (state: RootState) => state.projectPlanning.selectedprojectestimate
  );
  console.log(selectedProjectEstimate, "this is selected project estimate");

  async function handleFieldChange(fieldName: string, value: number) {
    const updatedTask = {
      ...currentTaskDetails,
      [fieldName]: value,
    };
    //  onHeaderFieldChange && onHeaderFieldChange(fieldName, value);
    dispatch(setTowerBasicDetails(updatedTask));
    await saveSyncData(updatedTask, "time", "TaskBasicDetails");
    dispatch({
      type: "projectPlanning/updateplanningPercentage",
      payload: {
        taskId: updatedTask._id,
        taskData: updatedTask,
      },
    });
  }
  console.log(currentTaskDetails, "this is current task details");
  const SkeletonBox = ({
    width = "100px",
    height = "24px",
    className = styles.skeleton_box,
  }) => (
    <span
      className={className}
      style={{
        width,
        height,
      }}
    />
  );

  useEffect(() => {
    if (currentTaskDetails && currentTaskDetails._id) {
      dispatch({
        type: "projectPlanning/updateplanningPercentage",
        payload: {
          taskId: currentTaskDetails._id,
          taskData: currentTaskDetails,
        },
      });
    }
  }, [
    currentTaskDetails?.area,
    currentTaskDetails?.payments,
    currentTaskDetails?.payment,
    currentTaskDetails?.durations,
    currentTaskDetails?.duration,
    currentTaskDetails?.shuttering,
  ]);

  useEffect(() => {
    dispatch(setEditMode(false));
  }, [seletectedTask, dispatch]);

  useEffect(() => {
    return () => {
      dispatch(setEditMode(false));
    };
  }, [dispatch]);

  const handleUpdateClick = async () => {
    try {
      await handleUpdateLocation();
    } catch (error) {
      console.error("Update failed:", error);
    }
  };
  const [showFullDescription, setShowFullDescription] = useState(false);

  function getTruncatedDescription(desc: string, wordLimit = 30) {
    const words = desc?.split(" ") || [];
    if (words.length <= wordLimit) return desc;
    return words.slice(0, wordLimit).join(" ");
  }

  console.log(currentTaskDetails);

  return (
    <div
      className={
        isPlanning ? styles.planningtable_header : styles.materialtable_header
      }
    >
      <div className={styles.planningtable_header_inner_container}>
        {isPlanning ? (
          <div>
            <div className={styles.planningtable_header_inner_container_text}>
              {currentTaskDetails  && currentTaskDetails._id  ? (
                <>
                  <h3>{currentTaskDetails?.name}</h3>
                  <p style={{ maxWidth: "50rem", display: "inline" }}>
                    {currentTaskDetails?.description &&
                    currentTaskDetails.description.split(" ").length > 30 ? (
                      <>
                        {getTruncatedDescription(
                          currentTaskDetails.description,
                          30
                        )}{" "}
                        <span
                          style={{
                            color: "var(--secondary_color)",
                            cursor: "pointer",
                            fontWeight: 500,
                            textDecoration: "underline",
                            textUnderlineOffset: "1.5px",
                            textDecorationThickness: "1px",
                            textDecorationColor: "var(--secondary_color)",
                          }}
                          onClick={() => setShowFullDescription(true)}
                        >
                          View all
                        </span>
                      </>
                    ) : (
                      currentTaskDetails?.description
                    )}
                  </p>
                </>
              ) : (
                <>
                  <SkeletonBox width="180px" height="32px" />
                  <br />
                  <SkeletonBox width="240px" height="18px" />
                </>
              )}
            </div>

            <div
              style={{ height: "38px" }}
              className={styles.planningtable_headerUnitsection}
            >
              <HeaderCard
                taskid={seletectedTask}
                unit="sqft"
                regex={/^\d{0,10}(\.\d{0,2})?$/}
                title="Area"
                onChange={(e) =>
                  handleFieldChange("area", Number(e.target.value))
                }
                id="Area"
                item={currentTaskDetails?.area ?? ""}
                edit={editMode}
                icon={UnitSvgs}
              />
              <HeaderCard
                taskid={seletectedTask}
                regex={/^\d{0,10}(\.\d{0,2})?$/}
                title="Rate"
                onChange={(e) =>
                  handleFieldChange("rate", Number(e.target.value))
                }
                id="Rate"
                unit=""
                item={selectedProjectRate ?? ""}
                edit={false}
                icon={RateIcon}
              />
              <HeaderCard
                taskid={seletectedTask}
                regex={/^(100(?:\.0{1,})?|[0-9]{1,2}(?:\.\d*)?)?$/}
                title="Payment"
                onChange={(e) =>
                  handleFieldChange("payment", Number(e.target.value))
                }
                id="Payment"
                unit="%"
                item={currentTaskDetails?.payment ?? ""}
                edit={editMode}
                icon={PaymentIcon}
              />
              <HeaderCard
                taskid={seletectedTask}
                regex={/^(?:[0-9]|[12]?\d{0,2}|3[0-5]?\d|365)$/}
                title="Duration"
                onChange={(e) =>
                  handleFieldChange("duration", Number(e.target.value))
                }
                id="Duration"
                unit="days"
                item={currentTaskDetails?.duration ?? ""}
                edit={editMode}
                icon={DurationIcon}
              />
              <HeaderCard
                taskid={seletectedTask}
                regex={/^\d{0,10}(\.\d{0,2})?$/}
                title="Shuttering"
                onChange={(e) =>
                  handleFieldChange("shuttering", Number(e.target.value))
                }
                id="Shuttering"
                unit="sqm"
                item={currentTaskDetails?.shuttering ?? ""}
                edit={editMode}
                icon={ShutteringIcon}
              />
            </div>
          </div>
        ) : (
          <div className={styles.planningtable_header_material_inner_container}>
            <h2 className={styles.planning_table_header_material_text}>
              Materials
            </h2>
            <Circle
              nameclass="material_header_bubble"
              icon={
                <h4 style={{ color: "var(--primary_color)" }}>{totalItems}</h4>
              }
            />
          </div>
        )}
      </div>

      <div className={styles.planningtable_headereditbtn}>
        {isPlanning && (
          <div
            className={styles.edit_pencil_container}
            onClick={async () => {
              if (!navigator.onLine) {
                showToast({
                  messageContent: "Oops! no internet connection!",
                  type: "danger",
                });
                return;
              }
              if (handleToggleEdit) {
                await handleToggleEdit();
              }
            }}
          >
            {edit ? (
              <div className={styles.redCrossStyle} onClick={handleUpdateClick}>
                <RedCross />
              </div>
            ) : (
              <TaskEditPencil />
            )}
          </div>
        )}
        <div className={styles.planning_header_badge_container}>
          {isPlanning && selectedProjectEstimate && (
            <div className={styles.estimateBadgeCustom}>
              <span>
                ₹{" "}
                {Number(selectedProjectEstimate).toLocaleString("en-IN", {
                  maximumFractionDigits: 0,
                })}
              </span>
            </div>
          )}
          <TargetBadge
            outerContainerClassName="target_badge_for_planning_table_header"
            valueTextTagName="h4"
            value={isPlanning ? "Materials" : "Planning"}
            order
            onClick={async () => {
              if (isPlanning) {
                navigate(
                  `/billing/main/${projectId}/location/${towerLocationId}/materials`
                );
              } else {
                navigate(
                  `/billing/main/${projectId}/location/${towerLocationId}`
                );
              }
            }}
            icon={<MenuIcon />}
          />
        </div>
      </div>
      {showFullDescription && currentTaskDetails && (
        <SummaryPopup
          header="View"
          callbackCross={() => setShowFullDescription(false)}
          callbackBack={() => setShowFullDescription(false)}
          callbackApprove={() => setShowFullDescription(false)}
        >
          <div style={{ display: "flex", flexWrap: "wrap" }}>
            <div
              className={styles.summaryDivData}
              style={{ flex: 1, minWidth: 220 }}
            >
              <div className={styles.summaryDataContent}>
                <p className="p_tag_14px">Name</p>
                <h4>{currentTaskDetails?.name}</h4>
              </div>
            </div>
          </div>
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p className="p_tag_14px">Description</p>
              <h4>{currentTaskDetails?.description}</h4>
            </div>
          </div>
          <div style={{ display: "flex", flexWrap: "wrap" }}>
            <div
              className={styles.summaryDivData}
              style={{ flex: 1, minWidth: 220 }}
            >
              <div className={styles.summaryDataContent}>
                <p className="p_tag_14px">Area</p>
                <h4>{`${currentTaskDetails?.area ?? ""} sqft`}</h4>
              </div>
            </div>
            <div
              className={styles.summaryDivData}
              style={{ flex: 1, minWidth: 220 }}
            >
              <div className={styles.summaryDataContent}>
                <p className="p_tag_14px">Rate</p>
                <h4>{`₹ ${selectedProjectRate ?? ""}`}</h4>
              </div>
            </div>
            <div
              className={styles.summaryDivData}
              style={{ flex: 1, minWidth: 220 }}
            >
              <div className={styles.summaryDataContent}>
                <p className="p_tag_14px">Payment</p>
                <h4>{`${currentTaskDetails?.payment ?? ""} %`}</h4>
              </div>
            </div>
          </div>
          <div style={{ display: "flex", flexWrap: "wrap" }}>
            <div
              className={styles.summaryDivData}
              style={{ flex: 1, minWidth: 220 }}
            >
              <div className={styles.summaryDataContent}>
                <p className="p_tag_14px">Duration</p>
                <h4>{`${currentTaskDetails?.duration ?? ""} days`}</h4>
              </div>
            </div>
            <div
              className={styles.summaryDivData}
              style={{ flex: 1, minWidth: 220 }}
            >
              <div className={styles.summaryDataContent}>
                <p className="p_tag_14px">Shuttering</p>
                <h4>{`${currentTaskDetails?.shuttering ?? ""} sqm`}</h4>
              </div>
            </div>
          </div>
        </SummaryPopup>
      )}
    </div>
  );
};

export default PlanningtableHeader;
