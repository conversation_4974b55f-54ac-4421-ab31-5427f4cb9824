import React, {
  FC,
  useState,
  SetStateAction,
  useEffect,
  useCallback,
  useRef,
} from "react";
import styles from "./styles/TriggerEvent.module.css";
import { useDispatch, useSelector } from "react-redux";

import { RootState } from "../../../../redux/store";
import {
  resetInputValues,
  setInputValue,
} from "../../../../redux/features/Modules/Reusble/floatinglabelslice";
import FloatingLabelInput from "../../../../components/Reusble/Global/FloatingLabel";
import { Cross } from "../../../../assets/icons";
import TEPopupComp from "./subcomponents/TEPopupComponent";
import TERadioValComp from "./subcomponents/TERadioComponent";
import Button from "../../../../components/Reusble/Global/Button";
import { TriggerEventDilalogueProps } from "../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import { closePopup } from "../../../../redux/features/Modules/Reusble/popupSlice";
import {
  setChangeAPiFlag,
  updateSubtaskData,
} from "../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import {
  changeActiveState,
  setIsOnlyResponse,
  setName,
  setTriggerFormData,
  setTriggerMode,
} from "../../../../redux/features/Modules/TaskMaster/Slices/TriggerEventSlice";
import { saveSyncData } from "../../../../Backup/BackupFunctions/BackupFunctions";
import { useToast } from "../../../../hooks/ToastHook";

const TEFormComponent: FC<TriggerEventDilalogueProps> = ({
  data,
  callBackCancel,
  mode,
  button1Content,
  button1Type,
  button2Content,
  button2Type,
  header,
  inputDecline,
}) => {
  const showToast = useToast();
  const [errors, setErrors] = useState<Record<string, boolean | null>>({});
  const [wasTrue, setWasTrue] = useState(false);
  const [isClosing, setIsClosing] = useState<boolean>(false);

  const subtaskData = useSelector(
    (state: RootState) => state.taskMaster.currentSubtaskData
  );

  const triggerData = useSelector(
    (state: RootState) => state.taskMaster.currentSubtaskData?.AutoId
  );

  const allsubroutes = useSelector(
    (state: RootState) => state.triggerEvent.allsubroutes
  );
  const [discard, setDiscard] = useState<boolean>(false);
  const inputValues = useSelector(
    (state: RootState) => state.floatingLabel.inputValues
  );
  const [isSummaryDialogueOpen, setisSummaryDialogueOpen] =
    useState<boolean>(false);

  const dispatch = useDispatch();

  //inputValues add and edit
  const startAfterAddInputValue = useSelector(
    (state: RootState) =>
      state.floatingLabel.inputValues.trigger_event_add_start_after_time
  );

  const triggerFormData = useSelector(
    (state: RootState) => state.triggerEvent.triggerFormData
  );
  const isOnlyResponse = useSelector(
    (state: RootState) => state.triggerEvent.isOnlyResponse
  );
  const responseTimeAddInputValue = useSelector(
    (state: RootState) =>
      state.floatingLabel.inputValues.trigger_event_add_response_time
  );
  //trigger action values
  const TriggerStructureData = useSelector(
    (state: RootState) => state.triggerEvent.triggerStructureData
  );

  const normalizeValue = (value: any) =>
    value === "" || value === null ? undefined : value;
  const normalizeNumber = (value: any) =>
    value === undefined || value === "" || value === null ? 0 : Number(value);

  const hasChanged = (initialData: any, finalData: any) => {
    return (
      normalizeValue(initialData?.TriggerResponse?._id) !==
        normalizeValue(finalData?.TriggerResponse?._id) ||
      normalizeValue(initialData?.TriggerAction?.ActionName?.id) !==
        normalizeValue(finalData?.TriggerAction?.ActionName?.id) ||
      normalizeNumber(initialData?.TriggerAction?.ActionTime) !==
        normalizeNumber(finalData?.TriggerAction?.ActionTime) ||
      normalizeNumber(initialData?.ResponseTime) !==
        normalizeNumber(finalData?.ResponseTime)
    );
  };

  const handleTriggerResponseSelect = (selected: {
    id: number | string;
    name: string;
    isFirst?: boolean;
  }) => {
    dispatch(
      setTriggerFormData({
        TriggerAction: {
          ActionName: null,
          ActionTime: "",
        },
        TriggerResponse: {
          _id: selected?.id,
          name: selected?.name,
        },
        ResponseTime: "",
      })
    );
    dispatch(resetInputValues());
  };

  useEffect(() => {
    if (triggerFormData?.TriggerResponse) {
      setErrors({ Response: false, TimeInterval: false });
    }
  }, [triggerFormData?.TriggerResponse]);

  useEffect(() => {
    setErrors({ Response: false, TimeInterval: false });
  }, [triggerData?.TriggerAction?.ActionTime]);

  const handleActionSelect = (selectedAction: {
    id: string | number;
    name: string;
  }) => {
    dispatch(
      setTriggerFormData({
        TriggerAction: {
          ActionName: selectedAction,
          ActionTime: "",
        },
        TriggerResponse: triggerFormData?.TriggerResponse,
        ResponseTime: "",
      })
    );
    dispatch(setInputValue({ trigger_event_add_start_after_time: "" }));
    dispatch(setInputValue({ trigger_event_add_response_time: "" }));
  };

  const handleInputValuesChange = () => {
    dispatch(
      setTriggerFormData({
        TriggerAction: {
          ActionName: triggerFormData?.TriggerAction?.ActionName,
          ActionTime: Number(startAfterAddInputValue),
        },
        TriggerResponse: triggerFormData?.TriggerResponse,
        ResponseTime: Number(responseTimeAddInputValue),
      })
    );
  };

  useEffect(() => {
    if (startAfterAddInputValue || responseTimeAddInputValue) {
      handleInputValuesChange();
    }
  }, [
    startAfterAddInputValue,
    responseTimeAddInputValue,
    triggerFormData?.TriggerResponse,
  ]);

  useEffect(() => {
    dispatch(
      changeActiveState({
        active:
          triggerFormData?.TriggerResponse?.name === "This is a First Subtask"
            ? false
            : true,
      })
    );
    dispatch(setName(triggerFormData?.TriggerResponse?.name ?? ""));
  }, [triggerFormData?.TriggerResponse?.name, subtaskData?._id]);

  const handleClose = () => {
    if (hasChanged(subtaskData?.AutoId, triggerFormData)) {
      setDiscard(true);
      return;
    }

    setIsClosing(true);
    setTimeout(() => {
      if (callBackCancel) {
        callBackCancel();
      }
    }, 300);
  };
const handleSubmitt = () =>{
    dispatch(closePopup("TEForm"));
              dispatch(
                updateSubtaskData({
                  ...subtaskData,
                  AutoId: triggerFormData,
                })
              );

              saveSyncData(
                {
                  ...subtaskData,
                  AutoId: triggerFormData,
                },
                "time",
                "SubTaskForm"
              );
             showToast({
                   messageContent: isOnlyResponse
                    ? "Response Time Added Successfully!"
                    : "Trigger Event Added Successfully!",
                  type: "success",
               });
           
              dispatch(setChangeAPiFlag(true));
              dispatch(
                setTriggerFormData({
                  TriggerAction: {
                    ActionName: null,
                    ActionTime: "",
                  },
                  TriggerResponse: null,
                  ResponseTime: "",
                })
              );
              dispatch(resetInputValues());
              dispatch(setIsOnlyResponse(false));
              setisSummaryDialogueOpen(false);
              setDiscard(false)
}
  
const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && e.shiftKey)  return; 
  if (e.key === "Enter") {
    e.preventDefault();
    e.stopPropagation();
     if(!discard && !isSummaryDialogueOpen){
      if (
                !triggerFormData?.TriggerResponse?._id &&
                triggerFormData?.TriggerResponse?.name !=
                  "This is a First Subtask"
              ) {
                setErrors({ Response: true });
                showToast({
                  messageContent: "Trigger Response is required.",
                    type: "warning",
                });
              
                return;
              }

              if (
                !triggerFormData?.TriggerAction?.ActionName &&
                triggerFormData?.TriggerResponse?.name !=
                  "This is a First Subtask"
              ) {
                setErrors({ ...errors, Action: true });
                showToast({
                  messageContent: "Action is required.",
                    type: "warning",
                });
               
                return;
              }

              if (
                !inputValues?.trigger_event_add_start_after_time &&
                (triggerFormData?.TriggerAction?.ActionName?.id === "102" ||
                  triggerFormData?.TriggerAction?.ActionName?.id === "104")
              ) {
                setErrors({ ...errors, TimeInterval: true });
                showToast({
                  messageContent: "Time Interval is required.",
                  type: "warning",
                });
                return;
              }

              setErrors({
                Response: false,
                TimeInterval: false,
                Action: false,
              });
              setisSummaryDialogueOpen(true);
            
   }
 if(isSummaryDialogueOpen){
 handleSubmitt();
 }
 if(discard){
    dispatch(resetInputValues());
              dispatch(setTriggerMode(""));
              dispatch(
                setTriggerFormData({
                  TriggerAction: {
                    ActionName: null,
                    ActionTime: "",
                  },
                  TriggerResponse: null,
                  ResponseTime: "",
                })
              );
              dispatch(setIsOnlyResponse(false));
              dispatch(changeActiveState({ active: true }));
              setisSummaryDialogueOpen(false);
              setDiscard(false);
 }
  }

  if (e.key === "Escape") {
     e.preventDefault();
    e.stopPropagation();
    if(!discard && !isSummaryDialogueOpen){
        handleClose();
    }
    if(discard){
   if (discard && wasTrue) {
                setDiscard(false);
                setisSummaryDialogueOpen(true);
                setWasTrue(false);
                return;
              }
              setDiscard(false);
    }
              if(isSummaryDialogueOpen){
                setisSummaryDialogueOpen(false);
              }
  }
};

  const formRef =useRef(null);
  useEffect(() => {
    if (true) {
      if (formRef.current) {
        formRef.current.focus();
      }
    }
}, []);
  return (
    <div
      className={`${styles.trigger_event_dialog_form_container} ${
        isClosing ? styles.closing : ""
      }`}
      tabIndex={0}
      onKeyDown={handleKeyDown}
      ref={formRef }
    >
      <div className={`${styles.trigger_event_dialog_form_top_header}`}>
      <h3
          className={`${styles.trigger_event_dialog_form_top_text}`}
          style={{
            color: discard ? "var( --warning_color)" : "var(--primary_color)",
          }}
        >
          {isSummaryDialogueOpen
            ? mode === "edit"
              ? "Are you sure you want to Update this Trigger Event?"
              : "Are you sure you want to Add this Trigger Event?"
            : discard
            ? "Are you sure you want to  discard These Changes?"
            : header}
        </h3>
        <button
          onClick={() => {
            if (isSummaryDialogueOpen) {
              setDiscard(true);
              setWasTrue(true);
              setisSummaryDialogueOpen(false);
              return;
            }
            if (discard && !wasTrue) {
              setDiscard(false);
              return;
            }
            if (discard && wasTrue) {
              setDiscard(false);
              setWasTrue(false);
              setisSummaryDialogueOpen(true);
              return;
            }

            handleClose();
          }}
          className={`${styles.trigger_event_cross_icon}`}
        >
          {" "}
          <Cross />{" "}
        </button>
      </div>
      <div className={`${styles.trigger_event_dialog_form_top}`}>
        
        {inputDecline ? (
          <FloatingLabelInput
            onInputChange={() => {}}
            id="101"
            focusOnInput={true}
            props="mt_confirmation_decline_reason_text_area"
            label="Reason"
          />
        ) : isSummaryDialogueOpen ? (
          <div>
            {triggerFormData?.TriggerResponse && (
              <div>
                <h4>Trigger Response</h4>
                <div className={styles.subtask_confirmation_subcard}>
                  <p className="p_tag_14px">Subtask</p>
                  <p
                    style={{
                      color:
                        normalizeValue(
                          subtaskData?.AutoId?.TriggerResponse?._id
                        ) !==
                          normalizeValue(
                            triggerFormData?.TriggerResponse?._id
                          ) && mode == "edit"
                          ? "var(--secondary_color)"
                          : "#00000099;",
                    }}
                  >
                    {triggerFormData?.TriggerResponse?.name}
                  </p>
                </div>
              </div>
            )}
            {triggerFormData?.TriggerAction?.ActionName?.name && (
              <div style={{ marginTop: "1rem" }}>
                <h4>Action</h4>
                <div className={styles.subtask_confirmation_subcard}>
                  <p
                    style={{
                      color:
                        normalizeValue(
                          subtaskData?.AutoId?.TriggerAction?.ActionName?.name
                        ) !==
                          normalizeValue(
                            triggerFormData?.TriggerAction?.ActionName?.name
                          ) && mode == "edit"
                          ? "var(--secondary_color)"
                          : "black",
                    }}
                  >
                    {triggerFormData?.TriggerAction?.ActionName?.name}
                  </p>
                  {!isNaN(triggerFormData?.TriggerAction?.ActionTime) &&
                    triggerFormData?.TriggerAction?.ActionTime != 0 && (
                      <div>
                        <p
                          className="p_tag_14px"
                          style={{ marginTop: "1rem", color: "#00000099;" }}
                        >
                          Time Interval
                        </p>
                        <div
                          className={
                            styles.subtask_action_discard_time_interval
                          }
                        >
                          <p
                            style={{
                              color:
                                normalizeNumber(
                                  subtaskData?.AutoId?.TriggerAction?.ActionTime
                                ) !==
                                  normalizeNumber(
                                    inputValues?.trigger_event_add_start_after_time
                                  ) && mode == "edit"
                                  ? "var(--secondary_color)"
                                  : "black",
                            }}
                          >
                            {inputValues?.trigger_event_add_start_after_time}
                          </p>
                          <div className={styles.time_unit}>
                            <h4>Hours</h4>
                          </div>
                        </div>
                      </div>
                    )}
                </div>
              </div>
            )}
            {triggerFormData?.TriggerResponse?.name !==
              "This is a First Subtask" &&
              triggerFormData?.TriggerAction?.ActionName && (
                <div style={{ marginTop: "1rem" }}>
                  <h4>Response Time</h4>
                  <div className={styles.subtask_confirmation_subcard}>
                    <div
                      className={styles.subtask_action_discard_time_interval}
                    >
                      <p
                        style={{
                          color:
                            normalizeNumber(
                              subtaskData?.AutoId?.ResponseTime
                            ) !==
                              normalizeNumber(
                                inputValues?.trigger_event_add_response_time
                              ) && mode == "edit"
                              ? "var(--secondary_color)"
                              : "black",
                        }}
                      >
                        {inputValues?.trigger_event_add_response_time || "time"}
                      </p>
                      <div className={styles.time_unit}>
                        <h4>Hours</h4>
                      </div>
                    </div>
                  </div>
                </div>
              )}
          </div>
        ) : discard ? (
          <div>
            {triggerFormData?.TriggerResponse && (
              <div>
                <h4>Trigger Response</h4>
                <div className={styles.subtask_confirmation_subcard}>
                  <p className="p_tag_14px" style={{ color: "#00000099;" }}>
                    Subtask
                  </p>
                  <p>{triggerFormData?.TriggerResponse?.name}</p>
                </div>
              </div>
            )}
            {triggerFormData?.TriggerAction?.ActionName?.name && (
              <div style={{ marginTop: "1rem" }}>
                <h4>Action</h4>
                <div className={styles.subtask_confirmation_subcard}>
                  <p>{triggerFormData?.TriggerAction?.ActionName?.name}</p>
                  {!isNaN(triggerFormData?.TriggerAction?.ActionTime) &&
                    triggerFormData?.TriggerAction?.ActionTime != 0 && (
                      <div>
                        <p
                          className="p_tag_14px"
                          style={{ marginTop: "1rem", color: "#00000099;" }}
                        >
                          Time Interval
                        </p>
                        <div
                          className={
                            styles.subtask_action_discard_time_interval
                          }
                        >
                          <p>
                            {inputValues?.trigger_event_add_start_after_time}
                          </p>
                          <div className={styles.time_unit}>
                            <h4>Hours</h4>
                          </div>
                        </div>
                      </div>
                    )}
                </div>
              </div>
            )}
            {triggerFormData?.TriggerResponse?.name !==
              "This is a First Subtask" &&
              triggerFormData?.TriggerAction?.ActionName && (
                <div style={{ marginTop: "1rem" }}>
                  <h4>Response Time</h4>
                  <div className={styles.subtask_confirmation_subcard}>
                    <div
                      className={styles.subtask_action_discard_time_interval}
                    >
                      <p>
                        {inputValues?.trigger_event_add_response_time || "time"}
                      </p>
                      <div className={styles.time_unit}>
                        <h4>Hours</h4>
                      </div>
                    </div>
                  </div>
                </div>
              )}
          </div>
        ) : (
          <div className={`${styles.trigger_event_dialog_form_first_main}`}>
            <div className={`${styles.trigger_event_response_div}`}>
              <h4 className={`${styles.trigger_event_keyHeader}`}>
                Trigger Response
              </h4>
              <TEPopupComp
                title="Trigger Response"
                label="Subtask"
                error={errors?.Response ?? ""}
                onSelectCallback={handleTriggerResponseSelect}
                data={
                  Array.isArray(allsubroutes)
                    ? allsubroutes?.length > 0
                      ? allsubroutes
                          ?.filter((item) => item._id !== subtaskData?._id)
                          ?.map((item: any) => {
                            return {
                              id: String(item._id),
                              name: item.name,
                            };
                          })
                      : []
                    : []
                }
                mode={mode}
                selectedItemFromTop={triggerFormData?.TriggerResponse}
              />
            </div>

            {triggerFormData?.TriggerResponse &&
              triggerFormData?.TriggerResponse?.name !==
                "This is a First Subtask" && (
                <div className={`${styles.trigger_event_action_section}`}>
                  <h4 className={`${styles.trigger_event_keyHeader}`}>
                    Action
                  </h4>
                  <div
                    className={`${styles.trigger_event_action_radio_keys} ${
                      isOnlyResponse ? styles.disable_field : ""
                    }`}
                  >
                    {TriggerStructureData?.action.map((item, index) => (
                      <TERadioValComp
                        onRadioSelect={() => {
                          setErrors({ ...errors, Action: false });
                          handleActionSelect({
                            id: item.id,
                            name: item.name,
                          });
                        }}
                        error={errors?.Action ?? false}
                        isChecked={
                          triggerFormData.TriggerAction?.ActionName?.id ===
                          item.id
                        }
                        key={index}
                        id={item.id.toString()}
                        value={item.name}
                      />
                    ))}

                    {triggerFormData?.TriggerResponse &&
                      triggerFormData.TriggerAction?.ActionName &&
                      (triggerFormData.TriggerAction?.ActionName?.id ===
                        "102" ||
                        triggerFormData.TriggerAction?.ActionName?.id ===
                          "104") && (
                        <div>
                          <div
                            className={`${styles.trigger_event_start_after_time}`}
                          >
                            <FloatingLabelInput
                              id={`${"trigger_event_add_start_after_time"}`}
                              label={"Time Interval"}
                              type="number"
                              error={errors?.TimeInterval ?? false}
                              props="trigger_event_input_time"
                              props2="one_line"
                              value={
                                inputValues?.trigger_event_add_start_after_time ??
                                ""
                              }
                              onInputChange={() => {
                                setErrors({ ...errors, TimeInterval: false });
                              }}
                            />
                            <h4 className={`${styles.time_unit}`}>Hours</h4>
                          </div>
                        </div>
                      )}
                    {isOnlyResponse && (
                      <div
                        className={styles.action_time_interval_overlay}
                      ></div>
                    )}
                  </div>
                </div>
              )}

            {triggerFormData?.TriggerResponse &&
              triggerFormData.TriggerAction?.ActionName &&
              (triggerFormData.TriggerAction?.ActionName?.id === "101" ||
              triggerFormData.TriggerAction?.ActionName?.id === "103"
                ? true
                : startAfterAddInputValue) &&
              triggerFormData?.TriggerResponse?.name !==
                "This is a First Subtask" && (
                <div className={`${styles.trigger_event_response_time}`}>
                  <h4 className={`${styles.trigger_event_keyHeader}`}>
                    Response Time
                  </h4>
                  <div className={`${styles.trigger_event_start_after_time}`}>
                    <FloatingLabelInput
                      id={`${"trigger_event_add_response_time"}`}
                      label={"Time"}
                      type="number"
                      props="trigger_event_input"
                      props2="one_line"
                      value={inputValues?.trigger_event_add_response_time ?? ""}
                    />
                    <h4 className={`${styles.time_unit}`}>Hours</h4>
                  </div>
                </div>
              )}
          </div>
        )}
      </div>
      {isSummaryDialogueOpen ? (
        <div className={`${styles.mt_confirmation_button_div}`}>
          <Button
            Callback={() => {
              setisSummaryDialogueOpen(false);
            }}
            type={"Cancel"}
            Content={"Back"}
            property="mtcd_form_button"
          />
          <Button
            Callback={() => {
          handleSubmitt();
            }}
            type={"Approve"}
            Content={"Submit"}
            property="mtcd_form_button"
          />
        </div>
      ) : discard ? (
        <div className={`${styles.mt_confirmation_button_div} `}>
          <Button
            Callback={() => {
              if (discard && wasTrue) {
                setDiscard(false);
                setisSummaryDialogueOpen(true);
                setWasTrue(false);
                return;
              }
              setDiscard(false);
            }}
            type={"Cancel"}
            Content={"No"}
            property="mtcd_form_button"
          />
          <Button
            Callback={() => {
              dispatch(closePopup("TEForm"));
              dispatch(resetInputValues());
              dispatch(setTriggerMode(""));
              dispatch(
                setTriggerFormData({
                  TriggerAction: {
                    ActionName: null,
                    ActionTime: "",
                  },
                  TriggerResponse: null,
                  ResponseTime: "",
                })
              );
              dispatch(setIsOnlyResponse(false));
              dispatch(changeActiveState({ active: true }));
              setisSummaryDialogueOpen(false);
              setDiscard(false);
            }}
            type={"Approve"}
            Content={"Yes"}
            property="mtcd_form_button"
          />
        </div>
      ) : (
        <div className={`${styles.mt_confirmation_button_div} `}>
          <Button
            Callback={() => {
              handleClose();
            }}
            type={button1Type || "Normal"}
            Content={button1Content || ""}
            property="mtcd_form_button"
          />
          <Button
            Callback={() => {
              if (
                !triggerFormData?.TriggerResponse?._id &&
                triggerFormData?.TriggerResponse?.name !=
                  "This is a First Subtask"
              ) {
                setErrors({ Response: true });
                showToast({
                  messageContent: "Trigger Response is required.",
                  type: "warning",
                });
                return;
              }

              if (
                !triggerFormData?.TriggerAction?.ActionName &&
                triggerFormData?.TriggerResponse?.name !=
                  "This is a First Subtask"
              ) {
                setErrors({ ...errors, Action: true });
                showToast({
                  messageContent: "Action is required.",
                  type: "warning",
                });
                return;
              }

              if (
                !inputValues?.trigger_event_add_start_after_time &&
                (triggerFormData?.TriggerAction?.ActionName?.id === "102" ||
                  triggerFormData?.TriggerAction?.ActionName?.id === "104")
              ) {
                setErrors({ ...errors, TimeInterval: true });
                showToast({
                  messageContent: "Time Interval is required.",
                  type: "warning",
                });
                return;
              }

              setErrors({
                Response: false,
                TimeInterval: false,
                Action: false,
              });
              setisSummaryDialogueOpen(true);
            }}
            type={button2Type || "Normal"}
            Content={button2Content || ""}
            property="mtcd_form_button"
          />
        </div>
      )}
    </div>
  );
};
export default TEFormComponent;
