// Author Name Charvi And Aayush Changes by rattan jagroop and aayush
import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
  useAddSubTasksByIdMutation,
  useDeleteSubTaskMutation,
  useLazyGetAllCategoryByTableNameQuery,
  useLazyGetsubTaskDetailsLazyQuery,
  useUpdateTaskByIdMutation,
} from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import { useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { GetTaskBuildingBlocks } from "../../../../../../redux/hooks/Modules/TaskMaster/TaskMasterHook";
import { RootState, store } from "../../../../../../redux/store";
import {
  currentSubtaskData,
  settaskChangeAPiFlag,
  updateTaskData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import { resetInputValues } from "../../../../../../redux/features/Modules/Reusble/floatinglabelslice";
import {
  closePopup,
  openPopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import { setToast } from "../../../../../../redux/features/Modules/Reusble/ToastSlice";

import AddToolTip from "../../../../../../components/Reusble/Global/AddToolTip";
import AddCategoryType from "../../../../../../components/Reusble/Global/AddCategoryType";
import {
  SuryconLogo,
  WieghtPercentageIcon,
} from "../../../../../../assets/icons";
import AddSubTasksPopup from "../../../../../../components/Reusble/TaskMaster/AddSubTasksPopup";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import styles from "../../Styles/TaskCreationForm.module.css";
import {
  AddDataMaster,
  requiredthings,
  subtaskdetails,
  taskBuildingBlocks,
  TaskDataType,
} from "../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import { saveSyncData } from "../../../../../../Backup/BackupFunctions/BackupFunctions";
import { useAppSelector } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { TaskForm } from "../../../Tasks/Subcomponents/AddTaskForm";
import { initializeDatabase } from "../../../../../../functions/functions";
import { Designations } from "../../../../../../Backup/MastersBackup";
import { useToast } from "../../../../../../hooks/ToastHook";

// import { debounce } from "../../../../functions/functions";
// import { setBackupChange } from "../../../../redux/features/commanFeatures/backupSlice";
// import DiscardPopup from "../../../../resublecomponents/DiscardPopup";

// import { debounce} from "../../../../functions/functions";

const TaskCreationMasters: React.FC = ({ isEdit = false }) => {
  const [updateTaskById, { isLoading }] = useUpdateTaskByIdMutation();
  const showToast = useToast();
  const [selectedOption, setSelectedOption] = useState("");
  const [selectedOptionApidata, setSelectedOptionApidata] = useState([]);
  const [allsubtaskdetails, setsubtaskdetails] = useState<subtaskdetails>({
    name: "",
    Description: "",
    Unit: "",
    subtaskWeighatages: "",
    Tracking: "",
  });
  const [getDataForCategoires] = useLazyGetAllCategoryByTableNameQuery();
  const [disableButton, setDisableButton] = useState<boolean>(false);
  const [deleteSubtask] = useDeleteSubTaskMutation();
  const { taskId } = useParams<{ taskId: string }>();
  const dispatch = useDispatch();
  const { popups } = useSelector((state: RootState) => state.popup);
  // const { data, refetch } = GetTaskBuildingBlocks();
  const [primaryLabelForAddCategoryType, SetprimaryLabelForAddCategoryType] =
    useState("");
  const [addSubTask] = useAddSubTasksByIdMutation();
  const TaskData = useSelector(
    (state: RootState) =>
      state.taskForm.currentSubtaskData || {
        _id: taskId,
        name: "",
        Unit: "",
        Description: "",
        subtaskWeighatages: 0,
        Tracking: "",
        MaterialId: [],
        ToolId: [],
        MachinaryId: [],
        ManpowerId: [],
        AdminId: [],
        AssigneeId: [],
        Reporter: [],
        Subtaskdetails: [],
        MethodId: {
          work_instruction_id: [],
          task_closing_requirement: [],
          Controlplan: [],
          Failuremode: [],
        },
      }
  );

  //transforming task data
  const transformTaskData = (task: any) => {
    const extractIds = (data: any) => data?.map((item: any) => item._id) || [];
    const excludeIds = (data: any[]) => data.map(({ _id, ...rest }) => rest);

    const transformWorkInstructions = (workInstructions: any[]) => {
      console.log(workInstructions, "check for work instructionse");
      return workInstructions.map((item: any) => {
        const isTimestampId = /^\d{13}$/.test(String(item._id)); // Check if _id is a timestamp

        return {
          ...(item._id && !isTimestampId && { _id: item._id }), // Include _id only if it's not a timestamp
          Description: item.Description || "",
          optionselected: item.optionselected?.toLowerCase() || "",
          departmentId: extractIds(item.departmentId),
          designationId: extractIds(item.designationId),
          materialId: extractIds(item.materialId),
          manpowerId: extractIds(item.manpowerId),
          toolsId: extractIds(item.toolsId),
          machinaryId: [...extractIds(item.machinaryId)],
          photoRef:
            (item.photoref?.photos?.length > 0 &&
              item.photoref.photos?.map((photo: any) => ({
                photos: photo?.photo,
                Decription: photo?.details,
              }))) ||
            [],
        };
      });
    };
    const transformTaskClosing = (taskClosing: any[]) => {
      return taskClosing.map((item: any) => ({
        Description: item.Description,
        optionselected: item.optionselected?.toLowerCase() || "",
        photoRef:
          (item.photoref?.photos?.length > 0 &&
            item.photoref.photos?.map((photo: any) => ({
              photos: photo?.photo,
              Decription: photo?.details,
            }))) ||
          [],
      }));
    };

    const transformReporters = (Reporter: any[]) => {
      return Reporter.map((item: any) => {
        const isTimestampId = /^\d{13}$/.test(String(item._id));

        return {
          ...(item._id && !isTimestampId && { _id: item._id }),
          Level: item?.Level,
          designationId: Array.isArray(item?.designationId)
            ? extractIds(item.designationId)
            : item.designationId,
        };
      });
    };

    return {
      _id: task._id,
      name: task.name,
      Unit: task.Unit,
      Description: task.Description,
      Quantity: task.subtaskWeighatages,
      DepartmentId: extractIds(task.DepartmentId),
      DesignationId: extractIds(task.DesignationId),
      MaterialId: extractIds(task.MaterialId),
      ToolId: extractIds(task.ToolId),
      MachinaryId: extractIds(task.MachinaryId),
      ManpowerId: extractIds(task.ManpowerId),
      Adminid: extractIds(task.Adminid),
      Reporters: transformReporters(task.ReporterId?.Reporter || []),
      AssigneeId: extractIds(task.AssigneeId) || [],
      WorkInstructions: transformWorkInstructions(
        task.MethodId?.work_instruction_id || []
      ),
      TaskClosing: transformTaskClosing(
        task.MethodId?.task_closing_requirement || []
      ),
      ControlPlan: excludeIds(task?.MethodId?.Controlplan || []),
      Failuremode: excludeIds(task?.MethodId?.Failuremode || []),
      Tobedeleted: {
        workinstruction: task?.Tobedeleted?.workinstruction || [],
      },
    };
  };

  console.log(TaskData, "check for task data here>>>>>>>");

  const taskworkinstructiondelete = useAppSelector(
    (state) => state.taskworkinstrucitonReducer.taskworkinstruction
  );
  const fetchSubtasks = async () => {
    const dbName = await initializeDatabase("SubTaskForm");

    let response = [];
    if (typeof window.electron.getSubtasksByTaskId === "function") {
      response = await window.electron.getSubtasksByTaskId({
        dbName,
        taskId,
        subtaskId: "",
      });
    } else {
      console.error("window.electron.getSubtasksByTaskId is not a function");
    }
    return response || [];
  };
  const updateTaskApi = async (data: any) => {
    const taskkdata = store.getState().taskForm.currentSubtaskData;
    const transformedData = transformTaskData(taskkdata);
    // const uniqueArray = (arr: string) => [...new Set(arr)];

    const response = await updateTaskById({
      taskId: taskId!,
      name: data.name,
      Unit: data.Unit,
      Description: data.Description,
      Quantity: data.Quantity,
      departmentId: transformedData.DepartmentId,
      designationId: transformedData.DesignationId,
      machinaryId: transformedData.MachinaryId,
      toolId: transformedData.ToolId,
      manpowerId: transformedData.ManpowerId,
      materialId: transformedData.MaterialId,
      Adminid: transformedData.Adminid,
      AssigneeId: transformedData.AssigneeId,
      Reporters: transformedData.Reporters,
      WorkInstructions: transformedData.WorkInstructions,
      TaskClosing: transformedData.TaskClosing,
      Failuremode: transformedData.Failuremode,
      ControlPlan: transformedData.ControlPlan,
      Tobedeleted: {
        workinstruction: transformedData?.Tobedeleted?.workinstruction,
      },
    }).unwrap();

    dispatch(settaskChangeAPiFlag(false));
    return response;
  };

  // added subtask using api by charvi
  const handleSubtaskSubmit = async (subtaskData: subtaskdetails) => {
    try {
      const credentials = { ...subtaskData, taskId };

      await updateTaskApi({
        name: TaskData.name,
        Unit: TaskData.Unit,
        Description: TaskData.Description,
        Quantity: TaskData.subtaskWeighatages,
      });

      dispatch(settaskChangeAPiFlag(false));

      const res = await addSubTask(credentials).unwrap();

      dispatch(
        setToast({
          isOpen: true,
          messageContent:
            res?.data?.data?.message || "Subtask Added Successfully",
          type: "success",
        })
      );

      setsubtaskdetails({
        name: "",
        Description: "",
        Unit: "",
        subtaskWeighatages: "",
        Tracking: "",
      });
    } catch (error) {
      setsubtaskdetails({
        name: "",
        Description: "",
        Unit: "",
        subtaskWeighatages: "",
        Tracking: "",
      });
      dispatch(
        setToast({
          isOpen: true,
          messageContent: error.data.message,
          type: "error",
        })
      );
      throw error;
    }
  };

  //

  //
  useEffect(() => {}, [TaskData]);

  // funtion to call the api to fetch category names according to the selected master ===material,manpower  etc
  const getCategories = async (tablename: string) => {
    console.log("fetheced category called");

    let response;
    let dbName;
    switch (tablename) {
      case "MaterialCategory":
        dbName = await initializeDatabase("MaterialCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "Manpowercategory":
        dbName = await initializeDatabase("ManpowerCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "ToolCategory":
        dbName = await initializeDatabase("ToolCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "machinaryCategory":
        dbName = await initializeDatabase("MachinaryCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "departmentdetails":
        dbName = await initializeDatabase("Departments");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "designationdetails":
        dbName = await initializeDatabase("MaterialCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      default:
        console.log("Invalid table name");
        response = null;
    }
    console.log(response, "this is response for selecte option");
    return (
      response?.map((e: { _id: string; name: string }) => ({
        id: e._id,
        category: e.name,
      })) || []
    );
  };
  // useEffect(() => {
  //   const fetchCategories = async () => {
  //     const data = await getCategories(selectedOption);
  //     setSelectedOptionApidata(data);
  //     console.log("selected option api data", data);
  //   };
  //   fetchCategories();
  // }, [selectedOption]);

  const handleSelect = useCallback(
    async (
      categoryName: keyof AddDataMaster,
      selectedItems: TaskDataType[],
      label?: string
    ) => {
      const newselectedItems = selectedItems.map((item) => ({
        _id: item.id,
        name: item.category,
      }));

      if (selectedItems.length > 0) {
        console.log(TaskData, "here is bro bro ");
        saveSyncData(
          {
            ...TaskData,
            [categoryName]: [
              ...(TaskData[categoryName] || []),
              ...newselectedItems,
            ],
          },
          "time",
          "TaskForm",
          true
        );

        dispatch(
          updateTaskData({
            ...TaskData,
            [categoryName]: [
              ...(TaskData[categoryName] || []),
              ...newselectedItems,
            ],
          })
        );

        saveSyncData(
          {
            ...TaskData,
            [categoryName]: [
              ...(TaskData[categoryName] || []),
              ...newselectedItems,
            ],
          },
          "time",
          "TaskForm"
        );

        dispatch(settaskChangeAPiFlag(true));

        dispatch(
          setToast({
            isOpen: true,
            messageContent: `${
              selectedItems.length > 1 ? label : selectedItems[0]?.category
            } added Successfully!`,
            type: "success",
          })
        );
      }
    },
    [dispatch, TaskData]
  );
  const [currentSelectedSubTaskData, setcurrentSelectedSubTaskData] = useState(
    {}
  );
  // const [getSubTaskDetail] = useLazyGetsubTaskDetailsLazyQuery();

  const handleFetchSubtaskData = async (subTaskId: any) => {
    // const subtaskResponse = await getSubTaskDetail(subTaskId);
    const subtask = TaskData?.Subtaskdetails?.find(
      (item: any) => item?._id === subTaskId
    );
    setcurrentSelectedSubTaskData(subtask!);
    // console.log(subtaskResponse?.data?.data?.response, "preetsire");
  };

  const handleToggleDropdown = async (name: string, modelname?: string) => {
    if (modelname) {
      const data = await getCategories(modelname);
      setSelectedOptionApidata(data);
      (() => {
        switch (modelname) {
          case "MaterialCategory":
            SetprimaryLabelForAddCategoryType("Add Materials Category");
            return;
          case "machinaryCategory":
            SetprimaryLabelForAddCategoryType("Add Machinery Category");
            return;
          case "ToolCategory":
            SetprimaryLabelForAddCategoryType("Add Tool Category");
            return;
          case "Manpowercategory":
            SetprimaryLabelForAddCategoryType("Add Manpower Category");
            return;
          case "departmentdetails":
            SetprimaryLabelForAddCategoryType("Add Department");
            return;
          default:
            return <>Unknown </>;
        }
      })();
    }

    dispatch(openPopup(name));
  };
  const [requiredThingsDelete, setRequiredThingsDelete] =
    useState<requiredthings>();
  const [requiredThingsDeleteName, setRequiredThingsDeleteName] =
    useState<string>();
  const [deleteId, setDeleteId] = useState<string>();

  const handleeditClose = () => {
    dispatch(resetInputValues());
    dispatch(closePopup("TaskEdit"));
  };

  return (
    <div className={styles.task_creation_master_container}>
      <div
        style={{ display: "grid", gridTemplateColumns: "3fr 1fr", gap: "1rem" }}
      >
        <div className={styles.task_creation_master_row_master}>
          <div>
            {/* Deparment Section here */}
            <div style={{ minWidth: "50px", position: "relative" }}>
              <AddToolTip
                showAddIcon={false}
                label="Departments"
                onClick={() => {
                  handleToggleDropdown("Department", "departmentdetails");
                  setSelectedOption("departmentdetails");
                }}
                className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
                data={TaskData.DepartmentId}
                isEdit={isEdit}
                // handleDelete={(item) => {
                //   setDeleteId("DepartmentId");
                //   setRequiredThingsDelete(item);
                //   setRequiredThingsDeleteName("Department");
                //   dispatch(openPopup("DeleteRequiredThings"));
                // }}
              />
              {popups["Department"] && (
                <AddCategoryType
                  modelname={selectedOption}
                  // primaryLabel={primaryLabelForAddCategoryType}
                  title="Add Departments"
                  data={selectedOptionApidata as TaskDataType[]}
                  initialSelected={TaskData?.DepartmentId}
                  label="Department"
                  placeholder="Search"
                  buttonLabel="Add Category"
                  onSelect={(item) =>
                    handleSelect("DepartmentId", item, "Department")
                  }
                  onClose={() => dispatch(closePopup("Department"))}
                />
              )}
            </div>

            {/* Manpower Section */}
            <div style={{ minWidth: "50px", position: "relative" }}>
              <AddToolTip
                label="Manpower"
                onClick={() => {
                  handleToggleDropdown("Manpower", "Manpowercategory");
                  setSelectedOption("Manpowercategory");
                }}
                className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
                data={TaskData.ManpowerId}
                isEdit={isEdit}
                showAddIcon={false}
                // handleDelete={(item) => {
                //   setDeleteId("ManpowerId");
                //   setRequiredThingsDelete(item);
                //   setRequiredThingsDeleteName("Manpower");
                //   dispatch(openPopup("DeleteRequiredThings"));
                // }}
              />
              {popups["Manpower"] && (
                <AddCategoryType
                  modelname={selectedOption}
                  isStepForm={true}
                  primaryLabel={primaryLabelForAddCategoryType}
                  singleSelected={true}
                  title="Add Manpower"
                  data={selectedOptionApidata as TaskDataType[]}
                  initialSelected={TaskData?.ManpowerId}
                  label="Manpower"
                  placeholder="Search"
                  buttonLabel="Add Category"
                  onSelect={(item) =>
                    handleSelect("ManpowerId", item, "Manpower")
                  }
                  onClose={() => dispatch(closePopup("Manpower"))}
                />
              )}
            </div>

            {/* Tools Section */}
            <div style={{ minWidth: "50px", position: "relative" }}>
              <AddToolTip
                showAddIcon={false}
                label="Tools"
                onClick={() => {
                  handleToggleDropdown("Tools", "ToolCategory");
                  setSelectedOption("ToolCategory");
                }}
                className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
                data={TaskData?.ToolId}
                isEdit={isEdit}
                // handleDelete={(item) => {
                //   setDeleteId("ToolId");
                //   setRequiredThingsDelete(item);
                //   setRequiredThingsDeleteName("Tools");
                //   dispatch(openPopup("DeleteRequiredThings"));
                // }}
              />
              {popups["Tools"] && (
                <AddCategoryType
                  primaryLabel={primaryLabelForAddCategoryType}
                  modelname={selectedOption}
                  isStepForm={true}
                  singleSelected={true}
                  title="Add Tools"
                  label="Tool"
                  data={selectedOptionApidata as TaskDataType[]}
                  placeholder="Search"
                  initialSelected={TaskData?.ToolId}
                  buttonLabel="Add Category"
                  onSelect={(item) => handleSelect("ToolId", item, "Tools")}
                  onClose={() => dispatch(closePopup("Tools"))}
                />
              )}
              {popups["DeleteRequiredThings"] && (
                <DeletePopup
                  width="23rem"
                  height="calc(100% - 9rem)"
                  heightupperlimit="0rem"
                  header={`Are you sure you want to delete this ${requiredThingsDeleteName} ?`}
                  callbackDelete={async () => {
                    if (deleteId) {
                      const newRequiredThings = (
                        TaskData[deleteId as keyof AddData] as {
                          _id: string;
                          name: string;
                        }[]
                      )?.filter(
                        (item) => item._id !== requiredThingsDelete?._id
                      );

                      dispatch(
                        updateTaskData({
                          ...TaskData,
                          [deleteId]: newRequiredThings,
                        })
                      );

                      saveSyncData(
                        {
                          ...TaskData,
                          [deleteId]: newRequiredThings,
                        },
                        "time",
                        "TaskForm"
                      );

                      dispatch(settaskChangeAPiFlag(true));

                      dispatch(
                        setToast({
                          isOpen: true,
                          messageContent: `${requiredThingsDelete?.name} deleted Successfully!`,
                          type: "success",
                        })
                      );
                    }
                    dispatch(closePopup("DeleteRequiredThings"));
                  }}
                  onClose={() => {
                    dispatch(closePopup("DeleteRequiredThings"));
                  }}
                >
                  <div className={styles.summaryDivData}>
                    <div className={styles.summaryDataContent}>
                      <p
                        style={{ color: "var(--text-black-60)" }}
                        className="p_tag_14px"
                      >
                        {requiredThingsDeleteName}
                      </p>
                      <div
                        className=""
                        style={{
                          display: "flex",
                          gap: "1rem",
                          flexWrap: "wrap",
                        }}
                      >
                        <h4 style={{ color: "var(--text-black-87)" }}>
                          {requiredThingsDelete?.name}
                        </h4>
                      </div>
                    </div>
                  </div>
                </DeletePopup>
              )}
              {popups["DeleteSubtask"] && (
                <DeletePopup
                  header="Are you sure you want to delete this Subtask?"
                  height="calc(100% - 7.25rem)"
                  heightupperlimit="0"
                  callbackDelete={
                    disableButton
                      ? () => {}
                      : async () => {
                          // await updateTaskApi({
                          //   name: TaskData.name,
                          //   Unit: TaskData.Unit,
                          //   Description: TaskData.Description,
                          //   Quantity: TaskData.subtaskWeighatages,
                          // });
                          setDisableButton(true);
                          const response = await deleteSubtask({
                            subTaskId: currentSelectedSubTaskData?._id || "",
                            taskId: TaskData?._id!,
                          });

                          console.log(
                            response,
                            "this is response fo deleted subtask"
                          );
                          if (response?.data?.success) {
                            const getallsubtasks = await fetchSubtasks();
                            console.log(
                              getallsubtasks,
                              "these are all subtask for this dasdf"
                            );
                            const currentSubtask = getallsubtasks?.find(
                              (item: any) =>
                                item?._id === currentSelectedSubTaskData?._id
                            );
                            console.log(
                              currentSubtask,
                              "this current subtask checkout ahsdfsadf"
                            );
                            const ItemsToDelete = [
                              ...(currentSubtask?.ManpowerId || []),
                              ...(currentSubtask?.ToolId || []),
                              ...(currentSubtask?.MachinaryId || []),
                              ...(currentSubtask?.MaterialId || []),
                              ...(currentSubtask?.AdminId || []),
                              ...(currentSubtask?.AssigneeId || []),
                              ...(currentSubtask?.ReporterId?.Reporter?.flatMap(
                                (e: any) =>
                                  Array.isArray(e.designationId)
                                    ? [...e.designationId]
                                    : []
                              ) || []),
                            ];
                            console.log(
                              ItemsToDelete,
                              "these are items to delete"
                            );
                            // Remove the current subtask from allsubtasks
                            const allsubtasks = getallsubtasks?.filter(
                              (item: any) =>
                                item?._id !== currentSelectedSubTaskData?._id
                            );
                            const allManpowers = allsubtasks?.map(
                              (item: any) => item?.ManpowerId || []
                            );
                            console.log(
                              allManpowers,
                              "these are all manpower for this subtask"
                            );
                            const allTools = allsubtasks?.map(
                              (item: any) => item?.ToolId || []
                            );

                            console.log(
                              allTools,
                              "these are all tools for this subtask"
                            );
                            const allMachinaries = allsubtasks?.map(
                              (item: any) => item?.MachinaryId || []
                            );
                            console.log(
                              allMachinaries,
                              "these are all machinaries for this subtask"
                            );
                            const allMaterials = allsubtasks?.map(
                              (item: any) => item?.MaterialId || []
                            );
                            console.log(
                              allMaterials,
                              "these are all materials for this subtask"
                            );
                            const Taskmanager = allsubtasks?.map(
                              (item: any) => item?.AdminId || []
                            );
                            console.log(
                              Taskmanager,
                              "these are all departments for this subtask"
                            );
                            const allAssignes = allsubtasks?.map(
                              (item: any) => item?.AssigneeId || []
                            );
                            // console.log(
                            //   allAssignes,
                            //   "these are all designations for this subtask"
                            // );
                            const allReporters = allsubtasks?.flatMap(
                              (item: any) =>
                                item?.ReporterId?.Reporter?.flatMap((e: any) =>
                                  Array.isArray(e.designationId)
                                    ? [...e.designationId]
                                    : []
                                ) || []
                            );
                            const allData = [
                              ...allManpowers.flat(),
                              ...allTools.flat(),
                              ...allMachinaries.flat(),
                              ...allMaterials.flat(),
                              ...Taskmanager.flat(),
                              ...allAssignes.flat(),
                              ...allReporters.flat(),
                            ];
                            console.log(allData, "this is all data to delete");
                            // ItemsToDelete is an array of objects, allData is an array of objects
                            // We want to filter allData to exclude objects that are present in ItemsToDelete (by _id)
                            console.log(
                              ItemsToDelete,
                              "checkou there is items to delte "
                            );
                            const allDeparmentIds = allData?.map(
                              (e) => e?.DepartmentId
                            );
                            const departmentIdsToDelete = ItemsToDelete.map(
                              (e) => e?.DepartmentId
                            );
                            const finalDeparmentsToDelete =
                              departmentIdsToDelete?.filter(
                                (id: any) => !allDeparmentIds.includes(id)
                              );
                            console.log(
                              allDeparmentIds,
                              finalDeparmentsToDelete,
                              "These are final deparrtments to der"
                            );
                            const itemsToDeleteIds = ItemsToDelete.map(
                              (item: any) => item._id
                            );
                            // Get all _id values from allData
                            const allDataIds = allData.map(
                              (obj: any) => obj._id
                            );
                            const finalItemsToDelete = itemsToDeleteIds?.filter(
                              (id: any) => !allDataIds.includes(id)
                            );
                            console.log(
                              allData,
                              finalItemsToDelete,
                              TaskData,
                              "these are final items to delete"
                            );

                            dispatch(
                              updateTaskData({
                                ...TaskData,
                                ManpowerId: TaskData?.ManpowerId?.filter(
                                  (e: any) =>
                                    !finalItemsToDelete.includes(e._id)
                                ),
                                ToolId: TaskData?.ToolId?.filter(
                                  (e: any) =>
                                    !finalItemsToDelete.includes(e._id)
                                ),
                                MachinaryId: TaskData?.MachinaryId?.filter(
                                  (e: any) =>
                                    !finalItemsToDelete.includes(e._id)
                                ),
                                MaterialId: TaskData?.MaterialId?.filter(
                                  (e: any) =>
                                    !finalItemsToDelete.includes(e._id)
                                ),
                                DesignationId: TaskData?.DesignationId?.filter(
                                  (e: any) =>
                                    !finalItemsToDelete.includes(e._id)
                                ),
                                DepartmentId: TaskData?.DepartmentId?.filter(
                                  (e: any) =>
                                    !finalDeparmentsToDelete.includes(e?._id)
                                ),
                                Subtaskdetails:
                                  TaskData?.Subtaskdetails?.filter(
                                    (e) =>
                                      e?._id !== currentSelectedSubTaskData?._id
                                  ),
                              } as any)
                            );

                            await saveSyncData(
                              {
                                ...TaskData,
                                ManpowerId: TaskData?.ManpowerId?.filter(
                                  (e: any) =>
                                    !finalItemsToDelete.includes(e._id)
                                ),
                                ToolId: TaskData?.ToolId?.filter(
                                  (e: any) =>
                                    !finalItemsToDelete.includes(e._id)
                                ),
                                MachinaryId: TaskData?.MachinaryId?.filter(
                                  (e: any) =>
                                    !finalItemsToDelete.includes(e._id)
                                ),
                                MaterialId: TaskData?.MaterialId?.filter(
                                  (e: any) =>
                                    !finalItemsToDelete.includes(e._id)
                                ),
                                DesignationId: TaskData?.DesignationId?.filter(
                                  (e: any) =>
                                    !finalItemsToDelete.includes(e._id)
                                ),
                                Subtaskdetails:
                                  TaskData?.Subtaskdetails?.filter(
                                    (e) =>
                                      e?._id !== currentSelectedSubTaskData?._id
                                  ),
                              },
                              "time",
                              "TaskForm"
                            );

                            await updateTaskApi({
                              name: TaskData.name,
                              Unit: TaskData.Unit,
                              Description: TaskData.Description,
                              Quantity: TaskData.subtaskWeighatages,
                            });

                            showToast({
                              messageContent: "Subtask Deleted Successfully",
                              type: "success",
                            });
                          } else {
                            showToast({
                              messageContent: "Oops! Something went wrong",
                              type: "danger",
                            });
                          }
                          // dispatch();
                          // dispatch(
                          //   setcurrentTaskData({
                          //     _id: "",
                          //     name: "",
                          //     Unit: "",
                          //     Description: "",
                          //     subtaskWeighatages: 0,
                          //     Tracking: "",
                          //     DepartmentId: [],
                          //     DesignationId: [],
                          //     MaterialId: [],
                          //     ToolId: [],
                          //     MachinaryId: [],
                          //     ManpowerId: [],
                          //     Adminid: [],
                          //     TaskmasterId: {},
                          //     ReporterId: {
                          //       Reporter: [],
                          //     },
                          //     AssigneeId: [],
                          //     Subtaskdetails: [],
                          //     MethodId: {
                          //       work_instruction_id: [],
                          //       task_closing_requirement: [],
                          //       Controlplan: [],
                          //     },
                          //   })
                          // );
                          // await deleteSubtask({
                          //   subTaskId: SutbaskData?._id || "",
                          //   taskId: SutbaskData?.TaskmasterId?._id,
                          // });
                          // navigate(`/category/${params.catId}/task/${params.taskId}`);
                          dispatch(closePopup("DeleteSubtask"));
                          setDisableButton(false);
                        }
                  }
                  onClose={() => {
                    dispatch(closePopup("DeleteSubtask"));
                  }}
                >
                  <DeleteTaskBody data={currentSelectedSubTaskData || {}} />
                </DeletePopup>
              )}
            </div>
          </div>

          <div>
            {/* Designation Section */}
            <div style={{ minWidth: "50px", position: "relative" }}>
              <AddToolTip
                showAddIcon={false}
                label="Designations"
                onClick={() => {
                  handleToggleDropdown("Designation", "departmentdetails");
                  setSelectedOption("departmentdetails");
                }}
                className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
                data={TaskData.DesignationId}
                isEdit={isEdit}
                // handleDelete={(item) => {
                //   setDeleteId("DesignationId");
                //   setRequiredThingsDelete(item);
                //   setRequiredThingsDeleteName("Designation");
                //   dispatch(openPopup("DeleteRequiredThings"));
                // }}
              />
              {popups["Designation"] && (
                <AddCategoryType
                  primaryLabel={"Add Department"}
                  singleSelected={true}
                  modelname={"designationdetails"}
                  isStepForm={true}
                  title="Add Designations"
                  data={selectedOptionApidata as TaskDataType[]}
                  initialSelected={TaskData?.DesignationId}
                  label="Designation"
                  placeholder="Search"
                  buttonLabel="Add Category"
                  onSelect={(item) =>
                    handleSelect("DesignationId", item, "Designation")
                  }
                  onClose={() => dispatch(closePopup("Designation"))}
                />
              )}
            </div>

            {/* Machinery Section */}
            <div style={{ minWidth: "50px", position: "relative" }}>
              <AddToolTip
                showAddIcon={false}
                label="Machinery"
                onClick={() => {
                  handleToggleDropdown("Machinery", "machinaryCategory");
                  setSelectedOption("machinaryCategory");
                }}
                className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
                data={TaskData?.MachinaryId}
                isEdit={isEdit}
                // handleDelete={(item) => {
                //   setDeleteId("MachinaryId");
                //   setRequiredThingsDelete(item);
                //   setRequiredThingsDeleteName("Machine");
                //   dispatch(openPopup("DeleteRequiredThings"));
                // }}
              />
              {popups["Machinery"] && (
                <AddCategoryType
                  primaryLabel={primaryLabelForAddCategoryType}
                  modelname={selectedOption}
                  isStepForm={true}
                  title="Add Machinery"
                  label="Machinery"
                  data={selectedOptionApidata as TaskDataType[]}
                  placeholder="Search"
                  buttonLabel="Add Category"
                  initialSelected={TaskData?.MachinaryId}
                  onSelect={(item) =>
                    handleSelect("MachinaryId", item, "Machinery")
                  }
                  onClose={() => dispatch(closePopup("Machinery"))}
                />
              )}
            </div>

            {/* Materials Section */}
            <div style={{ minWidth: "50px", position: "relative" }}>
              <AddToolTip
                showAddIcon={false}
                label="Materials"
                onClick={() => {
                  handleToggleDropdown("Material", "MaterialCategory");
                  setSelectedOption("MaterialCategory");
                }}
                className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
                data={TaskData?.MaterialId}
                isEdit={isEdit}
                // handleDelete={(item) => {
                //   setDeleteId("MaterialId");
                //   setRequiredThingsDelete(item);
                //   setRequiredThingsDeleteName("Material");
                //   dispatch(openPopup("DeleteRequiredThings"));
                //   console.log(popups, "check for popupsasdf here");
                // }}
              />
              {popups["Material"] && (
                <AddCategoryType
                  primaryLabel={primaryLabelForAddCategoryType}
                  modelname={selectedOption}
                  isStepForm={true}
                  singleSelected={true}
                  title="Add Materials"
                  initialSelected={TaskData?.MaterialId}
                  label="Material"
                  data={selectedOptionApidata as any}
                  placeholder="Search"
                  buttonLabel="Add Category"
                  onSelect={(item) =>
                    handleSelect("MaterialId", item, "Materials")
                  }
                  onClose={() => dispatch(closePopup("Material"))}
                />
              )}
              {popups["TaskEdit"] && (
                <TaskForm
                  mode="edit"
                  updateApi={updateTaskApi}
                  isHeader={true}
                  isTaskPage={true}
                  isEdit={true}
                  onClose={handleeditClose}
                />
              )}
            </div>
          </div>
        </div>

        <div className={styles.task_creation_master_row_subtask}>
          {/* subtask Section */}
          <div style={{ minWidth: "50px", position: "relative" }}>
            <AddToolTip
              icon={<SuryconLogo />}
              label="Subtasks"
              additionalClass="nobreak_class_tooltip"
              onClick={() => {
                const MethodId = TaskData?.MethodId;

                const isArrayEmpty = (arr: any) =>
                  Array.isArray(arr) && arr.length === 0;

                // Check if any of the arrays are empty
                const areArraysEmpty = () => {
                  return (
                    isArrayEmpty(MethodId?.work_instruction_id ?? []) &&
                    isArrayEmpty(MethodId?.task_closing_requirement ?? []) &&
                    isArrayEmpty(MethodId?.Failuremode ?? []) &&
                    isArrayEmpty(MethodId?.Controlplan ?? []) &&
                    isArrayEmpty(TaskData?.AssigneeId ?? []) &&
                    isArrayEmpty(TaskData?.ReporterId?.Reporter ?? []) &&
                    isArrayEmpty(TaskData?.Adminid ?? [])
                  );
                };

                if (!areArraysEmpty()) {
                  dispatch(
                    setToast({
                      isOpen: true,
                      messageContent:
                        "Please Delete the Method And Task Allocation Data!!",
                      type: "warning",
                    })
                  );
                  return;
                }
                handleToggleDropdown("Subtasks");
              }}
              className={`${styles.task_creation_add_tooltip} ${styles.three_cols}`}
              data={TaskData?.Subtaskdetails}
              isEdit={isEdit}
              handleDelete={(item) => {
                handleFetchSubtaskData(item?._id ?? "");
                dispatch(openPopup("DeleteSubtask"));
              }}
            />
            {popups["Subtasks"] && (
              <AddSubTasksPopup
                isEdit={false}
                onClose={() => dispatch(closePopup("Subtasks"))}
                onSubmit={handleSubtaskSubmit}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
interface DeleteTaskBodyProps {
  data: currentSubtaskData;
}
const DeleteTaskBody: React.FC<DeleteTaskBodyProps> = ({ data }) => {
  return (
    <div>
      <div className={styles.flexContainer}>
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Name
            </p>
            <h4 style={{ color: "var(--text-black-87)" }}>
              {data?.name || "No name provided"}
            </h4>
          </div>
        </div>
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Unit
            </p>
            <h4 style={{ color: "var(--text-black-87)" }}>
              {data?.Unit || "No unit"}
            </h4>
          </div>
        </div>
      </div>
      {data?.Description && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Description
            </p>
            <h4 style={{ color: "var(--text-black-87)" }}>
              {data?.Description || "No description"}
            </h4>
          </div>
        </div>
      )}
      <div className={styles.flexContainer}>
        {data?.subtaskWeighatages && (
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent_weightage}>
              <div>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Weightage
                </p>
                <h4 style={{ color: "var(--text-black-87)" }}>
                  {data?.subtaskWeighatages || "No weightage"}
                </h4>
              </div>
              <WieghtPercentageIcon />
            </div>
          </div>
        )}

        {data?.Tracking && (
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Tracking
              </p>
              <h4
                style={{
                  color: "var(--text-black-87)",
                  textTransform: "capitalize",
                }}
              >
                {data?.Tracking || "No tracking"}
              </h4>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
export default TaskCreationMasters;
