import { isDeleted } from "./../../redux/Interfaces/Modules/Reuseable/Reuseable.d";

import { editDeleteMap, initializeDatabase } from "../../functions/functions";
import { setBackupChange } from "../../redux/features/Modules/Reusble/backupSlice";

import { AppDispatch } from "../../redux/store";
import PQueue from "p-queue";
import FuncionMap from "../FunctionsMap/FunctionsMap";
import {
  setNewProject,
  setNewLocation,
  SetProjects,
} from "../../redux/features/Modules/Billing/ProjectPlanning/Slices/ProjectSlice";
import {
  addOrUpdateFetchedMasters,
  addOrUpdateFetchedMastersCategories,
  addOrUpdateFetchedTools,
  deleteOrUpdateFetchedMastersSearch,
  deleteOrUpdateFetchedMastersSearchTool,
  // removeMaster,
  // removeMasterById,
  // removeToolById,
} from "../../redux/features/Modules/Masters";
import {
  addorUpdateAllsubTaskBasicDetails,
  addorUpdateAllTaskBasicDetails,
  addOrUpdateTowerRoutes,
  updatesubtaskDetail,
  updateTaskBasicDetail,
} from "../../redux/features/Modules/Billing/ProjectPlanning/Slices/projectPlanningSlice";

export const saveSyncTime = async (date: string, Id: string) => {
  try {
    const Datedb = await initializeDatabase("localtime");

    if (Datedb && date) {
      const currentDoc = await window.electron.getDocument({
        db: Datedb,
        id: Id,
      });
      const docToSave: any = {
        _id: Id,
        date: date,
      };

      if (currentDoc) {
        docToSave.lastdate = currentDoc.date;
        docToSave._rev = currentDoc._rev;
      }
      const response = await window.electron.putDocument({
        db: Datedb,
        docs: docToSave,
      });
    }
  } catch (err) {
    console.warn("error during sync time");
  }
};

export const saveSyncData = async (
  data: any,
  time: string,
  db: string,
  isDeleted?: boolean,
  dispatch?: AppDispatch
) => {
  try {
    let transformObjToArr = [];
    if (!Array.isArray(data)) {
      transformObjToArr.push(data);
    }
    console.log(data, "this is data to be saveed in local db", db);
    const dbName = await initializeDatabase(db);
    console.log(dbName, "this is dbname", time, "time");
    if (dbName) {
      const res = await window.electron.bulkInsert({
        db: dbName,
        docs: Array.isArray(data) ? data : transformObjToArr,
        time: time,
      });
      console.log(res ?? "", "biling data is saved to local db");
      if (res && dispatch) {
        // console.log(res, "biling data is saved to local db");
        dispatch(setBackupChange());

        // const editCall = editDeleteMap[db];
        // console.log(editCall, db, "check for edit call and db");
        console.log(db, data, "this is data to be saved in local dbasdf");
        if (dbName === "projects") {
          dispatch(setNewProject(data));
          dispatch(deleteOrUpdateFetchedMastersSearch(data));
        }
        if (dbName === "Towerlocations") {
          dispatch(deleteOrUpdateFetchedMastersSearch(data));
          dispatch(setNewLocation(data));
        }
        if (dbName == "TowerRoutes") {
          dispatch(addOrUpdateTowerRoutes(data));
        }
        if (dbName == "TaskBasicDetails") {
          dispatch(updateTaskBasicDetail(data));
        }
        if (dbName == "SubTasksBasicDetails") {
          dispatch(addorUpdateAllsubTaskBasicDetails(data));
        }
        if (dbName == "SubtasklocDetail") {
          dispatch(updatesubtaskDetail(data));
        }
        console.log(dbName, "thisis dbname");
        if (
          dbName == "MaterialDesignation" ||
          dbName == "MachinaryDesignation" ||
          dbName == "Manpowerdesignation" ||
          dbName == "Designationmaster" ||
          dbName == "Taskmaster"
        ) {
          console.log(dbName, data, "data coming from the backe3nd");
          dispatch(addOrUpdateFetchedMasters(data));
          dispatch(deleteOrUpdateFetchedMastersSearch(data));
        }

        if (dbName == "ToolDesignation") {
          console.log(data, "this is tool datagettt");
          const conditionedData = Array.isArray(data) ? data?.[0] : data;
          console.log(conditionedData.data, " thisis sss");
          console.log(conditionedData, "thjasdfasf");
          dispatch(addOrUpdateFetchedTools(conditionedData));
          dispatch(deleteOrUpdateFetchedMastersSearchTool(conditionedData));
        }

        if (dbName == "MaterialCategory") {
          dispatch(
            addOrUpdateFetchedMastersCategories({ data, type: "material" })
          );
          dispatch(deleteOrUpdateFetchedMastersSearch(data));
        }
        if (dbName == "ToolCategory") {
          dispatch(addOrUpdateFetchedMastersCategories({ data, type: "tool" }));
          dispatch(deleteOrUpdateFetchedMastersSearch(data));
        }
        if (dbName == "Manpowercategory") {
          dispatch(
            addOrUpdateFetchedMastersCategories({ data, type: "manpower" })
          );
          dispatch(deleteOrUpdateFetchedMastersSearch(data));
        }
        if (dbName == "MachinaryCategory") {
          dispatch(
            addOrUpdateFetchedMastersCategories({ data, type: "machinary" })
          );
          dispatch(deleteOrUpdateFetchedMastersSearch(data));
        }
        if (dbName == "TaskCategory") {
          dispatch(
            addOrUpdateFetchedMastersCategories({ data, type: "taskcategory" })
          );
          dispatch(deleteOrUpdateFetchedMastersSearch(data));
        }
        if (dbName == "Departmentmaster") {
          console.log(data, "This is data asdfasdf");
          dispatch(
            addOrUpdateFetchedMastersCategories({
              data,
              type: "masterDepartment",
            })
          );
          dispatch(deleteOrUpdateFetchedMastersSearch(data));
        }

        // dispatch(editCall(Array.isArray(data) ? data : transformObjToArr));
      }

      return res;
    }
  } catch (err) {
    console.error("Error during syncing data to localdb:", err);
  }
};

//just so it could save data without triggering detectLocalDbChange
export const saveSyncDataForImageStack = async (
  data: any,
  time: string,
  db: string
) => {
  try {
    let transformObjToArr = [];
    if (!Array.isArray(data)) {
      transformObjToArr.push(data);
    }
    console.log(data, "this is data to be saveed in local db", db);
    const dbName = await initializeDatabase(db);
    console.log(dbName, "this is dbname");
    if (dbName) {
      const res = await window.electron.bulkInsert({
        db: dbName,
        docs: Array.isArray(data) ? data : transformObjToArr,
        time: time,
      });
      console.log(res ?? "", "biling data is saved to local db");
      return res;
    }
  } catch (err) {
    console.error("Error during syncing data to localdb:", err);
  }
};
