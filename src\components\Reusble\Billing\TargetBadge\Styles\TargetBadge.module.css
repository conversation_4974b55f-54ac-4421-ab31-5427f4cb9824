/* styles for tareget Badge end by <PERSON><PERSON><PERSON><PERSON> singh */
.target_badge_outer_container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem;
  box-shadow: 0px 0px 3px 0px #91a1a180;
  gap: 0.5rem;
  border-radius: 10rem;
  width: fit-content;
  border: 1px solid transparent;

}

.border {
  border: 1px solid var(--primary_color);
}

.order {
  flex-direction: row-reverse;
}

.targetBubble {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 100%;
  min-width: 22px;
  min-height: 22px;
}

.iconContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.toggle_with_bubble {
  box-shadow: none;
}

/* styles for tareget Badge end by <PERSON><PERSON><PERSON><PERSON> singh */

.approval_button_text {
  color: var(--text-white-100);
}

.monthly_target_header_buttons {
  padding-right: 1rem;
  padding-left: 1rem;
}

/* styles for target_badge for floor listing in monthly target start by rattan<PERSON><PERSON> singh */
.floor_badge {
  background-color: var(--main_background) !important;
  padding: 0.7rem !important;
}

.floor_badge_bubble {
  background-color: var(--primary_background) !important;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--primary_color) !important;
  padding: 0.3rem !important;
  border-radius: 100%;
  width: 1.75rem;
  height: 1.75rem;
}

.assigned_task_slide_floor_bubble_text {
  color: var(--text-black-60);
  
}

.assigned_task_slide_floor_bubble_target_badge {
  white-space: nowrap;
  padding: 0.25rem;
}

.target_badge_bubble_for_subtasks {
  background-color: var(--main_background) !important;
  color: var(--primary_color) !important;
}
.target_badge_for_subtasks {
  min-width: 9.5rem !important;
  margin-top: 1rem;
  background-color: var(--primary_color);
  color: var(--main_background);
}

/* styles for targetBadge for planningTable header */
.target_badge_for_planning_table_header {
  color: white;
  cursor: pointer;
  box-shadow:
    -0.0625rem -0.125rem 0.375rem 0rem rgba(255, 255, 255, 0.15),
    -0.125rem -0.25rem 0.75rem 0rem rgba(255, 255, 255, 0.15),
    0.1875rem 0.25rem 0.75rem 0rem rgba(0, 0, 0, 0.33),
    0.0625rem 0.125rem 0.375rem 0rem rgba(0, 0, 0, 0.35);
  padding: 0.375rem 0.5rem 0.375rem 0.5rem;
  margin-top: 1rem;
}

.task_assigned_selected_floor_slide_selected{
  border: 1px solid var(--primary_color);
}
.task_assigned_selected_floor_slide_not_selected{
  border: 1px solid transparent;
}

.assigned_task_slide_floor_bubble_text_selected{
  color: white;
}


.project_summary_quality_button{
  padding: 0.625rem 0.75rem;
  cursor: pointer;
}
.project_summary_quality_btn_text{
  color: var(--warning_color);
}

.project_summary_export_button{
  padding: 0.7rem 1rem;
  cursor: pointer;
}
.project_summary_export_btn_text{
  color:var(--text-black-60);
}

.project_summary_comments_button{
  padding: 0.7rem 0.625rem 0.7rem 0.625rem;
  cursor: pointer;
}
.project_summary_export_comments_text{
  color: var(--text-white-100);
}


.assigned_task_add_task_btn{
  padding: 0.75rem 1rem;
}

.assigned_task_add_task_text{
  color: var(--text-white-100);
}


@media (max-height:820px) {
  .assigned_task_slide_floor_bubble_target_badge{
    padding: 0.25rem;
  }
}



@media (max-width: 1280px) {
  .approval_button_text {
    font-size: 0.8rem;
  }

  .export_button_text {
    font-size: 0.8rem;
  }
}

/* material popup */
.material_popup{
  color: var(--primary_color);
  height: 24px;
  width:40px ;
}

/* button in task header */
.task_header_padding{
  padding: 0.6rem 1rem;
}

/* button in task header */
.task_header_padding{
  padding: 0.6rem 1rem;
}