.confirmation_modal {
  position: fixed;
  top: 7rem;
  right: 1.5rem;
  transform: translate(0%, 0%);
  background: var(--blur-background);
  padding: 1.25rem;
  backdrop-filter: blur(60px);
  box-shadow: var(--extra-shadow-five);
  border-radius: 2.6rem;
  z-index: 999;
  width: 34rem;
  height: calc(100% - 8.5rem);
  animation: slideIn 0.5s ease-out;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.confirmation_modal_header {
  color: var(--primary_color);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  border-radius: 2.6rem 2.6rem 0 0;
}

.closeButton {
  position: absolute;
  top: -0.85rem;
  right: -0.05rem;
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 1.7rem;
  color: #888;
  z-index: 2;
  transition: color 0.2s;
  padding: 0.5rem;
}
.closeButton:hover {
  color: #222;
}

.confirmation_modal_header h3 {
  color: var(--primary_color, #004d4d);
  text-align: center;
  margin: 0 auto;
  font-size: 1.15rem;
  font-weight: 600;
  letter-spacing: 0.01em;
}

.confirmation_modal_content {
  flex: 1 1 auto;

  overflow-y: auto;
  padding: 1rem 1rem 1rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: stretch;

  border-radius: 50px;
}

.task_list {
  margin: 0 0 0 0;
  padding: 0;
  list-style: none;
  width: 100%;
}

.task_item {
  margin-bottom: 0.7rem;
  font-weight: 600;
  color: var(--primary_color, #004d4d);
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between; /* Add this line */
  gap: 0.5rem;
  min-height: 2.2rem;
  padding: 0 0.2rem;
}

.task_item span:last-child {
  color: var(--secondary_color);
  font-size: 0.95rem;
  font-weight: 400;
  margin-left: 0.5rem;
  min-width: 60px;
  text-align: right;
}

.button_row {
  display: flex;
  justify-content: center;
  gap: 1.2rem;
  width: 100%;
  padding: 1rem 2.5rem 1.2rem 2.5rem;
  border-radius: 0 0 2.6rem 2.6rem;
  background: transparent;
  margin-top: 0; /* IMPORTANT: Remove any margin-top */
}

.cancel_btn,
.confirm_btn {
  padding: 0.55rem 1.5rem;
  border: none;
  border-radius: 1.2rem;
  font-size: 1rem;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.18s, color 0.18s;
}

.cancel_btn {
  background: #eee;
  color: #333;
}

.cancel_btn:hover {
  background: #e0e0e0;
}

.confirm_btn {
  background: var(--primary_color, #004d4d);
  color: #fff;
}

.confirm_btn:hover {
  background: #003333;
}

@keyframes slideIn {
  from {
    transform: translate(100%, 0%);
  }
  to {
    transform: translate(0%, 0%);
  }
}
