import React, { useEffect, useRef, useState } from "react";
import { Twodots } from "../../../../../assets/icons";
import { CardItems } from "../../../TaskMaster/CardItems";
import { useToast } from "../../../../../hooks/ToastHook";

import styles from "./Styles/MasterCard.module.css";
import CardDotPopup from "../../../Global/MainCardPopup";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { getFileName, isBase64 } from "../../../../../functions/functions";
import { openEye, closeEye } from "../../../../../assets/images";
import Tooltip from "../../../Global/Tooltip";
import { resetInitialFormData } from "../../../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import { image_url } from "../../../../../config/urls";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../redux/store";
type CardItemType = {
  title: string;
  name: number | string;
};

type MasterCardProps = {
  variant?: "category" | "tools" | "machinery" | "material" | "manpower";
  data: {
    _id?: string;
    title: string;
    images?: string;
    unit?: string;
    items: CardItemType[];
    brandDetails?: { Brandname: string; Specs: string[] }[];
  };
  editData?: any; //will change this in further changes
  cardBlockSize?: string;
  callbackEditData?: () => any;
};

const MasterCard: React.FC<MasterCardProps> = ({
  variant = "tools",
  data,
  editData,
  cardBlockSize,
  callbackEditData,
}) => {
  const { title, unit, items, images } = data;
  const [isEyeOpen, setIsEyeOpen] = useState(false);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [zIndex, setIndex] = useState("");
  const [actualImageSrc, setActualImageSrc] = useState("/SuryaconLogo.png");
  const dispatch = useAppDispatch();
  const popupRef = useRef<HTMLDivElement>(null);
  const imagePoll = useAppSelector((state) => state.masterForm.imageToggle);
  const showToast = useToast();
  const togglePopup = async () => {
    if (!navigator.onLine) {
      showToast({
        messageContent: "Oops! no internet connection",
        type: "danger",
      });
      return;
    }

    // const { popups, categoryPopup } = useSelector(
    //     (state: RootState) => state.popup
    //   );
    console.log("poopups:>>");

    if (callbackEditData) {
      try {
        const data = await callbackEditData();
        console.log("DATA1222:", data.data);

        if (data && data?.data?.data) {
          setIsPopupOpen((prev) => !prev);
          return;
        }

        showToast({
          messageContent: "Oops! Something Went Wrong",
          type: "danger",
        });
      } catch (error) {
        console.log("DATA1222:2", error);
        showToast({
          messageContent: "Something went wrong!",
          type: "danger",
        });
      }
    }
  };
  const handleEyeToggle = () => {
    if (isEyeOpen) {
      setTimeout(() => {
        setIndex("");
      }, 500);
    } else {
      setIndex("1");
    }

    setIsEyeOpen(!isEyeOpen);
  };
  const handleClickOutside = (event: MouseEvent) => {
    if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
      setIsEyeOpen(false);
      console.log("Clicked outside the popup1");
      // setTimeout(() => {
      setIndex("");
      // }, 500);
    }
  };

  useEffect(() => {
    if (!images || images.length === 0) {
      setActualImageSrc("/SuryaconLogo.png");
      return;
    }

    const imageUrl = `${image_url}/images/${getFileName(images)}`;
    const img = new Image();

    img.src = imageUrl;

    img.onload = () => {
      console.log("Image is now available:", imageUrl);
      setActualImageSrc(imageUrl); // Set the actual image
    };

    img.onerror = () => {
      console.log("Image still not available.");
      setActualImageSrc("/SuryaconLogo.png");
    };
  }, [imagePoll, images]);

  useEffect(() => {
    // Only add the listener when popup is open
    if (isEyeOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    // Clean up the event listener when the component unmounts or when isEyeOpen changes
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isEyeOpen]);

  return (
    <div
      ref={popupRef}
      key={data._id}
      className={styles.mastercard_first_outercontainer}
      style={{ zIndex: zIndex, blockSize: cardBlockSize }}
      // onMouseOver={() => {
      //   setIsEyeOpen(true);
      //   setIndex("1");
      // }}
      // onMouseLeave={() => {
      //   setIsEyeOpen(false),
      //     setTimeout(() => {
      //       setIndex("0");
      //     }, 300);
      // }}
    >
      <div
        className={`${styles.mastercard_outercontainer} ${
          isEyeOpen && styles.mastercard_outercontainer_expand
        } `}
      >
        <div className={`${styles.mastercard_outercontainer_subbox}  `}>
          {variant !== "machinery" && (
            <div className={styles.mastercard_image}>
              <img
                src={
                  variant === "manpower"
                    ? "/Frame 1000003151.png"
                    : actualImageSrc
                }
                alt={"SuryaCon"}
                className={styles.mastercard_image}
              />
            </div>
          )}

          {variant === "machinery" && (
            <div className={styles.mastercard_leftimgcontainer_machinery}>
              <img
                src={actualImageSrc}
                alt={"SuryaCon"}
                className={styles.mastercard_leftimgcontainer_machinery}
              />
            </div>
          )}

          <div className={styles.mastercard_rightcontainer}>
            <div className={styles.mastercard_header}>
              {" "}
              {isPopupOpen && (
                <CardDotPopup
                  editData={editData}
                  varient={variant}
                  setIsPopupOpen={setIsPopupOpen}
                />
              )}
              <div className={styles.mastercard_header_left}>
                <h3 className={styles.mastercard__header_heading}>{title}</h3>
                {variant === "material" && unit && (
                  <p className={styles.mastercard_header_unitdiv}>
                    {unit?.length === 1
                      ? unit?.[0]
                      : unit?.length > 2
                      ? "2+"
                      : "2"}
                  </p>
                )}
              </div>
              <div className={styles.masterCard_header_toggleBox}>
                {variant !== "manpower" && (
                  <div
                    className={styles.mastercard__header_eyetoggle}
                    onClick={() => handleEyeToggle()}
                  >
                    <img src={isEyeOpen ? openEye : closeEye} alt="openimg" />
                  </div>
                )}

                <div
                  className={styles.mastercard_header_righticon}
                  onClick={togglePopup}
                >
                  <Twodots />
                  {isPopupOpen && (
                    <div
                      style={{
                        height: "100%",
                        width: "100%",
                        zIndex: 2,
                        position: "absolute",
                      }}
                    ></div>
                  )}
                </div>
              </div>
            </div>

            <div
              className={styles.mastercard_items}
              style={{
                display: "grid",
                gridTemplateColumns:
                  variant === "tools" ? "repeat(3, 1fr)" : "repeat(2, 1fr)",
                gridTemplateRows:
                  variant === "material" ? "repeat(1, auto)" : "auto",
              }}
            >
              {items.map((item, index) => (
                <CardItems key={index} title={item.title} name={item.name} />
              ))}
            </div>
          </div>
        </div>

        <div
          className={`${styles.mastercard__subheading_box} ${
            isEyeOpen && styles.mastercard__subheading_box_visible
          }`}
        >
          {data?.brandDetails &&
            data?.brandDetails?.map((brandItem: any) => (
              <div className={styles.mastercard__subheading_item}>
                <h4 className={styles.mastercard__subheading}>
                  {brandItem?.Brandname}
                </h4>
                <div className={styles.mastercard__subheading_item_value}>
                  {brandItem &&
                    brandItem?.Specs?.map((item: any) => (
                      <Tooltip content={item} id={""} />
                    ))}
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
};

export default MasterCard;
