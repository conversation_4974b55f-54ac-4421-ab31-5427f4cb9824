import React, { useEffect, useMemo, useRef, useState } from "react";
import styles from "./Styles/PlanningProgress.module.css";
import { Loader } from "./loader";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../../../redux/store";
import { PlanningProgressProps } from "../../../../../../interfaces/Modules/Billing/ProjectPlanning/ProjectPlanning";
import {
  addProgressTowerDataByProject,
  selectLocationTaskId,
  setTowerBasicDetails,
  clearTaskData,
  setCurrentSelectedData,
  setAllTaskBasicDetails,
  setallSubtasksData,
  setIsEditPlanning,
  setselectedLocationId,
  setToDeleteData,
  setDeletingTaskId,
    addOrUpdateTowerRoutes,
} from "../../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/projectPlanningSlice";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import {
  closePopup,
  openPopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import { useParams } from "react-router-dom";
import { initializeDatabase } from "../../../../../../functions/functions";
import { DeleteIcon } from "../../../../../../assets/icons";
import { useOpencloseprojectplanningMutation } from "../../../../../../redux/api/Modules/Reusble/Reusble";
import { update } from "lodash";
import { useUpdateLocationDetailsMutation } from "../../../../../../redux/api/Modules/Billing/ProjectPlanningApi";

const PlanningProgress: React.FC<PlanningProgressProps> = ({
  currentStepIndex,
}) => {
  const [updateStatus] = useOpencloseprojectplanningMutation();
  const [updateLocationDetails] = useUpdateLocationDetailsMutation();

  const dispatch = useDispatch();
  const { towerLocationId: objectId } = useParams();

  const [selectedStep, setSelectedStep] = useState<number | null>(null);
  const [completedSteps, setCompletedSteps] = useState<{
    [key: string]: boolean;
  }>({});
  const prevSelectedStepRef = useRef<number | null>(null);

  const allTowerRoutes = useSelector(
    (state: RootState) => state.projectPlanning.progressTowerData || []
  );
  const planningProgress = useSelector(
    (state: RootState) => state.projectPlanning.planningProgress
  );
  const allSubtaskData = useSelector(
    (state: RootState) => state.projectPlanning.allSubtasksdata
  );
  const selectedLocationTaskId = useSelector(
    (state: RootState) => state.projectPlanning.selectedLocationTaskId
  );

  const editMode = useSelector(
    (state: RootState) => state.projectPlanning.editMode
  );
  const deletingtask = useSelector(
    (state: RootState) => state.projectPlanning.deletingtask
  );

  const stepLoadingPercentage = useSelector(
    (state: RootState) => state.projectPlanning.stepLoadingPercentage
  );

  const { popups } = useSelector((state: RootState) => state.popup);

  const allsubtaksBasicDetails = useSelector(
    (state: RootState) => state.projectPlanning.allSubtasksBasicDetails
  );

  console.log(allsubtaksBasicDetails, "this is data askljflaskfoio33")

  // Add state for delete
  const [deleteItem, setDeleteItem] = useState<any>(null);

  const selectedLocationTaskIdData = useSelector(
    (state: RootState) => state?.projectPlanning?.currentTaskbasicDetails
  );
  const tower_Id = useParams().towerLocationId;
  const toBeDeleteData = useSelector(
    (state: RootState) => state?.projectPlanning?.Todeletedata
  );
  const selectAllSubtasksData = useSelector(
    (state: RootState) =>
      (tower_Id && selectedLocationTaskId
        ? state.projectPlanning.allSubtasksdata?.[tower_Id]?.[
            selectedLocationTaskId
          ]
        : []) || []
  );
  const currentSelectedRate = useSelector(
    (state: RootState) => state.projectPlanning.selectedprojectRate
  );
  const originalDataRef = useRef<any>(null);

   const generateTransformedData = (customTodeleteData?: any, customDeletingTask?: any) => {
    const taskId =
      selectedLocationTaskIdData?.Master_taskid ||
      selectedLocationTaskIdData?.taskId;
    const taskName = selectedLocationTaskIdData?.name;

    if (!taskId || !taskName) {
      return null;
    }

    // Get the latest state from Redux
    const state = (window as any).store?.getState
      ? (window as any).store.getState()
      : null;

    // Fallback to useSelector if direct access is not available
    const latestState = state?.projectPlanning
      ? state.projectPlanning
      : {
        Todeletedata: toBeDeleteData,
        deletingtask: deletingtask,
        planningProgress: planningProgress,
        selectedLocationTaskIdData: selectedLocationTaskIdData,
        currentSelectedRate: currentSelectedRate,
      };

    return {
      location_id: tower_Id,
       Todeletedata: customTodeleteData ?? toBeDeleteData,
      Tasks: [
      {
        subtask_details: selectAllSubtasksData?.map((task: any) => ({
        ...(task?.itemtype !== "subtaskmodal" && { _id: task?._id }),
        Master_subtask_id: task?.subtaskId?._id,
        quantity: task?.quantity,
        weightage: task?.weightage,
        Description: task?.Description,

        manpower_details: task?.manpowerId?.map((manpower: any) => ({
          ...(manpower?._id &&
          !/^\d{13}-\d+\.\d+$/.test(manpower._id) && {
            _id: manpower._id,
          }),
          Mastermanpower_id: manpower?.Mastermanpower_id?._id,
          quantity: Number(manpower?.quantity),
          type: manpower?.type,
        })),

        machinary_details: task?.machinaryId?.map((machinery: any) => ({
          ...(machinery?._id &&
          !/^\d{13}-\d+\.\d+$/.test(machinery._id) && {
            _id: machinery._id,
          }),
          Mastermachinary_id: machinery?.Mastermachinary_id?._id,
          quantity: machinery?.quantity,
          Masterbrand_id: machinery?.Masterbrand_id?._id,
          spec: machinery?.spec,
        })),

        material_details: task?.materialId?.map((material: any) => ({
          ...(material?._id &&
          !/^\d{13}-\d+\.\d+$/.test(material._id) && {
            _id: material._id,
          }),
          Mastermaterial_id: material?.Mastermaterial_id?._id,
          quantity: material?.quantity,
          Masterbrand_id: material?.Masterbrand_id?._id,
          spec: material?.spec,
        })),

        Tools_details: task?.tool_id?.map((tool: any) => ({
          ...(tool?._id &&
          !/^\d{13}-\d+\.\d+$/.test(tool._id) && { _id: tool._id }),
          MasterTool_id: tool?.MasterTool_id?._id,
          quantity: tool?.quantity,
          spec: tool?.spec,
          Masterbrand_id: tool?.Masterbrand_id?._id,
        })),
        })),
        payment: latestState.selectedLocationTaskIdData?.payment,
        Area: latestState.selectedLocationTaskIdData?.area,
        Duration: latestState.selectedLocationTaskIdData?.duration,
        Shuttering: latestState.selectedLocationTaskIdData?.shuttering,
        progressPercentage: latestState.planningProgress,
        Master_taskid: taskId,
        rate: latestState.currentSelectedRate,
        name: taskName,
        ...(latestState.selectedLocationTaskIdData?.Master_taskid && {
        _id: latestState.selectedLocationTaskIdData?.taskid,
        }),
      },
      ],
      deletingtask: customDeletingTask ?? deletingtask,
    };
  };
  useEffect(() => {
    originalDataRef.current = generateTransformedData();
  }, [editMode]);

  const handleStepClick = async (index: number, id: string, step: any) => {
  
    if (selectedLocationTaskId === id) return;
    // Reset and update current selection
    dispatch(setTowerBasicDetails({}));
    dispatch(setAllTaskBasicDetails([]));
    dispatch(setallSubtasksData([]));

    dispatch(setCurrentSelectedData(step));
    dispatch(selectLocationTaskId(id));
    setSelectedStep(index);
    prevSelectedStepRef.current = index;
  };
  const currentTaskDetails = useSelector(
    (state: RootState) => state.projectPlanning.currentTaskbasicDetails
  );

  const fetchAndSetTowerRoutes = async () => {
    try {

      dispatch(addProgressTowerDataByProject([]));
      setSelectedStep(null);

      const dbName = await initializeDatabase("TowerRoutes");

      const towerRoutes = await window.electron.getDocumentByParentId({
        categoryId: "Tower_id",
        dbName,
        catId: objectId,
        needPagination: false,
      });

      console.log(towerRoutes, "data btao tower routes ka");

      console.log(towerRoutes, "data btao tower routes ka");

    if (towerRoutes && towerRoutes.length > 0) {
      const sortedRoutes = [...towerRoutes]
        ?.sort(
          (a, b) =>
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        )
        .map((route) => ({
          ...route,
          progressloading: route.progressloading ?? 0,
        }));

        dispatch(addProgressTowerDataByProject(sortedRoutes));
        console.log(sortedRoutes, "data after transformation");
      } else {
        dispatch(addProgressTowerDataByProject(null));
        setSelectedStep(null);
      }
    } catch (error) {
      console.error("Error fetching routes:", error);
    }
  };
 useEffect(() => {
  // Fetch tower routes when objectId changes
  fetchAndSetTowerRoutes();
}, [objectId]);

// useEffect(() => {
//   // Select step when selectedLocationTaskId or allTowerRoutes change
//   if (selectedLocationTaskId && allTowerRoutes.length > 0) {
//     const index = allTowerRoutes.findIndex(
//       (route) => route._id === selectedLocationTaskId
//     );
//     setSelectedStep(index !== -1 ? index : 0);
//   } else {
//     setSelectedStep(null);
//   }
// }, [selectedLocationTaskId, allTowerRoutes]);

useEffect(() => {
  // When allTowerRoutes change, ensure selectedLocationTaskId is valid
  if (allTowerRoutes.length > 0) {
    const foundIndex = allTowerRoutes.findIndex(
      (route) => route._id === selectedLocationTaskId
    );
    if (foundIndex === -1) {
      const firstId = allTowerRoutes[0]?._id;
      if (firstId) {
        dispatch(selectLocationTaskId(firstId));
        setSelectedStep(0);
      }
    } else {
      setSelectedStep(foundIndex);
    }
  } else {
    setSelectedStep(null);
  }
}, [allTowerRoutes, selectedLocationTaskId, objectId]);

  const loaderSteps = useMemo(() => {
    return allTowerRoutes.map((step, index) => (
      <div
        key={step._id}
        className={`${styles.progressStep} ${
          index <= currentStepIndex ? styles.active : ""
        }`}
        data-step-index={index}
        style={{ position: "relative" }}
      >
        <div className={styles.stepContainer}>
          <div
            className={`${styles.stepMarker} ${
              index <= currentStepIndex ? styles.activeMarker : ""
            }`}
            style={{
              background: "#004d4d",
              color: "#ffff",
              borderColor: "#E8B000",
            }}
          >
            <div
              className={styles.loadercontainer}
              onClick={() => handleStepClick(index, step._id, step)}
            >
              <Loader
                percentage={step.progressPercentage || 0}
                colour={"var(--secondary_color)"}
                isactive={selectedStep === index}
                height={`4.8rem ${
                  selectedStep === index ? styles.activeLoader : ""
                }`}
                width={"7.5rem"}
                number={`${step.progressPercentage ?? 0}%`}
              />
            </div>
          </div>
          <div
            className={`${styles.stepLabel} ${
              selectedStep === index ? styles.active : ""
            }`}
            onClick={() => handleStepClick(index, step._id, step)}
          >
            {(step.progressloading ?? 0) < 100 && (
              <div
                className={styles.stepLabelFill}
                style={{ width: `${step.progressloading || 0}%` }}
              ></div>
            )}
            <h4>
              {step.name}
            </h4>
          </div>
        </div>
        {/* Delete icon for manual tasks */}
        {editMode && step?.isManual && selectedStep === index && (
          <div
            className={styles.mt_target_card_delete}
            onClick={(e) => handleDeleteIconClick(step, index, e)}
            style={{
              position: "absolute",
              top: "-3px",
              right: "-2px",
              cursor: "pointer",
              zIndex: 2,
            }}
            title="Delete Task"
          >
            <DeleteIcon />
          </div>
        )}
        {index < allTowerRoutes.length - 1 && (
          <div className={styles.stepLine} />
        )}
      </div>
    ));
  }, [
    allTowerRoutes,
    currentStepIndex,
    editMode,
    handleStepClick,
    selectedStep,
  ]);

  const handleDeleteIconClick = (
    step: any,
    index: number,
    e: React.MouseEvent
  ) => {
    e.stopPropagation();
    // Try to get subtasks from step object if available
    const subtasks =
      step?.subtask_details || step?.subtasks || selectAllSubtasksData || [];
    setDeleteItem({
      step: currentTaskDetails,
      index,
      subtasks,
    });

    dispatch(openPopup("DeleteTaskPlanning"));
  };

  const handleConfirmDelete = async () => {
    if (!deleteItem) return;
    const { step, index } = deleteItem;

    const newTodeleteData = {
      Tasks: [currentTaskDetails.taskid],
      Subtasks: [],
      machinaries: [],
      tools: [],
      manpowers: [],
      materials: [],
    };

    try {
      const delteId = [currentTaskDetails.taskid];
      const data = generateTransformedData(newTodeleteData, delteId);
      const res = await updateLocationDetails({ data });
      console.log("MongoDB delete response:", res);

      dispatch(
        clearTaskData({
          towerId: step.Tower_id,
          taskId: step._id,
        })
      );

      const updatedRoutes = allTowerRoutes.filter(
        (route: any) => route._id !== step._id
      );
      dispatch(
        setToDeleteData({
          ...toBeDeleteData,
          Tasks: [selectedLocationTaskId],
        })
      );
      dispatch(setDeletingTaskId([selectedLocationTaskId]));
      console.log(toBeDeleteData, "this is to be delete data checko0ut her");

      if (updatedRoutes.length > 0) {
        dispatch(setDeletingTaskId([]));
        dispatch(
          setToDeleteData({
            Tasks: [],
            Subtasks: [],
            machinaries: [],
            tools: [],
            manpowers: [],
            materials: [],
          })
        );
        setSelectedStep(0);
        dispatch(selectLocationTaskId(updatedRoutes[0]._id));
        dispatch(closePopup("DeleteTaskPlanning"));
      }
    } catch (err) {
      console.error("Error deleting from MongoDB:", err);
    }
  };
  useEffect(() => {
    if (
      selectedStep !== null &&
      allTowerRoutes[selectedStep] &&
      stepLoadingPercentage === 100
    ) {
      setCompletedSteps((prev) => ({
        ...prev,
        [allTowerRoutes[selectedStep]._id]: true,
      }));
    }
  }, [stepLoadingPercentage, selectedStep, allTowerRoutes]);

  return (
    <div className={styles.planningProgress_container}>
      <div className={styles.verticalProgress}>
        <div className={styles.progressSteps}>
          {loaderSteps}
          <div
            className={styles.progressStep}
            onClick={() => dispatch(openPopup("AddTaskPlanning"))}
          >
            <div className={styles.stepAddMarker}>
              <span className={styles.plus_tag}>+</span>
            </div>
          </div>
        </div>
      </div>
      {/* Delete Task Popup */}
      {deleteItem && console.log("Delete Popup Data:", deleteItem)}
      {popups["DeleteTaskPlanning"] && deleteItem && (
        <DeletePopup
          header="Are you sure you want to delete this task?"
          height="calc(100% - 7.25rem)"
          heightupperlimit="0"
          callbackDelete={handleConfirmDelete}
          onClose={() => {
            setDeleteItem(null);
            dispatch(closePopup("DeleteTaskPlanning"));
          }}
        >
          <>
            <div style={{ display: "flex", gap: "16px", flexWrap: "wrap" }}>
              <div
                className={styles.summaryDivData}
                style={{ flex: 1, minWidth: 220 }}
              >
                <div className={styles.summaryDataContent}>
                  <p className="p_tag_14px">Name</p>
                  <h4>{deleteItem.step?.name}</h4>
                </div>
              </div>
            </div>
            <div className={styles.summaryDivData} style={{ marginTop: 16 }}>
              <div className={styles.summaryDataContent}>
                <p className="p_tag_14px">Description</p>
                <h4>{deleteItem.step?.description}</h4>
              </div>
            </div>
            <div
              style={{
                display: "flex",
                gap: "16px",
                flexWrap: "wrap",
                marginTop: 16,
              }}
            >
              <div
                className={styles.summaryDivData}
                style={{ flex: 1, minWidth: 220 }}
              >
                <div className={styles.summaryDataContent}>
                  <p className="p_tag_14px">Unit</p>
                  <h4>{deleteItem.step?.unit}</h4>
                </div>
              </div>
              {deleteItem.step?.area !== 0 && (
                <div
                  className={styles.summaryDivData}
                  style={{ flex: 1, minWidth: 220 }}
                >
                  <div className={styles.summaryDataContent}>
                    <p className="p_tag_14px">Area</p>
                    <h4>{`${deleteItem.step?.area} sqft`}</h4>
                  </div>
                </div>
              )}
              {deleteItem.step?.rate !== 0 && (
                <div
                  className={styles.summaryDivData}
                  style={{ flex: 1, minWidth: 220 }}
                >
                  <div className={styles.summaryDataContent}>
                    <p className="p_tag_14px">Rate</p>
                    <h4>{`₹ ${deleteItem.step?.rate}`}</h4>
                  </div>
                </div>
              )}
              {deleteItem.step?.payment !== 0 && (
                <div
                  className={styles.summaryDivData}
                  style={{ flex: 1, minWidth: 220 }}
                >
                  <div className={styles.summaryDataContent}>
                    <p className="p_tag_14px">Payment</p>
                    <h4>{`${deleteItem.step?.payment} %`}</h4>
                  </div>
                </div>
              )}
            </div>
            <div
              style={{
                display: "flex",
                gap: "16px",
                flexWrap: "wrap",
                marginTop: 16,
              }}
            >
              {deleteItem.step?.duration !== 0 && (
                <div
                  className={styles.summaryDivData}
                  style={{ flex: 1, minWidth: 220 }}
                >
                  <div className={styles.summaryDataContent}>
                    <p className="p_tag_14px">Duration</p>
                    <h4>{`${deleteItem.step?.duration} days`}</h4>
                  </div>
                </div>
              )}
              {deleteItem.step?.shuttering !== 0 && (
                <div
                  className={styles.summaryDivData}
                  style={{ flex: 1, minWidth: 220 }}
                >
                  <div className={styles.summaryDataContent}>
                    <p className="p_tag_14px">Shuttering</p>
                    <h4>{`${deleteItem.step?.shuttering} sqm`}</h4>
                  </div>
                </div>
              )}
            </div>
            {deleteItem.subtasks && deleteItem.subtasks.length > 0 && (
              <div style={{ marginTop: 24, width: "100%" }}>
                <p
                  className="p_tag_14px"
                  style={{ marginBottom: 8, fontWeight: 600 }}
                >
                  Subtasks
                </p>
                <div style={{ display: "flex", flexWrap: "wrap", gap: "16px" }}>
                  {deleteItem.subtasks.map((subtask: any, idx: number) => (
                    <div
                      key={subtask._id || idx}
                      className={styles.summaryDivData}
                      style={{ flex: 1, minWidth: 220 }}
                    >
                      <div className={styles.summaryDataContent}>
                        <p className="p_tag_14px">Name</p>
                        <h4>{subtask?.subtaskId?.name || subtask?.name}</h4>
                        {subtask?.Description && (
                          <>
                            <p className="p_tag_14px" style={{ marginTop: 4 }}>
                              Description
                            </p>
                            <h4>{subtask.Description}</h4>
                          </>
                        )}
                        {subtask?.quantity !== undefined && (
                          <>
                            <p className="p_tag_14px" style={{ marginTop: 4 }}>
                              Quantity
                            </p>
                            <h4>{subtask.quantity}</h4>
                          </>
                        )}
                        {subtask?.weightage !== undefined && (
                          <>
                            <p className="p_tag_14px" style={{ marginTop: 4 }}>
                              Weightage
                            </p>
                            <h4>{subtask.weightage}</h4>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            {Array.isArray(allSubtaskData) && allSubtaskData.length > 0 && (
              <div style={{ marginTop: 24 }}>
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p className="p_tag_14px" style={{ fontWeight: 600 }}>
                      Subtasks
                    </p>

                    <ul style={{ paddingTop: "0.5rem", margin: 0 }}>
                      {allSubtaskData.map((sub: any, idx: number) => (
                        <li
                          key={sub._id || idx}
                          style={{
                            marginBottom: "12px",
                            listStyle: "none",
                          }}
                        >
                          {sub.name || sub.subtaskId?.name}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </>
        </DeletePopup>
      )}
    </div>
  );
};

export default PlanningProgress;