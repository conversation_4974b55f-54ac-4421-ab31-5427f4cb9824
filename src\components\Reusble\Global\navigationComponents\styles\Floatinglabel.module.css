/*  AUTHOR NAME : CHARVI */
  .floatingLabelInput {
    position: relative;
    /* margin: 0.5rem 0.8rem 0.2rem 0.8rem; */
  
  }
  
  .inputText {
    padding: 0.9rem 1.4rem;
    width: 100%;
    line-height: 1.2rem;
    font-size: 1rem;
    background: var(--blur-background);
    border: 1px solid var(--text-black-28);
    border-radius: 1.5rem;
    resize: none;
    overflow: hidden;
    transition: outline 0.2s;
    min-height: 1.5rem;
    /* min-width: 6.25rem; */
    /* min-width: 14rem; */
  }
  
  .inputText::placeholder {
    opacity: 0;
  }
  
  .inputText:focus {
    outline: 1px solid #00000047;
  }
  
  .floatingLabelInput>label {
    position: absolute;
    top: 40%;
    left: 1rem;
    color: var(--text-black-60);
    background: var(--primary_background);
    transform: translateY(-50%);
    cursor: text;
    font-size: 0.6rem;
    transition: all 0.2s ease-out;
    font-weight: 400;
  
  }
  
  .inputText:focus+label,
  .inputText:not(:placeholder-shown)+label {
    padding: 0 0.5em;
    top: 0;
    left: 1.5em;
    color: var(--text-black-87);
    font-size: 0.5em;
    font-weight: 400;
  }
  
  .inputText:focus+label {
    color: var(--text-black-87);
    font-size: 0.75rem;
    font-weight: 600;
  }
  
  .invalid {
    border: 1px solid var(--warning_color);
  }
  
  .floatinglabel_icon {
    position: absolute;
    top: 25%;
    right: 20px;
    cursor: pointer;
  }