import { useCallback, useEffect, useRef, useState } from "react";
import { useAppSelector } from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import {
  resetInputValues,
  setInputValue,
} from "../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { useDispatch, useSelector } from "react-redux";
import {
  closePopup,
  togglePopup,
} from "../../../../redux/features/Modules/Reusble/popupSlice";
import {
  CloseIcon,
  DropDownArrowUpIcon,
  DropDownCategoryIcon,
  WieghtPercentageIcon,
} from "../../../../assets/icons";
import SubtaskSummary from "./Subcomponents/SubtaskSummary";
import FloatingLabelInput from "../../Global/FloatingLabel";
import UnitPopup from "../../Global/UnitPopup";
import Button from "../../Global/Button";
import styles from "./Styles/AddSubTasksPopup.module.css";
import { RootState, store } from "../../../../redux/store";
import { AddSubTasksPopupProps } from "../TaskMasterInterfaces/TaskMasterInterface";
import { isValidValue } from "../../../../functions/functions";
import { useCheckSubtaskWeightageMutation } from "../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import { useToast } from "../../../../hooks/ToastHook";

const unitData = [
  { id: 1, label: "Bag" },
  { id: 2, label: "Box" },
  { id: 3, label: "Cft" },
  { id: 4, label: "Cum" },
  { id: 5, label: "Feet" },
  { id: 6, label: "Kgs" },
  { id: 7, label: "Length" },
  { id: 8, label: "Ltrs" },
  { id: 9, label: "Month" },
  { id: 10, label: "Mtr" },
  { id: 11, label: "Nos" },
  { id: 12, label: "Pair" },
  { id: 13, label: "Pkts" },
  { id: 14, label: "Rft" },
  { id: 15, label: "Roll" },
  { id: 16, label: "Set" },
  { id: 17, label: "Sqft" },
  { id: 18, label: "Sqmt" },
  { id: 19, label: "YDS" },
];

const trackingData = [
  { id: 1, label: "Auto", backendValue: "auto" },
  { id: 2, label: "Manual", backendValue: "manual" },
];
function AddSubTasksPopup({
  onClose,
  onSubmit,
  isEdit,
  data,
}: AddSubTasksPopupProps) {
  const showToast = useToast();

  const [wasTrue, setWasTrue] = useState(false);
  const [selectedUnitId, setSelectedUnitId] = useState<number | null>(null);
  const [checkSubweigtages] = useCheckSubtaskWeightageMutation();
  const [selectedTrackingId, setSelectedTrackingId] = useState<number | null>(
    null
  );
  const [discard, setDiscard] = useState<boolean>(false);
  const [initialState, setInitialState] = useState<{
    _id?: string;
    name: string;
    Description: string;
    Unit: string;
    subtaskWeighatages: number | null;
    Tracking: string;
  }>({
    _id: "",
    name: "",
    Description: "",
    Unit: "",
    subtaskWeighatages: null,
    Tracking: "",
  });

  console.log("hasFormChanges >>initialState", initialState);
  const [isClosing, setIsClosing] = useState<boolean>(false);

  const [errors, setErrors] = useState({
    name: false,
    Unit: false,
    Tracking: false,
    subtaskWeighatages: false,
  });

  const TaskData = useSelector(
    (state: RootState) => state.taskForm.currentSubtaskData
  );

  const inputValue = useAppSelector(
    (state: RootState) => state.floatingLabel.inputValues
  );
  const dispatch = useDispatch();
  console.log("hasFormChanges >>initial", data);
  useEffect(() => {
    setInitialState({
      _id: data?._id || "",
      name: data?.name || "",
      Description: data?.Description || "",
      Unit: Array.isArray(data?.Unit) ? data?.Unit[0] : data?.Unit || "",
      subtaskWeighatages: data?.subtaskWeighatages || null,
      Tracking: data?.Tracking || "",
    });

    dispatch(
      setInputValue({
        _id: data?._id || "",
        name: data?.name || "",
        Description: data?.Description || "",
        Unit: Array.isArray(data?.Unit) ? data?.Unit[0] : data?.Unit || "",
        subtaskWeighatages: data?.subtaskWeighatages || null,
        Tracking: data?.Tracking || "",
      })
    );

    setSelectedUnitId(
      unitData.find(
        (v) =>
          v?.label === (Array.isArray(data?.Unit) ? data?.Unit[0] : data?.Unit)
      )?.id ?? null
    );

    setSelectedTrackingId(
      trackingData.find((v) => v?.label === data?.Tracking)?.id ?? null
    );
  }, [isEdit]);

  const isUnitPopUpVisible = useSelector(
    (state: RootState) => state.popup.popups["unitPopup"]
  );
  const isTrackingPopUpVisible = useSelector(
    (state: RootState) => state.popup.popups["trackingPopup"]
  );
  const [isSummaryPage, setIsSummaryPage] = useState(false);

  const toggleUnitPopUp = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isTrackingPopUpVisible) {
      dispatch(closePopup("trackingPopup"));
    }
    dispatch(togglePopup("unitPopup"));
  };
  const toggleTrackingPopUp = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isUnitPopUpVisible) {
      dispatch(closePopup("unitPopup"));
    }
    dispatch(togglePopup("trackingPopup"));
  };
  const handleUnitSelect = (unit: { id: number; label: string }) => {
    dispatch(setInputValue({ Unit: unit.label }));
    setSelectedUnitId(unit.id);
    clearError("Unit"); // Clear the error here for Unit
    dispatch(closePopup("unitPopup"));
  };

  const handleTrackingSelect = (tracking: {
    id: number;
    label: string;
    backendValue: string;
  }) => {
    setSelectedTrackingId(tracking.id);
    dispatch(setInputValue({ Tracking: tracking.backendValue }));
    clearError("Tracking");
    dispatch(closePopup("trackingPopup"));
  };

  const handleAddSubtask = async () => {
    if (isEdit) {
      const noChanges =
        String(inputValue?.name).trim() === String(initialState?.name).trim() &&
        String(inputValue?.Description).trim() ===
          String(initialState?.Description).trim() &&
        String(inputValue?.Unit).trim() === String(initialState?.Unit).trim() &&
        String(inputValue?.Tracking).toLowerCase() ===
          String(initialState?.Tracking).toLowerCase() &&
        String(inputValue?.subtaskWeighatages) ===
          String(initialState?.subtaskWeighatages);
      if (noChanges) {
        showToast({
          messageContent: "There were no changes!",
          type: "info",
        });
        setIsSummaryPage(false);
        return;
      }
    }
    try {
      // Only validate mandatory fields when submitting
      await onSubmit({
        name: String(inputValue?.name)?.trim(),
        Description: String(inputValue?.Description)?.trim(),
        Unit: String(inputValue?.Unit)?.trim(),
        subtaskWeighatages: Number(inputValue?.subtaskWeighatages),
        Tracking: String(inputValue?.Tracking)?.toLowerCase(),
      });

      setIsClosing(true);
      setTimeout(() => {
        if (isEdit) {
          dispatch(closePopup("SubTaskEdit"));
          return;
        }
        dispatch(closePopup("Subtasks"));
        dispatch(resetInputValues());
      }, 400);
    } catch (error) {
      showToast({
        messageContent: error?.data?.message || "Oops! Something went wrong",
        type: "danger", 
      });
    }
  };
  console.log("inpValue", inputValue);

  const handleNextClick = () => {
    const isNameEmpty = !String(inputValue.name).trim();
    const isUnitEmpty = !inputValue.Unit;
    const isTrackingEmpty = !inputValue.Tracking;
    const isSubtaskWeightageEmpty =
      !inputValue.subtaskWeighatages || inputValue.subtaskWeighatages == 0;

    setErrors({
      name: isNameEmpty,
      Unit: isUnitEmpty,
      Tracking: isTrackingEmpty,
      subtaskWeighatages: isSubtaskWeightageEmpty,
    });

    if (
      isNameEmpty ||
      isUnitEmpty ||
      isTrackingEmpty ||
      isSubtaskWeightageEmpty
    ) {
      showToast({
        messageContent: "Enter Required Fields!",
        type: "warning",
      });
      return;
    }

    console.log("number", inputValue?.subtaskWeighatages);

    const weightageValue = Number(inputValue?.subtaskWeighatages);
    if (isNaN(weightageValue) || weightageValue > 100) {
      setErrors({ ...errors, subtaskWeighatages: true });
      showToast({
        messageContent:
          weightageValue > 100 ? "Enter Upto 100%" : "Enter Numeric Value!",
        type: "warning",
      });
      return;
    }

    setErrors({
      name: false,
      Unit: false,
      Tracking: false,
      subtaskWeighatages: false,
    });

    setIsSummaryPage(true);
  };

  const debounce = (func: Function, wait: number) => {
    let timeout: NodeJS.Timeout | null = null;

    return function (...args: any) {
      if (timeout) clearTimeout(timeout);

      timeout = setTimeout(() => {
        func(...args);
      }, wait);
    };
  };

  const debouncedFunction = useCallback(
    debounce(async (value: any) => {
      const res = await checkSubweigtages({
        taskId: TaskData?._id ?? "",
        weightage: Number(value),
      });
      if (res.error) {
        showToast({
          messageContent: `${res?.error?.data?.message}`,
          type: "warning",
        });
      }
    }, 800),
    []
  );

  const handleInputChange = (id: string, value: string) => {
    dispatch(setInputValue({ ...inputValue, [id]: value }));
    if (value.trim()) {
      clearError(id);
    }
  };

  const handleSubWeighatages = (id: string, value: number | string) => {
    dispatch(setInputValue({ ...inputValue, [id]: value }));
    if (isValidValue(value)) {
      debouncedFunction(value);
    }
    if (value.trim()) {
      clearError(id);
    }
  };

  const clearError = (field: string) => {
    setErrors((prev) => ({ ...prev, [field]: false }));
  };

  const hasFormChanges = () => {
    console.log("hasFormChanges", inputValue, initialState);
    const inputValues = store.getState().floatingLabel.inputValues;
    if (isEdit) {
      console.log("its here brother", inputValue, initialState);
      return (
        String(inputValues?.name)?.trim() !== initialState?.name?.trim() ||
        String(inputValues?.Description)?.trim() !==
          initialState?.Description?.trim() ||
        String(inputValues?.Unit)?.trim() !== initialState?.Unit?.trim() ||
        inputValues?.Tracking?.trim().toLowerCase() !==
          initialState?.Tracking?.toLowerCase() ||
        String(inputValues?.subtaskWeighatages) !==
          String(initialState?.subtaskWeighatages)
      );
    } else {
      return (
        inputValues?.name?.trim() ||
        inputValues?.Description?.trim() ||
        inputValues?.Tracking?.trim() ||
        inputValues?.Unit?.trim() ||
        inputValues?.subtaskWeighatages
      );
    }
  };
  const handleClose = () => {
    // In handleClose:
    const hasChanges = hasFormChanges();
    // console.log('hasChanges<>?', hasChanges);

    if (hasChanges) {
      setDiscard(true);
      return;
    }

    setIsClosing(true);
    setTimeout(() => {
      onClose();
    }, 400);
  };

  console.log(
    "it is is is is is is is is is is is is",
    isSummaryPage,
    discard,
    wasTrue
  );

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if(isClosing) return;
    if (e.key === "Enter" && e.shiftKey) return;

    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();
      if (!isSummaryPage && !discard) {
        handleNextClick();
      }
      if (isSummaryPage) {
        handleAddSubtask();
      } else if (discard) {
        setIsClosing(true);
        setTimeout(() => {
          onClose();
          setInitialState({
            name: "",
            Unit: "",
            subtaskWeighatages: null,
            Tracking: "",
            Description: "",
          });
          dispatch(resetInputValues());
        }, 400);
      }
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      if (isSummaryPage) {
        setIsSummaryPage(false);
      } else if (discard) {
        setDiscard(false);
      } else {
        handleClose();
      }
    }
  };
  const formRef = useRef(null);
  useEffect(() => {
    if (isSummaryPage || discard) {
      if (formRef.current) {
        formRef.current.focus();
      }
    }
  }, [isSummaryPage, discard]);

  const isEmpty = (value: any) => {
    console.log("outisde click tools inside if>>val:", value);
    return !Object.values(value).some((val) => {
      // console.log('outisde click tools inside if>>val:', val)
      return val !== undefined && val !== null && val !== "";
    });
  };
  console.log(isSummaryPage, " this is summary page");
  // const taskFormRef = useRef<HTMLDivElement | null>(null);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      console.log("outisde click tools", inputValue);
      if (formRef.current && !formRef.current.contains(event.target as Node)) {
        console.log("outisde click tools inside if");
        const isEmp = isEmpty(inputValue);
        console.log("outisde click tools inside if >>isEmp:", isEmp);
        if (isEmp) {
          // setIsClosing(true);
          // setTimeout(onClose, 400);

          console.log("outisde click tools inside inner if");

          handleClose();
          return;
        }
        console.log();
        console.log(
          "setFormChanges >>initialState in outside click",
          initialState
        );
        const hasChanges = hasFormChanges();
        console.log("hasChanges<>?2", hasChanges);
        if (!hasChanges || (!hasChanges && !isSummaryPage)) {
          handleClose();
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [dispatch, initialState]);

  const handleFormClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // This will prevent the click event from bubbling up to the parent component
  };

  const [height, setHeight] = useState<number>();
  const headerRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const height = headerRef.current?.getBoundingClientRect().height;
    console.log("height>> of h3", height);
    if (height) {
      setHeight(Math.round(height));
    }
  }, [height, isSummaryPage]);
  return (
    <>
      <div
        className={`${styles.addtaskspopup_container} ${
          isClosing ? styles.closing : ""
        }`}
        tabIndex={0}
        ref={formRef}
        onClick={handleFormClick}
        onKeyDown={handleKeyDown}
      >
        <div
          className={styles.addtaskspopup_header}
          style={{ color: discard ? "var(--warning_color)" : "" }}
          ref={headerRef}
        >
          <button
            className={styles.closeButton}
            onClick={() => {
              if (!hasFormChanges() && isSummaryPage) {
                handleClose();
                return;
              }
              if (isSummaryPage) {
                setWasTrue(true);
                setDiscard(true);
                setIsSummaryPage(false);
                return;
              }
              if (discard && !wasTrue) {
                setDiscard(false);
                return;
              }
              if (discard && wasTrue) {
                setDiscard(false);
                setWasTrue(false);
                setIsSummaryPage(true);
                return;
              }

              handleClose();
            }}
          >
            <CloseIcon />
          </button>
          <h3 style={{ maxWidth: "90%", textAlign: "center" }}>
            {isSummaryPage && isEdit
              ? `Are you sure you want to update this Subtask?`
              : isSummaryPage
              ? `Are you sure you want to add this Subtask?`
              : discard
              ? `Are you sure you want to discard these Changes?`
              : isEdit
              ? `Edit Subtask`
              : `Add Subtask`}
          </h3>
        </div>
        {isSummaryPage ? (
          <SubtaskSummary
            height={height}
            isEdit={isEdit}
            tracking={
              selectedTrackingId === null
                ? ""
                : trackingData.find((v) => v?.id === selectedTrackingId)?.label
            }
            initialState={initialState}
            inputValues={inputValue}
          />
        ) : discard ? (
          <div className={styles.summary_subtask_main_content}>
            <div
              className={`${
                (inputValue?.name || initialState?.name) &&
                (inputValue.Unit || initialState?.Unit)
                  ? styles.flexContainer
                  : ""
              }`}
            >
              {(isValidValue(inputValue.name) || initialState?.name) && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Name
                    </p>
                    <h4
                      style={{
                        color:
                          initialState?.name !== inputValue?.name && isEdit
                            ? "var(--secondary_color)"
                            : "var(--text-black-87)",
                      }}
                    >
                      {inputValue.name || initialState?.name}
                    </h4>
                  </div>
                </div>
              )}
              {(isValidValue(inputValue.Unit) || initialState?.Unit) && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Unit
                    </p>
                    <h4
                      style={{
                        color:
                          initialState?.Unit !== inputValue?.Unit && isEdit
                            ? "var(--secondary_color)"
                            : "var(--text-black-87)",
                      }}
                    >
                      {inputValue.Unit || initialState?.Unit || "No unit"}
                    </h4>
                  </div>
                </div>
              )}
            </div>
            {(isValidValue(inputValue.Description) ||
              initialState?.Description) && (
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Description
                  </p>
                  <h4
                    style={{
                      color:
                        initialState?.Description !== inputValue?.Description &&
                        isEdit
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {inputValue.Description ||
                      initialState?.Description ||
                      "No description"}
                  </h4>
                </div>
              </div>
            )}
            <div
              className={`${
                (inputValue?.subtaskWeighatages ||
                  initialState?.subtaskWeighatages) &&
                (inputValue.Tracking || initialState?.Tracking)
                  ? styles.flexContainer
                  : ""
              }`}
            >
              {(isValidValue(inputValue.subtaskWeighatages) ||
                initialState?.subtaskWeighatages) && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent_weightage}>
                    <div>
                      <p
                        style={{ color: "var(--text-black-60)" }}
                        className="p_tag_14px"
                      >
                        Weightage
                      </p>
                      <h4
                        style={{
                          color:
                            String(inputValue?.subtaskWeighatages) !==
                              String(initialState?.subtaskWeighatages) && isEdit
                              ? "var(--secondary_color)"
                              : "var(--text-black-87)",
                        }}
                      >
                        {inputValue.subtaskWeighatages ||
                          initialState?.subtaskWeighatages ||
                          "No weightage"}
                      </h4>
                    </div>
                    <WieghtPercentageIcon />
                  </div>
                </div>
              )}
              {(isValidValue(
                selectedTrackingId === null
                  ? ""
                  : trackingData.find((v) => v?.id === selectedTrackingId)
                      ?.label
              ) ||
                initialState?.Tracking?.toLowerCase()) && (
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Tracking
                    </p>
                    <h4
                      style={{
                        color:
                          initialState?.Tracking?.toLowerCase() !==
                            String(inputValue?.Tracking)?.toLowerCase() &&
                          isEdit
                            ? "var(--secondary_color)"
                            : "var(--text-black-87)",
                      }}
                    >
                      {selectedTrackingId === null
                        ? ""
                        : trackingData.find((v) => v?.id === selectedTrackingId)
                            ?.label ||
                          initialState?.Tracking ||
                          "No tracking"}
                    </h4>
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <>
            <div className={styles.addtaskspopup_input1}>
              <FloatingLabelInput
                label="Name"
                id="name"
                focusOnInput={true}
                placeholder="Name"
                value={inputValue?.name}
                isInvalid={errors.name}
                props="one_line"
                onInputChange={(value) => {
                  handleInputChange("name", String(value));
                  clearError("name");
                }}
              />
              <FloatingLabelInput
                label="Unit"
                id="Unit"
                placeholder="Unit"
                isDisabled={true}
                isInvalid={errors.Unit}
                onInputChange={(value) => {
                  handleInputChange("Unit", String(value));
                  clearError("Unit");
                }}
                Icon={
                  isUnitPopUpVisible
                    ? DropDownArrowUpIcon
                    : DropDownCategoryIcon
                }
                iconClick={toggleUnitPopUp}
                value={
                  selectedUnitId === null
                    ? ""
                    : unitData.find((v) => v?.id === selectedUnitId)?.label
                }
              />
              {isUnitPopUpVisible && (
                <div
                  style={{
                    position: "absolute",
                    left: "52%",
                    top: "90%",
                    zIndex: 10,
                  }}
                >
                  <UnitPopup
                    data={unitData}
                    onSelect={handleUnitSelect}
                    selectedId={selectedUnitId}
                  />
                </div>
              )}
            </div>
            <div className={styles.addtaskspopup_description}>
              <FloatingLabelInput
                label="Description"
                id="Description"
                placeholder="Description"
                props="description_prop"
                isInvalid={false}
                value={inputValue?.Description}
                onInputChange={(value) =>
                  handleInputChange("Description", value)
                }
              />
            </div>
            <div className={styles.addtaskspopup_input1}>
              <FloatingLabelInput
                label="Weightage"
                id="subtaskWeighatages"
                maxlength={5}
                placeholder="Weightage"
                type="number"
                preventEnter={true}
                Icon={WieghtPercentageIcon}
                value={inputValue?.subtaskWeighatages}
                isInvalid={errors.subtaskWeighatages}
                onInputChange={(value) => {
                  const numValue = parseFloat(value);

                  if (isNaN(numValue) || numValue > 100) {
                    setErrors((prev) => ({
                      ...prev,
                      subtaskWeighatages: true,
                    }));
                    return;
                  }
                  clearError("subtaskWeighatages");
                  handleSubWeighatages("subtaskWeighatages", numValue);
                }}
              />
              <FloatingLabelInput
                label="Tracking"
                id="Tracking"
                isDisabled={true}
                placeholder="Tracking"
                isInvalid={errors.Tracking}
                onInputChange={(value) => {
                  handleInputChange("Tracking", value);
                  clearError("Tracking");
                }}
                Icon={
                  isTrackingPopUpVisible
                    ? DropDownArrowUpIcon
                    : DropDownCategoryIcon
                }
                iconClick={toggleTrackingPopUp}
                value={
                  selectedTrackingId === null
                    ? ""
                    : trackingData.find((v) => v?.id === selectedTrackingId)
                        ?.label
                }
              />
              {isTrackingPopUpVisible && (
                <div
                  style={{
                    position: "absolute",
                    left: "52%",
                    top: "90%",
                    zIndex: 10,
                  }}
                >
                  <UnitPopup
                    data={trackingData.map(({ id, label }) => ({ id, label }))}
                    onSelect={(item) => {
                      const trackingItem = trackingData.find(
                        (tracking) => tracking.id === item.id
                      );
                      if (trackingItem) {
                        handleTrackingSelect(trackingItem);
                      }
                    }}
                    selectedId={selectedTrackingId}
                  />
                </div>
              )}
            </div>
          </>
        )}
        <div className={styles.addtaskspopup_btngrp}>
          {isSummaryPage ? (
            <>
              <Button
                type="Cancel"
                Content="Back"
                Callback={() => setIsSummaryPage(false)}
              />
              <Button
                type="Next"
                Content="Submit"
                Callback={handleAddSubtask}
              />
            </>
          ) : discard ? (
            <>
              <Button
                type="Cancel"
                Content="No"
                Callback={() => {
                  if (discard && wasTrue) {
                    setDiscard(false);
                    setIsSummaryPage(true);
                    setWasTrue(false);
                    return;
                  }
                  setDiscard(false);
                }}
              />
              <Button
                type="Approve"
                Content="Yes"
                Callback={() => {
                  setIsClosing(true);
                  setTimeout(() => {
                    onClose();
                    setInitialState({
                      name: "",
                      Unit: "",
                      subtaskWeighatages: "",
                      Tracking: "",
                      Description: "",
                    });
                    dispatch(resetInputValues());
                  }, 400);
                }}
              />
            </>
          ) : isEdit ? (
            <>
              <Button type="Cancel" Content="Cancel" Callback={handleClose} />
              <Button type="Next" Content="Update" Callback={handleNextClick} />
            </>
          ) : (
            <>
              <Button type="Cancel" Content="Cancel" Callback={handleClose} />
              <Button type="Next" Content="Add" Callback={handleNextClick} />
            </>
          )}
        </div>
      </div>
    </>
  );
}

export default AddSubTasksPopup;
