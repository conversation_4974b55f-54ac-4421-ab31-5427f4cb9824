/* // Complete Component:: Author:: <PERSON><PERSON> */
.input_box {
    position: relative;
    /* margin-block: 16px; */
    /* margin-block-start: 16px; */
    /* background-color: rgb(31, 135, 135); */
}

.input_field {
    inline-size: 100%;
    border: 1px solid var(--text-black-28);
    background: var(--blur-background);
    border-radius: 1.5rem;
    padding-inline: 22px;
    padding-block: 1rem;
    outline: transparent;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    box-sizing: border-box;
    background-color: transparent;
    font-family: Nunito;
    font-weight: 400;
    font-size: 15px;
    /* line-height: 22px;  */
    letter-spacing: 0px;

}

.input_field:focus {
    outline: none;
    background-color: none;
}

.input_label {
    position: absolute;
    inset-block-start: 14px;
    inset-inline-start: 20px;
    border-radius: 4px;
    /* font-size: 16px; */
    color: var(--text-black-87);
    outline: transparent;
    padding-inline: 3px;
    pointer-events: none;
    transition: all 0.3s ease-in-out;

}

.input_label.active {
    inset-block-start: -10px;
    inset-inline-start: 24px;
    border-radius: 4px;
    font-size: 0.8rem !important;
    outline: transparent;
    color: var(--text-black-87);
    /* background-color: var(--white_background); */
}

.fileInput::placeholder {
    opacity: 0;
    position: absolute;
    inset: 0;
    cursor: pointer;
    z-index: 1;
}

.input_field[type=number]::-webkit-inner-spin-button,
.input_field[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}



.input_summary {
    margin-block: 0.5rem;
    /* background-color: rgb(247, 248, 243); */
    border-radius: 18px;
}

.invalid {
    border: 1px solid red;
}






.customClass {
    /* background-color: purple; */
    word-wrap: break-word;
    white-space: nowrap;
    overflow-wrap: break-word;
    max-width: 98%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.customClassOuter {
    /* background-color: pink; */
    max-width: 270px !important;
    /* Force override */
}