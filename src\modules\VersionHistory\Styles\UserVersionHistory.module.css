.user_version_history_container {
    /* width:27.875rem; */
    /* width: 60%; */
    border: 1px solid var(--primary_color);
    border-radius: 25px;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    display: none;
    margin-top: 1rem;
    width: 60%;
    transition: width 1s ease-in, display 1s ease-in;



}

.user_version_history_container_active {
    display: initial;
    overflow: scroll;
}

.user_version_history_top_section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.user_version_history_top_left {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.user_version_history_top_left_name {
    color: var(--text-black-87);




}

.user_version_history_top_left_id_role {
    display: flex;
    gap: 0.5rem;
}

.user_version_history_top_left_id {

    color: var(--text-black-60);
}

.user_version_history_top_left_role {

    color: var(--text-black-60);
}

.user_version_history_top_left_userimage {
    height: 38px;
    width: 38px;
    border-radius: 50%;
    overflow: hidden;
}

.user_version_history_top_left_userimage img {
    object-fit: cover;
    height: 100%;
    width: 100%;
}


.user_version_history_top_left_roles {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.user_version_history_top_left_roles_text {
    font-weight: 400;
    color: var(--text-black-60);
}



.user_version_history_top_left_role_small_circle {
    width: 6px;
    height: 6px;
    background-color: var(--primary_color);
    border-radius: 50%;
}

.user_version_history_top_right_sno {
    font-weight: 600;
    color: var(--text-white-100);
    height: 35px;
    width: 35px;
    background-color: var(--primary_color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}







/* 🍁User version history subcomponet css from here*/


.uvh_subcomponent_container {
    padding: 0.75rem;
    box-shadow: var(--extra-shdow-second);
    border: 1px solid;
    border-image-source: linear-gradient(130.72deg, rgba(237, 231, 231, 0.07) -22.43%, rgba(251, 251, 251, 0.05) 75.66%);
    border-radius: .75rem;
    height: 5rem;
    overflow: hidden;
    transition: height 0.3s ease-in-out;
    background-color: var(--white-50-background);
    margin-top: 1.5rem;
}

.uvh_subcomponent_container.expanded {
    height: 18rem;
    overflow-y: auto;
}

.uvh_subcomponent_container_top {
    /* padding:0.5rem; */
    display: flex;
    justify-content: space-between;
}

.uvh_subcomponent_container_topleft {}

.uvh_subcomponent_container_topleft_item_status_materials {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.uvh_subcomponent_container_topleft_item_status {
    color: var(--warning_color);
    text-align: left;

}

.uvh_subcomponent_container_topleft_time {
    color: var(--text-black-60);
    line-height: 1.2rem;
}

.uvh_subcomponent_container_topleft_material_text {
    color: var(--primary_color);

}

.uvh_subcomponent_container_topleft_material {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    box-shadow: var(--primary-shadow);
    border-radius: 1rem;
    padding: 0.25rem 0.25rem 0.25rem 0.5rem;
    border: 1px solid transparent;
    background-color: var(--white-50-background);

    cursor: pointer;



}

.selectedMaterial {
    border: 1px solid var(--primary_color);
}

.uvh_subcomponent_container_topleft_material_quantity {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--text-white-100);
    color: var(--text-black-60);
    box-shadow: var(--primary-shadow);


}


.uvh_subcomponent_container_topRight {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    position: relative;
}

.undo_arrow {
    box-shadow: var(--extra-shadow);
    background: var(--text-white-100);
    height: 22px;
    width: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    cursor: pointer;

}

.down_arrow {
    transform: rotate(180deg);
    cursor: pointer;
    transition: all 0.5s ease-in-out;

}

.dropdownOpen {
    transform: rotate(0deg);
    transition: all 0.5s ease-in-out;
    position: relative;
    bottom: 0.25rem;
}


.material_div {

    display: flex;
    flex-wrap: wrap;
    row-gap: .5rem;
    column-gap: 1.5rem;
    height: 0rem;
    transition: max-height 0.3s ease-in-out;
    overflow: hidden;

}


.material_div.expanded {
    height: 13rem;
    overflow-y: auto;
    transition: max-height 0.3s ease-in-out;
}







/* 🍁Material VH Component css from here*/

.material_vh_container {
    display: flex;
    margin-top: 1rem;
    height: fit-content;
    /* background-color: #A80000; */
}



.material_vh_right {}


.material_vh_left_linedot {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.2rem;
    height: auto;
}

.material_vh_left_dot {
    border-radius: 50px;
    width: 6px;
    height: 7px;
    background-color: var(--text-black-87);
    /* display: flex; */
}

.material_vh_left_line {
    height: 100%;
    width: 1px;
    border: 1px dashed var(--text-black-28);
    margin-bottom: 0.5rem;

}

.material_text {
    color: var(--text-black-87);
    position: relative;
    bottom: 0.4rem;
    left: 0.2rem;
}

.material_linedot {
    display: flex;
    align-items: center;
    gap: 0.2rem;
    color: var(--text-black-28);
}



.material_line {
    width: 0.5rem;
    height: 1px;
    border: 1px dashed var(--text-black-28);
}

.material_dot {
    border-radius: 50%;
    width: 6px;
    height: 6px;
    background-color: var(--text-black-28);
}

.material_old_new_div {
    display: flex;
    gap: 0.25rem;
}

.new_data {
    color: var(--text-black-87);
}

.old_data {
    color: var(--text-black-28);

}

.material_detail {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
}