import { useState } from 'react';
import { Tower, FloorDetail, Task } from './TowerTypes';

const API_URL = 'http://localhost:3001';

export const useTowerCrud = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Helper function for API requests
  const apiRequest = async (url: string, method: string, data?: any) => {
    const options: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(url, options);
    
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    
    // For DELETE requests, there might not be a response body
    if (method === 'DELETE') {
      return { success: true };
    }
    
    return await response.json();
  };

  // Create a new tower
  const createTower = async (towerName: string, towerData: Tower, isTower: boolean = true) => {
    try {
      setLoading(true);
      const category = isTower ? 'Towers' : 'NonTowers';
      const result = await apiRequest(`${API_URL}/${category}/${towerName}`, 'POST', towerData);
      setError(null);
      return result;
    } catch (err) {
      setError('Failed to create tower');
      console.error('Error creating tower:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update an existing tower
  const updateTower = async (towerName: string, towerData: Tower, isTower: boolean = true) => {
    try {
      setLoading(true);
      const category = isTower ? 'Towers' : 'NonTowers';
      const result = await apiRequest(`${API_URL}/${category}/${towerName}`, 'PUT', towerData);
      setError(null);
      return result;
    } catch (err) {
      setError('Failed to update tower');
      console.error('Error updating tower:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Delete a tower
  const deleteTower = async (towerName: string, isTower: boolean = true) => {
    try {
      setLoading(true);
      const category = isTower ? 'Towers' : 'NonTowers';
      const result = await apiRequest(`${API_URL}/${category}/${towerName}`, 'DELETE');
      setError(null);
      return result;
    } catch (err) {
      setError('Failed to delete tower');
      console.error('Error deleting tower:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Add a floor to a tower
  const addFloor = async (towerName: string, floorData: FloorDetail, isTower: boolean = true) => {
    try {
      setLoading(true);
      const category = isTower ? 'Towers' : 'NonTowers';
      const result = await apiRequest(`${API_URL}/${category}/${towerName}/FloorDetails`, 'POST', floorData);
      setError(null);
      return result;
    } catch (err) {
      setError('Failed to add floor');
      console.error('Error adding floor:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update a floor in a tower
  const updateFloor = async (
    towerName: string, 
    floorNumber: number, 
    floorData: FloorDetail, 
    isTower: boolean = true
  ) => {
    try {
      setLoading(true);
      const category = isTower ? 'Towers' : 'NonTowers';
      const result = await apiRequest(
        `${API_URL}/${category}/${towerName}/FloorDetails/${floorNumber}`, 
        'PUT', 
        floorData
      );
      setError(null);
      return result;
    } catch (err) {
      setError('Failed to update floor');
      console.error('Error updating floor:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Delete a floor from a tower
  const deleteFloor = async (
    towerName: string, 
    floorNumber: number, 
    isTower: boolean = true
  ) => {
    try {
      setLoading(true);
      const category = isTower ? 'Towers' : 'NonTowers';
      const result = await apiRequest(
        `${API_URL}/${category}/${towerName}/FloorDetails/${floorNumber}`,
        'DELETE'
      );
      setError(null);
      return result;
    } catch (err) {
      setError('Failed to delete floor');
      console.error('Error deleting floor:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Add a task to a floor
  const addTask = async (
    towerName: string, 
    floorNumber: number, 
    taskData: Task, 
    isTower: boolean = true
  ) => {
    try {
      setLoading(true);
      const category = isTower ? 'Towers' : 'NonTowers';
      const result = await apiRequest(
        `${API_URL}/${category}/${towerName}/FloorDetails/${floorNumber}/Tasks`, 
        'POST', 
        taskData
      );
      setError(null);
      return result;
    } catch (err) {
      setError('Failed to add task');
      console.error('Error adding task:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update a task in a floor
  const updateTask = async (
    towerName: string, 
    floorNumber: number, 
    taskIndex: number, 
    taskData: Task, 
    isTower: boolean = true
  ) => {
    try {
      setLoading(true);
      const category = isTower ? 'Towers' : 'NonTowers';
      const result = await apiRequest(
        `${API_URL}/${category}/${towerName}/FloorDetails/${floorNumber}/Tasks/${taskIndex}`, 
        'PUT', 
        taskData
      );
      setError(null);
      return result;
    } catch (err) {
      setError('Failed to update task');
      console.error('Error updating task:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Delete a task from a floor
  const deleteTask = async (
    towerName: string, 
    floorNumber: number, 
    taskIndex: number, 
    isTower: boolean = true
  ) => {
    try {
      setLoading(true);
      const category = isTower ? 'Towers' : 'NonTowers';
      const result = await apiRequest(
        `${API_URL}/${category}/${towerName}/FloorDetails/${floorNumber}/Tasks/${taskIndex}`,
        'DELETE'
      );
      setError(null);
      return result;
    } catch (err) {
      setError('Failed to delete task');
      console.error('Error deleting task:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update card quantity
  const updateCardQuantity = async (towerName: string, floorNumber: number, taskIndex: number, newQuantity: number, isTower: boolean = true) => {
    try {
      setLoading(true);
      const category = isTower ? 'Towers' : 'NonTowers';
      
      // First get the current task data
      const towerResponse = await apiRequest(`${API_URL}/${category}/${towerName}`, 'GET');
      const floorDetails = towerResponse.FloorDetails || [];
      const floor = floorDetails.find((f: any) => f.FloorNumber === floorNumber);
      
      if (!floor || !floor.Tasks || !floor.Tasks[taskIndex]) {
        throw new Error('Task not found');
      }
      
      // Update the quantity
      const updatedTask = { ...floor.Tasks[taskIndex], Quantity: newQuantity };
      
      // Send the update request
      const result = await apiRequest(
        `${API_URL}/${category}/${towerName}/FloorDetails/${floorNumber}/Tasks/${taskIndex}`, 
        'PUT', 
        updatedTask
      );
      
      setError(null);
      return result;
    } catch (err) {
      setError('Failed to update quantity');
      console.error('Error updating quantity:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    createTower,
    updateTower,
    deleteTower,
    addFloor,
    updateFloor,
    deleteFloor,
    addTask,
    updateTask,
    deleteTask,
    updateCardQuantity,
  };
};
