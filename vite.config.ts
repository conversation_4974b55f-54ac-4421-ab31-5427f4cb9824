import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import fs from "fs";
export default defineConfig({
  plugins: [react()],
  resolve: {
    extensions: [".ts", ".tsx", ".js", ".jsx"], // Ensure correct extensions are listed
  },
  build: {
    outDir: "dist",
    assetsDir: "assets",
  },
  server: {
    // headers: {
    //   "Cross-Origin-Opener-Policy": "same-origin",
    //   "Cross-Origin-Embedder-Policy": "require-corp",
    // },
    //   https:{
    //     key:fs.readFileSync('./ssl/localhost-key.pem'),
    //     cert:fs.readFileSync('./ssl/localhost.pem')
    //   },
  },

  define: { global: "window" },
});
