import underconsvideo from "../../../assets/videos/undercons.mp4";
import TMMMNav from "../TMMMNav";

const UnderConstruction: React.FC = () => {
  return (
    <>
      <TMMMNav
        Label={"Categories"}
        variant={"Under Contruction"}
        TargetForm={"Under Contruction"}
        isHide={false}
      />

      <div
        style={{
          marginTop: "2rem",
   
          height: "100%",
          width: "100%",
        }}
      >
        {/* <div className={styles.cardview}> */}

        <video src={'/underconstruction.mp4'} autoPlay={true} muted={true} loop></video>
      </div>
      {/* </div> */}
    </>
  );
};

export default UnderConstruction;
