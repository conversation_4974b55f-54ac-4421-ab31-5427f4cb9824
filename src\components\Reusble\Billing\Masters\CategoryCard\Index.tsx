import React, { useState } from "react";
import { Twodots } from "../../../../../assets/icons";
import { CardItems } from "../../../TaskMaster/CardItems";
import { useToast } from "../../../../../hooks/ToastHook";

import styles from "./Styles/CategoryCard.module.css";
import CardDotPopup from "../../../Global/MainCardPopup";
import { useNavigate } from "react-router-dom";
import { setNavigate } from "../../../../../redux/features/Modules/Reusble/navigationSlice";
import { useAppDispatch } from "../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../redux/store";

type CardItemType = {
  title: string;
  name: number | string;
};

type MasterCardProps = {
  data: {
    _id: string;
    title: string;
    total?: number;
    unit?: string;
    path?: string;
    items: CardItemType[];
  };
  editData?: any; //will change this in further changes
  callbackEditData?: () => void;
};

const CategoryCard: React.FC<MasterCardProps> = ({
  data,
  editData,
  callbackEditData,
}) => {
  const { title, unit, items } = data;
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const showToast = useToast();
  // const { popups, categoryPopup } = useSelector(
  //   (state: RootState) => state.popup
  // );
  // console.log('poopups:>>',categoryPopup);
  // console.log('mastercard_header_righticon',categoryPopup)
  const togglePopup = () => {
    setIsPopupOpen((prev) => !prev);
    callbackEditData && callbackEditData();
  };

  return (
    <div
      className={styles.mastercard_outercontainer}
      onClick={() => {
        navigate(`/${data?.path}/${data?._id}`);
        dispatch(
          setNavigate({
            title: data?.title ?? "",
            route: `/${data?.path}/${data?._id}`,
          })
        );
      }}
    >
      <div className={styles.mastercard_rightcontainer}>
        <div className={styles.mastercard_header}>
          {isPopupOpen && (
            <CardDotPopup
              varient="category"
              editData={editData}
              setIsPopupOpen={setIsPopupOpen}
            />
          )}
          <div className={styles.mastercard_header_left}>
            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: "8px",
              }}
            >
              <h3 className={styles.categorycard__header_heading}>{title}</h3>
              <div className={styles.heading_numeric_values}>
                {data?.total ?? "0"}
              </div>
            </div>
          </div>
          <div
            className={styles.mastercard_header_righticon}
            onClick={(e) => {
              e.stopPropagation();
              if (!navigator.onLine) {
                showToast({
                  messageContent: "Oops! no internet connection!",
                  type: "danger",
                });
                return;
              }

              togglePopup();
            }}
          >
            <Twodots />
            {isPopupOpen&&<div style={{height:'100%',width:"100%",zIndex:2,position:"absolute"}}></div>}
          </div>
        </div>

        <div
          className={styles.mastercard_items}
          style={{
            display: "grid",
            gridTemplateColumns:
              data.path === "tools-master"
                ? "repeat(3, 1fr)"
                : "repeat(2, 1fr)",
            gridTemplateRows:
              data.path === "material-master" ? "repeat(2, auto)" : "auto",
          }}
        >
          {items.map((item, index) => (
            <CardItems key={index} title={item.title} name={item.name} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default CategoryCard;
