

.progress_overview_card_outer_container{
    padding: 0.25rem;

    border-radius: 0.75rem;
    border: 1px solid   rgba(237, 231, 231, 0.5) ;
    
    position: relative;

    height: 100%;
    }
    
    .progress_overview_card_inner_container{
    height: 100%;
    width: 100%;
    /* background: #0ea3a31a; */
    border-radius: 0.75rem;
    /* padding: 0.25rem; */

    

    }
    .with_icon{
        display: grid;
        grid-template-columns: 4fr 7fr;
        gap: 0.25rem;
    }
    
    .progress_overview_card_icon_container{
        width: 100%;
        min-width: 1.25rem;
        height: 100%;
        /* height: 2.4rem;
        width: 2.4rem; */
        display: flex;
        align-items:center ;
        background-color: var(--primary_background);
        justify-content: center;
        border-radius: 0.4rem;
    
    }
    
    .progress_overview_card_image_container img{
        
        /* width: 100%;
        height: 100%; */
        
        object-fit: cover;
        
    }
    
    
    .progress_overview_card_key_value_section{
        display: flex;
        flex-direction: column;
        padding:  0.25rem 0;
        justify-content:space-between ;
        height: auto;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        
    }
    



.progress_overview_key_text{

    color: var(--text-black-60);
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;


}

.progress_overview_value_text{
    color: var(--text-black-87);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}