import styles from "../Styles/ValuePopup.module.css";

interface ValuePopupSummaryProps {
  selectedAction: string;
  quantity: string;
}
function ValuePopupSummary({
  selectedAction,
  quantity,
}: ValuePopupSummaryProps) {
  return (
    <>
      <div className={styles.summaryDivData}>
        <div className={styles.summaryDataContent}>
          <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
            Type
          </p>
          <h4 style={{ color: "var(--text-black-87)" }}>
            {selectedAction || "No action selected"}
          </h4>
        </div>
      </div>
      <div className={styles.summaryDivData}>
        <div className={styles.summaryDataContent}>
          <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
            Quantity
          </p>
          <h4 style={{ color: "var(--text-black-87)" }}>
            {quantity || "No Quantity"}
          </h4>
        </div>
      </div>
    </>
  );
}

export default ValuePopupSummary;
