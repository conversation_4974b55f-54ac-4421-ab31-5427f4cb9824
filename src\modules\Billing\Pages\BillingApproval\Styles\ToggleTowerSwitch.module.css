.toggleCheckbox {
  display: none;
}

.toggle_main {
  display: flex;

  align-items: center;
  background: var(--main_background);
  border-radius: 10rem;
  max-width: 100%;
}

.toggleContainer {
  position: relative;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  border-radius: 100px;
  max-width: 100%;

  height: 42px;
  cursor: pointer;
  align-items: center;
}

.toggleContainer::before {
  content: "";
  position: absolute;
  width: 50%;
  height: 100%;
  left: 0.2rem;
  border-radius: 100px;
  background: var(--primary_color);
  transition: all 0.3s ease;
}

.toggleContainer div {
  display: flex;    
  justify-content: center;
  align-items: center;
  z-index: 1;
  transition: color 0.3s ease;
}

.toggleCheckbox:checked+.toggleContainer::before {
  left: 47%;
}

.toggleCheckbox+.toggleContainer div {
  color: var(--text-black-60);
}

.toggleCheckbox:checked+.toggleContainer div:first-child {
  color: var(--text-black-60);
}

.toggleCheckbox:checked+.toggleContainer div:last-child {
  color: var(--text-white-100);
}

.toggleCheckbox+.toggleContainer div:first-child {
  color: var(--text-white-100);
}


.targetBubbleUnchecked {
  background-color: var(--primary_color) !important;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white !important;
  margin-left: 0.25rem;
  border-radius: 50%;
  min-width: 22px;
  transition: color 0.3s ease;
  min-height: 22px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.targetBubblechecked {
  background-color: var(--main_background);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--primary_color) !important;
  margin-left: 0.25rem;
  border-radius: 50%;
  transition: color 0.3s ease;
  min-width: 22px;
  min-height: 22px;
  transition: background-color 0.3s ease, color 0.3s ease;
}



.billing_toggle_bubble {
  background-color: var(--primary_background) !important;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-black-28) !important;
  margin-left: 0.25rem;
  border-radius: 50%;
  min-width: 22px;
  transition: color 0.3s ease;
  min-height: 22px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.site_task_details_tower {
  padding: 0.25rem;
  background-color: var(--primary_background);
}
.toggleProjectviewSwitch{
  background-color: var(--primary_background);
  padding: 0.3rem;
}