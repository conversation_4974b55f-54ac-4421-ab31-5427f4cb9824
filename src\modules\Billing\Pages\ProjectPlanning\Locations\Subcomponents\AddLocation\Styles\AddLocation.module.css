.add_new_projectForm_overlay{
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
}

.taskcard_header_leftpara {
  position: absolute;
  top: 60%;
  right: 0.75rem;
  transform: translateY(-50%);
  margin: 0;
  pointer-events: none;
  z-index: 2;
  font-size: 0.95rem;
  background-color: var(--primary_background);
  border-radius: 2rem;
  max-width: 8rem;
  text-align: center;
  box-shadow: 0px 4px 8px 0px #ffffff;
  border: 1px solid;
  padding: 0.2rem 0.6rem;
  border-image-source: linear-gradient(
    130.72deg,
    rgba(237, 231, 231, 0.07) -16.06%,
    rgba(251, 251, 251, 0.05) 82.03%
  );
}

.add_new_projectForm_container {
    position: fixed;
    top: 7rem;
    right: 1.5rem;
    transform: translate(0%, 0%);
    background: var(--blur-background);
    padding: 1.25rem;
    backdrop-filter: blur(60px);
    box-shadow: var(--extra-shadow-five);
    border-radius: 2.6rem;
    z-index: 3;
    width: 34rem;
    height: calc(100% - 8.5rem);
    animation: slideIn 0.5s ease-out;
  }
  
  .add_new_projectForm_header {
    color: var(--primary_color);
    display: flex;
    justify-content: center;
    padding: 0.6rem;
  }
  
  .closeButton {
    position: absolute;
    top: 0.625rem;
    right: 0.625rem;
    padding: 1rem;
    background: transparent;
    border: none;
    cursor: pointer;
  }
  
  .add_new_projectForm_inpts {
    max-height: 68vh;
    overflow-y: auto;
  }
  
  .add_new_projectForm_rowOneInputs {
    display: flex;
    gap: 1rem;
    justify-content: space-between;
    position: relative;
  }
  
  .add_new_projectForm_btngrp {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    padding: 1rem;
    display: flex;
    justify-content: center;
    gap: 1rem;
    border-radius: 0rem 0rem 2.5rem 2.5rem;
  }
  
  .tcr_fileNames_div {
    /* margin-left: 1rem; */
    display: flex;
    flex-wrap: wrap;
  }
  
  .tcr_fileNames {
    background-color: var(--primary_background);
    color: var(--text-black-87);
   
    border-radius: 100px;
    position: relative;
    border: 1px solid;
    border-image-source: var(--primary-bg-gradient);
    box-shadow: var(--extra-shdow-four);
  }
  
  .tcr_fileNames:hover .file_cross_div {
    display: block !important;
  }
  /* styles for add project summary start by Rattandeep singh */
  
  .summaryDivData {
    display: flex;
    align-items: center;
  }
  
  .summaryDataContent {
    display: flex;
    flex-direction: column;
    background: var(--main_background);
    border-radius: 0.75rem;
    width: 30.8rem;
    min-height: 3rem;
    padding: 1rem;
    white-space: normal;
    margin: 0.6rem;
    line-height: 1.363rem;
    text-align: left;
  }
  
  .summaryItem {
    display: flex;
  }
  .summaryItems {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }
  /* styles for add project summary end by Rattandeep singh */
  .ratePopup {
    background-color: var(--main_background);
    width: 100%;
    position: absolute;
    display: flex;
    top: 100%;
    z-index: 99;
    align-items: center;
    padding-block: 1rem;
   
    border-radius: 1rem;
    height: 5rem;
  }
  .ratePopupinner {
    
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .notAvailable {
    opacity: 1;
    pointer-events: none;
    position: absolute;
    transition: opacity 0.3s ease-in-out;
    color: var(--warning_color);
  }
  .itemRatecontainer:hover + .notAvailable {
    opacity: 1;
    pointer-events: auto;
  }
  
  .add_new_projectForm_container.closing {
    animation: slideOut 0.5s ease-out;
  }

  .progress_bar_container_drawing_addlocation {
       position: absolute;
    left: 35px;
    width: 86%;
    height: 4px;
    bottom: 594px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 106px;
    overflow: hidden;
    z-index: 5;
}

.progress_bar_drawing_addlocation {
  height: 100%;
  width: 50%;
  background-color: var(--primary_color);
  position: absolute;
  animation: progressAnimation 1.5s linear infinite;
}


@keyframes progressAnimation {
  0% {
    left: -60%;
  }
  100% {
    left: 100%;
  }
}
  
  @keyframes slideIn {
    from {
      transform: translate(100%, 0%);
    }
  
    to {
      transform: translate(0%, 0%);
    }
  }
  
  @keyframes slideOut {
    from {
      transform: translate(0%, 0%);
    }
  
    to {
      transform: translate(100%, 0%);
    }
  }
  