import React, { useCallback, useEffect, useState } from "react";
import PlanningProgress from "./PlanningProgress";
import styles from "./Styles/Planning.module.css";
import PlanningTable from "./PlanningTable";

import { Outlet, useLocation, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../../redux/store";
import { TransformedTask } from "../../../../../redux/api/Modules/Billing/Interfaces/BillingInterfaces";
import { getTowerLocationByObjectId } from "../../../../../redux/hooks/Modules/Billing/billinghooks";
import {
  addProgressTowerDataByProject,
  getTransformedRoutes,
  selectLocationTaskId, 
} from "../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/projectPlanningSlice";
import AddCategoryType from "../../../../../components/Reusble/Global/AddCategoryType";
import { closePopup } from "../../../../../redux/features/Modules/Reusble/popupSlice";
import {
  useAddTaskInPlanningRouteMutation,
  useGetAllTasksFromTaskMasterQuery,
} from "../../../../../redux/api/Modules/Billing/ProjectPlanningApi";
import { useAppSelector } from "../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { useToast } from "../../../../../hooks/ToastHook";
import Sidebar from "../../../../../components/Common/Sidebar";
import { initializeDatabase } from "../../../../../functions/functions";

const Planning: React.FC = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const params = useParams();
  console.log(params, "these are params");
  const { towerLocationId: objectId } = useParams();
  //object id will come from top level component
  const { data, refetch } = getTowerLocationByObjectId(objectId);
  const { data: tasks } = useGetAllTasksFromTaskMasterQuery(undefined);
  const [AddTaskInPlanningRoute] = useAddTaskInPlanningRouteMutation();

  console.log("first<<<<<<<<<<", tasks);

  console.log("Dddddata in Planning:", data?.data);

  const selectedLocationId = useAppSelector(
    (state) => state.projectPlanning.selectedLocationId
  );
  console.log(selectedLocationId, "thisisselectedlocationid");
  const progressTowerData = useAppSelector(
    (state) => state.projectPlanning.progressTowerData
  );
  const { popups } = useSelector((state: RootState) => state.popup);

  const [useData, useSetData] = useState<TransformedTask[]>();

  // const taskId = progressTowerData?.TaskId;

  // const finalTransformedRoutes =
  // console.log("Final Transformed Routes:", finalTransformedRoutes);

  const showToast = useToast();

  
  useEffect(() => {
    dispatch(selectLocationTaskId(null));
  }, [location.pathname, dispatch]);

  return (
    <>
      {/* <div className={styles.project_planning_outer_container}>
        <div className={styles.projectPlanning_container}>
          <div className={styles.planning_progress_bar}>
            <PlanningProgress />
          </div>
          <div className={styles.planningTable_container}>
            <Outlet />
          </div>
        </div>
      </div> */}

      <div className={styles.projectPlanning_container}>
        <div className={styles.planning_progress_bar}>
          <PlanningProgress />
        </div>
        <div className={styles.planning_Table_OuterMostContainer}>
          <Outlet />
        </div>
      </div>

      {popups["AddTaskPlanning"] && (
        <AddCategoryType
          title="Add Task"
          data={
            tasks?.data
              ?.filter(
                (taskItem) =>
                  !progressTowerData?.some(
                    (progressItem) =>
                      progressItem?.Master_taskid === taskItem?.id
                  )
              )
              ?.map((task) => ({
                category: task?.name,
                id: task?._id,
              })) || []
          }
          initialSelected={[]}
          label="Task"
          singleSelected={true}
          placeholder="Select"
          buttonLabel="Add Category"
          onSelect={async (item) => {
            const data = {
              taskName: item[0]?.category,
              taskId: item[0]?.id,
              location_id: selectedLocationId,
            };
            const res = await AddTaskInPlanningRoute(data);

            if (res.error) {
              showToast({
                messageContent: "Oops! Something went wrong!",
                type: "danger",
              });
              return;
            }
            refetch().then((updatedTasks) => {
              console.log(updatedTasks, "these are updated tasks");
              if (updatedTasks.data) {
                dispatch(
                  addProgressTowerDataByProject(updatedTasks?.data?.data)
                );
              }
            });
            showToast({
              messageContent: "Task Added Successfully!",
              type: "success",
            });
          }}
          onClose={() => dispatch(closePopup("AddTaskPlanning"))}
        />
      )}
    </>
  );
};

export default Planning;
