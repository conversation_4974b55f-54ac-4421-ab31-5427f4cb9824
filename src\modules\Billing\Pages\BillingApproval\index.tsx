import React, { useState } from "react";
import styles from "./styles/BillingApproval.module.css";
import {  useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { RootState } from "../../../../redux/store";
import TEFormComponent from "../../../TaskMaster/Pages/TriggerEventForm";
import MTConfirmationDialogForm from "./Subcomponents/MTConfirmationDialogForm";
import TargetList from "./Subcomponents/TargetList";
import TargetDetails from "./Subcomponents/TargetDetails";
import { closeDialogs } from "../../../../redux/features/Modules/Billing/BillingApproval/Slices/BillingApprovalSlice";

const BillingApproval: React.FC = () => {
  // Get projectId from URL params
  const { projectId } = useParams<{ projectId: string }>();
  
  const [selectedTower, setSelectedTower] = useState<string | null>(null);
  const [isTowerSelected, setIsTowerSelected] = useState(true);
   const {
    isTaskApprovalFormOpen,
    isDeclineDialogOpen,
    isApproveDialogOpen,
    isSummaryDialogueOpen,
  } = useSelector((state: RootState) => state.monthlyTarget);
const dispatch = useDispatch();
  const mtSubtaskModifiedData = useSelector(
    (state: RootState) => state.monthlyTarget.modifiedMonthlyTargetSubTaskData
  );
  const mtTaskModifiedData = useSelector(
    (state: RootState) => state.monthlyTarget.modifiedMonthlyTargetTaskData
  );
  
  return (
    <div className={styles.monthlyTargetContainer}>
      <div className={styles.targetListContainer}>
        <TargetList 
          selectedTower={selectedTower}
          setSelectedTower={setSelectedTower}
          isTowerSelected={isTowerSelected}
          setIsTowerSelected={setIsTowerSelected}
          projectId={projectId} 
        />
      </div>
      <div className={styles.target_details_outer_container}>
        <TargetDetails 
          selectedTowerName={selectedTower}
          isTower={isTowerSelected}
          projectId={projectId}
        />
      </div>
      {/* Dialog components */}
      {isDeclineDialogOpen && (
        <MTConfirmationDialogForm
          inputDecline={true}
          header="Are you sure you want to decline this request?"
          headerColor={"var(--warning_color)"}
          callBack1={() => dispatch(closeDialogs())}
          button1Content="Cancel"
          button1Type="Cancel"
          button2Content="Decline"
          button2Type="Decline"
          callBackCross={() => dispatch(closeDialogs())}
        />
      )}
      {isApproveDialogOpen && (
        <MTConfirmationDialogForm
          data={mtSubtaskModifiedData}
          header="Are you sure you want to approve this request?"
          headerColor={"var(--primary_color)"}
          callBack1={() => dispatch(closeDialogs())}
          button1Content="Cancel"
          button1Type="Cancel"
          button2Content="Approve"
          button2Type="Approve"
          callBackCross={() => dispatch(closeDialogs())}
        />
      )}
      {isSummaryDialogueOpen && (
        <TEFormComponent
          mode="add"
          header="Summary"
          headerColor="var(--primary_color)"
          data={mtSubtaskModifiedData}
          callBackCross={() => dispatch(closeDialogs())}
        />
      )}
      {isTaskApprovalFormOpen && (
        <MTConfirmationDialogForm
          header="Are you sure you want to approve this request?"
          headerColor="var(--primary_color)"
          data={mtTaskModifiedData}
          callBack1={() => dispatch(closeDialogs())}
          button1Content="Cancel"
          button1Type="Cancel"
          button2Content="Approve"
          button2Type="Approve"
          callBackCross={() => dispatch(closeDialogs())}
        />
      )}
    </div>
  );
};

export default BillingApproval;
