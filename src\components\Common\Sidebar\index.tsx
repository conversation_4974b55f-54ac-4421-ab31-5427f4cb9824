import { useDispatch, useSelector } from "react-redux";
import styles from "./Styles/Sidebar.module.css";

import { useNavigate } from "react-router-dom";
import {
  currentActiveSubRouteIndex,
  setLabel,
} from "../../../redux/features/Modules/Reusble/sidebarSlice";
import { RootState } from "../../../redux/store";
import { sidebarItemProps, SubRoute } from "../CommonInterface/CommonInterface";
import {
  setSearchKey,
  setTypeSearchKey,
} from "../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";

const Sidebar = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const currentpaths = useSelector((state: RootState) => state.currentpaths);
  const allsubroutes = useSelector(
    (state: RootState) => state.currentpaths.subRoutes
  );
  return (
    <div className={`${styles.sidebar_container}`}>
      <div className={`${styles.sidebar_inner_container}`}>
        {allsubroutes?.map((item: SubRoute) => (
          <SidebarItem
            label={item?.label}
            // SvgElement={item?.icon}
            isSelected={
              currentpaths?.currentActiveSubRouteIndex ===
              allsubroutes.indexOf(item)
            }
            onclick={() => {
              dispatch(currentActiveSubRouteIndex(allsubroutes.indexOf(item)));
              dispatch(setLabel(item?.label));
              navigate(item?.route);
            }}
          />
        ))}
      </div>
    </div>
  );
};

const SidebarItem: React.FC<sidebarItemProps> = ({
  // SvgElement,
  label,
  isSelected,
  onclick,
}) => {
  return (
    <>
      <div
        onClick={onclick}
        style={
          isSelected
            ? { background: "var(--primary_color)", color: "white" }
            : {}
        }
        className={`${styles.sidebar_items}`}
      >
        {/* <SvgElement
          color={isSelected ? "var(--text-white-100)" : "var(--text-black-87)"}
        /> */}
        {label}
      </div>
    </>
  );
};
export default Sidebar;
