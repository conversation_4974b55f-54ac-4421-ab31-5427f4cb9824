import React, { useEffect, useRef, useState } from "react";

import { useDispatch, useSelector } from "react-redux";
import { RootState, store } from "../../../../../../redux/store";
import {
  setChangeAPiFlag,
  setIsChangeSubtask,
  settaskChangeAPiFlag,
  updateSubtaskData,
  updateTaskData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import {
  closePopup,
  openPopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";

import WorkInstructionsPopup, {
  PhotoSection,
} from "../../../../../../components/Reusble/TaskMaster/WorkInstructionsPopup";
import { DeleteIcon, SuryconLogo } from "../../../../../../assets/icons";
import AddToolTip from "../../../../../../components/Reusble/Global/AddToolTip";
import TCRpopup from "../../../../../../components/Reusble/Global/TCRpopup";
import SubTaskTcrPopup from "../SubTaskTcrPopup/SubTaskTcrPopup";
import ControlPlanpopup from "../../../../../../components/Reusble/TaskMaster/ControlPlanpopup";
import FMEApopup from "../../../../../../components/Reusble/TaskMaster/FMEApopup";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import styles from "../../Styles/SubtaskCreationForm.module.css";
import {
  ControlPlanDetails,
  FailureModeDetails,
  requiredthings,
} from "../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import SubTaskWorkInstructionsPopup from "../SubTaskWorkInstucionsPopup/SubTaskWorkinstructionsPopup";
import { setWorkInstructionDelete } from "../../../../../../redux/features/Modules/TaskMaster/Slices/WorkInstructionSlice";
import { useAppSelector } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { saveSyncData } from "../../../../../../Backup/BackupFunctions/BackupFunctions";
import {
  fileTypeMapper,
  getFileName,
  initializeDatabase,
  isBase64,
} from "../../../../../../functions/functions";
import { useToast } from "../../../../../../hooks/ToastHook";
import { useParams } from "react-router-dom";
import { isEqual } from "lodash";

interface SubTaskCreationMethodProps {
  isEdit?: boolean;
}
const SubTaskCreationMethod: React.FC<SubTaskCreationMethodProps> = ({
  isEdit = false,
}) => {
  const [popupIdParent, setPopupIdParent] = useState<any>();
  const [categortDataParent, setCategoryDataParent] = useState<any>();
  const [deleteIdParent, setDeleteIdParent] = useState<any>();
  const handleSetPopupId = (id: string) => {
    setPopupIdParent(id);
  };

  const handlePopupName = (name: string) => {
    setDeleteIdParent(name);
  };

  const handleCategoryData = (data: any) => {
    setCategoryDataParent(data);
  };
  const dispatch = useDispatch();
  const { popups } = useSelector((state: RootState) => state.popup);
  const subTaskData = useSelector(
    (state: RootState) =>
      state.taskMaster.currentSubtaskData || {
        name: "",
        Unit: "",
        description: "",
        subtaskWeighatages: 0,
        Tracking: "",
        MaterialId: [],
        ToolId: [],
        MachinaryId: [],
        ManpowerId: [],
        AdminId: [],
        AssigneeId: [],
        Reporter: [],
        MethodId: {
          work_instruction_id: [],
          task_closing_requirement: [],
          Controlplan: [],
          Failuremode: [],
        },
      }
  );
  const showToast = useToast();
  const [controlPlanInitialState, setControlPlanInitialState] = useState<
    ControlPlanDetails | undefined
  >({
    _id: "",
    description: "",
  });

  const failureModeData = subTaskData?.MethodId?.Failuremode;

  const [fmeaInitialState, setFmeaInitialState] =
    useState<FailureModeDetails>();
  const [allSubtasks, SetallSubtasks] = useState<any[]>([]);
  const [requiredThingsDelete, setRequiredThingsDelete] = useState<
    FailureModeDetails | undefined
  >();
  const [requiredThingsDeleteName, setRequiredThingsDeleteName] =
    useState<string>();
  const [deleteId, setDeleteId] = useState<string>();
  const isDeletedNext = useAppSelector(
    (state) => state.isDeletedSLice.isDeletedNext
  );
  const { taskId, subtaskId } = useParams<{
    taskId: string;
    subtaskId: string;
  }>();

  useEffect(() => {
    async function fetchData() {
      const dbName = await initializeDatabase("SubTaskForm");
      const response = await window.electron.getSubtasksByTaskId({
        dbName,
        taskId,
      });
      SetallSubtasks(response || []);
      // Update Redux state here with response
    }
    fetchData();
  }, [taskId]);
  // console.log('requiredThingsDelete?.file?.name ::',requiredThingsDelete?.file)

  // fileTypeMapper(requiredThingsDelete?.file)
  const handleFmeaDataChange = (data: {
    _id: number | string;
    Description: string;
    solution: string;
    severity: string;
  }) => {
    if (fmeaInitialState) {
      dispatch(
        updateSubtaskData({
          ...subTaskData,
          MethodId: {
            ...subTaskData.MethodId,
            Failuremode: failureModeData?.map((item) => {
              return item._id === data._id ? data : item;
            }),
          },
        })
      );

      saveSyncData(
        {
          ...subTaskData,
          MethodId: {
            ...subTaskData.MethodId,
            Failuremode: failureModeData?.map((item) => {
              return item._id === data._id ? data : item;
            }),
          },
        },
        "time",
        "SubTaskForm"
      );

      setFmeaInitialState(undefined);

      showToast({
        messageContent: "Quality Ensuring Measures Updated Successfully",
        type: "success",
      });

      dispatch(setChangeAPiFlag(true));
      return;
    }

    dispatch(
      updateSubtaskData({
        ...subTaskData,
        MethodId: {
          ...subTaskData.MethodId,
          Failuremode: [...(failureModeData || []), data],
        },
      })
    );

    saveSyncData(
      {
        ...subTaskData,
        MethodId: {
          ...subTaskData.MethodId,
          Failuremode: [...(failureModeData || []), data],
        },
      },
      "time",
      "SubTaskForm"
    );

    showToast({
      messageContent: "Quality Ensuring Measures Added Successfully",
      type: "success",
    });
    dispatch(setChangeAPiFlag(true));
  };

  const handleToggleDropdown = (name: string) => {
    dispatch(openPopup(name));
  };

  const workInstructionData: any = useAppSelector(
    (state) =>
      state.taskMaster.currentSubtaskData?.MethodId?.work_instruction_id || []
  );

  const taskClosingRequirementData =
    subTaskData?.MethodId?.task_closing_requirement;

  console.log("workinstructionData", workInstructionData);

  //formating work instruction data
  const formattedWorkInstructionData = workInstructionData?.map(
    (data: any) => ({
      id: data?._id,
      data: {
        description: data?.Description,
        file: data?.file,
        category: data?.optionselected,
        photoDetails: data?.photoref?.photos?.map(
          (photo: any, index: number) => ({
            id: index + 1,
            photo: photo.photo,
            fileName: photo.fileName,
            referenceDetail: photo.details,
          })
        ),
      },
    })
  );

  console.log("required things delete", requiredThingsDelete);
  //this is for the these for mentioned properties inside the work instructions as they are entered after adding work instruction's main body
  const [popupCategoryData, setPopupCategoryData] = useState<{
    [key: string]: {
      Manpower: requiredthings[];
      Machinery: requiredthings[];
      Tools: requiredthings[];
      Materials: requiredthings[];
    };
  }>({});

  useEffect(() => {
    if (!Array.isArray(workInstructionData) || workInstructionData.length === 0)
      return;

    console.log("sync check", workInstructionData);

    // Prepare new popup data
    const newPopupCategoryData = workInstructionData.reduce((acc, data) => {
      acc[`popup-${data._id}`] = {
        Manpower: data?.manpowerId || [],
        Machinery: data?.machinaryId || [],
        Tools: data?.toolsId || [],
        Materials: data?.materialId || [],
      };
      return acc;
    }, {});

    // Use current popupCategoryData directly, not via callback
    if (!isEqual(popupCategoryData, newPopupCategoryData)) {
      console.log("Updating popupCategoryData");
      setPopupCategoryData(newPopupCategoryData);
    } else {
      console.log("No change in popupCategoryData");
    }
  }, [workInstructionData]);

  const formattedTaskClosingRequirementData =
    taskClosingRequirementData?.map((data) => ({
      id: data?._id,
      data: {
        description: data?.Description,
        file: data?.file,
        category: data?.optionselected,
        photoDetails: data?.photoref?.photos?.map((photo, index) => ({
          id: index + 1,
          photo: photo.photo,
          fileName: photo.fileName,
          referenceDetail: photo.details,
        })),
      },
    })) || [];

  const controlPlandata = subTaskData?.MethodId?.Controlplan ?? [];

  //will remove when working on this
  // const [tcrData, setTcrData] = useState<TCRDataType[]>(
  //   formattedTaskClosingRequirementData || []
  // );

  //this runs whenever work instructions are added
  const handleWorkInstructionsSubmit = (data: {
    description: string;
    file: { name: string; type: string } | null;
    category: string;
    photoDetails?: {
      id: number;
      fileName: string;
      photo: string | null;
      referenceDetail: string;
    }[];
  }) => {
    const newId = String(Date.now());

    // Ensure work_instruction_id is always an array
    const workInstructionIdArray = Array.isArray(
      subTaskData.MethodId?.work_instruction_id
    )
      ? subTaskData.MethodId?.work_instruction_id
      : [];

    dispatch(
      updateSubtaskData({
        ...subTaskData,
        MethodId: {
          ...subTaskData.MethodId,
          work_instruction_id: [
            ...workInstructionIdArray,
            {
              photoref: {
                photos:
                  data.photoDetails && Array.isArray(data.photoDetails)
                    ? data.photoDetails.map((photo) => ({
                        id: photo.id.toString(),
                        fileName: photo.fileName,
                        details: photo.referenceDetail,
                        photo: photo.photo as string,
                      }))
                    : [],
              },
              file: data.file,
              _id: newId,
              Description: data.description,
              optionselected: data.category,
              materialId: [],
              manpowerId: [],
              toolsId: [],
              machinaryId: [],
            },
          ],
        },
      })
    );

    saveSyncData(
      {
        ...subTaskData,
        MethodId: {
          ...subTaskData.MethodId,
          work_instruction_id: [
            ...workInstructionIdArray,
            {
              photoref: {
                photos:
                  data.photoDetails && Array.isArray(data.photoDetails)
                    ? data.photoDetails.map((photo) => ({
                        id: photo.id.toString(),
                        fileName: photo.fileName,
                        details: photo.referenceDetail,
                        photo: photo.photo as string,
                      }))
                    : [],
              },
              file: data.file,
              _id: newId,
              Description: data.description,
              optionselected: data.category,
              materialId: [],
              manpowerId: [],
              toolsId: [],
              machinaryId: [],
            },
          ],
        },
      },
      "time",
      "SubTaskForm"
    );

    dispatch(setChangeAPiFlag(true));
    showToast({
      messageContent: "Work Instruction Added Successfully",
      type: "success",
    });
  };

  const handleTcrSubmit = (data: {
    description: string;
    file: { name: string; type: string } | null;
    category: string;
    photoDetails?: {
      id: number;
      fileName: string;
      photo: string | null;
      referenceDetail: string;
    }[];
  }) => {
    const newId = String(Date.now());

    // Ensure work_instruction_id is always an array
    const taskClosingRequirementsArray = Array.isArray(
      subTaskData.MethodId?.task_closing_requirement
    )
      ? subTaskData.MethodId?.task_closing_requirement
      : [];

    dispatch(
      updateSubtaskData({
        ...subTaskData,
        MethodId: {
          ...subTaskData.MethodId,
          task_closing_requirement: [
            ...taskClosingRequirementsArray,
            {
              photoref: {
                photos:
                  data.photoDetails && Array.isArray(data.photoDetails)
                    ? data.photoDetails.map((photo) => ({
                        id: photo.id.toString(),
                        fileName: photo.fileName,
                        details: photo.referenceDetail,
                        photo: photo.photo as string,
                      }))
                    : [],
              },
              file: data.file,
              _id: newId,
              Description: data.description,
              optionselected: data.category,
            },
          ],
        },
      })
    );

    saveSyncData(
      {
        ...subTaskData,
        MethodId: {
          ...subTaskData.MethodId,
          task_closing_requirement: [
            ...taskClosingRequirementsArray,
            {
              photoref: {
                photos:
                  data.photoDetails && Array.isArray(data.photoDetails)
                    ? data.photoDetails.map((photo) => ({
                        id: photo.id.toString(),
                        fileName: photo.fileName,
                        details: photo.referenceDetail,
                        photo: photo.photo as string,
                      }))
                    : [],
              },
              file: data.file,
              _id: newId,
              Description: data.description,
              optionselected: data.category,
            },
          ],
        },
      },
      "time",
      "SubTaskForm"
    );

    dispatch(setChangeAPiFlag(true));
    showToast({
      messageContent: "Task Closing Requirement Added Successfully",
      type: "success",
    });
  };

  //deep comparison
  const deepEqual = (a: any, b: any): boolean => {
    if (a === b) return true;

    if (
      typeof a !== "object" ||
      typeof b !== "object" ||
      a === null ||
      b === null
    )
      return false;

    if (Array.isArray(a) !== Array.isArray(b)) return false;

    if (Array.isArray(a)) {
      if (a.length !== b.length) return false;
      return a.every((item, index) => deepEqual(item, b[index]));
    }

    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    if (keysA.length !== keysB.length) return false;

    return keysA.every((key) => deepEqual(a[key], b[key]));
  };

  //this useEffect is for updating the changed properties mentioned in the above state
  // useEffect(() => {
  //   const subtaskLatest = store.getState().taskMaster.currentSubtaskData;
  //   if (
  //     !subtaskLatest ||
  //     !subtaskLatest.MethodId ||
  //     !subtaskLatest._id ||
  //     Object.keys(popupCategoryData).length !== workInstructionData.length
  //   )
  //     return;

  //   console.log("work instruction data 2", workInstructionData);
  //   console.log("work instruction data 3", subtaskLatest);

  //   const updatedWorkInstructions =
  //     subtaskLatest.MethodId?.work_instruction_id?.map((item) => {
  //       const newItem = workInstructionData.find(
  //         (data: any) => data._id === item._id
  //       );

  //       if (newItem) {
  //         return {
  //           ...item,
  //           manpowerId:
  //             popupCategoryData[`popup-${newItem._id}`]?.Manpower ??
  //             item.manpowerId ??
  //             [],
  //           machinaryId:
  //             popupCategoryData[`popup-${newItem._id}`]?.Machinery ??
  //             item.machinaryId ??
  //             [],
  //           toolsId:
  //             popupCategoryData[`popup-${newItem._id}`]?.Tools ??
  //             item.toolsId ??
  //             [],
  //           materialId:
  //             popupCategoryData[`popup-${newItem._id}`]?.Materials ??
  //             item.materialId ??
  //             [],
  //         };
  //       }

  //       return item;
  //     });

  //   console.log(
  //     "work instruction data 4",
  //     updatedWorkInstructions,
  //     subtaskLatest?.MethodId.work_instruction_id
  //   );

  //   if (
  //     !deepEqual(
  //       updatedWorkInstructions,
  //       subtaskLatest?.MethodId.work_instruction_id
  //     )
  //   ) {
  //     const updatedSubTaskData: any = {
  //       ...subtaskLatest,
  //       MethodId: {
  //         ...subtaskLatest.MethodId,
  //         work_instruction_id: updatedWorkInstructions,
  //       },
  //     };
  //     console.log("updatedSubtaskData", updatedSubTaskData);
  //     dispatch(updateSubtaskData(updatedSubTaskData));

  //     saveSyncData(updatedSubTaskData, "time", "SubTaskForm");

  //     // dispatch(setChangeAPiFlag(true));
  //   }
  // }, [popupCategoryData, subTaskData, workInstructionData]);

  //this is updating the popupcategory state / not sure
  const handleUpdateCategoryData = (
    popupId: string,
    category: string,
    newData: requiredthings[]
  ) => {
    console.log("newData", newData, category, popupId);
    setPopupCategoryData((prev) => ({
      ...prev,
      [popupId]: {
        ...(prev[popupId] || {
          Manpower: [],
          Machinery: [],
          Tools: [],
          Materials: [],
        }),
        [category]: newData,
      },
    }));
  };

  interface WorkInstructionsData {
    id?: string;
    description: string;
    file: { name: string; type: string } | null;
    category: string;
    photoDetails: PhotoSection[];
  }

  // console.log("work instruction data>>>>>>>>>>>>>>>>>>>>", formattedWorkInstructionData);

  const handlePhotoPopupClose = () => {
    dispatch(closePopup(getPopupId("workinstructionsphoto")));
  };
  const [initaldata, setInitalData] = useState<
    WorkInstructionsData | undefined
  >();
  const fetchSubtasks = async () => {
    const dbName = await initializeDatabase("SubTaskForm");

    const response = await window.electron.getSubtasksByTaskId({
      dbName,
      taskId,
      subtaskId,
    });
    return response || [];
  };
  const getPopupId = (category: string) => `${initaldata?.id}-${category}`;
  console.log("ddd3", requiredThingsDelete);
  return (
    <div className={styles.subtask_creation_method_container}>
      <div className={styles.subtask_creation_designation_header}>
        <SuryconLogo />
        <h3>Method</h3>
      </div>
      <div style={{ display: "flex" }}>
        <div className={styles.subtaskcreation_column}>
          <div style={{ minWidth: "50px", position: "relative" }}>
            <AddToolTip
              label="Work Instructions"
              onClick={(event) => {
                event.stopPropagation();
                handleToggleDropdown("WorkInstructions");
              }}
              isEdit={isEdit}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              className2={
                formattedWorkInstructionData?.length ? "widthfull_1rem" : ""
              }
            />

            {/* Render Work Instructions Popups Dynamically */}
            {(formattedWorkInstructionData || [])?.map((popup: any, index) => (
              <div style={{ position: "relative" }}>
                <SubTaskWorkInstructionsPopup
                  isEdit={isEdit}
                  key={popup.id}
                  workId={popup.id}
                  id={String(index)}
                  initaldata={
                    initaldata || {
                      id: "",
                      description: "",
                      file: null,
                      category: "",
                      photoDetails: [],
                    }
                  }
                  onClick={(e: any) => {
                    e.stopPropagation();
                    if (!isDeletedNext) {
                      setInitalData({
                        id: popup.id,
                        ...popup.data,
                        photoDetails: popup.data.photoDetails || [],
                      });
                      dispatch(
                        openPopup(`popup-${popup.id}-workinstructionsphoto`)
                      );
                    }
                  }}
                  popupId={`popup-${popup.id}`}
                  popupIdParent={popupIdParent}
                  setCategoryDataParent={handleCategoryData}
                  categortDataParent={categortDataParent}
                  setPopupIdParent={handleSetPopupId}
                  setDeleteIdParent={handlePopupName}
                  deleteIdParent={deleteIdParent}
                  data={{
                    ...popup.data,
                    photoDetails: popup.data.photoDetails || [],
                  }}
                  handleDelete={() => {
                    const workinstruction =
                      subTaskData?.MethodId?.work_instruction_id?.find(
                        (item) => item?._id === popup.id
                      );
                    setDeleteId("work_instruction_id");
                    setRequiredThingsDeleteName("Work Instruction");
                    setRequiredThingsDelete(popup);
                    setRequiredThingsDelete(workinstruction as any);
                    setTimeout(() => {
                      dispatch(openPopup("DeleteMethodId"));
                    }, 400);
                  }}
                  categoryData={
                    popupCategoryData[`popup-${popup.id}`] || {
                      Manpower: [],
                      Machinery: [],
                      Tools: [],
                      Materials: [],
                    }
                  }
                  onUpdateCategoryData={handleUpdateCategoryData}
                  //this is updating the work instructions
                  onUpdateWorkInstructions={(updatedData: any) => {
                    const updatedWorkInstructions =
                      formattedWorkInstructionData.map((e) => {
                        if (e.id === updatedData.id) {
                          return {
                            ...e,
                            data: {
                              ...updatedData,
                              photoDetails: updatedData.photoDetails || [],
                            },
                          };
                        }
                        return e;
                      });

                    dispatch(
                      updateSubtaskData({
                        ...subTaskData,
                        MethodId: {
                          ...subTaskData.MethodId,
                          work_instruction_id: updatedWorkInstructions.map(
                            (e) => ({
                              photoref: {
                                photos:
                                  e?.data.photoDetails
                                    ?.filter(
                                      (photo: { photo: string | null }) =>
                                        photo.photo !== null
                                    )
                                    .map(
                                      (photo: {
                                        id: number;
                                        fileName: string;
                                        referenceDetail: string;
                                        photo: string | null;
                                      }) => ({
                                        id: photo.id.toString(),
                                        fileName: photo.fileName,
                                        details: photo.referenceDetail,
                                        photo: photo.photo as string,
                                      })
                                    ) || [],
                              },
                              file: e?.data.file,
                              _id: e?.id,
                              materialId:
                                popupCategoryData[`popup-${e?.id}`]
                                  ?.Materials || [], //this might a bit confusing ask me directly
                              optionselected: e?.data.category,
                              Description: e?.data.description,
                              machinaryId:
                                popupCategoryData[`popup-${e?.id}`]
                                  ?.Machinery || [],
                              toolsId:
                                popupCategoryData[`popup-${e?.id}`]?.Tools ||
                                [],
                              manpowerId:
                                popupCategoryData[`popup-${e?.id}`]?.Manpower ||
                                [],
                            })
                          ),
                        },
                      })
                    );

                    saveSyncData(
                      {
                        ...subTaskData,
                        MethodId: {
                          ...subTaskData.MethodId,
                          work_instruction_id: updatedWorkInstructions.map(
                            (e) => ({
                              photoref: {
                                photos:
                                  e?.data.photoDetails
                                    ?.filter(
                                      (photo: { photo: string | null }) =>
                                        photo.photo !== null
                                    )
                                    .map(
                                      (photo: {
                                        id: number;
                                        fileName: string;
                                        referenceDetail: string;
                                        photo: string | null;
                                      }) => ({
                                        id: photo.id.toString(),
                                        fileName: photo.fileName,
                                        details: photo.referenceDetail,
                                        photo: photo.photo as string,
                                      })
                                    ) || [],
                              },
                              file: e?.data.file,
                              _id: e?.id,
                              materialId:
                                popupCategoryData[`popup-${e?.id}`]
                                  ?.Materials || [], //this might a bit confusing ask me directly
                              optionselected: e?.data.category,
                              Description: e?.data.description,
                              machinaryId:
                                popupCategoryData[`popup-${e?.id}`]
                                  ?.Machinery || [],
                              toolsId:
                                popupCategoryData[`popup-${e?.id}`]?.Tools ||
                                [],
                              manpowerId:
                                popupCategoryData[`popup-${e?.id}`]?.Manpower ||
                                [],
                            })
                          ),
                        },
                      },
                      "time",
                      "SubTaskForm"
                    );

                    showToast({
                      messageContent: "Work Instruction Updated Successfully",
                      type: "success",
                    });

                    dispatch(setChangeAPiFlag(true));
                    handlePhotoPopupClose();
                  }}
                />
              </div>
            ))}

            {/* Main Popup for Submitting Data */}
            {popups["WorkInstructions"] && (
              <WorkInstructionsPopup
                onCancel={() => dispatch(closePopup("WorkInstructions"))}
                onSubmit={(data) => {
                  handleWorkInstructionsSubmit(data);
                  setTimeout(() => {
                    dispatch(closePopup("WorkInstructions"));
                  }, 400);
                }}
              />
            )}
            {popups["Tcrphoto"] && (
              <TCRpopup
                onCancel={() => dispatch(closePopup("Tcrphoto"))}
                onSubmit={(data) => {
                  handleTcrSubmit(data);
                  setTimeout(() => {
                    dispatch(closePopup("Tcrphoto"));
                  }, 400);
                }}
              />
            )}
          </div>

          <div style={{ minWidth: "50px", position: "relative" }}>
            <AddToolTip
              label="Quality Ensuring Measures"
              onClick={() => {
                setFmeaInitialState(undefined);
                handleToggleDropdown("FMEA");
              }}
              isEdit={isEdit}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              className2={failureModeData?.length! > 0 ? "widthfull_1rem" : ""}
            />

            <div
              style={{ display: "flex", gap: "1rem", flexDirection: "column" }}
            >
              {[...(failureModeData ?? [])]
                .sort(
                  (a, b) => Number(b.severity || 0) - Number(a.severity || 0)
                )
                .map((item) => (
                  <>
                    <div>
                      <div
                        className={styles.fmea_data_container}
                        onClick={
                          isEdit
                            ? () => {
                                if (!isDeletedNext) {
                                  setFmeaInitialState(item);
                                  dispatch(openPopup("FMEA"));
                                }
                              }
                            : () => {}
                        }
                        style={{ position: "relative" }}
                      >
                        <div className={styles.fmea_tooltip_solution}>
                          <div
                            className={styles.fmea_tooltip_solution_container}
                          >
                            <h4>
                              <span className={styles.fmea_tooltip_description}>
                                Description:{" "}
                              </span>
                              {item.Description || "No description provided"}
                            </h4>
                          </div>
                          <div
                            className={styles.fmea_tooltip_severity_container}
                          >
                            <p>Severity</p>
                            <p>{item.severity || "(1-10)"}</p>
                          </div>
                        </div>
                        <div className={styles.fmea_tooltip}>
                          <h4>
                            <span className={styles.fmea_tooltip_Solution}>
                              Solution:{" "}
                            </span>
                            {item.solution || "No solution provided"}
                          </h4>
                        </div>
                        {isEdit && (
                          <div
                            className={styles.delete_icon_tooltip}
                            onClick={(e) => {
                              e.stopPropagation();
                              setDeleteId("Failuremode");
                              setRequiredThingsDeleteName("Failure Mode");
                              setRequiredThingsDelete(item);
                              setTimeout(() => {
                              dispatch(openPopup("DeleteMethodId"));
                            }, 400);
                            }}
                          >
                            <DeleteIcon />
                          </div>
                        )}
                      </div>
                    </div>
                  </>
                ))}
            </div>

            {popups["FMEA"] && (
              <div className={styles.fmea_container}>
                <FMEApopup
                  onCancel={() => dispatch(closePopup("FMEA"))}
                  onSubmit={handleFmeaDataChange}
                  initialData={fmeaInitialState}
                />
              </div>
            )}
            {popups["DeleteMethodId"] && (
              <DeletePopup
                isOpen={isEdit}
                width="35rem"
                height="calc(100% - 9rem)"
                heightupperlimit={
                  requiredThingsDeleteName === "Work instruction"
                    ? "2rem"
                    : "0.35rem"
                }
                header={`Are you sure you want to delete this ${
                  requiredThingsDeleteName == "Failure Mode"
                    ? "Quality Ensuring Measures"
                    : requiredThingsDeleteName == "Control Plan"
                    ? "Quality Control Plan "
                    : requiredThingsDeleteName
                } ?`}
                callbackDelete={async () => {
                  if (deleteId && deleteId === "Failuremode") {
                    const newfailureModeData = failureModeData?.filter(
                      (e) => e?._id !== requiredThingsDelete?._id
                    );

                    dispatch(
                      updateSubtaskData({
                        ...subTaskData,
                        MethodId: {
                          ...subTaskData.MethodId,
                          Failuremode: newfailureModeData,
                        },
                      })
                    );

                    await saveSyncData(
                      {
                        ...subTaskData,
                        MethodId: {
                          ...subTaskData.MethodId,
                          Failuremode: newfailureModeData,
                        },
                      },
                      "time",
                      "SubTaskForm"
                    );

                    showToast({
                      messageContent: `Quality Ensuring Measures Deleted Successfully!`,
                      type: "success",
                    });

                    dispatch(setChangeAPiFlag(true));
                  }
                  if (deleteId && deleteId === "Controlplan") {
                    const newControlPlanData = controlPlandata?.filter(
                      (e) => e?._id !== requiredThingsDelete?._id
                    );

                    dispatch(
                      updateSubtaskData({
                        ...subTaskData,
                        MethodId: {
                          ...subTaskData.MethodId,
                          Controlplan: newControlPlanData,
                        },
                      })
                    );

                    await saveSyncData(
                      {
                        ...subTaskData,
                        MethodId: {
                          ...subTaskData.MethodId,
                          Controlplan: newControlPlanData,
                        },
                      },
                      "time",
                      "SubTaskForm"
                    );

                    showToast({
                      messageContent:
                        requiredThingsDeleteName === "Control Plan"
                          ? "Quality Control Plan Deleted Successfully!"
                          : `${requiredThingsDeleteName} Deleted Successfully!`,
                      type: "success",
                    });
                    dispatch(setChangeAPiFlag(true));
                  }
                  if (deleteId && deleteId == "task_closing_requirement") {
                    const newTaskClosingRequirementData =
                      taskClosingRequirementData?.filter(
                        (e) => e?._id !== requiredThingsDelete?.id
                      );

                    dispatch(
                      updateSubtaskData({
                        ...subTaskData,
                        MethodId: {
                          ...subTaskData.MethodId,
                          task_closing_requirement:
                            newTaskClosingRequirementData,
                        },
                      })
                    );

                    await saveSyncData(
                      {
                        ...subTaskData,
                        MethodId: {
                          ...subTaskData.MethodId,
                          task_closing_requirement:
                            newTaskClosingRequirementData,
                        },
                      },
                      "time",
                      "SubTaskForm"
                    );

                    showToast({
                      messageContent: `${requiredThingsDeleteName} Deleted Successfully!`,
                      type: "success",
                    });

                    dispatch(setChangeAPiFlag(true));
                  }
                  if (deleteId && deleteId == "work_instruction_id") {
                    dispatch(
                      setWorkInstructionDelete(requiredThingsDelete?._id)
                    );
                    const uniqueArray = (arr: any) => [...new Set(arr)];
                    const newwork_instruction_id = workInstructionData?.filter(
                      (e) => e?._id !== requiredThingsDelete?._id
                    );

                    dispatch(
                      updateSubtaskData({
                        ...subTaskData,
                        MethodId: {
                          ...subTaskData.MethodId,
                          work_instruction_id: newwork_instruction_id,
                        },
                        Tobedeleted: {
                          workinstruction: uniqueArray([
                            ...(subTaskData?.Tobedeleted?.workinstruction ||
                              []),
                            ...(!/^\d{13}$/.test(
                              String(requiredThingsDelete?._id)
                            )
                              ? [requiredThingsDelete!?._id]
                              : []),
                          ]),
                        },
                      })
                    );

                    await saveSyncData(
                      {
                        ...subTaskData,
                        MethodId: {
                          ...subTaskData.MethodId,
                          work_instruction_id: newwork_instruction_id,
                        },
                        Tobedeleted: {
                          workinstruction: uniqueArray([
                            ...(subTaskData?.Tobedeleted?.workinstruction ||
                              []),
                            ...(!/^\d{13}$/.test(
                              String(requiredThingsDelete?._id)
                            )
                              ? [requiredThingsDelete!?._id]
                              : []),
                          ]),
                        },
                      },
                      "time",
                      "SubTaskForm"
                    );
                    const workInstructionToDelete = workInstructionData?.filter(
                      (e: any) => e?._id === requiredThingsDelete?._id
                    )[0];
                    console.log(
                      workInstructionToDelete,
                      "thi is work instruction dataasdfasdfasdf"
                    );
                    const machinaryId =
                      workInstructionToDelete?.machinaryId || [];
                    const manpowerId =
                      workInstructionToDelete?.manpowerId || [];
                    const toolsId = workInstructionToDelete?.toolsId || [];
                    const materialId =
                      workInstructionToDelete?.materialId || [];
                    console.log(
                      machinaryId,
                      manpowerId,
                      toolsId,
                      materialId,
                      "these are al.lisc checkout aheras"
                    );
                    const ItemsToDelete = [
                      ...machinaryId,
                      ...manpowerId,
                      ...toolsId,
                      ...materialId,
                    ];
                    const subtaskLatest =
                      store.getState().taskMaster.currentSubtaskData;

                    // Collect all related items from remaining work instructions
                    const allMachinaryItems =
                      subtaskLatest?.MethodId?.work_instruction_id?.flatMap(
                        (el: any) => el.machinaryId || []
                      );
                    const allManpowerItems =
                      subtaskLatest?.MethodId?.work_instruction_id?.flatMap(
                        (el: any) => el.manpowerId || []
                      );
                    const allToolsItems =
                      subtaskLatest?.MethodId?.work_instruction_id?.flatMap(
                        (el: any) => el.toolsId || []
                      );
                    const allMaterialItems =
                      subtaskLatest?.MethodId?.work_instruction_id?.flatMap(
                        (el: any) => el.materialId || []
                      );
                    const allitems = [
                      ...(allMachinaryItems || []),
                      ...(allManpowerItems || []),
                      ...(allToolsItems || []),
                      ...(allMaterialItems || []),
                    ];
                    const notpresentInAnotherWI = ItemsToDelete?.filter(
                      (el: any) =>
                        !allitems?.some((item: any) => item._id === el?._id)
                    );
                    console.log(
                      ItemsToDelete,
                      "these are the items to delete bro"
                    );
                    console.log(
                      allitems,
                      "these are the items in another work instruction bro"
                    );
                    console.log(
                      notpresentInAnotherWI,
                      "these are present in another work instruction broasdfasdfasdfas"
                    );
                    if (notpresentInAnotherWI?.length > 0) {
                      console.log(
                        subtaskLatest,
                        "this is altestasdf subtasdfas"
                      );
                      const updatedSubtask2 = {
                        ...subtaskLatest,
                        MachinaryId: (
                          (subtaskLatest as any).MachinaryId || []
                        ).filter(
                          (el: any) =>
                            !notpresentInAnotherWI?.some(
                              (item: any) => item._id === el?._id
                            )
                        ),
                        ManpowerId: (
                          (subtaskLatest as any).ManpowerId || []
                        ).filter(
                          (el: any) =>
                            !notpresentInAnotherWI?.some(
                              (item: any) => item._id === el?._id
                            )
                        ),
                        MaterialId: (
                          (subtaskLatest as any).MaterialId || []
                        ).filter(
                          (el: any) =>
                            !notpresentInAnotherWI?.some(
                              (item: any) => item._id === el?._id
                            )
                        ),
                        ToolId: ((subtaskLatest as any).ToolId || []).filter(
                          (el: any) =>
                            !notpresentInAnotherWI?.some(
                              (item: any) => item._id === el?._id
                            )
                        ),
                      };
                      dispatch(updateSubtaskData(updatedSubtask2 as any));
                      try {
                        await saveSyncData(
                          updatedSubtask2,
                          "time",
                          "SubTaskForm"
                        );
                      } catch (error) {
                        console.error(
                          "Failed to save subtask (secondary) to local DB:",
                          error
                        );
                      }
                      console.log(
                        subtaskId,
                        "this is subtask id checkout herer"
                      );

                      const allSubtask = await fetchSubtasks();
                      const currentSubtaskId = subtaskId;
                      const updatedSubtaskFromRedux =
                        store.getState().taskMaster.currentSubtaskData;

                      console.log(
                        updatedSubtaskFromRedux,
                        "this is updated subtask data "
                      );

                      const allMachineries = allSubtask?.flatMap(
                        (e: any) => e.MachinaryId
                      );
                      const allManpowers = allSubtask?.flatMap(
                        (e: any) => e.ManpowerId
                      );
                      const allMaterials = allSubtask?.flatMap(
                        (e: any) => e.MaterialId
                      );
                      const allTools = allSubtask?.flatMap(
                        (e: any) => e.ToolId
                      );
                      const alldata = [
                        ...(allMachineries || []),
                        ...(allManpowers || []),
                        ...(allMaterials || []),
                        ...(allTools || []),
                      ];
                      const notpresentInAnotherSubtask = ItemsToDelete?.filter(
                        (el: any) =>
                          !alldata?.some((item: any) => item._id === el?._id)
                      );
                      if (notpresentInAnotherSubtask?.length > 0) {
                        const TaskDataLatest =
                          store.getState().taskForm.currentSubtaskData;
                        console.log(
                          TaskDataLatest,
                          "this is task data latestsasdf"
                        );
                        const updatedTaskData = {
                          ...TaskDataLatest,
                          MachinaryId: (
                            (TaskDataLatest as any).MachinaryId || []
                          ).filter(
                            (el: any) =>
                              !notpresentInAnotherSubtask?.some(
                                (item: any) => item._id === el?._id
                              )
                          ),
                          ManpowerId: (
                            (TaskDataLatest as any).ManpowerId || []
                          ).filter(
                            (el: any) =>
                              !notpresentInAnotherSubtask?.some(
                                (item: any) => item._id === el?._id
                              )
                          ),
                          MaterialId: (
                            (TaskDataLatest as any).MaterialId || []
                          ).filter(
                            (el: any) =>
                              !notpresentInAnotherSubtask?.some(
                                (item: any) => item._id === el?._id
                              )
                          ),
                          ToolId: ((TaskDataLatest as any).ToolId || []).filter(
                            (el: any) =>
                              !notpresentInAnotherSubtask?.some(
                                (item: any) => item._id === el?._id
                              )
                          ),
                        };
                        await saveSyncData(updatedTaskData, "time", "TaskForm");
                        dispatch(updateTaskData(updatedTaskData as any));
                      }
                      console.log(
                        notpresentInAnotherSubtask,
                        "these are  not presnt in antohersubasdfa"
                      );
                    }
                    showToast({
                      messageContent: `${requiredThingsDeleteName} Deleted Successfully!`,
                      type: "success",
                    });
                    dispatch(settaskChangeAPiFlag(true));
                    dispatch(setChangeAPiFlag(true));
                  }
                  // dispatch(closePopup("DeleteMethodId"));
                }}
                onClose={() => {
                  dispatch(closePopup("DeleteMethodId"));
                }}
              >
                {deleteId == "work_instruction_id" && (
                  <>
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Description
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {requiredThingsDelete?.Description}
                          </h4>
                        </div>
                      </div>
                    </div>
                    {requiredThingsDelete?.file?.name && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            {fileTypeMapper(requiredThingsDelete?.file)}
                          </p>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              gap: "1rem",
                              flexWrap: "wrap",
                            }}
                          >
                            <h4 style={{ color: "var(--text-black-87)" }}>
                              {requiredThingsDelete?.file?.name}
                            </h4>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Action
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4
                            style={{
                              color: "var(--text-black-87)",
                              textTransform: "capitalize",
                            }}
                          >
                            {requiredThingsDelete?.optionselected}
                          </h4>
                        </div>
                      </div>
                    </div>
                    {requiredThingsDelete?.optionselected == "photo" && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          {requiredThingsDelete?.photoref?.photos?.map((e) => (
                            <>
                              <p>
                                {" "}
                                {isBase64(e?.photo)
                                  ? e?.fileName
                                  : getFileName(e?.photo)}
                              </p>
                              {e?.details && (
                                <p
                                  style={{ color: "var(--text-black-60)" }}
                                  className="p_tag_14px"
                                >
                                  Reference Detail
                                </p>
                              )}
                              <div
                                className=""
                                style={{
                                  display: "flex",
                                  gap: "1rem",
                                  flexWrap: "wrap",
                                }}
                              >
                                <h4 style={{ color: "var(--text-black-87)" }}>
                                  {e?.details}
                                </h4>
                              </div>
                            </>
                          ))}
                        </div>
                      </div>
                    )}
                    {(requiredThingsDelete?.manpowerId?.length ?? 0) > 0 && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            Manpower
                          </p>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              gap: "1rem",
                              flexWrap: "wrap",
                            }}
                          >
                            {requiredThingsDelete?.manpowerId.map((e) => (
                              <h4
                                style={{
                                  marginTop: "0.3rem",
                                  color: "var(--text-black-87)",
                                }}
                              >
                                {e?.name}
                              </h4>
                            )) || "No description"}
                          </div>
                        </div>
                      </div>
                    )}
                    {(requiredThingsDelete?.machinaryId?.length ?? 0) > 0 && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            Machinery
                          </p>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              gap: "1rem",
                              flexWrap: "wrap",
                            }}
                          >
                            {requiredThingsDelete?.machinaryId.map((e) => (
                              <h4
                                style={{
                                  marginTop: "0.3rem",
                                  color: "var(--text-black-87)",
                                }}
                              >
                                {e?.name}
                              </h4>
                            )) || "No description"}
                          </div>
                        </div>
                      </div>
                    )}
                    {(requiredThingsDelete?.materialId?.length ?? 0) > 0 && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            Materials
                          </p>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              gap: "1rem",
                              flexWrap: "wrap",
                            }}
                          >
                            {requiredThingsDelete?.materialId?.map((e) => (
                              <h4
                                style={{
                                  marginTop: "0.3rem",
                                  color: "var(--text-black-87)",
                                }}
                              >
                                {e?.name}
                              </h4>
                            )) || "No description"}
                          </div>
                        </div>
                      </div>
                    )}
                    {(requiredThingsDelete?.toolsId?.length ?? 0) > 0 && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            Tools
                          </p>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              gap: "1rem",
                              flexWrap: "wrap",
                            }}
                          >
                            {requiredThingsDelete?.toolsId.map((e) => (
                              <h4
                                style={{
                                  marginTop: "0.3rem",
                                  color: "var(--text-black-87)",
                                }}
                              >
                                {e?.name}
                              </h4>
                            )) || "No description"}
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )}

                {deleteId == "Controlplan" && (
                  <>
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Description
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {requiredThingsDelete?.name}
                          </h4>
                        </div>
                      </div>
                    </div>
                  </>
                )}
                {deleteId == "Failuremode" && (
                  <>
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Description
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {requiredThingsDelete?.Description}
                          </h4>
                        </div>
                      </div>
                    </div>
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Solution
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {requiredThingsDelete?.solution}
                          </h4>
                        </div>
                      </div>
                    </div>
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Severity
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {requiredThingsDelete?.severity}
                          </h4>
                        </div>
                      </div>
                    </div>
                  </>
                )}
                {deleteId == "task_closing_requirement" && (
                  <>
                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Description
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                          }}
                        >
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {requiredThingsDelete?.data?.description}
                          </h4>
                        </div>
                      </div>
                    </div>

                    {requiredThingsDelete?.data?.file?.name && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          <p
                            style={{ color: "var(--text-black-60)" }}
                            className="p_tag_14px"
                          >
                            {fileTypeMapper(requiredThingsDelete?.data?.file)}
                          </p>
                          <div
                            className=""
                            style={{
                              display: "flex",
                              gap: "1rem",
                              flexWrap: "wrap",
                            }}
                          >
                            <h4 style={{ color: "var(--text-black-87)" }}>
                              {requiredThingsDelete?.data?.file?.name}
                            </h4>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className={styles.summaryDivData}>
                      <div className={styles.summaryDataContent}>
                        <p
                          style={{ color: "var(--text-black-60)" }}
                          className="p_tag_14px"
                        >
                          Action
                        </p>
                        <div
                          className=""
                          style={{
                            display: "flex",
                            gap: "1rem",
                            flexWrap: "wrap",
                            textTransform: "capitalize",
                          }}
                        >
                          <h4 style={{ color: "var(--text-black-87)" }}>
                            {requiredThingsDelete?.data?.category}
                          </h4>
                        </div>
                      </div>
                    </div>
                    {requiredThingsDelete?.data?.category?.toLowerCase() ==
                      "photo" && (
                      <div className={styles.summaryDivData}>
                        <div className={styles.summaryDataContent}>
                          {requiredThingsDelete?.data?.photoDetails?.map(
                            (e) => (
                              <>
                                <p>
                                  {" "}
                                  {isBase64(e?.photo)
                                    ? e?.fileName
                                    : getFileName(e?.photo)}
                                </p>
                                {e?.referenceDetail && (
                                  <>
                                    <p
                                      style={{ color: "var(--text-black-60)" }}
                                      className="p_tag_14px"
                                    >
                                      Reference Detail
                                    </p>
                                    <div
                                      className=""
                                      style={{
                                        display: "flex",
                                        gap: "1rem",
                                        flexWrap: "wrap",
                                      }}
                                    >
                                      <h4
                                        style={{
                                          color: "var(--text-black-87)",
                                        }}
                                      >
                                        {e?.referenceDetail}
                                      </h4>
                                    </div>
                                  </>
                                )}
                              </>
                            )
                          )}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </DeletePopup>
            )}
          </div>
        </div>
        <div
          className={styles.subtaskcreation_column}
          style={{ marginRight: "1rem" }}
        >
          <div style={{ minWidth: "50px", position: "relative" }}>
            <AddToolTip
              label="Task Closing Requirements"
              isEdit={isEdit}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              onClick={() => handleToggleDropdown("Tcrphoto")}
              className2={
                formattedTaskClosingRequirementData.length > 0
                  ? "widthfull_1rem"
                  : ""
              }
            />
            {(formattedTaskClosingRequirementData || [])?.map((popup, indx) => (
              <div style={{ position: "relative" }}>
                <SubTaskTcrPopup
                  key={popup.id}
                  initaldata={
                    initaldata || {
                      id: "asdf",
                      description: "",
                      file: null,
                      category: "",
                      photoDetails: [],
                    }
                  }
                  id={String(indx)}
                  isEdit={isEdit}
                  handleDelete={() => {
                    setDeleteId("task_closing_requirement");
                    setRequiredThingsDeleteName("Task Closing Requirement");
                    setRequiredThingsDelete(popup);
                    setTimeout(() => {
                      dispatch(openPopup("DeleteMethodId"));
                    }, 400);
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (!isDeletedNext) {
                      setInitalData({
                        id: popup.id,
                        ...popup.data,
                        photoDetails: popup.data.photoDetails || [],
                      });
                      dispatch(openPopup(`popup-${popup.id}-Tcrphoto`));
                    }
                  }}
                  popupId={`popup-${popup.id}`}
                  data={{
                    ...popup.data,
                    photoDetails: popup.data.photoDetails || [],
                  }}
                  OnUpdateTcrData={(updatedData) => {
                    const updatedTaskClosingRequirements =
                      formattedTaskClosingRequirementData.map((e) => {
                        if (e.id === updatedData.id) {
                          return {
                            ...e,
                            data: {
                              ...updatedData,
                              photoDetails: updatedData.photoDetails || [],
                            },
                          };
                        }
                        return e;
                      });

                    dispatch(
                      updateSubtaskData({
                        ...subTaskData,
                        MethodId: {
                          ...subTaskData.MethodId,
                          task_closing_requirement:
                            updatedTaskClosingRequirements.map((e) => ({
                              photoref: {
                                photos:
                                  e?.data.photoDetails
                                    ?.filter(
                                      (photo: { photo: string | null }) =>
                                        photo.photo !== null
                                    )
                                    .map(
                                      (photo: {
                                        id: number;
                                        fileName: string;
                                        referenceDetail: string;
                                        photo: string | null;
                                      }) => ({
                                        id: photo.id.toString(),
                                        fileName: photo.fileName,
                                        details: photo.referenceDetail,
                                        photo: photo.photo as string,
                                      })
                                    ) || [],
                              },
                              file: e?.data.file,
                              _id: e?.id,
                              optionselected: e?.data.category,
                              Description: e?.data.description,
                            })),
                        },
                      })
                    );

                    saveSyncData(
                      {
                        ...subTaskData,
                        MethodId: {
                          ...subTaskData.MethodId,
                          task_closing_requirement:
                            updatedTaskClosingRequirements.map((e) => ({
                              photoref: {
                                photos:
                                  e?.data.photoDetails
                                    ?.filter(
                                      (photo: { photo: string | null }) =>
                                        photo.photo !== null
                                    )
                                    .map(
                                      (photo: {
                                        id: number;
                                        fileName: string;
                                        referenceDetail: string;
                                        photo: string | null;
                                      }) => ({
                                        id: photo.id.toString(),
                                        fileName: photo.fileName,
                                        details: photo.referenceDetail,
                                        photo: photo.photo as string,
                                      })
                                    ) || [],
                              },
                              file: e?.data.file,
                              _id: e?.id,
                              optionselected: e?.data.category,
                              Description: e?.data.description,
                            })),
                        },
                      },
                      "time",
                      "SubTaskForm"
                    );

                    showToast({
                      messageContent:
                        "Task Closing Requirement Updated Successfully!",
                      type: "success",
                    });

                    console.log(updatedData, "updateddata");
                    dispatch(setChangeAPiFlag(true));
                    dispatch(closePopup(getPopupId("Tcrphoto")));
                  }}
                />
              </div>
            ))}
          </div>
          <div style={{ minWidth: "50px", position: "relative" }}>
            <AddToolTip
              label="Quality Control Plan"
              onClick={() => {
                setControlPlanInitialState(undefined);
                handleToggleDropdown("Control Plan");
              }}
              isEdit={isEdit}
              handleDelete={(item) => {
                setDeleteId("Controlplan");
                setRequiredThingsDeleteName("Control Plan");
                setRequiredThingsDelete(item);
                setTimeout(() => {
                      dispatch(openPopup("DeleteMethodId"));
                    }, 400);
              }}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              // className2="width_full"
              additionalClass="left_align"
              data={controlPlandata?.map((data) => ({
                _id: data?._id,
                name: data?.Description,
              }))}
              onTooltipClick={
                isEdit
                  ? (item) => {
                      console.log(item, "rat");

                      if (!isDeletedNext) {
                        setControlPlanInitialState({
                          _id: item?._id,
                          description: item?.name,
                        });
                        handleToggleDropdown("Control Plan");
                      }
                    }
                  : () => {}
              }
            />
            {popups["Control Plan"] && (
              <ControlPlanpopup
                onSubmit={(data) => {
                  if (controlPlanInitialState) {
                    const updatedControlPlanData = controlPlandata.map((p) => {
                      console.log("data", p);
                      return p._id === data._id
                        ? { ...p, Description: data.description }
                        : p;
                    });

                    dispatch(
                      updateSubtaskData({
                        ...subTaskData,
                        MethodId: {
                          ...subTaskData.MethodId,
                          Controlplan: updatedControlPlanData,
                        },
                      })
                    );

                    saveSyncData(
                      {
                        ...subTaskData,
                        MethodId: {
                          ...subTaskData.MethodId,
                          Controlplan: updatedControlPlanData,
                        },
                      },
                      "time",
                      "SubTaskForm"
                    );

                    dispatch(closePopup("Control Plan"));

                    showToast({
                      messageContent:
                        "Quality Control Plan Updated Successfully!",
                      type: "success",
                    });

                    dispatch(setChangeAPiFlag(true));
                    return;
                  }
                  dispatch(
                    updateSubtaskData({
                      ...subTaskData,
                      MethodId: {
                        ...subTaskData.MethodId,
                        Controlplan: [
                          ...controlPlandata,
                          {
                            _id: String(Date.now()),
                            Description: data.description,
                          },
                        ],
                      },
                    })
                  );

                  saveSyncData(
                    {
                      ...subTaskData,
                      MethodId: {
                        ...subTaskData.MethodId,
                        Controlplan: [
                          ...controlPlandata,
                          {
                            _id: String(Date.now()),
                            Description: data.description,
                          },
                        ],
                      },
                    },
                    "time",
                    "SubTaskForm"
                  );

                  // dispatch(closePopup("Control Plan"));

                  showToast({
                    messageContent: "Quality Control Plan Added Successfully!",
                    type: "success",
                  });

                  dispatch(setChangeAPiFlag(true));
                }}
                initialData={controlPlanInitialState && controlPlanInitialState}
                isEdit={controlPlanInitialState ? true : false}
                onCancel={() => dispatch(closePopup("Control Plan"))}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubTaskCreationMethod;
