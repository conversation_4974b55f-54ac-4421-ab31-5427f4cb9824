// TaskCreation Designation.tsx Author Charvi Changes by <PERSON><PERSON><PERSON>
import React, { useCallback, useMemo, useState } from "react";
import styles from "../../Styles/TaskCreationForm.module.css";

import { useAppDispatch } from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { RootState, store } from "../../../../../../redux/store";
import { useSelector } from "react-redux";
import { useGetTaskBuildingBlocksQuery } from "../../../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import {
  settaskChangeAPiFlag,
  updateTaskData,
} from "../../../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import {
  closePopup,
  openPopup,
} from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import { SuryconLogo } from "../../../../../../assets/icons";
import AddToolTip from "../../../../../../components/Reusble/Global/AddToolTip";
import AddCategoryType from "../../../../../../components/Reusble/Global/AddCategoryType";
import Dialogbox from "../../../../../../components/Reusble/Global/DialogBox";
import ReportingLevel from "../../../../../../components/Reusble/TaskMaster/ReportingLevel";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import {
  AddDataDesignation,
  requiredthings,
  taskBuildingBlocks,
  TaskDataType,
} from "../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import { saveSyncData } from "../../../../../../Backup/BackupFunctions/BackupFunctions";
import { useParams } from "react-router-dom";
import { initializeDatabase } from "../../../../../../functions/functions";
import { useToast } from "../../../../../../hooks/ToastHook";

const TaskCreationDesignation: React.FC = ({ isEdit = false }) => {
  const dispatch = useAppDispatch();
  const showToast = useToast();
  const [selectedOption, setSelectedOption] = useState("");
  const [selectedOptionApidata, setSelectedOptionApidata] = useState([]);
  const [deleteDepartmentId, setDeleteDepartmentId] = useState<string>();
  const [isClosingDelete, setIsClosingDelete] = useState(false);

  const [primaryLabelForAddCategoryType, SetprimaryLabelForAddCategoryType] =
    useState("");
  const [initialReporterData, setInitialReporterData] = useState<any>([]);
  const [reporterAdded, setReporterAdded] = useState<boolean>(false);
  const { popups } = useSelector((state: RootState) => state.popup);
  const reportedData = useSelector(
    (state: RootState) =>
      state.taskForm.currentSubtaskData?.ReporterId?.Reporter
  );
  const [isOpen, setIsOpen] = useState(false);
  const [currentLevel, setCurrentLevel] = useState<number | string>();
  const { data } = useGetTaskBuildingBlocksQuery();

  // task building blaocks designation get from here by aayush
  const taskBuildingBlocks: taskBuildingBlocks = {
    designation:
      data?.data?.response?.designation?.map((item: any) => ({
        id: item._id,
        category: item.name,
      })) || [],
  };

  // defines roles here for trigeer event by aayush
  const roles = ["Task Manager", "Assign To", "Reporter"] as const;
  const { taskId } = useParams<{ taskId: string }>();
  // task data here get from
  const TaskData = useSelector(
    (state: RootState) =>
      state.taskForm.currentSubtaskData || {
        _id: taskId,
        name: "",
        Unit: "",
        description: "",
        subtaskWeighatages: 0,
        Tracking: "",
        MaterialId: [],
        ToolId: [],
        MachinaryId: [],
        ManpowerId: [],
        Adminid: [],
        AssigneeId: [],
        TaskmasterId: {},
        ReporterId: { Reporter: [] },
        MethodId: {
          work_instruction_id: [],
          task_closing_requirement: [],
          Controlplan: [],
          Failuremode: [],
        },
      }
  );
  const [reporterIndex, setReporterIndex] = useState();

  // Handle selection of categories and update subtask data
  const handleSelect = useCallback(
    async (
      categoryName: keyof AddDataDesignation,
      selectedItems: TaskDataType[],
      label: string,
      parents: any
    ) => {
      const newselectedItems = selectedItems.map((item) => {
        return {
          _id: item.id,
          name: item.category,
          DepartmentId: item?.parentId,
        };
      });
      console.log(parents, "thse are parents bo checakojsdfasdf");
      const formattedParents = parents.map((item: any) => {
        return {
          _id: item.id,
          name: item.category,
        };
      });

      let prevAdminId = [];
      let presentInReporter: any = [];
      let foundDepartment: any = [];
      if (categoryName === "Adminid") {
        prevAdminId = (TaskData as any)[categoryName] || [];
      }

      if (categoryName === "Adminid" && prevAdminId?.[0]?._id) {
        const prevId = prevAdminId[0]._id;

        // Find if prevAdminId exists in reporters
        presentInReporter = Array.isArray(TaskData?.ReporterId?.Reporter)
          ? TaskData.ReporterId.Reporter.flatMap(
              (reporter: any) => reporter?.designationId || []
            ).filter((item: any) => item?._id === prevId)
          : [];

        const taskDataLatest = store.getState().taskForm.currentSubtaskData;

        const taskManagersInTask = taskDataLatest?.Adminid ?? [];
        const assigneesInTask = taskDataLatest?.AssigneeId ?? [];
        const reportersInTask =
          taskDataLatest?.ReporterId?.Reporter?.map(
            (e: any) => e?.designationId
          ) ?? [];

        const allData = [
          ...assigneesInTask,
          ...reportersInTask.flat(),
        ];

        foundDepartment = allData?.filter((e) =>
          e.DepartmentId && Array.isArray(prevAdminId?.[0]?.DepartmentId)
            ? prevAdminId?.[0]?.DepartmentId.includes(e.DepartmentId)
            : e.DepartmentId === prevAdminId?.[0]?.DepartmentId
        );
      }

      console.log(
        "present in another reporter",
        presentInReporter,
        foundDepartment
      );
      dispatch(settaskChangeAPiFlag(true));
      

      if (selectedItems.length > 0) {
        dispatch(
          updateTaskData({
            ...(TaskData as any),

            DepartmentId: [
              ...((TaskData as any).DepartmentId || []).filter((item: any) =>
                !formattedParents.some(
                  (parent: any) => parent._id === item._id
                ) && foundDepartment?.length === 0
                  ? !(Array.isArray(prevAdminId?.[0]?.DepartmentId)
                      ? prevAdminId?.[0]?.DepartmentId.includes(item?._id)
                      : item?._id === prevAdminId?.[0]?.DepartmentId)
                  : true
              ),
              ...formattedParents,
            ].filter(
              (item, index, self) =>
                index === self.findIndex((t) => t._id === item._id)
            ),
            DesignationId: [
              ...((TaskData as any).DesignationId || []).filter(
                (item: any) =>
                  !newselectedItems.some(
                    (selected: any) => selected._id === item._id
                  ) &&
                  (presentInReporter.length > 0
                    ? true
                    : item._id !== prevAdminId?.[0]?._id)
              ),
              ...newselectedItems,
            ],
            [categoryName]:
              categoryName === ("Adminid" as keyof AddDataDesignation)
                ? newselectedItems // Replace the array entirely for AdminId
                : [
                    ...((TaskData as any)[categoryName] || []).filter(
                      (item: any) =>
                        !newselectedItems.some(
                          (selected: any) => selected._id === item._id
                        )
                    ),
                    ...newselectedItems,
                  ], // Append for other cases
          })
        );

        await saveSyncData(
          {
            ...TaskData,
            DepartmentId: [
              ...((TaskData as any).DepartmentId || []).filter((item: any) =>
                !formattedParents.some(
                  (parent: any) => parent._id === item._id
                ) && foundDepartment?.length === 0
                  ? !(Array.isArray(prevAdminId?.[0]?.DepartmentId)
                      ? prevAdminId?.[0]?.DepartmentId.includes(item?._id)
                      : item?._id === prevAdminId?.[0]?.DepartmentId)
                  : true
              ),
              ...formattedParents,
            ].filter(
              (item, index, self) =>
                index === self.findIndex((t) => t._id === item._id)
            ),
            DesignationId: [
              ...(((TaskData as any).DesignationId || []) as any).filter(
                (item: any) =>
                  !newselectedItems.some(
                    (selected: any) => selected._id === item._id
                  ) &&
                  (presentInReporter.length > 0
                    ? true
                    : item._id !== prevAdminId?.[0]?._id)
              ),
              ...newselectedItems,
            ],
            [categoryName]:
              categoryName === "Adminid"
                ? newselectedItems // Replace the array entirely for AdminId
                : [
                    ...((TaskData[categoryName] || []) as any).filter(
                      (item: any) =>
                        !newselectedItems.some(
                          (selected: any) => selected._id === item._id
                        )
                    ),
                    ...newselectedItems,
                  ], // Append for other cases
          },
          "time",
          "TaskForm"
        );
        showToast({
          messageContent: `${
            selectedItems.length > 1 ? label : selectedItems[0]?.category
          } Added Successfully`,
          type: "success",
        });

        console.log(TaskData, "this is task data bro check here");
        // dispatch(
        //   updateTaskData({
        //     ...TaskData,
        //     DesignationId: newselectedItems, // Replace the array entirely
        //   })
        // );
      }
    },
    [dispatch, TaskData]
  );
  const [requiredThingsDelete, setRequiredThingsDelete] =
    useState<requiredthings>();
  const [requiredThingsDeleteName, setRequiredThingsDeleteName] =
    useState<string>();
  const [deleteId, setDeleteId] = useState<string>();

  // Toggle dropdown visibility
  const getCategories = async (tablename: string) => {
    console.log("fetheced category called");

    let response;
    let dbName;
    switch (tablename) {
      case "MaterialCategory":
        dbName = await initializeDatabase("MaterialCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "Manpowercategory":
        dbName = await initializeDatabase("ManpowerCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "ToolCategory":
        dbName = await initializeDatabase("ToolCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "machinaryCategory":
        dbName = await initializeDatabase("MachinaryCategory");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "departmentdetails":
        dbName = await initializeDatabase("Departmentmaster");
        response = await window.electron.getCategoryData({ dbName });
        break;
      case "designationdetails":
        dbName = await initializeDatabase("Designationmaster");
        response = await window.electron.getCategoryData({ dbName });
        break;
      default:
        console.log("Invalid table name");
        response = null;
    }
    console.log(response, "this is response for selecte option");
    return (
      response?.map((e: { _id: string; name: string }) => ({
        id: e._id,
        category: e.name,
      })) || []
    );
  };
  const handleToggleDropdown = async (name: string, modelname?: string) => {
    if (modelname) {
      const data = await getCategories(modelname);
      setSelectedOptionApidata(data);
      (() => {
        switch (modelname) {
          case "MaterialCategory":
            SetprimaryLabelForAddCategoryType("Add Materials Category");
            return;
          case "machinaryCategory":
            SetprimaryLabelForAddCategoryType("Add Machinery Category");
            return;
          case "ToolCategory":
            SetprimaryLabelForAddCategoryType("Add Tool Category");
            return;
          case "Manpowercategory":
            SetprimaryLabelForAddCategoryType("Add Manpower Category");
            return;
          case "departmentdetails":
            SetprimaryLabelForAddCategoryType("Add Department");
            return;
          default:
            return <>Unknown </>;
        }
      })();
    }

    dispatch(openPopup(name));
  };

  const handleReporter = (item: any[], reportedData: any, parents: any) => {
    const newFormat = item.map((el) => ({
      _id: el.id,
      name: el.category,
      DepartmentId: el.parentId,
    }));
    const formattedParents = parents.map((item: any) => {
      return {
        _id: item.id,
        name: item.category,
      };
    });

    console.log(newFormat);
    console.log(TaskData, "this is taskdata bro checkoiutasdf");
    let updatedReporters = [...(TaskData?.ReporterId?.Reporter || [])];

    // Find if the currentLevel exists in the array
    const existingIndex = updatedReporters.findIndex(
      (_, index) => index === currentLevel
    );

    if (existingIndex !== -1) {
      // If index exists, update designationId
      updatedReporters = updatedReporters.map((reporter, index) =>
        index === currentLevel
          ? {
              ...reporter,
              designationId: [...(reporter.designationId || []), ...newFormat], // Append new designation
            }
          : reporter
      );
    } else {
      // If index does not exist, add a new entry
      updatedReporters.push({
        _id: String(Date.now()), // Ensure unique _id
        Level: updatedReporters.length + 1, // Add the missing Level property
        designationId: newFormat,
      });
    }

    console.log("updatedata", updatedReporters);

    // Create a new state object with updated Reporter data
    const updatedtaskData = {
      ...TaskData,
      DepartmentId: [
        ...((TaskData as any).DepartmentId || []).filter(
          (item: any) =>
            !formattedParents.some((parent: any) => parent._id === item._id)
        ),
        ...formattedParents,
      ],
      DesignationId: [
        ...((TaskData as any).DesignationId || []).filter(
          (item: any) =>
            !newFormat.some((selected: any) => selected._id === item._id)
        ),
        ...newFormat,
      ],
      ReporterId: {
        _id: TaskData.ReporterId?._id || "1", // Ensure _id is always a string
        Reporter: updatedReporters, // Update only the Reporter array
      },
    };

    showToast({
      messageContent: `${
        item.length > 1 ? "Reporters" : "Reporter"
      } Added Successfully`,
      type: "success",
    });

    dispatch(updateTaskData(updatedtaskData as any));
    saveSyncData(updatedtaskData, "time", "TaskForm");

    dispatch(settaskChangeAPiFlag(true));
  };

  const [levelIndex, setLevelIndex] = useState(null);

  const deleteLevel = async (level: number) => {
    const reporterData = TaskData?.ReporterId?.Reporter || [];
    console.log(reporterData, "pankajji");
    // Remove the reporter at the given level
    const updatedReporters = reporterData.filter(
      (_: any, index: number) => index !== level
    );
    const latestTaskData = store.getState().taskForm.currentSubtaskData;
    // ReporterId: {
    //   Reporter: [],
    // },
    console.log(updatedReporters, "pankajji");
    const updatedTaskData = {
      ...latestTaskData,
      ReporterId: {
        _id: latestTaskData?.ReporterId?._id || "1",
        Reporter: updatedReporters,
      },
    };
    console.log(updatedTaskData, "deeeeev");
    const designationIdsToDelete =
      latestTaskData?.ReporterId?.Reporter?.[level]?.designationId?.map(
        (d: any) => d
      ) || [];
    console.log("tjese are ids to delete", designationIdsToDelete);
    await dispatch(updateTaskData(updatedTaskData));

    await saveSyncData(updatedTaskData, "time", "TaskForm");
    dispatch(settaskChangeAPiFlag(true));
    await deleteDesignationsFromTask(
      designationIdsToDelete?.map((e) => e._id),
      designationIdsToDelete?.map((e) => e.DepartmentId)
    );
  };

  const deleteSublevel = async (level: number, designationId: string) => {
    let updatedReporters = TaskData?.ReporterId?.Reporter?.map(
      (reporter: any) => ({
        ...reporter,
        designationId: [...reporter.designationId], // Create a copy of designationId array
      })
    );
    const latestTaskData = store.getState().taskForm.currentSubtaskData;
    // Ensure level exists
    if (updatedReporters && updatedReporters[level]) {
      // Remove the specific designation
      updatedReporters[level].designationId = updatedReporters[
        level
      ].designationId.filter((item: any) => item._id !== designationId);

      if (updatedReporters[level].designationId.length === 0) {
        updatedReporters.splice(level, 1);
      }
    }

    await dispatch(
      updateTaskData({
        ...latestTaskData,
        ReporterId: {
          _id: TaskData.ReporterId?._id || "1",
          Reporter: updatedReporters || [],
        },
      })
    );

    await saveSyncData(
      {
        ...latestTaskData,
        ReporterId: {
          _id: TaskData.ReporterId?._id || "1",
          Reporter: updatedReporters || [],
        },
      },
      "time",
      "TaskForm"
    );
    await deleteDesignationsFromTask(
      [designationId],
      [deleteDepartmentId as any]
    );
    dispatch(settaskChangeAPiFlag(true));
  };

  console.log("reporters data in task", TaskData?.ReporterId);

  const handleDeleteReporter = async () => {
    dispatch(settaskChangeAPiFlag(true));
    await dispatch(
      updateTaskData({
        ...TaskData,
        ReporterId: { _id: TaskData.ReporterId?._id || "1", Reporter: [] },
      })
    );

    await saveSyncData(
      {
        ...TaskData,
        ReporterId: { _id: TaskData.ReporterId?._id || "1", Reporter: [] },
      },
      "time",
      "TaskForm"
    );
  };
  const deleteDesignationsFromTask = async (
    ids: string[],
    departments: string[]
  ) => {
    const taskDataLatest = store.getState().taskForm.currentSubtaskData;

    const taskManagersInTask = taskDataLatest?.Adminid ?? [];
    const assigneesInTask = taskDataLatest?.AssigneeId ?? [];
    const reportersInTask =
      taskDataLatest?.ReporterId?.Reporter?.map((e: any) => e?.designationId) ??
      [];

    const allData = [
      ...taskManagersInTask,
      ...assigneesInTask,
      ...reportersInTask.flat(),
    ];
    // Check if the designation is still used elsewhere
    const foundItem = allData?.filter(
      (e: any) =>
        e?._id && (Array.isArray(ids) ? ids.includes(e._id) : e._id === ids)
    );
    const foundDepartment = allData?.filter((e) =>
      e.DepartmentId && Array.isArray(departments)
        ? departments.includes(e.DepartmentId)
        : e.DepartmentId === departments
    );
    console.log(
      foundDepartment,
      "fAllasdfasdfasdf related designations in task (assignees, admins, reporters)asdfasdfasdfasd"
    );
    console.log(foundItem, "Found items that are still in use");

    //Skip deletion if any of the IDs are still in use
    if (foundItem?.length > 0) {
      console.log("Skipped deletion. Some designations are still in use.");
      return;
    }

    // Safe to delete from DesignationId
    const updatedTaskdata = {
      ...(taskDataLatest as any),
      DesignationId: [
        ...((taskDataLatest as any).DesignationId || []).filter(
          (item: any) =>
            !(Array.isArray(ids) ? ids.includes(item?._id) : item?._id === ids)
        ),
      ],
    };

    try {
      dispatch(updateTaskData(updatedTaskdata));
      await saveSyncData(updatedTaskdata, "time", "TaskForm");
      const TaskDataAfter = store.getState().taskForm.currentSubtaskData;
      // Remove DepartmentId if not found
      if (foundDepartment?.length === 0) {
        dispatch(
          updateTaskData({
            ...(TaskDataAfter as any),
            DepartmentId: [
              ...((TaskDataAfter as any).DepartmentId || []).filter(
                (item: any) =>
                  !(Array.isArray(departments)
                    ? departments.includes(item?._id)
                    : item?.DepartmentId === departments)
              ),
            ],
          })
        );

        await saveSyncData(
          {
            ...TaskDataAfter,
            DepartmentId: [
              ...((TaskDataAfter as any).DepartmentId || []).filter(
                (item: any) =>
                  !(Array.isArray(departments)
                    ? departments.includes(item?._id)
                    : item?.DepartmentId === departments)
              ),
            ],
          },
          "time",
          "TaskForm"
        );
        dispatch(settaskChangeAPiFlag(true));
      }

      console.log(taskDataLatest, "thisis tasklastestadata");
      dispatch(settaskChangeAPiFlag(true));
      console.log("Designation removed from task successfully.");
    } catch (error) {
      console.error("Failed to delete designations from task:", error);
    }
  };

  console.log("this is for checking", [
    ...(Array.isArray(TaskData?.ReporterId?.Reporter)
      ? TaskData.ReporterId.Reporter.map((reporter) =>
          reporter?.designationId ? reporter.designationId : []
        ).reduce((acc, curr) => acc.concat(curr), [])
      : []),
  ]);

  const [designationId, setdesignationId] = useState();
  return (
    <>
      <div className={styles.task_creation_designation_container}>
        <div className={styles.task_creation_designation_header}>
          <SuryconLogo />
          <h3>Task Allocation</h3>
        </div>
        <div className={styles.task_creation_master_row}>
          {/* Task Manager  Section*/}

          <div style={{ minWidth: "50px", position: "relative" }}>
            <AddToolTip
              label="Task Manager"
              onClick={() => {
                setIsClosingDelete(true);
                dispatch(closePopup("DeleteTaskAllocation"));
                setTimeout(() => {
                  handleToggleDropdown("TaskManager", "departmentdetails");
                  setSelectedOption("departmentdetails");
                }, 400);
              }}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              data={TaskData?.Adminid}
              isEdit={isEdit}
              handleDelete={(item) => {
                dispatch(closePopup("DeleteTaskAllocation"));
                setTimeout(() => {
                  setDeleteId("Adminid"); // <-- Yahan "AdminId" ko "Adminid" karo
                  setRequiredThingsDelete(item);
                  setRequiredThingsDeleteName("Task Manager");
                  dispatch(openPopup("DeleteTaskAllocation"));
                }, 400);
              }}
            />
            {popups["TaskManager"] && (
              <AddCategoryType
                primaryLabel2="Add Task Manager"
                primaryLabel={"Department"}
                modelname={"taskManagerDataDetail"}
                singleSelected={true}
                isStepForm={true}
                title="Add Task Manager"
                data={selectedOptionApidata as TaskDataType[]}
                initialSelected={[
                  ...(TaskData?.Adminid || []),
                  ...(TaskData?.AssigneeId || []),
                ]}
                placeholder="Search"
                label="Task Manager"
                buttonLabel="Add Category"
                onSelect={(item, label, parents) => {
                  console.log(parents, "these parents are hrer");
                  handleSelect("Adminid", item, "", parents);
                }}
                onClose={() => dispatch(closePopup("TaskManager"))}
              />
            )}
          </div>

          {/* Assigni Section */}
          <div style={{ minWidth: "50px", position: "relative" }}>
            <AddToolTip
              label="Assign To"
              onClick={() => {
                setIsClosingDelete(true);
                dispatch(closePopup("DeleteTaskAllocation"));
                setTimeout(() => {
                  handleToggleDropdown("AssigneeId", "departmentdetails");
                  setSelectedOption("departmentdetails");
                }, 400);
              }}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              data={TaskData.AssigneeId}
              isEdit={isEdit}
              handleDelete={(item) => {
                dispatch(closePopup("DeleteTaskAllocation"));
                setTimeout(() => {
                  setDeleteId("AssigneeId");
                  setRequiredThingsDelete(item);
                  setRequiredThingsDeleteName("Assignee");
                  dispatch(openPopup("DeleteTaskAllocation"));
                }, 400);
              }}
            />
            {popups["AssigneeId"] && (
              <AddCategoryType
                primaryLabel2="Add Assignees"
                isStepForm={true}
                primaryLabel={"Department"}
                modelname={"assigneeDataDetail"}
                data={selectedOptionApidata as TaskDataType[]}
                title="Add Assignees"
                placeholder="Search"
                label="Assignee"
                buttonLabel="Add Category"
                initialSelected={[
                  ...(TaskData?.Adminid || []),
                  ...(TaskData?.AssigneeId || []),
                  ...(Array.isArray(TaskData?.ReporterId?.Reporter)
                    ? TaskData.ReporterId.Reporter.flatMap((reporter) =>
                        Array.isArray(reporter.designationId)
                          ? reporter.designationId.filter(Boolean)
                          : []
                      )
                    : []),
                ]}
                onSelect={(item, label, parents) => {
                  handleSelect("AssigneeId", item, "Assignees", parents);
                }}
                onClose={() => dispatch(closePopup("AssigneeId"))}
              />
            )}
          </div>
          <div style={{ minWidth: "50px", position: "relative" }}>
            <AddToolTip
              label="Reporters"
              isEdit={isEdit}
              isReporter={true}
              setReporterAdded={setReporterAdded}
              reporterAdded={reporterAdded}
              onClick={() => {
                setInitialReporterData([
                  {
                    _id: "1",
                    Level: -1,
                    designationId: [
                      {
                        _id: "1",
                        name: "",
                      },
                    ],
                  },
                ]);
                setReporterAdded(true);
                setIsOpen(true);
              }}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              // data={TaskData.AssigneeId}
            />
            <Dialogbox isOpen={reportedData ? true : isOpen}>
              <ReportingLevel
                key={JSON.stringify(reportedData)}
                isEdit={isEdit}
                setReporterAdded={setReporterAdded}
                reportedData={
                  reportedData?.length > 0 ? reportedData : initialReporterData
                }
                onAddLevel={() => console.log("yes")}
                closeDialogBox={() => {
                  handleDeleteReporter();
                  setIsOpen(false);
                }}
                onAddLevelData={(id) => {
                  setCurrentLevel(id);
                  handleToggleDropdown("Reporter", "departmentdetails");
                  setSelectedOption("departmentdetails");
                }}
                onDeleteReportedData={(index) => {
                  if (index !== undefined && index !== null) {
                    setLevelIndex(index);
                    if (TaskData?.ReporterId?.Reporter?.[index] == undefined) {
                      deleteLevel(index);
                      return;
                    }
                    dispatch(openPopup("DeleteReporterLevel"));
                  }
                }}
                onDeleteRoleName={(
                  levelIndex,
                  designationIdIndex,
                  departmentId
                ) => {
                  setDeleteDepartmentId(departmentId);
                  setReporterIndex(levelIndex);
                  setdesignationId(designationIdIndex);
                  dispatch(openPopup("DeleteReporter"));
                }}
              />
            </Dialogbox>
            {popups["Reporter"] && (
              <AddCategoryType
                primaryLabel2="Add Reporters"
                isStepForm={true}
                primaryLabel={"Department"}
                modelname={"reporterDataDetail"}
                title="Reporters"
                label="Reporter"
                data={selectedOptionApidata as TaskDataType[]}
                placeholder="Search"
                initialSelected={[
                  ...(Array.isArray(TaskData?.ReporterId?.Reporter)
                    ? TaskData.ReporterId.Reporter.map((reporter) =>
                        reporter?.designationId ? reporter.designationId : []
                      ).reduce((acc, curr) => acc.concat(curr), [])
                    : []),
                  ...(TaskData?.AssigneeId || []),
                ]}
                buttonLabel="Add Category"
                onSelect={(item, label, parents) => {
                  handleReporter(item, reportedData, parents);
                }}
                onClose={() => dispatch(closePopup("Reporter"))}
              />
            )}
            {popups["DeleteTaskAllocation"] && (
              <DeletePopup
                width="23rem"
                height="calc(100% - 9rem)"
                heightupperlimit="0rem"
                header={`Are you sure you want to delete this ${requiredThingsDeleteName} ?`}
                isClosing={isClosingDelete}
                callbackDelete={async () => {
                  if (deleteId) {
                    setIsClosingDelete(true); // Animation flag (optional)
                    setTimeout(async () => {
                      const newRequiredThings = (
                        TaskData[deleteId as keyof AddData] as {
                          _id: string;
                          name: string;
                        }[]
                      )?.filter(
                        (item) => item._id !== requiredThingsDelete?._id
                      );
                      const updatedTaskData =
                        store.getState().taskForm.currentSubtaskData;

                      await dispatch(
                        updateTaskData({
                          ...updatedTaskData,
                          [deleteId]: newRequiredThings,
                        } as any)
                      );

                      await saveSyncData(
                        {
                          ...updatedTaskData,
                          [deleteId]: newRequiredThings,
                        },
                        "time",
                        "TaskForm"
                      );
                      await deleteDesignationsFromTask(
                        [requiredThingsDelete?._id as any],
                        [requiredThingsDelete?.DepartmentId as any]
                      );
                      dispatch(closePopup("DeleteTaskAllocation"));
                      setIsClosingDelete(false); // Reset animation flag
                      showToast({
                        messageContent: `${requiredThingsDelete?.name} deleted Successfully!`,
                        type: "success",
                      });
                    }, 400); // <-- Yahan delay set karen (ms me)
                  }
                }}
                onClose={() => {
                  dispatch(closePopup("DeleteTaskAllocation"));
                  setIsClosingDelete(false); // Reset for next time
                }}
              >
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      {requiredThingsDeleteName}
                    </p>
                    <div
                      className=""
                      style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      <h4 style={{ color: "var(--text-black-87)" }}>
                        {requiredThingsDelete?.name}
                      </h4>
                    </div>
                  </div>
                </div>
              </DeletePopup>
            )}
            {popups["DeleteReporter"] && (
              <DeletePopup
                width="23rem"
                height="calc(100% - 9rem)"
                heightupperlimit="0rem"
                header={`Are you sure you want to delete this Reporter ?`}
                callbackDelete={async () => {
                  if (
                    reporterIndex !== undefined &&
                    designationId !== undefined
                  ) {
                    deleteSublevel(reporterIndex, designationId);
                    showToast({
                      messageContent: `${
                        TaskData?.ReporterId?.Reporter[
                          reporterIndex
                        ]?.designationId?.find((e) => e?._id == designationId)
                          ?.name
                      } deleted successfully`,
                      type: "success",
                    });

                    dispatch(settaskChangeAPiFlag(true));
                  }
                }}
                onClose={() => {
                  dispatch(closePopup("DeleteReporter"));
                }}
              >
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Reporter
                    </p>
                    <div
                      className=""
                      // style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      <h4 style={{ color: "var(--text-black-87)" }}>
                        {
                          TaskData?.ReporterId?.Reporter[
                            reporterIndex
                          ]?.designationId?.find((e) => e?._id == designationId)
                            ?.name
                        }
                      </h4>
                    </div>
                  </div>
                </div>
              </DeletePopup>
            )}
            {popups["DeleteReporterLevel"] && (
              <DeletePopup
                width="23rem"
                height="calc(100% - 9rem)"
                heightupperlimit="0rem"
                header={`Are you sure you want to delete these Reporters ?`}
                callbackDelete={async () => {
                  if (levelIndex) {
                    deleteLevel(levelIndex);
                    showToast({
                      messageContent: `Reporters deleted successfully`,
                      type: "success",
                    });
                    dispatch(settaskChangeAPiFlag(true));
                  }
                }}
                onClose={() => {
                  dispatch(closePopup("DeleteReporterLevel"));
                }}
              >
                <div className={styles.summaryDivData}>
                  <div className={styles.summaryDataContent}>
                    <p
                      style={{ color: "var(--text-black-60)" }}
                      className="p_tag_14px"
                    >
                      Reporters
                    </p>
                    <div
                      className=""
                      // style={{ display: "flex", gap: "1rem", flexWrap: "wrap" }}
                    >
                      {TaskData?.ReporterId?.Reporter[
                        levelIndex
                      ]?.designationId?.map((e) => (
                        <h4 style={{ color: "var(--text-black-87)" }}>
                          {e?.name}
                        </h4>
                      ))}
                    </div>
                  </div>
                </div>
              </DeletePopup>
            )}
          </div>
        </div>
      </div>
      <div className={styles.taskCreation_reporter_popup}></div>
    </>
  );
};

export default TaskCreationDesignation;
