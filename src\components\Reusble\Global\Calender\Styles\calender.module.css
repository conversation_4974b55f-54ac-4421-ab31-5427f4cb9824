.calendar {
  width: 100%;
  border-radius: 12px;
  background-color: white;
  overflow: hidden;
  border: 1px solid #f0e6ff;
  position: absolute;
  z-index: 10000;
  backdrop-filter: blur(150px);
  box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.2);

}

.header {
  display: flex;
  justify-content: space-between;
  padding: 16px 20px;
  background-color: white;
  border-bottom: 1px solid #f5f5f5;
}

/* Custom Dropdown Styles */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdownToggle {
  background: transparent;
  border: none;
  font-size: 1rem;
  font-weight: 300;
  color: var(--text-black-87);
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 0;
}

.arrow {
  font-size: 12px;
  margin-left: 6px;
  transform: rotate(180deg);
  transition: all 0.3s ease-in-out;
}
.revarrow {
  font-size: 12px;
  margin-left: 6px;
  transform: rotate(360deg) translateY(-10%);
  transition: all 0.3s ease-in-out;
}

.dropdownMenu {
  overflow-y: auto;
  max-height: 200px; /* Adjust the height as needed */
}
.dropdownmenuOuterContainer {
  position: absolute;
  top: calc(100% + 8px);
  transform: translateX(-50%);
  overflow-y: scroll;

  min-width: 180px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 10;
  padding: 8px;
  max-height: 10rem;
}
.popupDropdownMonth{
  left: 5rem;
}
.popupDropdownYear{
  left: -1.5rem
}
/* Custom scrollbar */
.dropdownMenu::-webkit-scrollbar {
  width: 6px;
}

.dropdownMenu::-webkit-scrollbar-track {
  background: transparent;
}

.dropdownMenu::-webkit-scrollbar-thumb {
  background-color: #006d77;
  border-radius: 6px;
}

.dropdownItem {
  padding: 10px 16px;
  cursor: pointer;
  border-radius: 30px;
  margin: 4px 0;
  transition: all 0.2s ease;
  font-size: 16px;
  text-align: center;
}

.dropdownItem:hover {
  background-color: #f5f5f5;
}

.dropdownItem.active {
  background-color: white;
  color: #006d77;
  border: 2px solid #006d77;
  font-weight: 500;
}

/* Calendar Grid Styles */
.daysHeader {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  padding: 10px 0;
  background-color: white;
}

.dayName {
  font-size: 0.75rem;
  color: #666;
  font-weight: 600;
}

.daysGrid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: repeat(6, 1fr);
  background-color: white;
  padding-bottom: 10px;
}

.day {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

.day:hover {
  background-color: #f5f5f5;
  border-radius: 50%;
}

.otherMonth {
  color: #ccc;
  pointer-events: none;
}

.current_date {
  color: #006d77;
}
.selected {
  background-color: #006d77;
  color: white;
  border-radius: 50%;
}

.selected:hover {
  background-color: #006d77;
}
