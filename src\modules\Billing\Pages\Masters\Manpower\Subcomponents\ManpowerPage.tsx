import { useEffect, useRef, useState } from "react";
import { Loader } from "../../../../../../assets/loader";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import {
  useDeleteManpowerByToolIdMutation,
  useLazyGetManpowerDesiginationDetailByIdQuery,
} from "../../../../../../redux/api/Modules/Billing/Billingapi";
import TMMMNav from "../../../../../../components/Reusble/TMMMNav";
import styles from "../Styles/Manpower.module.css";
import MasterCard from "../../../../../../components/Reusble/Billing/Masters/MasterCard";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import { closePopup } from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import { resetFormManpowerData } from "../../../../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import { initializeDatabase } from "../../../../../../functions/functions";
import { useParams } from "react-router-dom";
import { useNestedPouchSearch } from "../../../../../../functions/useNestedLocalSearch";
import ManpowerDiscard from "./ManpowerDiscard";
import { useToast } from "../../../../../../hooks/ToastHook";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../../redux/store";
import {
  clearFetchedMasters,
  SetCategoryId,
  setFetchedMastersDesignation,
  setSearchData,
} from "../../../../../../redux/features/Modules/Masters";

const ManpowerPage = () => {
  const data = useSelector(
    (state: RootState) => state.masterReduxSlice.fetchedData
  );
      const searchedData = useSelector((state: RootState) => state.masterReduxSlice.searchedData);

  const currentOpenPopup = useAppSelector((state) => state.popup.popups);
  const formData = useAppSelector((state) => state.masterForm.formManpowerData);
  const dispatch = useAppDispatch();
  const { manpowerCategoryId } = useParams();
  // const detectChanges = useAppSelector((state) => state.backupSlice.isOpen);

  const [page, setPage] = useState<number>(1);
  const [manpowerCardData, setManpowerCardData] = useState<any>([]);
  const [manpowerSearchCardData, setSearchManpowerCardData] = useState<any>([]);
  const [editManpowerData, setEditManpowerData] = useState<any>([]);
  const localChange = useAppSelector(
    (state) => state.backupSlice.isLocalChange
  );
  const [deleteManpowerDesignation] = useDeleteManpowerByToolIdMutation();
  const [getManpowerDesiginationDetails] =
    useLazyGetManpowerDesiginationDetailByIdQuery();
  const searchKey = useAppSelector((state) => state.taskMaster.searchKey);
  const [searchLocalKey, setSearchLocalKey] = useState("");
  const showToast = useToast();
  const getDatafromDb = async (p: any, id: string) => {
    const dbName = await initializeDatabase("Manpowerdesignation");
    const fetchedData = await window.electron.getDocumentByParentId({
      dbName,
      page: p,
      catId: id,
      categoryId: "manpowerCategoryId",
      isDeletedNext: false,
      needSorting: true,
    });


    if (p === 1) {
      setManpowerCardData(fetchedData);
      dispatch(setFetchedMastersDesignation({ data: fetchedData, page: p }));
    } else {
      const newData = [...data, ...fetchedData];
      setManpowerCardData((prev: any) => [...prev, ...fetchedData]);
      dispatch(setFetchedMastersDesignation({ data: newData, page: p }));
    }
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const { scrollHeight, clientHeight, scrollTop } = target;

    if (scrollTop + clientHeight >= scrollHeight - 1) {
      setPage((prev) => prev + 1);
    }
  };

  // useEffect(() => {
  //   if(localChange){

  //     setPage(1);
  //   }
  //   // setManpowerCardData([]);
  // }, [detectChanges]);

  useEffect(() => {
    if (page && manpowerCategoryId) {
      getDatafromDb(page, manpowerCategoryId);
    }
  }, [page]);

  useEffect(() => {
    setSearchLocalKey(searchKey);
  }, [searchKey]);

  useNestedPouchSearch({
    pathRecord: "Manpowerdesignation",
    searchKey: searchLocalKey,
    setData: setSearchData,
    setPage,
    key: "name",
    extraSearchParams: {
      catId: manpowerCategoryId,
      categoryId: "manpowerCategoryId",
      isDeletedNext: false,
    },
  });

  const navRef = useRef<HTMLDivElement>(null);
  const mainContentRef = useRef<HTMLDivElement>(null);
  const [width, setWidth] = useState<null | number>(null);
  const clientFunction = () => {
    const mainContentWidth =
      mainContentRef?.current?.getBoundingClientRect()?.width;
    // console.log('details of card view container in useEffect details',details,);
    // setWidth(details?.width);
    console.log("inner width");
    // if (window.innerWidth < 1200) {
    navRef.current?.style.setProperty("width", `${mainContentWidth}px`);
    // }
  };
  const condition =
    mainContentRef.current &&
    mainContentRef.current.getBoundingClientRect().width < 1200;
  useEffect(() => {
    clientFunction();
    const handleResize = () => clientFunction();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [window.innerWidth, condition]);

  useEffect(() => {
    return () => {
      dispatch(clearFetchedMasters());
    };
  }, []);
  useEffect(() => {
    console.log(manpowerCategoryId, "this is id>>>>>>>>>>");
    dispatch(SetCategoryId(manpowerCategoryId as string));
  }, [manpowerCategoryId]);

  return (
    <>
      <div ref={navRef}>
        <TMMMNav Label={"Manpower"} TargetForm={"AddManpowerForm"} />
      </div>
      <div
        ref={mainContentRef}
        className={styles.main_content_wrapper}
        style={{ marginTop: "1.5rem" }}
      >
        <div className={styles.cardview} onScroll={handleScroll}>
          <div className={`${styles.inner_cardview} ${styles.inner_cardview2}`}>
            {(searchedData.length > 0
              ? searchedData
              : data
            )?.map((item: any) => (
              <MasterCard
                key={item?._id}
                variant="manpower"
                callbackEditData={async () => {
                  const response = await getManpowerDesiginationDetails({
                    manpowerId: item?._id,
                  });
                  setEditManpowerData(response?.data?.data);
                  return response;
                }}
                editData={editManpowerData}
                data={{
                  _id: item?._id,
                  title: item?.name,
                  items: [
                    { title: "Skills", name: item?.skillcount },
                    {
                      title: "Type",
                      name:
                        item?.Types?.charAt(0).toUpperCase() +
                        item?.Types?.slice(1),
                    },
                  ],
                }}
              />
            ))}
            {data.length === 0 && searchedData.length === 0 && (
              <div className={styles.loader_loading}>
                <img
                  src={Loader.suryaconLogo}
                  alt="Loading..."
                  className={styles.loader_loading_image}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      {currentOpenPopup["deleteManpower"] && (
        <DeletePopup
          header="Are you sure you want to delete this Manpower?"
          height="calc(100% - 9rem"
          callbackDelete={async () => {
            await deleteManpowerDesignation({
              manpowerId: formData?._id,
            }).unwrap();

            showToast({
              messageContent: `Manpower Deleted Successfully!`,
              type: "success",
            });

            dispatch(closePopup("deleteManpower"));
            dispatch(resetFormManpowerData());
          }}
          onClose={() => {
            dispatch(closePopup("deleteManpower"));
            dispatch(resetFormManpowerData());
          }}
        >
          <ManpowerDiscard
            formData={formData}
            // initialFormData={initialFormData}
            // formMode={formMode}
            // deletedFormData={deletedFormData}
            // deletedGradeData={deletedGradeData}
          />
        </DeletePopup>
      )}
    </>
  );
};

export default ManpowerPage;
