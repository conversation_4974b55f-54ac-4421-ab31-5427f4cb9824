// add location from made by rat<PERSON><PERSON><PERSON> singh from start

import { FC, useCallback, useRef, useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { resetInputValues } from "../../../../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { closePopup } from "../../../../../../../redux/features/Modules/Reusble/popupSlice";
import styles from "../AddLocation/Styles/AddLocation.module.css";

import {
  AttachmentIcon,
  CloseIcon,
  SuryaconLogoSecondary,
} from "../../../../../../../assets/icons";
import FloatingLabelInput from "../../../../../../../components/Reusble/Global/FloatingLabel";

import Button from "../../../../../../../components/Reusble/Global/Button";
import { RootState } from "../../../../../../../redux/store";
import RadioBtns from "../../../../../../../components/Reusble/Global/RadioBtns";
import Datafield from "../../../../../../../components/Reusble/Billing/Masters/Datafield";

import {
  setSelectedTowerLocationData,
  setTowerLocationFormData,
} from "../../../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/ProjectSlice";

import {
  compressImage,
  getFileName,
  slicedData,
} from "../../../../../../../functions/functions";
import { useParams } from "react-router-dom";
import {
  useAddTowerLocationMutation,
  useUpdateTowerLocationDetailsMutation,
} from "../../../../../../../redux/api/Modules/Billing/Billingapi";
import { useToast } from "../../../../../../../hooks/ToastHook";

interface AddLocationFormProps {}

interface TowerLocationData {
  category?: string;
  _id?: string;
  name: string;
  location_drawing: string[] | File[];
  area: string;
  number_of_basements: string;
  location_duration: string;
  remarks: string;
  conventionals?: any[];
  mivan?: any[];
  number_of_floors: string; // Ensure it's always a string
  structure_type: string; // Ensure it's always a string
  project_id: string; // Added to fix the error
}

export const AddLocationForm: FC<AddLocationFormProps> = () => {
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [isClosing, setIsClosing] = useState<boolean>(false);
  const TowerLocationData = useSelector(
    (state: RootState) => state?.projectLocalDb?.towerLocationFormdata
  );
  console.log(TowerLocationData, "thisistowerlocaitonfiormdat");
  // const [selectedTowerType, setselectedTowerType] = useState<String>();
  // const [TowerLocationData, setTowerLocationData] = useState<TowerLocationData>(
  //   towerLocationFormdata
  //     ? towerLocationFormdata
  //     : {
  //         category: "",
  //         name: "",
  //         location_drawing: [],
  //         project_id: "",
  //         area: "",
  //         number_of_floors: "",
  //         number_of_basements: "",
  //         location_duration: "",
  //         structure_type: "",
  //         conventionals: [],
  //         mivan: [],
  //         remarks: "",
  //       }
  // );

  const { projectId } = useParams();
  console.log(projectId, "this is towerlocation form data");
  const showToast = useToast();
  const [drawingsLoader, setDrawingsLoader] = useState(false);

  // handleinput for change for change values
  const handleInputChange = (key: string, data: any) => {
    // setTowerLocationData({
    //   ...TowerLocationData,
    //   [key]: data,
    // });
    const trimmedData = typeof data == typeof "string" ? data.trim() : data;
    dispatch(
      setTowerLocationFormData({
        ...TowerLocationData,
        [key]: trimmedData,
      })
    );

    setinvalidErrors(invalidErrors?.filter((e) => e !== key));
  };
  const selectedTowerLocationdata = useSelector(
    (state: RootState) => state.projectLocalDb.selectedTowerLocationdata
  );
  const drawingsInputRef = useRef<HTMLInputElement>(null);
  // const handleIconClick = () => {
  //   if (fileInputRef.current) {
  //     fileInputRef.current.click();
  //   }
  // };

  console.log(currentStep, "this is selected project id data");
  const dispatch = useDispatch();

  const handleDrawingClick = () => {
    if (drawingsInputRef.current) {
      drawingsInputRef.current.click();
    }
  };

  dispatch(resetInputValues());
  console.log(
    selectedTowerLocationdata,
    "this is selected tower location data"
  );

  const handleDrawingsChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const fileInput = event.target;
    const files = fileInput.files ? Array.from(fileInput.files) : [];
    if (!files || files.length === 0) return;

    try {
      setDrawingsLoader(true);
      const processedFiles: File[] = [];
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        if (file.type.match(/image\/(jpeg|jpg|png)/)) {
          try {
            const compressedFile = await compressImage(file, 0.2);
            processedFiles.push(compressedFile);
          } catch (error) {
            console.error(`Error compressing file ${file.name}:`, error);
            processedFiles.push(file);
          }
        } else {
          processedFiles.push(file);
        }
      }

      handleInputChange("location_drawing", [
        ...(TowerLocationData?.location_drawing ?? []),
        ...processedFiles,
      ]);
      if (drawingsInputRef.current) {
        drawingsInputRef.current.value = "";
      }
      fileInput.value = "";
    } catch (error) {
      console.error("Error processing drawings:", error);
      showToast({
        messageContent: "Error processing some files",
        type: "error",
      });
    } finally {
      setDrawingsLoader(false);
    }
  };
  const [addTowerlocationApi] = useAddTowerLocationMutation();
  const [updateTowerLocationDetails] = useUpdateTowerLocationDetailsMutation();
  const [invalidErrors, setinvalidErrors] = useState<string[]>([]);
  // const [isClosing, setIsClosing] = useState<boolean>(false);
  console.log("iscloseing", isClosing);
  const handleClose = useCallback(() => {
    setIsClosing(true);
    setTimeout(() => dispatch(closePopup("AddLocationForm")), 400);
  }, [isClosing]);
  console.log(TowerLocationData, "project data is here jagga bro");
  const formRef = useRef<HTMLDivElement>(null);

  const handleSubmitTower = async () => {
    console.log(TowerLocationData, "thisisdatabefore subbmission");
    if (TowerLocationData) {
      if (TowerLocationData?.category == "Non-Tower") {
        const {
          mivan,
          conventionals,
          number_of_floors,
          structure_type,
          project_id,
          ...filteredData
        } = TowerLocationData;
        console.log(filteredData, "bumbardino crocodilo");
        if (filteredData?._id) {
          // Check for no changes before submitting update
          const originalData = selectedTowerLocationdata;
          // Remove fields that are not part of filteredData for comparison
          const {
            mivan: origMivan,
            conventionals: origConventionals,
            number_of_floors: origNumFloors,
            structure_type: origStructType,
            project_id: origProjectId,
            ...originalFiltered
          } = originalData || {};
          const isNoChange =
            JSON.stringify(filteredData) === JSON.stringify(originalFiltered);
          if (isNoChange) {
            showToast({
              messageContent: "There Were No changes!",
              type: "warning",
            });
            return;
          }
          const formData = new FormData();
          Object.keys(filteredData).forEach((key) => {
            if (key === "location_drawing") {
              // Handle multiple project drawings
              if (Array.isArray(filteredData.location_drawing)) {
                const projectDrawingStrings: string[] = [];
                filteredData.location_drawing?.forEach((file) => {
                  if (typeof file === "string") {
                    projectDrawingStrings.push(file); // Collect strings in an array
                  } else {
                    formData.append("location_drawing", file as File); // Append file
                  }
                });
                formData.append(
                  "location_drawing",
                  JSON.stringify(projectDrawingStrings)
                ); // Append array of strings as JSON
              }
            } else {
              formData.append(key, (filteredData as any)[key]);
            }
          });
          formData.append("project_id", projectId as string);
          console.log(
            filteredData,
            "this is location data for non toasdfasdfwer "
          );
          const response = await updateTowerLocationDetails({
            locationid: filteredData?._id,
            data: formData,
          });
          if (response?.data?.success) {
            showToast({
              messageContent: `Location updated successfully`,
              type: "success",
            });
            handleClose();
          } else if (response?.error?.data?.message) {
            showToast({
              messageContent: response.error.data.message,
              type: "warning",
            });
          }
          console.log(response, "this is response of tower add or update");
        } else {
          const response = await addTowerlocationApi(
            (() => {
              const formData = new FormData();
              Object.entries(filteredData).forEach(([key, value]) => {
                if (Array.isArray(value)) {
                  value.forEach((item) => {
                    if (typeof item === "string" || item instanceof File) {
                      formData.append(key, item);
                    } else if (
                      item &&
                      typeof item === "object" &&
                      "_id" in item
                    ) {
                      formData.append(key, (item as { _id: string })._id);
                    }
                  });
                } else if (value !== undefined && value !== null) {
                  formData.append(key, value as string | Blob);
                }
              });
              formData.append("project_id", projectId as string);
              return formData;
            })()
          );
          if (response?.data?.success) {
            showToast({
              messageContent: `Location added successfully`,
              type: "success",
            });
            handleClose();
          } else if (response?.error?.data?.message) {
            showToast({
              messageContent: response.error.data.message,
              type: "warning",
            });
          }
          console.log(response, "this is response of tower add or update");
        }
      } else {
        const filteredData = TowerLocationData;
        if (filteredData?._id) {
          // Check for no changes before submitting update
          const originalData = selectedTowerLocationdata;
          const isNoChange =
            JSON.stringify(filteredData) === JSON.stringify(originalData);
          if (isNoChange) {
            showToast({
              messageContent: "There Were No changes!",
              type: "warning",
            });
            return;
          }
          const formData = new FormData();
          Object.keys(filteredData).forEach((key) => {
            if (key === "location_drawing") {
              // Handle multiple project drawings
              if (Array.isArray(filteredData.location_drawing)) {
                const projectDrawingStrings: string[] = [];
                filteredData.location_drawing?.forEach((file) => {
                  if (typeof file === "string") {
                    projectDrawingStrings.push(file); // Collect strings in an array
                  } else {
                    formData.append("location_drawing", file as File); // Append file
                  }
                });
                formData.append(
                  "location_drawing",
                  JSON.stringify(projectDrawingStrings)
                ); // Append array of strings as JSON
              }
            } else if (key === "conventionals") {
              // Handle multiple conventionals
              if (
                Array.isArray(
                  filteredData?.conventionals as unknown as keyof TowerLocationData
                )
              ) {
                filteredData.conventionals?.forEach((floor: any) => {
                  if (typeof floor === "string") {
                    formData.append("conventionals", floor); // Collect strings in an array
                  } else {
                    formData.append("conventionals", floor?._id); // Append file
                  }
                });
              }
            } else if (key === "mivan") {
              // Handle multiple mivan
              if (
                Array.isArray(
                  filteredData?.mivan as unknown as keyof TowerLocationData
                )
              ) {
                filteredData.mivan?.forEach((floor: any) => {
                  if (typeof floor === "string") {
                    formData.append("mivan", floor); // Collect strings in an array
                  } else {
                    formData.append("mivan", floor?._id); // Append fdfile
                  }
                });
              }
            } else {
              formData.append(key, (filteredData as any)[key]);
            }
          });
          // formData.append("project_id", projectId as string);

          // Convert conventionals and mivan to array of strings
          const conventionals =
            TowerLocationData?.conventionals?.map((item: any) =>
              typeof item === "string" ? item : item?._id
            ) || [];
          const mivan =
            TowerLocationData?.mivan?.map((item: any) =>
              typeof item === "string" ? item : item?._id
            ) || [];
          formData.delete("conventionals");
          formData.delete("mivan");
          // Append converted arrays to formData
          formData.append("conventionals", JSON.stringify(conventionals));
          formData.append("mivan", JSON.stringify(mivan));

          const response = await updateTowerLocationDetails({
            locationid: filteredData?._id,
            data: formData,
          });
          if (response?.data?.success) {
            showToast({
              messageContent: `Location updated successfully`,
              type: "success",
            });
            handleClose();
          } else if (response?.error?.data?.message) {
            showToast({
              messageContent: response.error.data.message,
              type: "warning",
            });
          }
          console.log(response, "this is response of tower add or update");
        } else {
          const response = await addTowerlocationApi(
            (() => {
              const formData = new FormData();
              Object.entries(filteredData).forEach(([key, value]) => {
                if (Array.isArray(value)) {
                  value.forEach((item) => {
                    if (typeof item === "string" || item instanceof File) {
                      formData.append(key, item);
                    } else if (
                      item &&
                      typeof item === "object" &&
                      "_id" in item
                    ) {
                      formData.append(key, (item as { _id: string })._id);
                    }
                  });
                } else if (value !== undefined && value !== null) {
                  formData.append(key, value as string | Blob);
                }
              });

              // Convert conventionals and mivan to array of strings
              const conventionals =
                TowerLocationData?.conventionals?.map((item: any) =>
                  typeof item === "string" ? item : item?._id
                ) || [];
              const mivan =
                TowerLocationData?.mivan?.map((item: any) =>
                  typeof item === "string" ? item : item?._id
                ) || [];
              formData.delete("conventionals");
              formData.delete("mivan");
              // Append converted arrays to formData
              formData.append("conventionals", JSON.stringify(conventionals));
              formData.append("mivan", JSON.stringify(mivan));

              formData.append("project_id", projectId as string);
              return formData;
            })()
          );
          if (response?.data?.success) {
            showToast({
              messageContent: `Location added successfully`,
              type: "success",
            });
            handleClose();
          } else if (response?.error?.data?.message) {
            showToast({
              messageContent: response.error.data.message,
              type: "warning",
            });
          }
          console.log(response, "this is response of tower add or update");
        }
      }
      console.log(TowerLocationData, "this is tower location sdfsdfdata");
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (formRef.current && !formRef.current.contains(event.target as Node)) {
        if (currentStep !== 3) {
          const hasUnsavedChanges = Object.values(TowerLocationData).some(
            (value) =>
              value !== "" && value?.length !== 0 && value !== undefined
          );
          if (!hasUnsavedChanges) {
            handleClose();
            return;
          }

          if (
            TowerLocationData?._id &&
            JSON.stringify(TowerLocationData) ===
              JSON.stringify(selectedTowerLocationdata)
          ) {
            handleClose();
            return;
          }
          return;
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [currentStep, TowerLocationData, selectedTowerLocationdata, handleClose]);

  const upref = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (formRef.current) {
      formRef.current.scrollTop = 0;
    }
  }, [currentStep]);

  useEffect(() => {
    if (currentStep === 1) {
      setTimeout(() => {
        const locationNameInput = document.getElementById("LocationName");
        if (locationNameInput) {
          (locationNameInput as HTMLTextAreaElement).focus();

          locationNameInput.dispatchEvent(new Event("focus"));
        }
      }, 0);
    }
  }, [currentStep]);
  // Add this function for keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isClosing) return;
    if (e.key === "Enter" && e.shiftKey) return;

    if (e.key === "Enter") {
      e.preventDefault();
      e.stopPropagation();

      if (currentStep === 1) {
        if (TowerLocationData.name && TowerLocationData.name.length < 5) {
          showToast({
            messageContent: "Tower Location Name must be at least 5 characters",
            type: "warning",
          });
          setinvalidErrors((prev) => [
            ...prev.filter((e) => e !== "name"),
            "name",
          ]);
          return;
        }
        const emptyKeys = Object.keys(TowerLocationData).filter(
          (key) =>
            (key !== "remarks" &&
              key !== "conventionals" &&
              key !== "mivan" &&
              key !== "project_id" &&
              key !== "isApprovesBy" &&
              key !== "isDeniedBy" &&
              key !== "DeniedComment" &&
              key !== "isOpenBy" &&
              key !== "updatedAt" &&
              ((TowerLocationData &&
                TowerLocationData[key as keyof TowerLocationData] === "") ||
                (Array.isArray(
                  TowerLocationData &&
                    TowerLocationData[key as keyof TowerLocationData]
                ) &&
                  Array.isArray(
                    TowerLocationData &&
                      TowerLocationData[key as keyof TowerLocationData]
                  ) &&
                  (TowerLocationData[key as keyof TowerLocationData] as any[])
                    .length === 0))) ||
            (TowerLocationData &&
              TowerLocationData[key as keyof TowerLocationData] === undefined)
        );

        // Check for missing keys in TowerLocationData
        const requiredKeys = [
          "category",
          "name",
          "location_drawing",
          "area",
          "number_of_floors",
          "number_of_basements",
          "location_duration",
          "structure_type",
        ];
        const missingKeys = requiredKeys.filter(
          (key) => !(key in TowerLocationData)
        );

        const allErrors = [...new Set([...emptyKeys, ...missingKeys])];

        if (allErrors.length > 0) {
          if (TowerLocationData?.category === "Non-Tower") {
            const allowedKeys = [
              "number_of_floors",
              "structure_type",
              "conventionals",
              "mivan",
              "isApprovesBy",
              "isDeniedBy",
              "DeniedComment",
              "isOpenBy",
              "updatedAt",
            ];
            const hasDisallowedFields = allErrors.some(
              (key) => !allowedKeys.includes(key)
            );
            if (hasDisallowedFields) {
              setinvalidErrors(allErrors);
              showToast({
                messageContent: "Enter required fields!",
                type: "warning",
              });
              return;
            } else {
              setCurrentStep(2);
            }
          } else {
            setinvalidErrors(allErrors);
            showToast({
              messageContent: "Enter required fields!",
              type: "warning",
            });
          }
        } else {
          if (TowerLocationData?.category == "Tower") {
            if (
              (TowerLocationData?.conventionals?.length ?? 0) +
                (TowerLocationData?.mivan?.length ?? 0) !==
              Number(TowerLocationData?.number_of_floors ?? 0)
            ) {
              setinvalidErrors((prev) => [
                ...prev,
                "conventionals",
                "mivan",
                "floorsleft",
              ]);
              return;
            }
          }
          setCurrentStep(2);
        }
      }

      if (currentStep === 2) {
        handleSubmitTower();
      }

      if (currentStep === 3) {
        handleClose();
      }
    }

    if (e.key === "Escape") {
      e.preventDefault();
      e.stopPropagation();
      if (currentStep === 2) {
        setCurrentStep(1);
      }
      if (currentStep === 1) {
        // Same as Cancel button logic
        const hasUnsavedChanges = Object.values(TowerLocationData).some(
          (value) => value !== "" && value?.length !== 0 && value !== undefined
        );
        if (
          TowerLocationData?._id &&
          JSON.stringify(TowerLocationData) ===
            JSON.stringify(selectedTowerLocationdata)
        ) {
          handleClose();
        } else if (hasUnsavedChanges) {
          setCurrentStep(3);
        } else {
          handleClose();
        }
      }
      if (currentStep === 3) {
        setCurrentStep(1);
      }
    }
  };

  useEffect(() => {
    if (currentStep === 1 || currentStep === 2 || currentStep === 3) {
      if (formRef.current) {
        formRef.current.focus();
      }
    }
  }, [currentStep]);

  return (
    <>
      <div className={styles.add_new_projectForm_overlay}>
        <div
          className={`${styles.add_new_projectForm_container} ${
            isClosing && styles.closing
          } }`}
          ref={formRef}
          tabIndex={0}
          onKeyDown={handleKeyDown}
        >
          <div className={styles.add_new_projectForm_header}>
            <button
              className={styles.closeButton}
              onClick={() => {
                if (currentStep !== (3 as number)) {
                  const hasUnsavedChanges = Object.values(
                    TowerLocationData
                  ).some(
                    (value) =>
                      value !== "" && value?.length !== 0 && value !== undefined
                  );
                  if (
                    TowerLocationData?._id &&
                    JSON.stringify(TowerLocationData) ===
                      JSON.stringify(selectedTowerLocationdata)
                  ) {
                    handleClose();
                  } else if (hasUnsavedChanges) {
                    setCurrentStep(3);
                  } else {
                    handleClose();
                  }
                } else {
                  setCurrentStep(1);
                }
              }}
            >
              <CloseIcon />
            </button>
            <h3
              style={{
                color:
                  currentStep == 3
                    ? "var(--warning_color)"
                    : "var(--primary_color)",
              }}
            >
              {currentStep == 1
                ? TowerLocationData?._id
                  ? "Edit Location"
                  : "Add Location"
                : currentStep == 2
                ? TowerLocationData?._id
                  ? "Are you sure you want to update this location"
                  : "Are you sure you want to add this location"
                : ""}
              {currentStep == 3 &&
                "Are you sure you want to discard these changes?"}
            </h3>
          </div>
          {currentStep == 1 && (
            <div ref={upref} className={styles.add_new_projectForm_inpts}>
              <div className={styles.add_new_projectForm_rowOneInputs}>
                <RadioBtns
                  id="category"
                  error={invalidErrors?.includes("category")}
                  selectedValue={TowerLocationData?.category ?? ""}
                  onValueChange={(value: String) => {
                    handleInputChange("category", value);
                    setTimeout(() => {
                      const locationNameInput =
                        document.getElementById("LocationName");
                      if (locationNameInput) {
                        (locationNameInput as HTMLTextAreaElement).focus();
                      }
                    }, 0);
                  }}
                  options={
                    TowerLocationData?._id
                      ? [
                          {
                            value: TowerLocationData.category || "",
                            label:
                              TowerLocationData.category === "Tower"
                                ? "Tower"
                                : "Non Tower",
                          },
                        ]
                      : [
                          { value: "Tower", label: "Tower" },
                          { value: "Non-Tower", label: "Non Tower" },
                        ]
                  }
                />
              </div>

              {(TowerLocationData?.category == "Tower" ||
                TowerLocationData?.category == "Non-Tower") && (
                <>
                  <h4>Details</h4>
                  <FloatingLabelInput
                    width="14rem"
                    enterAllowed={false}
                    label="Location Name"
                    // clearInput={true}
                    id="LocationName"
                    focusOnInput={true}
                    autoFocus={true}
                    value={TowerLocationData?.name}
                    props="one_line"
                    placeholder="Location Tower"
                    isInvalid={invalidErrors?.includes("name") ? true : false}
                    onInputChange={(data) => {
                      handleInputChange("name", data);
                    }}
                    onMount={() => {
                      const input = document.getElementById("LocationName");
                      if (input) {
                        (input as HTMLTextAreaElement).focus();

                        input.dispatchEvent(new Event("focus"));
                        // Trigger focus handler
                        input.dispatchEvent(new Event("focusin"));
                      }
                    }}
                  />
                  <FloatingLabelInput
                    width="14rem"
                    enterAllowed={false}
                    handledelete={(e, index) => {
                      e.preventDefault();
                      const allDrawings = TowerLocationData?.location_drawing;
                      const filteredDrawings = allDrawings?.filter(
                        (_, i) => i !== index
                      );
                      console.log(allDrawings, "these are fileterd drawigns");
                      handleInputChange("location_drawing", filteredDrawings);
                    }}
                    label="Drawings"
                    id="Drawings"
                    placeholder="Drawings"
                    isInvalid={
                      invalidErrors?.includes("location_drawing") ? true : false
                    }
                    isDisabled={true}
                    onInputChange={() => {}}
                    value={undefined}
                    iconClick={() => {
                      handleDrawingClick();
                    }}
                    itemLabels={TowerLocationData?.location_drawing
                      ?.map((e) => {
                        if (typeof e === "object" && e instanceof File) {
                          return slicedData(e.name, 12);
                        }
                        if (typeof e === "string") {
                          return slicedData((e as string).split("/")[2], 12);
                        }
                        return undefined;
                      })
                      .filter(
                        (e): e is string =>
                          e !== undefined && typeof e === "string"
                      )}
                    Icon={AttachmentIcon}
                  />
                  <input
                    type="file"
                    ref={drawingsInputRef}
                    style={{ display: "none" }}
                    onChange={handleDrawingsChange}
                    accept="image/*" // Only allows image files
                  />
                  {drawingsLoader && (
                    <div
                      className={
                        styles.progress_bar_container_drawing_addlocation
                      }
                    >
                      <div
                        className={styles.progress_bar_drawing_addlocation}
                      ></div>
                    </div>
                  )}
                  {TowerLocationData?.category == "Non-Tower" ? (
                    <>
                      <FloatingLabelInput
                        width="14rem"
                        enterAllowed={false}
                        label="Area"
                        id="Area"
                        maxlength={15}
                        placeholder="Location Tower"
                        value={TowerLocationData?.area}
                        isInvalid={
                          invalidErrors?.includes("area") ? true : false
                        }
                        preventEnter={true}
                        props="one_line"
                        type="twoDigitAfterDecimal"
                        onPaste={(e) => e.preventDefault()}
                        onInputChange={(value) => {

                          setinvalidErrors((prev) =>
                            prev.filter((e) => e !== "area")
                          );
                          handleInputChange("area", value);
                        }}
                      />
                      <div className={styles.add_new_projectForm_rowOneInputs}>
                        <FloatingLabelInput
                          width="14rem"
                          enterAllowed={false}
                          label="Basements"
                          id="Basements"
                          maxlength={2}
                          placeholder="Basements"
                          value={TowerLocationData?.number_of_basements}
                          isInvalid={
                            invalidErrors?.includes("number_of_basements")
                              ? true
                              : false
                          }
                          preventEnter={true}
                          props="one_line"
                          type="numberWithoutDecimal"
                          onPaste={(e) => e.preventDefault()}
                          onInputChange={(value) => {
                            
                            setinvalidErrors((prev) =>
                              prev.filter((e) => e !== "number_of_basements")
                            );
                            handleInputChange("number_of_basements", value);
                          }}
                        />
                        <div style={{ position: "relative", width: "14.5rem" }}>
                          <FloatingLabelInput
                            width="14rem"
                            enterAllowed={false}
                            label="Duration"
                            id="Duration"
                            maxlength={10}
                            placeholder="Duration"
                            value={TowerLocationData?.location_duration}
                            isInvalid={
                              invalidErrors?.includes("location_duration")
                                ? true
                                : false
                            }
                            preventEnter={true}
                            props="one_line"
                            type="oneDigitAfterDecimal"
                            onPaste={(e) => e.preventDefault()}
                            onInputChange={(value) => {
                              
                              setinvalidErrors((prev) =>
                                prev.filter((e) => e !== "location_duration")
                              );
                              handleInputChange("location_duration", value);
                            }}
                          />
                          <p className={styles.taskcard_header_leftpara}>
                            Months
                          </p>
                        </div>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className={styles.add_new_projectForm_rowOneInputs}>
                        <FloatingLabelInput
                          width="14rem"
                          enterAllowed={false}
                          label="Area"
                          id="Area"
                          maxlength={10}
                          placeholder="Location Tower"
                          value={TowerLocationData?.area}
                          isInvalid={
                            invalidErrors?.includes("area") ? true : false
                          }
                          preventEnter={true}
                          props="one_line"
                          type="twoDigitAfterDecimal"
                          onPaste={(e) => e.preventDefault()}
                          onInputChange={(value) => {
                            setinvalidErrors((prev) =>
                              prev.filter((e) => e !== "area")
                            );
                            handleInputChange("area", value);
                          }}
                        />
                        <FloatingLabelInput
                          width="14rem"
                          enterAllowed={false}
                          label="Number of Floors"
                          id="numberoffloors"
                          maxlength={3}
                          placeholder="numberoffloors"
                          value={TowerLocationData?.number_of_floors}
                          isInvalid={
                            invalidErrors?.includes("number_of_floors")
                              ? true
                              : false
                          }
                          preventEnter={true}
                          props="one_line"
                          type="numberWithoutDecimal"
                          onPaste={(e) => e.preventDefault()}
                          onInputChange={(value) => {
                           
                            setinvalidErrors((prev) =>
                              prev.filter((e) => e !== "number_of_floors")
                            );
                            handleInputChange("number_of_floors", value);
                          }}
                        />
                      </div>

                      <div className={styles.add_new_projectForm_rowOneInputs}>
                        <FloatingLabelInput
                          width="14rem"
                          enterAllowed={false}
                          label="Basements"
                          id="Basements"
                          maxlength={2}
                          placeholder="Basements"
                          value={TowerLocationData?.number_of_basements}
                          isInvalid={
                            invalidErrors?.includes("number_of_basements")
                              ? true
                              : false
                          }
                          preventEnter={true}
                          props="one_line"
                          type="numberWithoutDecimal"
                          onPaste={(e) => e.preventDefault()}
                          onInputChange={(value) => {
                           
                            setinvalidErrors((prev) =>
                              prev.filter((e) => e !== "number_of_basements")
                            );
                            handleInputChange("number_of_basements", value);
                          }}
                        />
                        <div style={{ position: "relative", width: "14.5rem" }}>
                          <FloatingLabelInput
                            width="14rem"
                            enterAllowed={false}
                            label="Duration"
                            id="Duration"
                            maxlength={10}
                            placeholder="Duration"
                            value={TowerLocationData?.location_duration}
                            isInvalid={
                              invalidErrors?.includes("location_duration")
                                ? true
                                : false
                            }
                            preventEnter={true}
                            props="one_line"
                            type="oneDigitAfterDecimal"
                            onPaste={(e) => e.preventDefault()}
                            onInputChange={(value) => {
                              

                              setinvalidErrors((prev) =>
                                prev.filter((e) => e !== "location_duration")
                              );
                              handleInputChange("location_duration", value);
                            }}
                          />
                          <p className={styles.taskcard_header_leftpara}>
                            Months
                          </p>
                        </div>
                      </div>
                      <h4>Structure Type</h4>
                      <RadioBtns
                        id="structure_type"
                        error={invalidErrors?.includes("structure_type")}
                        selectedValue={TowerLocationData?.structure_type}
                        onValueChange={(value: String) => {
                          handleInputChange("structure_type", value);
                        }}
                        options={[
                          { value: "Ground", label: "Ground" },
                          { value: "Stilt", label: "Stilt" },
                        ]}
                      />
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <SuryaconLogoSecondary color="var(--primary_color)" />
                        <h4>Conventional</h4>
                      </div>
                      <div
                        style={{
                          position: "relative",
                        }}
                      >
                        <Datafield
                          handlePlusIcon={(message) => {
                            if (message == "number_of_floors") {
                              showToast({
                                messageContent:
                                  "Please enter number of Floors First",
                                type: "warning",
                              });
                              setinvalidErrors([...invalidErrors, message]);
                            }
                            if (message == "noFloorLeft") {
                              showToast({
                                messageContent:
                                  "All the Floors are already added",
                                type: "warning",
                              });
                              setinvalidErrors([...invalidErrors, message]);
                            }
                          }}
                          label="Conventional"
                          error={invalidErrors?.includes("conventionals")}
                          setIsClosing={setIsClosing}
                          selectedValues={
                            TowerLocationData?.conventionals?.map((e: any) =>
                              typeof e === "string"
                                ? { _id: e, name: `${e}th floor` }
                                : e
                            ) || []
                          }
                          varient="AddToolsForm"
                          callbackDelete={(id) => {
                            const filterdConventionals =
                              TowerLocationData?.conventionals?.filter(
                                (singleConventional) =>
                                  typeof singleConventional == "string"
                                    ? singleConventional !== id
                                    : singleConventional?._id !== id
                              );
                            handleInputChange(
                              "conventionals",
                              filterdConventionals
                            );
                          }}
                        />
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                          }}
                        >
                          <SuryaconLogoSecondary color="var(--secondary_color)" />
                          <h4>Mivan</h4>
                        </div>
                        <div>
                          <Datafield
                            handlePlusIcon={(message) => {
                              if (message == "number_of_floors") {
                                showToast({
                                  messageContent:
                                    "Please enter number of Floors First",
                                  type: "warning",
                                });
                                setinvalidErrors([...invalidErrors, message]);
                              }
                              if (message == "noFloorLeft") {
                                showToast({
                                  messageContent:
                                    "All the Floors are already added",
                                  type: "warning",
                                });
                                setinvalidErrors([...invalidErrors, message]);
                              }
                            }}
                            label="Mivan"
                            error={invalidErrors?.includes("mivan")}
                            setIsClosing={setIsClosing}
                            selectedValues={
                              TowerLocationData?.mivan?.map((e: any) =>
                                typeof e === "string"
                                  ? { _id: e, name: `${e}th floor` }
                                  : e
                              ) || []
                            }
                            varient="AddToolsForm"
                            callbackDelete={(id) => {
                              const filterdMivan =
                                TowerLocationData?.mivan?.filter(
                                  (singleMivan) =>
                                    typeof singleMivan == "string"
                                      ? singleMivan !== id
                                      : singleMivan?._id !== id
                                );
                              handleInputChange("mivan", filterdMivan);
                            }}
                          />
                        </div>
                        {invalidErrors?.includes("floorsleft") && (
                          <p
                            className="small_text_p_400"
                            style={{
                              color: "var(--warning_color)",
                            }}
                          >
                            Kindly define all the floors to add this location
                          </p>
                        )}
                      </div>
                    </>
                  )}

                  <FloatingLabelInput
                    width="14rem"
                    enterAllowed={false}
                    label="Remarks"
                    id="Remarks"
                    value={TowerLocationData?.remarks}
                    placeholder="Remarks"
                    isInvalid={
                      invalidErrors?.includes("remarks") ? true : false
                    }
                    onInputChange={(data) => {
                      handleInputChange("remarks", data);
                    }}
                  />
                  <div
                    className={styles.add_new_projectForm_rowOneInputs}
                  ></div>
                </>
              )}
            </div>
          )}
          {currentStep == 2 && (
            <div className={styles.add_new_projectForm_inpts}>
              <CategorySummary
                previousdata={
                  TowerLocationData?._id
                    ? selectedTowerLocationdata
                    : (TowerLocationData as any)
                }
                selectedItems={(() => {
                  if (TowerLocationData?.category == "Non-Tower") {
                    const {
                      mivan,
                      conventionals,
                      number_of_floors,
                      structure_type,
                      ...filteredData
                    } = TowerLocationData;
                    return {
                      ...filteredData,
                      number_of_floors: "", // Ensure it's initialized as an empty string
                      conventionals: [],
                      mivan: [],
                      structure_type: "", // Default to an empty string
                    } as TowerLocationData;
                  } else return TowerLocationData;
                })()}
              />
            </div>
          )}
          {currentStep == 3 && (
            <div className={styles.add_new_projectForm_inpts}>
              <CategorySummary
                previousdata={
                  TowerLocationData?._id
                    ? selectedTowerLocationdata
                    : (TowerLocationData as any)
                }
                selectedItems={(() => {
                  if (TowerLocationData?.category == "Non-Tower") {
                    const {
                      mivan,
                      conventionals,
                      number_of_floors,
                      structure_type,
                      ...filteredData
                    } = TowerLocationData;
                    return {
                      ...filteredData,
                      number_of_floors: "",
                      conventionals: [],
                      mivan: [],
                      structure_type: "",
                    } as TowerLocationData;
                  } else return TowerLocationData;
                })()}
              />
            </div>
          )}
          {currentStep == 1 && (
            <div className={styles.add_new_projectForm_btngrp}>
              <Button
                type="Cancel"
                Content="Cancel"
                Callback={() => {
                  if (currentStep !== 3) {
                    const hasUnsavedChanges = Object.values(
                      TowerLocationData
                    ).some(
                      (value) =>
                        value !== "" &&
                        value?.length !== 0 &&
                        value !== undefined
                    );
                    if (
                      TowerLocationData?._id &&
                      JSON.stringify(TowerLocationData) ===
                        JSON.stringify(selectedTowerLocationdata)
                    ) {
                      handleClose();
                    } else if (hasUnsavedChanges) {
                      setCurrentStep(3);
                    } else {
                      handleClose();
                    }
                  } else {
                    setCurrentStep(1);
                  }
                }}
              />
              <Button
                type="Next"
                Content={TowerLocationData?._id ? "Update" : "Add"}
                Callback={() => {
                  if (
                    TowerLocationData.name &&
                    TowerLocationData.name.length < 5
                  ) {
                    showToast({
                      messageContent:
                        "Tower Location Name must be at least 5 characters",
                      type: "warning",
                    });
                    setinvalidErrors((prev) => [
                      ...prev.filter((e) => e !== "name"),
                      "name",
                    ]);
                    return;
                  }
                  const emptyKeys = Object.keys(TowerLocationData).filter(
                    (key) =>
                      (key !== "remarks" &&
                        key !== "conventionals" &&
                        key !== "mivan" &&
                        key !== "project_id" &&
                        key !== "isApprovesBy" &&
                        key !== "isDeniedBy" &&
                        key !== "DeniedComment" &&
                        key !== "isOpenBy" &&
                        key !== "updatedAt" &&
                        ((TowerLocationData &&
                          TowerLocationData[key as keyof TowerLocationData] ===
                            "") ||
                          (Array.isArray(
                            TowerLocationData &&
                              TowerLocationData[key as keyof TowerLocationData]
                          ) &&
                            Array.isArray(
                              TowerLocationData &&
                                TowerLocationData[
                                  key as keyof TowerLocationData
                                ]
                            ) &&
                            (
                              TowerLocationData[
                                key as keyof TowerLocationData
                              ] as any[]
                            ).length === 0))) ||
                      (TowerLocationData &&
                        TowerLocationData[key as keyof TowerLocationData] ===
                          undefined)
                  );

                  // Check for missing keys in TowerLocationData
                  const requiredKeys = [
                    "category",
                    "name",
                    "location_drawing",
                    "area",
                    "number_of_floors",
                    "number_of_basements",
                    "location_duration",
                    "structure_type",
                  ];
                  const missingKeys = requiredKeys.filter(
                    (key) => !(key in TowerLocationData)
                  );

                  const allErrors = [
                    ...new Set([...emptyKeys, ...missingKeys]),
                  ];

                  console.log(allErrors, "these are all errors");

                  if (allErrors.length > 0) {
                    if (TowerLocationData?.category === "Non-Tower") {
                      // Allowed missing fields for "Non Tower"
                      const allowedKeys = [
                        "number_of_floors",
                        "structure_type",
                        "conventionals",
                        "mivan",
                        "isApprovesBy",
                        "isDeniedBy",
                        "DeniedComment",
                        "isOpenBy",
                        "updatedAt",
                      ];

                      // Check if there are any disallowed missing fields
                      const hasDisallowedFields = allErrors.some(
                        (key) => !allowedKeys.includes(key)
                      );

                      if (hasDisallowedFields) {
                        setinvalidErrors(allErrors); // Show missing fields
                        showToast({
                          messageContent: "Enter required fields!",
                          type: "warning",
                        });
                        return; // Stop execution
                      } else {
                        setCurrentStep(2);
                      }
                    } else {
                      setinvalidErrors(allErrors);
                      showToast({
                        messageContent: "Enter required fields!",
                        type: "warning",
                      });
                    }
                  } else {
                    console.log("else block running");
                    console.log(
                      `Conventionals: ${
                        TowerLocationData?.conventionals?.length
                      }, Mivan: ${
                        TowerLocationData?.mivan?.length
                      }, Number of Floors: ${Number(
                        TowerLocationData?.number_of_floors
                      )}`
                    );
                    if (TowerLocationData?.category == "Tower") {
                      if (
                        (TowerLocationData?.conventionals?.length ?? 0) +
                          (TowerLocationData?.mivan?.length ?? 0) !==
                        Number(TowerLocationData?.number_of_floors ?? 0)
                      ) {
                        setinvalidErrors((prev) => [
                          ...prev,
                          "conventionals",
                          "mivan",
                          "floorsleft",
                        ]);
                        return;
                      }
                    }
                    setCurrentStep(2); // Proceed only if all fields (except Remarks) are filled
                  }
                }}
              />
            </div>
          )}
          {currentStep == 2 && (
            <div className={styles.add_new_projectForm_btngrp}>
              <Button
                type="Cancel"
                Content="Back"
                Callback={() => {
                  setCurrentStep(1);
                }}
              />
              <Button
                type="Next"
                Content="Submit"
                Callback={handleSubmitTower}
              />
            </div>
          )}
          {currentStep == 3 && (
            <div className={styles.add_new_projectForm_btngrp}>
              <Button
                type="Cancel"
                Content="No"
                Callback={() => {
                  setCurrentStep(1);
                }}
              />
              <Button
                type="Next"
                Content="Yes"
                Callback={() => {
                  handleClose();
                  // dispatch(
                  //   setTowerLocationFormData({
                  //     category: "",
                  //     name: "",
                  //     location_drawing: [],
                  //     project_id: "",
                  //     area: "",
                  //     number_of_floors: "",
                  //     number_of_basements: "",
                  //     location_duration: "",
                  //     structure_type: "",
                  //     conventionals: [],
                  //     mivan: [],
                  //     remarks: "",
                  //   } as TowerLocationData)
                  // );
                }}
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
};

const CategorySummary: FC<{
  previousdata?: TowerLocationData | null;
  selectedItems: TowerLocationData | null;
}> = ({ selectedItems, previousdata }) => {
  return (
    <>
      {selectedItems?.category && (
        <div className={styles.summaryDataContent}>
          <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
            Location Type
          </p>
          <div
            className={styles.summaryItems}
            style={{ color: "var(--text-black-87)" }}
          >
            <h4
              className={styles.summaryItem}
              style={{
                paddingRight: "16px",
                color:
                  previousdata &&
                  selectedItems?.category !== previousdata?.category
                    ? "var(--secondary_color)"
                    : "var(--text-black-87)",
              }}
            >
              {selectedItems?.category}
            </h4>
          </div>
        </div>
      )}
      {selectedItems?.name && (
        <div className={styles.summaryDataContent}>
          <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
            Location Name
          </p>
          <div
            className={styles.summaryItems}
            style={{ color: "var(--text-black-87)" }}
          >
            <h4
              className={styles.summaryItem}
              style={{
                paddingRight: "16px",
                color:
                  previousdata && selectedItems?.name !== previousdata?.name
                    ? "var(--secondary_color)"
                    : "var(--text-black-87)",
              }}
            >
              {selectedItems?.name}
            </h4>
          </div>
        </div>
      )}
       {selectedItems?.location_drawing &&
        selectedItems?.location_drawing?.length > 0 && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Drawings
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              {/* Render selected items and highlight changes */}
              {selectedItems?.location_drawing?.map((e, index) => {
                const currentName = getFileName(e instanceof File ? e.name : e);
                const previousRaw = previousdata?.location_drawing?.[index];
                const previousName = getFileName(
                  previousRaw instanceof File ? previousRaw.name : previousRaw
                );

                return (
                  <h4
                    key={`selected-${index}`}
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color:
                        previousdata && currentName !== previousName
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {slicedData(currentName ?? "", 12)}
                  </h4>
                );
              })}

              {/* Show missing items in warning color */}
              {previousdata?.location_drawing
                ?.filter((prev) => {
                  const prevName = getFileName(
                    prev instanceof File ? prev.name : prev
                  );
                  return !selectedItems?.location_drawing?.some((sel) => {
                    const selName = getFileName(
                      sel instanceof File ? sel.name : sel
                    );
                    return selName === prevName;
                  });
                })
                .map((missing, idx) => {
                  const name = getFileName(
                    missing instanceof File ? missing.name : missing
                  );

                  return (
                    <h4
                      key={`missing-${idx}`}
                      className={styles.summaryItem}
                      style={{
                        paddingRight: "16px",
                        color: "var(--warning_color)",
                      }}
                    >
                      {slicedData(name ?? "", 12)}
                    </h4>
                  );
                })}
            </div>
          </div>
        )}

      <div className={styles.summaryDivData}>
        {selectedItems?.area && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Area
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                  color:
                    previousdata && selectedItems?.area !== previousdata?.area
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                }}
              >
                {selectedItems?.area}
              </h4>
            </div>
          </div>
        )}
        {selectedItems?.number_of_floors && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Number of Floors
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                  color:
                    previousdata &&
                    selectedItems?.number_of_floors !==
                      previousdata?.number_of_floors
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                }}
              >
                {selectedItems?.number_of_floors}
              </h4>
            </div>
          </div>
        )}
      </div>
      <div className={styles.summaryDivData}>
        {selectedItems?.number_of_basements && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Basements
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                  color:
                    previousdata &&
                    selectedItems?.number_of_basements !==
                      previousdata?.number_of_basements
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                }}
              >
                {selectedItems?.number_of_basements}
              </h4>
            </div>
          </div>
        )}
        {selectedItems?.location_duration && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Duration
            </p>
            <div
              className={styles.summaryItems}
              style={{ color: "var(--text-black-87)" }}
            >
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                  color:
                    previousdata &&
                    selectedItems?.location_duration !==
                      previousdata?.location_duration
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                }}
              >
                {selectedItems?.location_duration}
              </h4>
            </div>
          </div>
        )}
      </div>
      {selectedItems?.structure_type && (
        <div className={styles.summaryDataContent}>
          <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
            Structure Type
          </p>
          <div
            className={styles.summaryItems}
            style={{ color: "var(--text-black-87)" }}
          >
            <h4
              className={styles.summaryItem}
              style={{
                paddingRight: "16px",
                color:
                  previousdata &&
                  selectedItems?.structure_type !== previousdata?.structure_type
                    ? "var(--secondary_color)"
                    : "var(--text-black-87)",
              }}
            >
              {selectedItems?.structure_type}
            </h4>
          </div>
        </div>
      )}
      {selectedItems?.conventionals &&
        selectedItems?.conventionals?.length > 0 && (
          <div className={styles.summaryDataContent}>
            <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
              Conventionals
            </p>
            <div
              className={styles.summaryItems}
              style={{
                color: "var(--text-black-87)",
                display: "flex",
                gap: "1rem",
              }}
            >
              {selectedItems?.conventionals?.map((e) => (
                <h4
                  className={styles.summaryItem}
                  style={{
                    paddingRight: "16px",
                    color: previousdata?.conventionals?.includes(e || e?.name)
                      ? "var(--text-black-87)"
                      : "var(--secondary_color)",
                  }}
                >
                  {e?.name || `${e}th floor`}
                </h4>
              ))}
              {previousdata?.conventionals
                ?.filter(
                  (prev) =>
                    !selectedItems?.conventionals?.some(
                      (sel) => sel === prev || sel?.name === prev
                    )
                )
                .map((missing) => (
                  <h4
                    className={styles.summaryItem}
                    style={{
                      paddingRight: "16px",
                      color: "var(--warning_color)",
                    }}
                  >
                    {missing?.name || `${missing}th floor`}
                  </h4>
                ))}
            </div>
          </div>
        )}
      {selectedItems?.mivan && selectedItems?.mivan?.length > 0 && (
        <div className={styles.summaryDataContent}>
          <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
            Mivan
          </p>
          <div
            className={styles.summaryItems}
            style={{
              color: "var(--text-black-87)",
              display: "flex",
              gap: "1rem",
            }}
          >
            {selectedItems?.mivan?.map((e) => (
              <h4
                className={styles.summaryItem}
                style={{
                  paddingRight: "16px",
                  color: previousdata?.mivan?.includes(e || e?.name)
                    ? "var(--text-black-87)"
                    : "var(--secondary_color)",
                }}
              >
                {e?.name || `${e}th floor`}
              </h4>
            ))}
            {previousdata?.mivan
              ?.filter(
                (prev) =>
                  !selectedItems?.mivan?.some(
                    (sel) => sel === prev || sel?.name === prev
                  )
              )
              .map((missing) => (
                <h4
                  className={styles.summaryItem}
                  style={{
                    paddingRight: "16px",
                    color: "var(--warning_color)",
                  }}
                >
                  {missing?.name || `${missing}th floor`}
                </h4>
              ))}
          </div>
        </div>
      )}

      {selectedItems?.remarks && (
        <div className={styles.summaryDataContent}>
          <p style={{ color: "var(--text-black-60)" }} className="p_tag_14px">
            Remarks
          </p>
          <div
            className={styles.summaryItems}
            style={{ color: "var(--text-black-87)" }}
          >
            <h4
              className={styles.summaryItem}
              style={{
                paddingRight: "16px",
                color:
                  previousdata &&
                  selectedItems?.remarks !== previousdata?.remarks
                    ? "var(--secondary_color)"
                    : "var(--text-black-87)",
              }}
            >
              {selectedItems?.remarks}
            </h4>
          </div>
        </div>
      )}
    </>
  );
};
