import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";

import { useCallback, useEffect, useRef, useState } from "react";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import { updateNumValue } from "../../../../redux/features/Modules/TaskMaster/Slices/CategorySlice";
import { setisDeletedNext } from "../../../../redux/features/Modules/Reusble/deletedSlice";
import { setNavigate } from "../../../../redux/features/Modules/Reusble/navigationSlice";

import {
  clearCategoryPopup,
  openCategoryPopup,
  openPopup,
} from "../../../../redux/features/Modules/Reusble/popupSlice";
import { setInputValue } from "../../../../redux/features/Modules/Reusble/floatinglabelslice";
import {
  DeleteIcon,
  RestoreIcon,
  Twodots,
  ViewIcon,
  YellowEditPencil,
} from "../../../../assets/icons";
import { CardItems } from "../CardItems";
import Button from "../../Global/Button";
import styles from "./Styles/Card.module.css";
import { RootState } from "../../../../redux/store";
import { CardProps } from "../TaskMasterInterfaces/TaskMasterInterface";
import {
  ResponseDataItem,
  TaskInterface,
} from "../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
import {
  useRestoreCategoryMutation,
  useRestoreTaskMutation,
} from "../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
import { setcurrentTaskData } from "../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";
import { useToast } from "../../../../hooks/ToastHook";

const items: { title: string; name: keyof ResponseDataItem }[] = [
  { title: "Departments", name: "totalDepartmentIdLength" },
  { title: "Designations", name: "totalDesiginationIdLength" },
  { title: "Manpower", name: "TotalManpowerIdLength" },
  { title: "Machinery", name: "TotalMachinaryIdLength" },
  { title: "Tools", name: "TotalToolIdLength" },
  { title: "Materials", name: "TotalMaterialIdLength" },
];

const taskItems: { title: string; name: keyof TaskInterface }[] = [
  { title: "Subtasks", name: "SubtaskId" },
  { title: "Designation", name: "desigination" },
  { title: "Manpower", name: "manpower" },
  { title: "Machinery", name: "machinery" },
  { title: "Tools", name: "tools" },
  { title: "Materials", name: "material" },
];

export function Card({
  category,
  path,
  taskId,
  isTask,
  isApprove,
  Callback,
  id,
}: CardProps) {
  const navigate = useNavigate();
  const [categoryData, setCategoryData] = useState<TaskInterface | null>(null);
  const isDeletedNext = useAppSelector(
    (state) => state.isDeletedSLice.isDeletedNext
  );
  const deleted = useAppSelector((state) => state.isDeletedSLice.isDeleted);

  useEffect(() => {
    setCategoryData(category);
  }, [category]);
  const showToast = useToast();
  const dispatch = useAppDispatch();
  const handleTaskNavigation = () => {
    if (!isApprove) return;
    dispatch(updateNumValue(1));
    dispatch(setisDeletedNext({ isDelete: isDeletedNext }));
    dispatch(
      setcurrentTaskData({
        _id: "",
        name: "",
        Unit: "",
        Description: "",
        subtaskWeighatages: 0,
        Tracking: "",
        DepartmentId: [],
        DesignationId: [],
        MaterialId: [],
        ToolId: [],
        MachinaryId: [],
        ManpowerId: [],
        Adminid: [],
        TaskmasterId: {},
        ReporterId: {
          Reporter: [],
        },
        AssigneeId: [],
        Subtaskdetails: [],
        MethodId: {
          work_instruction_id: [],
          task_closing_requirement: [],
          Controlplan: [],
          Failuremode: [],
        },
      })
    );

    if (isTask) {
      if (taskId) {
        navigate(`${path}`);
        dispatch(
          setNavigate({
            route: path,
            title: categoryData?.categoryName ?? categoryData?.taskname ?? "",
          })
        );
        return;
      } else {
        showToast({
          messageContent: "Waiting for Server Response",
          type: "warning",
        });
        return;
      }
    }

    navigate(`${path}`);
    dispatch(
      setNavigate({
        route: path,
        title: categoryData?.categoryName ?? categoryData?.taskname ?? "",
      })
    );
  };
  // handlerestore function to restore the delete category

  const [restoreCategory] = useRestoreCategoryMutation();
  const handleRestore = async (catid) => {
    const response = await restoreCategory({ categoryId: catid });
    console.log(response, "thisisreponsemann");
    if (response?.data?.success) {
      showToast({
        messageContent: "Category Restored Successfully",
        type: "success",
      });
    } else {
      showToast({
        messageContent: "Something Went Wrong",
        type: "warning",
      });
    }
  };
  // handletaskrestore to restore deleted task
  const [restoreTask] = useRestoreTaskMutation();
  const handleTaskResotre = async (taskId) => {
    const response = await restoreTask({ taskId: taskId });
    if (response?.data?.success) {
      showToast({
        messageContent: "Task Restored Successfully!",
        type: "success",
      });
    } else {
      showToast({
        messageContent: "Something Went Wrong",
        type: "warning",
      });
    }
  };
  // const { data } = GetTaskCategoryApi();
  // console.log("agyaaa", data);

  const { popups, categoryPopup } = useSelector(
    (state: RootState) => state.popup
  );
  // console.log('poopups:',categoryPopup);

  // const handleDotDropdown = (cardId: string) => {
  //   if (popups[cardId]) {
  //     dispatch(closePopup(cardId));
  //   } else {
  //     dispatch(openPopup(cardId));
  //   }
  // };
  const handleDotDropdown2 = (cardId: string) => {
    console.log("function called hereee broooo");
    if (!navigator.onLine) {
      showToast({
        messageContent: "Oops! no internet connection!",
        type: "danger",
      });
      return;
    }
    dispatch(openCategoryPopup(cardId));
  };

  const handleEdit = () => {
    if (isTask) {
      dispatch(openPopup("AddTaskForm"));
      dispatch(
        setInputValue({
          mode: "task",
          tskId: categoryData?._id ?? "",
          TaskName: categoryData?.taskname ?? "",
          Description: categoryData?.Description ?? "",
          Quantity: String(categoryData?.Quantity ?? 0),
          Unit: categoryData?.Unit ?? "",
        })
      );
    } else {
      dispatch(openPopup("AddCategoryForm"));
      dispatch(
        setInputValue({
          mode: "category",
          catId: categoryData?._id ?? "",
          CategoryName: categoryData?.categoryName ?? "",
          Description: categoryData?.Description ?? "",
        })
      );
    }
  };

  const handleDelete = () => {
    if (isTask) {
      dispatch(openPopup("DeletePopUp"));
      dispatch(
        setInputValue({
          mode: "task",
          taskId: categoryData?._id ?? "",
          TaskName: categoryData?.taskname ?? "",
          Description: categoryData?.Description ?? "",
          Quantity: String(categoryData?.Quantity ?? 0),
          Unit: categoryData?.Unit ?? "",
        })
      );
    } else {
      dispatch(openPopup("DeleteCatPopUp"));
      dispatch(
        setInputValue({
          catId: categoryData?._id ?? "",
          mode: "category",
          CategoryName: categoryData?.categoryName ?? "",
          Description: categoryData?.Description ?? "",
        })
      );
    }
  };

  /* 🐱‍👤 Code by abhishek Raj Start*/

  const cardRef = useRef<HTMLDivElement>(null);
  const popup = useRef<HTMLDivElement>(null);

  const handleClickOutside = useCallback(
    (event: MouseEvent) => {
      if (
        cardRef.current &&
        !cardRef.current.contains(event.target as Node) &&
        popup.current &&
        !popup.current.contains(event.target as Node)
      ) {
        dispatch(clearCategoryPopup());
      }
    },
    [dispatch]
  );

  //🍁 memoised the handleclickoutside function to only rerender on dispatch function, otherwise it will rerender on every render-->🐱‍👤Abhishek Raj

  useEffect(() => {
    document.addEventListener("mousedown", (e) => handleClickOutside(e));
    return () => {
      document.removeEventListener("mousedown", (e) => handleClickOutside(e));
    };
  }, [handleClickOutside]);

  /* 🐱‍👤 Code by abhishek Raj End*/
  const [isHovered, setIsHovered] = useState<{
    id: number | null;
    hoverState: boolean;
  }>({
    id: null,
    hoverState: false,
  });

  console.log("isHovered", isHovered);

  const handleMouseEnter = (id: number) => {
    setIsHovered({ id, hoverState: true });
    console.log("mouse enter with id", id);
  };

  const handleMouseLeave = (id: number) => {
    setIsHovered((prev) => ({
      ...prev,
      hoverState: false,
      id: id,
    }));
  };

  const SkeletonBox = ({
    width = "100px",
    height = "24px",
    className = styles.skeleton_box,
  }) => (
    <span
      className={className}
      style={{
        width,
        height,
      }}
    />
  );

  const name = (
    isTask ? !categoryData?.taskname : !categoryData?.categoryName
  ) ? (
    <SkeletonBox
      width="100px"
      height="24px"
      className={styles.skeleton_box_light}
    />
  ) : (
    <span>{isTask ? categoryData?.taskname : categoryData?.categoryName}</span>
  );

  return (
    <div
      className={`${styles.taskcard_container} ${
        isApprove ? styles.isApproved : ""
      } `}
      onClick={handleTaskNavigation}
      ref={cardRef}
    >
      <div
        className={`
          ${
            styles.blur_content //main
            // categoryData?.updatedData ? styles.blur_for_now : ""
          } ${
          isApprove ? styles.noBlurCard : "" //main
          // !categoryData?.updatedData ? styles.noBlurCard : ""
        }`}
      >
        <div className={styles.taskcard_header}>
          <div className={styles.taskcard_header_left}>
            <h3 className={styles.taskcard_header_heading}>{name}</h3>
            <p className={styles.taskcard_header_leftpara}>
              {!isTask
                ? categoryData && "taskmasterDataLength" in categoryData
                  ? `${categoryData.taskmasterDataLength || 0} Tasks`
                  : "0 Tasks"
                : categoryData?.Unit || "Unit"}
            </p>
          </div>
          <div
            className={styles.taskcard_header_righticon}
            onClick={(e) => {
              e.stopPropagation();
              if (categoryPopup) {
                dispatch(clearCategoryPopup());
              } else {
                // if (!categoryData?.updatedData) {
                handleDotDropdown2(category?._id);
                // }
              }
            }}
          >
            <Twodots />
            {(categoryPopup === category?._id || categoryPopup === id) &&
              !deleted &&
              !isDeletedNext && (
                <div ref={popup} className={styles.cat_popup}>
                  <div
                    onClick={(e) => {
                      e.stopPropagation();
                      handleTaskNavigation();
                      dispatch(clearCategoryPopup());
                    }}
                    className={`${styles.cat_popup_view} ${
                      isHovered?.id === 1
                        ? isHovered.hoverState
                          ? styles.view_hovered
                          : styles.view_notHovered
                        : ""
                    }`}
                    onMouseEnter={() => handleMouseEnter(1)}
                    onMouseLeave={() => handleMouseLeave(1)}
                  >
                    <div className={styles.cat_popup_transition_div_view}></div>
                    <div className={styles.cat_popup_viewicon}>
                      <ViewIcon />
                    </div>
                    <h4>View</h4>
                  </div>
                  <div
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEdit();
                      dispatch(clearCategoryPopup());
                    }}
                    onMouseEnter={() => handleMouseEnter(2)}
                    onMouseLeave={() => handleMouseLeave(2)}
                    className={`${styles.cat_popup_edit} ${
                      isHovered?.id === 2
                        ? isHovered.hoverState
                          ? styles.edit_hovered
                          : styles.edit_notHovered
                        : ""
                    }`}
                  >
                    <div className={styles.cat_popup_transition_div_edit}></div>
                    <div className={styles.cat_popup_editicon}>
                      <YellowEditPencil />
                    </div>
                    <h4>Edit</h4>
                  </div>
                  <div
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete();
                      dispatch(clearCategoryPopup());
                    }}
                    onMouseEnter={() => handleMouseEnter(3)}
                    onMouseLeave={() => handleMouseLeave(3)}
                    className={`${styles.cat_popup_dlt} ${
                      isHovered?.id === 3
                        ? isHovered.hoverState
                          ? styles.dlt_hovered
                          : styles.dlt_notHovered
                        : ""
                    }`}
                  >
                    <div
                      className={styles.cat_popup_transition_div_delete}
                    ></div>
                    <div className={styles.cat_popup_dlticon}>
                      <DeleteIcon />
                    </div>
                    <h4>Delete</h4>
                  </div>
                </div>
              )}
            {(categoryPopup === category?._id || categoryPopup === id) &&
              categoryPopup &&
              (deleted || isDeletedNext) && (
                <div ref={popup} className={styles.cat_popup}>
                  <div
                    onClick={(e) => {
                      e.stopPropagation();
                      handleTaskNavigation();
                      // handleClickOutside(e);
                      dispatch(clearCategoryPopup());
                    }}
                    className={`${styles.cat_popup_view} ${
                      isHovered?.id === 1
                        ? isHovered.hoverState
                          ? styles.view_hovered
                          : styles.view_notHovered
                        : ""
                    }`}
                    onMouseEnter={() => handleMouseEnter(1)}
                    onMouseLeave={() => handleMouseLeave(1)}
                  >
                    <div className={styles.cat_popup_transition_div_view}></div>
                    <div className={styles.cat_popup_viewicon}>
                      <ViewIcon />
                    </div>
                    <h4>View</h4>
                  </div>
                  <div
                    onClick={(e) => {
                      e.stopPropagation();
                      if (!isTask) {
                        handleRestore(category?._id);
                      } else {
                        handleTaskResotre(category._id);
                      }
                      // Instead of passing the React event, create a native event for handleClickOutside
                      handleClickOutside(new window.MouseEvent("mousedown"));
                      dispatch(clearCategoryPopup());
                    }}
                    onMouseEnter={() => handleMouseEnter(2)}
                    onMouseLeave={() => handleMouseLeave(2)}
                    className={`${styles.cat_popup_edit} ${
                      isHovered?.id === 2
                        ? isHovered.hoverState
                          ? styles.edit_hovered
                          : styles.edit_notHovered
                        : ""
                    }`}
                  >
                    <div className={styles.cat_popup_transition_div_edit}></div>
                    <div className={styles.cat_popup_restoreicon}>
                      <RestoreIcon color="var(--secondary_color)" />
                    </div>
                    <h4>Restore</h4>
                  </div>
                </div>
              )}
          </div>
        </div>

        {/* Render items for task or category */}
        {!isTask ? (
          <div className={styles.taskcard_items}>
            {items.map((item, index) => (
              <CardItems
                key={index}
                title={item.title}
                name={
                  categoryData && "totalDepartmentIdLength" in categoryData
                    ? String((categoryData as any)[item.name] ?? "0")
                    : "0"
                }
              />
            ))}
          </div>
        ) : (
          <div className={styles.taskcard_items}>
            {taskItems.map((item, index) => (
              <CardItems
                key={index}
                title={item.title}
                name={
                  categoryData && "department" in categoryData
                    ? String(categoryData[item.name] ?? "N/A")
                    : "N/A"
                }
              />
            ))}
          </div>
        )}
      </div>

      {!isApprove && (
        <div className={styles.hiddenrequestbtn}>
          <Button type="Next" Content="Edit request" Callback={Callback} />
        </div>
      )}
    </div>
  );
}


// import { useLocation, useNavigate } from "react-router-dom";
// import { useDispatch, useSelector } from "react-redux";

// import { useCallback, useEffect, useRef, useState } from "react";
// import {
//   useAppDispatch,
//   useAppSelector,
// } from "../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
// import { updateNumValue } from "../../../../redux/features/Modules/TaskMaster/Slices/CategorySlice";
// import { setisDeletedNext } from "../../../../redux/features/Modules/Reusble/deletedSlice";
// import { setNavigate } from "../../../../redux/features/Modules/Reusble/navigationSlice";

// import {
//   clearCategoryPopup,
//   openCategoryPopup,
//   openPopup,
// } from "../../../../redux/features/Modules/Reusble/popupSlice";
// import { setInputValue } from "../../../../redux/features/Modules/Reusble/floatinglabelslice";
// import {
//   DeleteIcon,
//   RestoreIcon,
//   Twodots,
//   User,
//   ViewIcon,
//   YellowEditPencil,
// } from "../../../../assets/icons";
// import { CardItems } from "../CardItems";
// import Button from "../../Global/Button";
// import styles from "./Styles/Card.module.css";
// import { RootState } from "../../../../redux/store";
// import { CardProps } from "../TaskMasterInterfaces/TaskMasterInterface";
// import {
//   ResponseDataItem,
//   TaskInterface,
// } from "../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";
// import {
//   useRestoreCategoryMutation,
//   useRestoreTaskMutation,
// } from "../../../../redux/api/Modules/TaskMaster/TaskMasterapi";
// import { setToast } from "../../../../redux/features/Modules/Reusble/ToastSlice";
// import { useAuth } from "../../../../AuthProvider";
// import { setApprovalFormStep } from "../../../../redux/features/Modules/Reusble/popupSlice";
// import { setcurrentTaskData } from "../../../../redux/features/Modules/TaskMaster/Slices/TaskMasterSlice";

// const items: { title: string; name: keyof ResponseDataItem }[] = [
//   { title: "Departments", name: "totalDepartmentIdLength" },
//   { title: "Designations", name: "totalDesiginationIdLength" },
//   { title: "Manpower", name: "TotalManpowerIdLength" },
//   { title: "Machinery", name: "TotalMachinaryIdLength" },
//   { title: "Tools", name: "TotalToolIdLength" },
//   { title: "Materials", name: "TotalMaterialIdLength" },
// ];

// const taskItems: { title: string; name: keyof TaskInterface }[] = [
//   { title: "Subtasks", name: "SubtaskId" },
//   { title: "Designation", name: "desigination" },
//   { title: "Manpower", name: "manpower" },
//   { title: "Machinery", name: "machinery" },
//   { title: "Tools", name: "tools" },
//   { title: "Materials", name: "material" },
// ];

// export function Card({
//   category,
//   path,
//   taskId,
//   isTask,
//   // isApprove,
//   Callback,
//   id,
// }: CardProps) {
//   const navigate = useNavigate();
//   const [categoryData, setCategoryData] = useState<TaskInterface | null>(null);
//   console.log("cat data", categoryData);

//   //------------------------------------------------------------------------------------------------------------------------------------------

//   const { user } = useAuth();
//   const isMD = user?.designationId?.name === "MD";
//   const isHOD = user?.designationId?.name === "HOD";

//   //------------------------------------------------------------------------------------------------------------------------------------------
//   const isDeletedNext = useAppSelector(
//     (state) => state.isDeletedSLice.isDeletedNext
//   );
//   const deleted = useAppSelector((state) => state.isDeletedSLice.isDeleted);

//   useEffect(() => {
//     setCategoryData(category);
//   }, [category]);

//   const dispatch = useAppDispatch();

//   const handleTaskNavigation = () => {
//     if (!isTask && categoryData?.isApproved === false) {
//       return;
//     }
//     dispatch(updateNumValue(1));
//     dispatch(setisDeletedNext({ isDelete: isDeletedNext }));

//     dispatch(
//       setcurrentTaskData({
//         _id: "",
//         name: "",
//         Unit: "",
//         Description: "",
//         subtaskWeighatages: 0,
//         Tracking: "",
//         DepartmentId: [],
//         DesignationId: [],
//         MaterialId: [],
//         ToolId: [],
//         MachinaryId: [],
//         ManpowerId: [],
//         Adminid: [],
//         TaskmasterId: {},
//         ReporterId: {
//           Reporter: [],
//         },
//         AssigneeId: [],
//         Subtaskdetails: [],
//         MethodId: {
//           work_instruction_id: [],
//           task_closing_requirement: [],
//           Controlplan: [],
//           Failuremode: [],
//         },
//       })
//     );

//     if (isTask) {
//       if (taskId) {
//         navigate(`${path}`);
//         dispatch(
//           setNavigate({
//             route: path,
//             title: categoryData?.categoryName ?? categoryData?.taskname ?? "",
//           })
//         );
//         return;
//       } else {
//         showToast({
//           messageContent: "Waiting for Server Response",
//           type: "warning",
//         });
//         return;
//       }
//     }

//     navigate(`${path}`);
//     dispatch(
//       setNavigate({
//         route: path,
//         title: categoryData?.categoryName ?? categoryData?.taskname ?? "",
//       })
//     );
//   };
//   // handlerestore function to restore the delete category

//   const [restoreCategory] = useRestoreCategoryMutation();
//   const handleRestore = async (catid) => {
//     const response = await restoreCategory({ categoryId: catid });
//     console.log(response, "thisisreponsemann");
//     if (response?.data?.success) {
//       showToast({
//         messageContent: "Category Restored Successfully",
//         type: "success",
//       });
//     } else {
//       showToast({
//         messageContent: "Something Went Wrong",
//         type: "warning",
//       });
//     }
//   };
//   // handletaskrestore to restore deleted task
//   const [restoreTask] = useRestoreTaskMutation();
//   const handleTaskResotre = async (taskId) => {
//     const response = await restoreTask({ taskId: taskId });
//     if (response?.data?.success) {
//       showToast({
//         messageContent: "Task Restored Successfully!",
//         type: "success",
//       });
//     } else {
//       dispatch(
//         setToast({
//           isOpen: true,
//           messageContent: "Something Went Wrong",
//           type: "warning",
//         })
//       );
//     }
//   };
//   // const { data } = GetTaskCategoryApi();
//   // console.log("agyaaa", data);

//   const { popups, categoryPopup } = useSelector(
//     (state: RootState) => state.popup
//   );
//   // console.log('poopups:',categoryPopup);

//   // const handleDotDropdown = (cardId: string) => {
//   //   if (popups[cardId]) {
//   //     dispatch(closePopup(cardId));
//   //   } else {
//   //     dispatch(openPopup(cardId));
//   //   }
//   // };
//   const handleDotDropdown2 = (cardId: string) => {
//     console.log("function called hereee broooo");
//     dispatch(openCategoryPopup(cardId));
//   };

//   const handleEdit = () => {
//     if (isTask) {
//       dispatch(openPopup("AddTaskForm"));

//       dispatch(
//         setInputValue({
//           mode: "task",
//           tskId: categoryData?._id ?? "",
//           TaskName:
//             categoryData?.updatedData?.name || categoryData?.taskname || "",
//           Description:
//             categoryData?.updatedData?.Description ||
//             categoryData?.Description ||
//             "",
//           Quantity: String(
//             categoryData?.updatedData?.Quantity || categoryData?.Quantity || 0
//           ),
//           Unit: categoryData?.updatedData?.Unit || categoryData?.Unit || "",
//         })
//       );
//     } else {
//       dispatch(openPopup("AddCategoryForm"));
//       dispatch(
//         setInputValue({
//           mode: "category",
//           catId: categoryData?._id ?? "",
//           CategoryName:
//             categoryData?.updatedData?.name || categoryData?.categoryName || "",
//           Description:
//             categoryData?.updatedData?.Description ||
//             categoryData?.Description ||
//             "",
//         })
//       );
//     }
//   };

//   const handleDelete = () => {
//     if (isTask) {
//       dispatch(openPopup("DeletePopUp"));
//       dispatch(
//         setInputValue({
//           mode: "task",
//           taskId: categoryData?._id ?? "",
//           TaskName: categoryData?.taskname ?? "",
//           Description: categoryData?.Description ?? "",
//           Quantity: String(categoryData?.Quantity ?? 0),
//           Unit: categoryData?.Unit ?? "",
//         })
//       );
//     } else {
//       dispatch(openPopup("DeleteCatPopUp"));
//       dispatch(
//         setInputValue({
//           catId: categoryData?._id ?? "",
//           mode: "category",
//           CategoryName: categoryData?.categoryName ?? "",
//           Description: categoryData?.Description ?? "",
//         })
//       );
//     }
//   };

//   // code by Maheshwar Kohal
//   // this is the payload that we are sending to our store so that it can be directly accessesd and used in approval form

//   const handleTakeAction = (step: "reason" | "initial") => {
//     const payload = {
//       mode: isTask ? "task" : "category", // sending mode along so that we can render accordingly.
//       taskId: categoryData?._id ?? "",
//       TaskName: categoryData?.updatedData?.name || categoryData?.taskname || "",
//       catId: categoryData?._id ?? "",
//       CategoryName:
//         categoryData?.updatedData?.name || categoryData?.categoryName || "",
//       Description:
//         categoryData?.updatedData?.Description ||
//         categoryData?.Description ||
//         "",
//       Quantity:
//         categoryData?.updatedData?.Quantity || categoryData?.Quantity || 0,
//       Unit: categoryData?.updatedData?.Unit || categoryData?.Unit || "",
//       Reason: categoryData?.MDdeniedComment,
//     };

//     console.log("Final Payload: ", payload);
//     dispatch(setApprovalFormStep(step));
//     dispatch(openPopup("ApprovalForm"));
//     dispatch(setInputValue(payload));
//   };

//   const handleTakeActionClick = () => handleTakeAction("initial");
//   const handleReasonClick = () => handleTakeAction("reason");

//   /* 🐱‍👤 Code by abhishek Raj Start*/

//   const cardRef = useRef<HTMLDivElement>(null);
//   const popup = useRef<HTMLDivElement>(null);

//   const handleClickOutside = useCallback(
//     (event: MouseEvent) => {
//       if (
//         cardRef.current &&
//         !cardRef.current.contains(event.target as Node) &&
//         popup.current &&
//         !popup.current.contains(event.target as Node)
//       ) {
//         dispatch(clearCategoryPopup());
//       }
//     },
//     [dispatch]
//   );

//   //🍁 memoised the handleclickoutside function to only rerender on dispatch function, otherwise it will rerender on every render-->🐱‍👤Abhishek Raj

//   useEffect(() => {
//     document.addEventListener("mousedown", (e) => handleClickOutside(e));
//     return () => {
//       document.removeEventListener("mousedown", (e) => handleClickOutside(e));
//     };
//   }, [handleClickOutside]);

//   /* 🐱‍👤 Code by abhishek Raj End*/
//   const [isHovered, setIsHovered] = useState<{
//     id: number | null;
//     hoverState: boolean;
//   }>({
//     id: null,
//     hoverState: false,
//   });

//   console.log("isHovered", isHovered);

//   const handleMouseEnter = (id: number) => {
//     setIsHovered({ id, hoverState: true });
//     console.log("mouse enter with id", id);
//   };

//   const handleMouseLeave = (id: number) => {
//     setIsHovered((prev) => ({
//       ...prev,
//       hoverState: false,
//       id: id,
//     }));
//   };

//   const SkeletonBox = ({
//     width = "100px",
//     height = "24px",
//     className = styles.skeleton_box,
//   }) => (
//     <span
//       className={className}
//       style={{
//         width,
//         height,
//       }}
//     />
//   );

//   const name = (
//     isTask ? !categoryData?.taskname : !categoryData?.categoryName
//   ) ? (
//     <SkeletonBox
//       width="100px"
//       height="24px"
//       className={styles.skeleton_box_light}
//     />
//   ) : (
//     <span>{isTask ? categoryData?.taskname : categoryData?.categoryName}</span>
//   );

//   return (
//     <div
//       className={`${styles.taskcard_container} 
        
//          ${
//            isMD &&
//            categoryData?.isApproved === false &&
//            categoryData?.MDDenied === false
//              ? styles.mdHoverEffect
//              : ""
//          }

           
//          ${isHOD ? styles.HODhoverEffect : ""}  
         
//          ${
//            categoryData?.MDDenied && categoryData?.MDdeniedComment
//              ? styles.denied
//              : ""
//          }`}
//       onClick={handleTaskNavigation}
//       ref={cardRef}
//     >
//       {isMD &&
//         categoryData?.isApproved === false &&
//         categoryData?.MDDenied === false &&
//         !isTask && (
//           <div className={styles.mdActionBtnWrapper}>
//             <Button
//               type="Approve"
//               Content="Take Action"
//               Callback={handleTakeActionClick}
//             />
//           </div>
//         )}
//       {isHOD &&
//         categoryData?.isApproved === false &&
//         categoryData?.MDDenied === false &&
//         !isTask && (
//           <div className={styles.HODactionBtnWrapper}>
//             <Button
//               type="EditRequest"
//               Content="Edit Request"
//               Callback={(e) => {
//                 e.stopPropagation();
//                 handleEdit();
//                 dispatch(clearCategoryPopup());
//               }}
//             />
//           </div>
//         )}

//       {isHOD &&
//         categoryData?.MDDenied &&
//         categoryData?.MDdeniedComment &&
//         !isTask && (
//           <div className={styles.HODactionBtnWrapper}>
//             <Button
//               type="Reason"
//               Content="Reason"
//               Callback={handleReasonClick}
//             />
//             <Button
//               type="EditRequest"
//               Content="Edit"
//               Callback={(e) => {
//                 e.stopPropagation();
//                 handleEdit();
//                 dispatch(clearCategoryPopup());
//               }}
//             />
//           </div>
//         )}

//       <div
//         className={`
//     ${
//       isTask
//         ? styles.noBlurCard
//         : categoryData?.isApproved
//         ? styles.noBlurCard
//         : styles.blur_for_now
//     }

//   `}
//       >
//         <div className={styles.taskcard_header}>
//           <div className={styles.taskcard_header_left}>
//             <h3 className={styles.taskcard_header_heading}>
//               {categoryData?.taskname ?? categoryData?.categoryName ?? "NA"}
//             </h3>

//             <p className={styles.taskcard_header_leftpara}>
//               {!isTask
//                 ? categoryData && "taskmasterDataLength" in categoryData
//                   ? `${categoryData.taskmasterDataLength || 0} Tasks`
//                   : "0 Tasks"
//                 : categoryData?.Unit || "Unit"}
//             </p>
//           </div>
//           <div
//             className={styles.taskcard_header_righticon}
//             onClick={(e) => {
//               e.stopPropagation();
//               if (categoryPopup) {
//                 dispatch(clearCategoryPopup());
//               } else {
//                 if (categoryData?.isApproved === true || isTask === true) {   
               
//                   handleDotDropdown2(category?._id);
//                 }
//               }
//             }}
//           >
//             {!isMD && <Twodots />}
//             {(categoryPopup === category?._id || categoryPopup === id) &&
//               !deleted &&
//               !isDeletedNext && (
//                 <div ref={popup} className={styles.cat_popup}>
//                   <div
//                     onClick={(e) => {
//                       e.stopPropagation();
//                       handleTaskNavigation();
//                       dispatch(clearCategoryPopup());
//                     }}
//                     className={`${styles.cat_popup_view} ${
//                       isHovered?.id === 1
//                         ? isHovered.hoverState
//                           ? styles.view_hovered
//                           : styles.view_notHovered
//                         : ""
//                     }`}
//                     onMouseEnter={() => handleMouseEnter(1)}
//                     onMouseLeave={() => handleMouseLeave(1)}
//                   >
//                     <div className={styles.cat_popup_transition_div_view}></div>
//                     <div className={styles.cat_popup_viewicon}>
//                       <ViewIcon />
//                     </div>
//                     <h4>View</h4>
//                   </div>
//                   <div
//                     onClick={(e) => {
//                       e.stopPropagation();
//                       handleEdit();
//                       dispatch(clearCategoryPopup());
//                     }}
//                     onMouseEnter={() => handleMouseEnter(2)}
//                     onMouseLeave={() => handleMouseLeave(2)}
//                     className={`${styles.cat_popup_edit} ${
//                       isHovered?.id === 2
//                         ? isHovered.hoverState
//                           ? styles.edit_hovered
//                           : styles.edit_notHovered
//                         : ""
//                     }`}
//                   >
//                     <div className={styles.cat_popup_transition_div_edit}></div>
//                     <div className={styles.cat_popup_editicon}>
//                       <YellowEditPencil />
//                     </div>
//                     <h4>Edit</h4>
//                   </div>
//                   <div
//                     onClick={(e) => {
//                       e.stopPropagation();
//                       handleDelete();
//                       dispatch(clearCategoryPopup());
//                     }}
//                     onMouseEnter={() => handleMouseEnter(3)}
//                     onMouseLeave={() => handleMouseLeave(3)}
//                     className={`${styles.cat_popup_dlt} ${
//                       isHovered?.id === 3
//                         ? isHovered.hoverState
//                           ? styles.dlt_hovered
//                           : styles.dlt_notHovered
//                         : ""
//                     }`}
//                   >
//                     <div
//                       className={styles.cat_popup_transition_div_delete}
//                     ></div>
//                     <div className={styles.cat_popup_dlticon}>
//                       <DeleteIcon />
//                     </div>
//                     <h4>Delete</h4>
//                   </div>
//                 </div>
//               )}
//             {(categoryPopup === category?._id || categoryPopup === id) &&
//               categoryPopup &&
//               (deleted || isDeletedNext) && (
//                 <div ref={popup} className={styles.cat_popup}>
//                   <div
//                     onClick={(e) => {
//                       e.stopPropagation();
//                       handleTaskNavigation();
//                       // handleClickOutside(e);
//                       dispatch(clearCategoryPopup());
//                     }}
//                     className={`${styles.cat_popup_view} ${
//                       isHovered?.id === 1
//                         ? isHovered.hoverState
//                           ? styles.view_hovered
//                           : styles.view_notHovered
//                         : ""
//                     }`}
//                     onMouseEnter={() => handleMouseEnter(1)}
//                     onMouseLeave={() => handleMouseLeave(1)}
//                   >
//                     <div className={styles.cat_popup_transition_div_view}></div>
//                     <div className={styles.cat_popup_viewicon}>
//                       <ViewIcon />
//                     </div>
//                     <h4>View</h4>
//                   </div>
//                   <div
//                     onClick={(e) => {
//                       e.stopPropagation();
//                       if (!isTask) {
//                         handleRestore(category?._id);
//                       } else {
//                         handleTaskResotre(category._id);
//                       }
//                       // Instead of passing the React event, create a native event for handleClickOutside
//                       handleClickOutside(new window.MouseEvent("mousedown"));
//                       dispatch(clearCategoryPopup());
//                     }}
//                     onMouseEnter={() => handleMouseEnter(2)}
//                     onMouseLeave={() => handleMouseLeave(2)}
//                     className={`${styles.cat_popup_edit} ${
//                       isHovered?.id === 2
//                         ? isHovered.hoverState
//                           ? styles.edit_hovered
//                           : styles.edit_notHovered
//                         : ""
//                     }`}
//                   >
//                     <div className={styles.cat_popup_transition_div_edit}></div>
//                     <div className={styles.cat_popup_restoreicon}>
//                       <RestoreIcon color="var(--secondary_color)" />
//                     </div>
//                     <h4>Restore</h4>
//                   </div>
//                 </div>
//               )}
//           </div>
//         </div>

//         {/* Render items for task or category */}
//         {!isTask ? (
//           <div className={styles.taskcard_items}>
//             {items.map((item, index) => (
//               <CardItems
//                 key={index}
//                 title={item.title}
//                 name={
//                   categoryData && "totalDepartmentIdLength" in categoryData
//                     ? String((categoryData as any)[item.name] ?? "0")
//                     : "0"
//                 }
//               />
//             ))}
//           </div>
//         ) : (
//           <div className={styles.taskcard_items}>
//             {taskItems.map((item, index) => (
//               <CardItems
//                 key={index}
//                 title={item.title}
//                 name={
//                   categoryData && "department" in categoryData
//                     ? String(categoryData[item.name] ?? "N/A")
//                     : "N/A"
//                 }
//               />
//             ))}
//           </div>
//         )}
//       </div>
//     </div>
//   );
// }
