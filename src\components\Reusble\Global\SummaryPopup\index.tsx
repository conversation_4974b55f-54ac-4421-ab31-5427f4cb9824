import React, { useState, useRef, useEffect } from "react";
import styles from "./Styles/Summary.module.css";

import { CloseIcon } from "../../../../assets/icons";
import { SummaryPopupProps } from "../GlobalInterfaces/GlobalInterface";

const SummaryPopup: React.FC<SummaryPopupProps> = ({
  header,
  callbackBack,
  callbackCross,
  callbackApprove,
  children,
}) => {
  const [closing, setClosing] = useState(false);
  const popupRef = useRef<HTMLDivElement>(null);

  const handleClose = () => {
    setClosing(true);  
  };

  const handleAnimationEnd = () => {
    if (closing && callbackCross) {
      callbackCross();
    }
  };

  // Outside click handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popupRef.current &&
        !popupRef.current.contains(event.target as Node)
      ) {
        setClosing(true);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        e.preventDefault();
        setClosing(true);
      }
    };
    document.addEventListener("keydown", handleEsc);
    return () => document.removeEventListener("keydown", handleEsc);
  }, []);

  return (
    <div
      ref={popupRef}
      onClick={(e) => e.stopPropagation()}
      tabIndex={0}
      className={`${styles.summary_popup_form_container} ${closing ? styles.closing : ""}`}
      onAnimationEnd={handleAnimationEnd}
    >
      <div className={styles.summary_popup_header}>
        <h3
          className={styles.summary_popup_form_top_text}
          style={{ color: "var(--primary_color)" }}
        >
          {header}
        </h3>
        <button className={styles.closeButton} onClick={handleClose}>
          <CloseIcon />
        </button>
      </div>
      <div className={styles.summary_popup_main_body}>{children}</div>
      <div className={styles.summary_popup_button_div}>
        {/* <Button type="Cancel" Content="Back" Callback={callbackBack} />
        <Button type="Approve" Content="Submit" Callback={callbackApprove} /> */}
      </div>
    </div>
  );
};

export default SummaryPopup;