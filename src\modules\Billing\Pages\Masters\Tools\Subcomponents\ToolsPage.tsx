import { useEffect, useRef, useState } from "react";
import { Loader } from "../../../../../../assets/loader";
import {
  useAppDispatch,
  useAppSelector,
} from "../../../../../../redux/hooks/Modules/Reduxhooks/ReduxHooks";
import {
  useDeleteToolsByToolIdMutation,
  useLazyGetToolDesiginationDetailByIdQuery,
} from "../../../../../../redux/api/Modules/Billing/Billingapi";
import TMMMNav from "../../../../../../components/Reusble/TMMMNav";
import styles from "../Styles/Tools.module.css";
import MasterCard from "../../../../../../components/Reusble/Billing/Masters/MasterCard";
import { DeletePopup } from "../../../../../../components/Reusble/Global/DeletePopup";
import { closePopup } from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import { resetFormData } from "../../../../../../redux/features/Modules/TaskMaster/Slices/MastersSlice";
import {
  checkAndDownloadImages,
  initializeDatabase,
  withInternetCheck,
} from "../../../../../../functions/functions";
import { useParams } from "react-router-dom";
import { useNestedPouchSearch } from "../../../../../../functions/useNestedLocalSearch";
import ToolsDiscard from "./ToolsDiscard";
import { useToast } from "../../../../../../hooks/ToastHook";
import { useSelector } from "react-redux";
import { RootState } from "../../../../../../redux/store";
import {
  clearFetchedMasters,
  SetCategoryId,
  setToolData,
  setSearchToolData,
} from "../../../../../../redux/features/Modules/Masters";

const ToolsPage = () => {
  const data = useSelector(
    (state: RootState) => state.masterReduxSlice.fetchedToolData
  );
  const searchToolCardData = useSelector((state: RootState)=> state.masterReduxSlice.searchedToolData)
  const showToast = useToast();
  const [filter, setFilter] = useState<string>("Consumable");
  const currentOpenPopup = useAppSelector((state) => state.popup.popups);
  const formData = useAppSelector((state) => state.masterForm.formToolsData);
  const { toolsCategoryId } = useParams();
  const searchKey = useAppSelector((state) => state.taskMaster.searchKey);
  //to detect changes in the localdb
  // const detectChanges = useAppSelector((state) => state.backupSlice.isOpen);
  const [searchLocalKey, setSearchLocalKey] = useState("");

  const [toolCardData, setToolCardData] = useState<any>({});
  // const [searchToolCardData, setSearchToolCardData] = useState<any>([]);
  const [editToolData, setEditToolData] = useState<any>({});
  const [page, setPage] = useState<number>(1);
  const pageSize = 16;

  const fullDataRef = useRef<any[]>([]);
  const dispatch = useAppDispatch();

  //get tool's edit data
  const [getToolDesiginationDetails] =
    useLazyGetToolDesiginationDetailByIdQuery();
    

  // const localChange = useAppSelector(
  //   (state) => state.backupSlice.isLocalChange
  // );

  //delete tool
  const [deleteToolDesignation] = useDeleteToolsByToolIdMutation();

  //this is previous code before useRef
  const getDatafromDb = async (toolid: string) => {
    const dbName = await initializeDatabase("ToolDesignation");

    const fetchedData = await window.electron.getDocumentByParentId({
      dbName,
      catId: toolid,
      categoryId: "categoryId",
      isDeletedNext: false,
      type: filter,
    });

    let sortedData = fetchedData.data || [];
    sortedData.sort((a: any, b: any) => {
      const nameA = a.name?.toLowerCase() || "";
      const nameB = b.name?.toLowerCase() || "";
      return nameA.localeCompare(nameB);
    });
    console.log(fetchedData, "trhasdfsdfsd");
    fullDataRef.current = sortedData;

    // Reset paginated view to first page
    // setPage(1);
    // const initialSlice = sortedData.slice(0, pageSize);
    // setToolCardData({ ...fetchedData, data: initialSlice });

    const initialSlice = Array.isArray(sortedData)
      ? sortedData.slice(0, pageSize)
      : [];
    dispatch(setToolData({ ...fetchedData, data: initialSlice }));
    withInternetCheck(() =>
      checkAndDownloadImages("ToolDesignation", initialSlice, dispatch)
    )();
  };
  console.log(data,"Thois9sdafasdf")

  useEffect(() => {
    if (page === 1 || fullDataRef.current.length === 0) return;

    const start = (page - 1) * pageSize;
    const end = page * pageSize;
    const nextSlice = fullDataRef.current.slice(start, end);
    const newData = {
      ...data,
      data: [
        ...(Array.isArray((data as any)?.data) ? (data as any).data : []),
        ...nextSlice,
      ],
    };

    dispatch(setToolData(newData));



    // setToolCardData((prev: any) => ({
    //   ...prev,
    //   data: [...(prev.data || []), ...nextSlice],
    // }));

    withInternetCheck(() =>
      checkAndDownloadImages("ToolDesignation", nextSlice, dispatch)
    )();
  }, [page]);
  console.log(data, "Thsiis");

  useEffect(() => {
    setSearchLocalKey(searchKey);
  }, [searchKey]);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollTop + clientHeight >= scrollHeight - 1) {
      setPage((prev) => prev + 1);
    }
  };
  useEffect(() => {
    if (toolsCategoryId) getDatafromDb(toolsCategoryId);
  }, [filter, toolsCategoryId]);

  useEffect(() => {
    if (searchToolCardData?.length > 0) {
      const imageDownload = withInternetCheck(() =>
        checkAndDownloadImages("ToolDesignation",( searchToolCardData as any), dispatch)
      );
      imageDownload();
    }
  }, [searchToolCardData]);

  useNestedPouchSearch({
    pathRecord: "ToolDesignation",
    searchKey: searchLocalKey,
    setData: setSearchToolData,
    setPage,
    key: "name",
    extraSearchParams: {
      catId: toolsCategoryId,
      categoryId: "categoryId",
      isDeletedNext: false,
      typed: filter,
    },
  });

  console.log("toolCardData", toolCardData);
  const navRef = useRef<HTMLDivElement>(null);
  const mainContentRef = useRef<HTMLDivElement>(null);
  const [width, setWidth] = useState<null | number>(null);
  const clientFunction = () => {
    // const cardViewcontainer = document.querySelector(`.${styles.progress_card_view_container}`) as HTMLDivElement;
    // console.log( 'details of card view container in useEffect div',cardViewcontainer);
    // const details = cardViewcontainer?.getBoundingClientRect();
    // console.log('details of card view container in useEffect details',details,);
    // setWidth(details?.width);
    const mainContentWidth =
      mainContentRef?.current?.getBoundingClientRect()?.width;
    console.log("inner width");
    // if (window.innerWidth < 1200) {
    navRef.current?.style.setProperty("width", `${mainContentWidth}px`);
    // }
  };
  const condition =
    mainContentRef.current &&
    mainContentRef.current.getBoundingClientRect().width < 1200;

  useEffect(() => {
    clientFunction();
    const handleResize = () => clientFunction();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [window.innerWidth, condition]);

  useEffect(() => {
    return () => {
      dispatch(clearFetchedMasters());
    };
  }, []);

  useEffect(() => {
    dispatch(SetCategoryId(toolsCategoryId as string));
  }, [toolsCategoryId]);

  console.log((data as any).consumableCount, "here is data");
    console.log(searchToolCardData, "searchedT0ool")

  return (
    <>
      <div ref={navRef}>
        <TMMMNav
          isLeftCallback={(isLeft: boolean) => {
            if (isLeft) {
              setFilter("Consumable");
            } else {
              setFilter("Returnable");
            }
          }}
          countLeft={(data as any)?.consumableCount} //this is consumable count for toggle switch
          countRight={(data as any)?.returnableCount}
          Label={"Tools"}
          TargetForm={"AddToolsForm"}
        />
      </div>
      <div
        ref={mainContentRef}
        style={{ position: "relative", zIndex: 0, marginTop: "2rem" }}
        className={styles.main_content_wrapper}
      >
        <div className={styles.cardview} onScroll={(e) => handleScroll(e)}>
          <div className={`${styles.inner_cardview} ${styles.inner_cardview2}`}>
            {(data as any)?.data && (data as any)?.data?.length > 0 ? (
              (searchToolCardData?.length > 0
                ? searchToolCardData
                : (data as any)?.data
              )
                .filter((item: any) =>
                  filter === "Consumable"
                    ? item?.type?.toLowerCase() === "consumable"
                    : item?.type?.toLowerCase() === "returnable"
                )
                .map((item: any) => (
                  <MasterCard
                    variant="tools"
                    callbackEditData={async () => {
                      const response = await getToolDesiginationDetails({
                        toolId: item?._id,
                      });
                      console.log("response tools", response);

                      setEditToolData(response?.data?.data);
                      return response;
                    }}
                    editData={editToolData}
                    data={{
                      _id: item?._id,
                      images: item?.images?.[0],
                      title: item?.name,
                      items: [
                        { title: "Brands", name: item?.Brands },
                        { title: "Grades", name: item?.Grades },
                        { title: "Users", name: item.users },
                      ],
                      brandDetails: item?.BrandDetails,
                    }}
                  />
                ))
            ) : (
              <div className={styles.loader_loading}>
                <img
                  src={Loader.suryaconLogo}
                  alt="Loading..."
                  className={styles.loader_loading_image}
                />
              </div>
            )}
          </div>
        </div>
      </div>
      {currentOpenPopup["deleteTool"] && (
        <DeletePopup
          header="Are you sure you want to delete this Tool?"
          height="calc(100% - 7.25rem)"
          heightupperlimit="0"
          callbackDelete={async () => {
            await deleteToolDesignation({
              toolId: formData?._id,
            }).unwrap();
            showToast({
              messageContent: `Tool Deleted Successfully!`,
              type: "success",
            });

            dispatch(closePopup("deleteTool"));
            dispatch(resetFormData());
          }}
          onClose={() => {
            dispatch(closePopup("deleteTool"));
            dispatch(resetFormData());
          }}
        >
          <ToolsDiscard
            formData={formData}
            // initialFormData={initialFormData}
            // formMode={formMode}
            // deletedFormData={deletedFormData}
            // deletedGradeData={deletedGradeData}
          />
        </DeletePopup>
      )}
    </>
  );
};

export default ToolsPage;
