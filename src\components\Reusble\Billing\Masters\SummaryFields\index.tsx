import styles from "./Styles/SummaryFields.module.css";

interface SummaryFieldsProps {
  data: {
    label: string;
    value: string | string[]; // Allow multiple values as an array
  }[];
  flexLayout?: boolean; // Optional prop to control layout
}

const SummaryFields: React.FC<SummaryFieldsProps> = ({
  data,
  flexLayout = false,
}) => {
  return (
    <div
      className={`${styles.summarymaster_container} ${
        flexLayout ? styles.flexLayout : ""
      }`}
    >
      {data.map((item, index) => (
        <div key={index} className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <p
              className="p_tag_14px_weight"
              style={{ color: "var(--text-black-60)" }}
            >
              {item.label}
            </p>
            <h4
              className={Array.isArray(item.value) ? styles.flexValue : ""}
              style={{ color: "var(--text-black-87)"}}
            >
            
              {Array.isArray(item.value)
                ? item.value.map((val, idx) => <span key={idx}>{val}</span>)
                : item.value}
            </h4>
          </div>
        </div>
      ))}
    </div>
  );
};

export default SummaryFields;
