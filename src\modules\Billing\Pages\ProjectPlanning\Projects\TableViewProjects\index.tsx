import styles from "./Styles/TableViewProjects.module.css";

import { useDispatch, useSelector } from "react-redux";

import SearchBar from "../../../../../../components/Reusble/Global/SearchBar";
import { TableViewProjectsProps } from "../../../../../../interfaces/Modules/Billing/ProjectPlanning/ProjectPlanning";
import { RootState } from "../../../../../../redux/store";
import { ProjectData } from "../AddProjectForm/Interfaces/interface";
import React from "react";
import {
  extractDateParts,
  getFileName,
} from "../../../../../../functions/functions";
import { image_url } from "../../../../../../config/urls";
import { setpage } from "../../../../../../redux/features/Modules/Billing/ProjectPlanning/Slices/ProjectSlice";

// const data = [
//   {
//     projectName: "Homeland Group",
//     projectType: "Hospital & Development",
//     clientName: "Homeland Construction Ltd.",
//     area: "1,00,000",
//     areaUnit: "Sft",
//     estimatedCost: "250,00,00,000",
//     projectCompleted: "280,00,00,000",
//     costCurrency: "INR",
//     startDate: "March,2024",
//     number: "7888776677",
//     duration: "36 Months",
//     typeOfBuilding: "Commercial",
//     ratePerUnit: "Sft",
//     rate: "650",
//     icon: PorfileImg,
//   },

//   {
//     projectName: "CP 67",
//     clientName: "CP Constructions",
//     projectType: "Hospital & Development",
//     projectCompleted: "280,00,00,000",
//     area: "1,00,000",
//     areaUnit: "Sft",
//     estimatedCost: "250,00,00,000",
//     costCurrency: "INR",
//     startDate: "March,2024",
//     number: "7888776677",
//     duration: "36 Months",
//     typeOfBuilding: "Commercial",
//     ratePerUnit: "Sft",
//     rate: "650",
//     icon: PorfileImg,
//   },

//   {
//     projectName: "Fintech Square",
//     clientName: "Fintech Innovations",
//     projectType: "Hospital & Development",
//     projectCompleted: "280,00,00,000",
//     area: "1,00,000",
//     areaUnit: "Sft",
//     estimatedCost: "250,00,00,000",
//     costCurrency: "INR",
//     startDate: "March,2024",
//     number: "7888776677",
//     duration: "36 Months",
//     typeOfBuilding: "Residential",
//     ratePerUnit: "Item rate",
//     rate: "Not decided",
//     icon: PorfileImg,
//   },
// ];

const TableViewProjects: React.FC<TableViewProjectsProps> = ({
  data,
  searchedData,
}) => {
  const dispatch = useDispatch();
  const page = useSelector((state: RootState) => state.projectLocalDb.page);
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    // Avoid multiple triggers while loading

    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    if (scrollTop + clientHeight >= scrollHeight - 10) {
      dispatch(setpage(page + 1));
      // setPage((prevPage) => prevPage + 1);
    }
  };
  console.log(data, "projectdata for tables");

  // }

  return (
    <div
      onScroll={(e) => handleScroll(e)}
      className={styles.tableViewProject_outerdiv}
    >
      <div className={styles.tableViewProject_tablecontainer}>
        <table className={styles.tableViewProject_projectTable}>
          <thead>
            <tr>
              <th>
                <input type="checkbox" />
              </th>

              <th>
                <SearchBar
                  className="tableview_searchbar"
                  placeholder="Search"
                  placeholderClassName="tableview_search_placeholder"
                  isTypeForm={true}
                />
              </th>

              <th>
                <h4 className={styles.table_headings}>
                  Client Name/Mobile Number
                </h4>
              </th>
              <th>
                <h4 className={styles.table_headings}>Start Date/Duration</h4>
              </th>
              <th>
                <h4 className={styles.table_headings}>Estimated Budget</h4>
              </th>
              <th>
                <h4 className={styles.table_headings}>Project Completed</h4>
              </th>
              <th>
                <h4 className={styles.table_headings}>Area</h4>
              </th>
              <th>
                <h4 className={styles.table_headings}>Rate</h4>
              </th>
            </tr>
          </thead>
          <tbody className={styles.table_body}>
            {(searchedData.length > 0 ? searchedData : data).map(
              (item: ProjectData, index: number) => (
                <tr key={index}>
                  <td>
                    <input type="checkbox" />
                  </td>
                  <td>
                    <div className={styles.projectdiv}>
                      <img
                        width="50px"
                        height="50px"
                        src={`${image_url}/images/${getFileName(
                          item.photo as string
                        )}`}
                        alt=""
                        className={styles.projectIcon}
                      />
                      <div className={styles.projectName}>
                        <p
                          style={{
                            color: "var(--text-black-87)",
                          }}
                        >
                          {" "}
                          {item.name}
                        </p>
                        <p
                          style={{
                            color: "var(--text-black-28)",
                          }}
                        >
                          {item.project_type}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className={styles.projectArea}>
                      <p>{item.clientName}</p>
                      <p
                        style={{
                          color: "var(--text-black-28)",
                        }}
                      >
                        {item.ClientPhoneNumber}
                      </p>
                    </div>
                  </td>
                  <td>
                    <div className={styles.projectArea}>
                      <p>
                        {((date) =>
                          `${date?.day} ${date?.monthName} ${date?.year}`)(
                          extractDateParts(item?.project_start_date)
                        )}
                      </p>
                      <p
                        style={{
                          color: "var(--text-black-28)",
                        }}
                      >
                        {item.project_duration}Months
                      </p>
                    </div>
                  </td>
                  <td>
                    <div className={styles.estimatedCost}>
                      <p>{item.estimate_budget}</p>
                      <p className={styles.estimatedcostCurrency}>
                        <p
                          style={{
                            color: "var(--text-black-28)",
                          }}
                        >
                          INR
                        </p>
                      </p>
                    </div>
                  </td>
                  <td>
                    <div className={styles.estimatedCost}>
                      <p
                        style={{
                          color: "var(--secondary_color)",
                        }}
                      >
                        {item.estimate_budget}
                      </p>
                      <p className={styles.estimatedcostCurrency}>
                        <p
                          style={{
                            color: "var(--text-black-28)",
                          }}
                        >
                          INR
                        </p>
                      </p>
                    </div>
                  </td>
                  <td>
                    <div className={styles.projectArea}>
                      <p>{item.project_area}</p>
                      <p
                        style={{
                          color: "var(--text-black-28)",
                        }}
                      >
                        {item.project_area}
                      </p>
                    </div>
                  </td>
                  <td>
                    <div className={styles.rates}>
                      <p>{item.rate}</p>
                      <p
                        style={{
                          color: "var(--text-black-28)",
                        }}
                      >
                        {item.rate_type}
                      </p>
                    </div>
                  </td>
                  {/* <td>
                  <ActionBtn />
                </td> */}
                </tr>
              )
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TableViewProjects;
