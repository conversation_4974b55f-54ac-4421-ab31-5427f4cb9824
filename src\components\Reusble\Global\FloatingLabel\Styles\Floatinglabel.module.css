/*  AUTHOR NAME : CHARVI */
.floatingLabelInput {
  position: relative;
  margin: 1.5rem 0rem 0rem 0rem;
  /* height: 57px; */
  min-height: 57px;
  /* width: 100%; */
}

.inputText {
  padding: 0.85rem 1.4rem;
  width: 100%;
  background: var(--blur-background);
  border: 1px solid var(--text-black-28);
  border-radius: 1.5rem;
  resize: none;
  overflow: hidden;
  outline: transparent;
  transition: outline 0.2s;
  line-height: 1.5rem;
  min-height: 3rem;
  min-width: 15rem;
  font-size: 1rem;
  flex-wrap: wrap;
  /* padding-inline-end: 2rem;
  white-space: nowrap; */
  
}

.search_key::placeholder {
  opacity: 1 !important;
}

.inputText.fixed_height {
  max-height: 166px !important;
  overflow-y: scroll !important;
}

.inputText.mt_confirmation_decline_reason_text_area {
  border-radius: 0.75rem;
  margin: 0;
  padding: 0;
  min-height: 6rem;
  height: auto;
}

.inputText::placeholder {
  opacity: 0;
}

.inputText.mt_confirmation_decline_reason_text_area {
  padding: 0.5rem 0.5rem;
  overflow: auto;
}

.inputText:focus {
  outline: none;
}

.floatingLabelInput > label {
  position: absolute;
  top: 45%;
  left: 1rem;
  color: var(--text-black-60);
  transform: translateY(-50%);
  cursor: text;
  transition: all 0.2s ease-out;
  
}

.floatingLabelInput > label.mt_confirmation_decline_reason_text_area {
  /* font-size: 0.6rem; */
  top: 20%;
}

.inputText:focus:not(:read-only) + label,
.inputText:not(:placeholder-shown) + label {
  padding: 0 0.5em;
  top: 0;
  left: 1.5em;
  color: var(--text-black-87);
  font-size: 0.75rem;
}

.inputText:focus:not(:read-only) + label {
  color: var(--text-black-87);
  font-size: 0.75rem;
}

.invalid {
  border: 1px solid var(--warning_color);
}

.floatinglabel_icon {
  position: absolute;
  top: 50%;
  transform: translateY(-65%);
  right: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

/*🍁. Below modifications by Abhishek raj */

.floatingLabelInput.label_cart {
  margin: 0;
  height: 1.75rem;
  font-size: 0.75rem;
  /* background-color: aquamarine; */
}

.inputText.label_cart {
  padding: 0 0.5rem;
  height: 1.75rem;
  font-size: 0.75rem;
  min-height: auto;
  min-width: auto;
}

.inputText.label_cart::placeholder {
  opacity: 0;
  font-size: 0.75rem;
}

.floatingLabelInput.label_cart > label {
  font-size: 0.6rem;
}

.inputText.label_cart:focus + label,
.inputText.label_cart:not(:placeholder-shown) + label {
  font-size: 0.75em;
}

.floatingLabelInput.trigger_event_input {
  margin: 0rem;
}

.inputText.trigger_event_input,
.inputText.trigger_event_input_time {
  border: 1px solid var(--text-black-28);
  margin: 0;
  height: auto;
  max-height: 166px !important;

}

.inputText.inputText.trigger_event_input_time.error {
  border-color: var(--warning_color);
}

.textError {
  border-color: var(--warning_color) !important;
}

.floatingLabelInput.trigger_event_input_time {
  margin: 0rem;
  margin-top: 0.75rem;
}

.trigger_event_input.inputText:focus {
  outline: none;
  box-shadow: inset 0 0 0 1px var(--text-black-28);
}

.trigger_event_input_time.inputText:focus {
  outline: none;
  box-shadow: inset 0 0 0 1px var(--text-black-28);

}


.tcr_fileNames_div {
  margin-left: 1rem;
  display: flex;
  flex-wrap: wrap;
}

.tcr_fileNames {
  background-color: var(--primary_background);
  color: var(--text-black-87);

  border-radius: 100px;

  border: 1px solid;
  border-image-source: var(--primary-bg-gradient);
  box-shadow: var(--extra-shdow-four);
}

.tcr_fileNames:hover .file_cross_div {
  display: block !important;
}

/* styles for add project form by rattandeep singh start */
.addprojectflaotinglabel {
  min-width: 11rem !important;
}

/* styles for add project form by rattandeep singh end */

.description_prop {
  height: auto;
  max-height: 166px !important;
  /* overflow: auto; */
}

.inputText.description_prop2{
  overflow: auto;
}
.description_prop2{
  height: auto;
  max-height: 166px !important;
}

.inputText.one_line {
  max-height: 53px !important;
  overflow-y: scroll !important;
  /* overflow: auto; */
}


