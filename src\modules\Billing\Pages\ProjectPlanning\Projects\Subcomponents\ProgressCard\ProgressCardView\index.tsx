import ProgressCard from "..";
import { Loader } from "../../../../../../../../assets/loader";
import { ProjectData } from "../../../AddProjectForm/Interfaces/interface";
import styles from "../../../Styles/AddNewProject.module.css";
import React, { useEffect, useState } from "react";

interface ProgressCardViewProps {
  projectData: ProjectData[];
  searchedData: ProjectData[];
}

const ProgressCardView: React.FC<ProgressCardViewProps> = ({ projectData, searchedData }) => {
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setIsLoading(projectData.length === 0);
  }, [projectData]);

  return (
    <div className={styles.add_new_project_cards}>
      {isLoading ? (
        <div className={styles.loader_loading}>
          <img
            src={Loader.suryaconLogo}
            alt="Loading..."
            className={styles.loader_loading_image}
          />
        </div>
      ) : (
        (searchedData.length > 0 ? searchedData : projectData).map((project: ProjectData) => (
          <ProgressCard key={project._id || project.id} {...project} />
        ))
      )}
    </div>
  );
};

export default ProgressCardView;
