import React, { <PERSON> } from "react";
import styles from "../Styles/Materials.module.css";
import {
  isValidValue,
  slicedData,
} from "../../../../../../functions/functions";

interface MaterialDiscardProps {
  formData: any;
  initialFormData?: any;
  formMode?: any;
  //   deletedGradeData: Array<Record<number, any[]>>;
  //   deletedFormData: any;
}

const MaterialDiscard: FC<MaterialDiscardProps> = ({
  formData,
  initialFormData,
  formMode,
  //   deletedGradeData,
  //   deletedFormData,
}) => {
  console.log("formData", formData);
  return (
    <div style={{ display: "flex", flexDirection:"column"  }}>
      <div style={{ display: "flex", flexWrap: "wrap" }}>
        {formData?.Name && isValidValue(formData?.Name) && (
          <div
            className={styles.summaryDivData}
            style={{ width: formData?.Photo?.name ? "50%" : "100%" }}
          >
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Name
              </p>
              <h4
                style={{
                  color: "var(--text-black-87)",
                  marginTop: "0.3rem",
                }}
              >
                {formData?.Name}
              </h4>
            </div>
          </div>
        )}
        {formData?.Photo?.name && isValidValue(formData?.Photo?.name) && (
          <div
            className={styles.summaryDivData}
            style={{ width: formData?.Name ? "50%" : "100%" }}
          >
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Cover Photo
              </p>
              <h4
                style={{
                  color: "var(--text-black-87)",
                  marginTop: "0.3rem",
                }}
              >
                {slicedData(formData?.Photo?.name, 14)}
              </h4>
            </div>
          </div>
        )}
      </div>
      {formData?.Description && isValidValue(formData?.Description) && (
        <div className={styles.summaryDivData}>
          <div className={styles.summaryDataContent}>
            <div>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Description
              </p>
              <h4
                style={{
                  color: "var(--text-black-87)",
                  marginTop: "0.3rem",
                }}
              >
                {formData?.Description}
              </h4>
            </div>
          </div>
        </div>
      )}
      {formData?.Unit && formData?.Unit?.length > 0 && (
        <>
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                {formData?.Unit?.length > 1 ? "Units" : "Unit"}
              </p>
              <div
                style={{
                  color: "var(--text-black-87)",
                  display: "flex",
                  marginTop: "0.3rem",
                  gap: "0.3rem 2rem",
                  flexWrap: "wrap",
                }}
              >
                {formData?.Unit?.map((item: any) => (
                  <h4>{item?.name}</h4>
                ))}
              </div>
            </div>
          </div>
        </>
      )}
      {formData?.Brands &&
        formData?.Brands?.length > 0 &&
        formData?.Brands?.[0]?.brand?.name && (
          <>
            <h4 style={{ margin: "0.6rem" }}>Brand</h4>
            {formData?.Brands?.map((item: any) => (
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Name
                  </p>
                  <h4
                    style={{
                      color: "var(--text-black-87)",
                      marginTop: "0.3rem",
                    }}
                  >
                    {item?.brand?.name}
                  </h4>
                  {formData?.Brands?.[0]?.Grade &&
                    formData?.Brands?.[0]?.Grade.length > 0 &&
                    formData?.Brands?.[0]?.Grade?.[0] && (
                      <>
                        <p
                          style={{
                            color: "var(--text-black-60)",
                            marginTop: "0.3rem",
                          }}
                          className="p_tag_14px"
                        >
                          Grades
                        </p>
                        <div
                          style={{
                            color: "var(--text-black-87)",
                            display: "flex",
                            marginTop: "0.3rem",
                            gap: "0.3rem 2rem",
                            flexWrap: "wrap",
                          }}
                        >
                          {item?.Grade?.map((item: any) => (
                            <h4>{item}</h4>
                          ))}
                        </div>
                      </>
                    )}
                  {formData?.Brands?.[0]?.ConversionRates &&
                    formData?.Brands?.[0]?.ConversionRates?.length > 0 &&
                    formData?.Brands?.[0]?.ConversionRates?.[0] &&
                    item?.ConversionRates?.map((item: any, index: number) => (
                      <div>
                        <p
                          style={{
                            color: "var(--text-black-60)",
                            marginTop: "0.3rem",
                          }}
                          className="p_tag_14px"
                        >
                          {formData?.Brands?.[0]?.ConversionRates?.length == 1
                            ? `Conversion Rate`
                            : `Conversion Rate ${index + 1}`}
                        </p>
                        <div
                          style={{
                            color: "var(--text-black-87)",
                            marginTop: "0.3rem",
                            gap: "0.3rem 2rem",
                          }}
                        >
                          <h4>{item?.fromUnit}</h4>
                          <h4>{item?.rate}</h4>
                          <h4>{item?.toUnit}</h4>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            ))}
          </>
        )}
    </div>
  );
};

export default MaterialDiscard;
