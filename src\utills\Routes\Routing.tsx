import { Route, Routes } from "react-router-dom";
import { useState } from "react";
import Layout from "../../layout/MainLayout";
import SubTaskCreationForm from "../../modules/TaskMaster/Pages/SubTaskForm";
import Category from "../../modules/TaskMaster/Pages/Category";
import CategoryCardView from "../../modules/TaskMaster/Pages/Category/Subcomponents/CategoryCardView/CategoryCardView";

import TaskCreation from "../../modules/TaskMaster/Pages/TaskForm";
import TaskDetails from "../../modules/TaskMaster/Pages/TaskForm/Subcomponents/TaskDetails/TaskDetails";
import TaskCardView from "../../modules/TaskMaster/Pages/Tasks/Subcomponents/TaskCardView/TaskCardView";
import BillingApproval from "../../modules/Billing/Pages/BillingApproval";
import ProjectPlanning from "../../modules/Billing/Pages/ProjectPlanning";
import Project from "../../modules/Billing/Pages/ProjectPlanning/Projects";
import Planning from "../../modules/Billing/Pages/ProjectPlanning/Planning";
import BillingMainPage from "../../modules/Billing/Pages/MainPage/index";
import Location from "../../modules/Billing/Pages/ProjectPlanning/Locations";
import PlanningTable from "../../modules/Billing/Pages/ProjectPlanning/Planning/PlanningTable";
import MaterialsTable from "../../modules/Billing/Pages/ProjectPlanning/Planning/MaterialsTable";
import AuthLayout from "../../layout/AuthLayout";
import Login from "../../modules/Auth/Login";
import OtpVerification from "../../modules/Auth/OtpVerfication";

import Tools from "../../modules/Billing/Pages/Masters/Tools";
import Materials from "../../modules/Billing/Pages/Masters/Materials";
import Machinery from "../../modules/Billing/Pages/Masters/Machinery";
import Manpower from "../../modules/Billing/Pages/Masters/Manpower";
import MachineryPage from "../../modules/Billing/Pages/Masters/Machinery/Subcomponents/MachineryPage";
import MachineryCategory from "../../modules/Billing/Pages/Masters/Machinery/Subcomponents/MachineryCategory";
import ManpowerPage from "../../modules/Billing/Pages/Masters/Manpower/Subcomponents/ManpowerPage";
import ManpowerCategory from "../../modules/Billing/Pages/Masters/Manpower/Subcomponents/ManpowerCategory";
import MaterialsPage from "../../modules/Billing/Pages/Masters/Materials/Subcomponents/MaterialsPage";
import MaterialsCategory from "../../modules/Billing/Pages/Masters/Materials/Subcomponents/MaterialsCategory";
import ToolsCategory from "../../modules/Billing/Pages/Masters/Tools/Subcomponents/ToolsCategory";
import ToolsPage from "../../modules/Billing/Pages/Masters/Tools/Subcomponents/ToolsPage";
import ProtectedRoute from "../ProtectedRoute";
import UnderConstruction from "../../components/Reusble/UnderConstruction";
import UnderConstruction2 from "../../components/Reusble/UnderConstruction/UcForBillingPage";
import VersionHistory from "../../modules/VersionHistory";

import Department from "../../modules/Billing/Pages/Masters/Department";
import DesignationPage from "../../modules/Billing/Pages/Masters/Department/Subcomponents/DesignationPage";
import DepartmentPage from "../../modules/Billing/Pages/Masters/Department/Subcomponents/DepartmentPage";

const Routing: React.FC = () => {
  // const handleClick = () => {};
  // const [comment, setComment] = useState("");

  return (
    <Routes>
      <Route>
        <Route path="/" element={<Layout />}>
          <Route path="*" element={<UnderConstruction />} />

          <Route path="/subtask" element={<SubTaskCreationForm />} />

          <Route path="/category" element={<Category />}>
            <Route index element={<CategoryCardView />} />
            <Route path=":catId" element={<TaskCardView />} />
          </Route>

          <Route
            path="/category/:catId/task/:taskId"
            element={<TaskCreation />}
          >
            <Route index element={<TaskDetails />}></Route>
            <Route
              path="subtask/:subtaskId"
              element={<SubTaskCreationForm />}
            ></Route>
          </Route>

          <Route path="/billing" element={<ProjectPlanning />}>
            <Route index element={<Project />} />
            <Route path="main/:projectId" element={<BillingMainPage />}>
              <Route index element={<UnderConstruction2 />} />
              <Route path="summary" element={<UnderConstruction2 />} />
              <Route path="location" element={<Location />} />
              <Route path="location/:towerLocationId" element={<Planning />}>
                <Route index element={<PlanningTable />}></Route>
                <Route path="materials" element={<MaterialsTable />}></Route>
              </Route>
              <Route path="monthly-target" element={<BillingApproval />} />
              <Route path="actual-work" element={<UnderConstruction2 />} />
              <Route path="inventory" element={ <UnderConstruction2 />} />
              <Route path="petty-contractors" element={<UnderConstruction2 />} />
               <Route path="site-billing" element={<UnderConstruction2 />} />
              <Route path="settings" element={<UnderConstruction2 />} />
              <Route path="support" element={<UnderConstruction2 />} />
            </Route>
          </Route>

          <Route path="/tools-master" element={<Tools />}>
            <Route index element={<ToolsCategory />} />
            <Route path=":toolsCategoryId" element={<ToolsPage />} />
          </Route>
          <Route path="/materials-master" element={<Materials />}>
            <Route index element={<MaterialsCategory />} />
            <Route path=":materialsCategoryId" element={<MaterialsPage />} />
          </Route>
          <Route path="/machinery-master" element={<Machinery />}>
            <Route index element={<MachineryCategory />} />
            <Route path=":machineryCategoryId" element={<MachineryPage />} />
          </Route>
          <Route path="/manpower-master" element={<Manpower />}>
            <Route index element={<ManpowerCategory />} />
            <Route path=":manpowerCategoryId" element={<ManpowerPage />} />
          </Route>

          <Route path="/version" element={<VersionHistory />} />

          <Route path="/department" element={<Department/>}>
            <Route index element={<DepartmentPage/>} />
            <Route path="deleted" element={<DepartmentPage/>} />
            <Route path=":departmentId" element={<DesignationPage />} />
              <Route path=":departmentId/deleted" element={<DesignationPage />} />
            <Route/>
          </Route>
        </Route>
      </Route>
      <Route path="/auth" element={<AuthLayout />}>
        <Route index element={<Login />} />
        <Route path="otp-verification" element={<OtpVerification />} />
      </Route>
    </Routes>
  );
};

export default Routing;
