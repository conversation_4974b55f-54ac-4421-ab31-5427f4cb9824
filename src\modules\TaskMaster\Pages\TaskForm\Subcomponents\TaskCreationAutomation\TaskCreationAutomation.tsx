import React, { useCallback, useState } from "react";
import styles from "../Styles/TaskCreationForm.module.css"
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../../../redux/store";
import { closePopup, openPopup } from "../../../../../../redux/features/Modules/Reusble/popupSlice";
import { SuryconLogo } from "../../../../../../assets/icons";
import AddToolTip from "../../../../../../components/Reusble/Global/AddToolTip";
import AddCategoryType from "../../../../../../components/Reusble/Global/AddCategoryType";
import { AddData, taskBuildingBlocks, TaskDataType } from "../../../../../../interfaces/Modules/TaskMaster/TaskMasterInterface/TaskMaster";




const TaskCreationAutomation: React.FC = () => {
  const [addData, setAddData] = useState<AddData>({
    Trigger: [],
    Action: [],
    ResponseTime: [],
  });

  const handleSelect = useCallback(
    (categoryName: keyof AddData, selectedItems: TaskDataType[]) => {
      setAddData((prevData) => {
        const newCategories = selectedItems
          .map((item) => item.category)
          .filter((category) => !prevData[categoryName].includes(category));

        if (newCategories.length > 0) {
          return {
            ...prevData,
            [categoryName]: [...prevData[categoryName], ...newCategories],
          };
        }
        return prevData;
      });
    },
    []
  );

  const sampleData: taskBuildingBlocks = {
    Trigger: [{ id: "1", category: "setting layout" }],
    Action: [
      { id: "1", category: "Accounts" },
      { id: "2", category: "Sales" },
      { id: "3", category: "HR" },
      { id: "4", category: "IT" },
    ],
    ResponseTime: [
      { id: "1", category: "Accounts" },
      { id: "2", category: "Sales" },
      { id: "3", category: "HR" },
      { id: "4", category: "IT" },
    ],
  };

  const dispatch = useDispatch();
  const { popups } = useSelector((state: RootState) => state.popup);

  const handleToggleDropdown = (name: string) => {
    dispatch(openPopup(name));
  };

  const categoryConfig = {
    Trigger: {
      label: "Trigger Response",
      title: "Trigger",
    },
    Action: {
      label: "Action",
      title: "Action",
    },
    ResponseTime: {
      label: "Response Time",
      title: "Response Time",
    },
  } as const;

  return (
    <div className={styles.task_creation_automation_container}>
      <div className={`${styles.task_creation_designation_header}`}>
        <SuryconLogo/>
        <h3>Trigger Event</h3>
      </div>
      <div className={styles.task_creation_master_row}>
        {(
          Object.keys(categoryConfig) as Array<keyof typeof categoryConfig>
        ).map((category) => (
          <div
            key={category}
            style={{ minWidth: "50px", position: "relative" }}
          >
            <AddToolTip
              label={categoryConfig[category].label}
              onClick={() => handleToggleDropdown(category)}
              className={`${styles.subtask_creation_add_tooltip} ${styles.three_cols}`}
              data={addData[category]}
            />
            {popups[category] && (
              <AddCategoryType
                title={categoryConfig[category].title}
                data={sampleData[category]}
                placeholder="Select"
                buttonLabel="Add Category"
                onSelect={(items) => handleSelect(category, items)}
                onClose={() => dispatch(closePopup(category))}
                initialSelected={addData[category]}

              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default TaskCreationAutomation;;
