import React, { FC } from "react";
import {
  isValidValue,
  slicedData,
} from "../../../../../../functions/functions";
import styles from "../Styles/Materials.module.css";

interface MaterialSummaryProps {
  formData: any;
  initialFormData: any;
  formMode: any;
  deletedGradeData: Array<Record<number, any[]>>;
  deletedFormData: any;
  deletedUnitData?: any;
  deletedUnitArray?: any;
}

const MaterialSummary: FC<MaterialSummaryProps> = ({
  formData,
  initialFormData,
  formMode,
  deletedGradeData,
  deletedFormData,
  deletedUnitData,
  deletedUnitArray,
}) => {
  return (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <div style={{ display: "flex", flexWrap: "wrap" }}>
        {formData?.Name && isValidValue(formData?.Name) && (
          <div
            className={styles.summaryDivData}
            style={{ width: formData?.Photo?.name ? "50%" : "100%" }}
          >
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Name
              </p>
              <h4
                style={{
                  color:
                    formMode === "Edit" &&
                    formData?.Name?.trim() !== initialFormData?.Name?.trim()
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                  marginTop: "0.3rem",
                }}
              >
                {formData?.Name}
              </h4>
            </div>
          </div>
        )}
        {formData?.Photo?.name && isValidValue(formData?.Photo?.name) && (
          <div
            className={styles.summaryDivData}
            style={{ width: formData?.Name ? "50%" : "100%" }}
          >
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                Cover Photo
              </p>
              <h4
                style={{
                  color:
                    formMode === "Edit" &&
                    formData?.Photo?.name?.trim() !==
                      initialFormData?.Photo?.name?.trim()
                      ? "var(--secondary_color)"
                      : "var(--text-black-87)",
                  marginTop: "0.3rem",
                }}
              >
                {slicedData(formData?.Photo?.name, 14)}
              </h4>
            </div>
          </div>
        )}
      </div>
      {(formData?.Description || initialFormData?.Description?.trim()) &&
        isValidValue(formData?.Description || initialFormData?.Description) && (
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <div>
                <p
                  style={{ color: "var(--text-black-60)" }}
                  className="p_tag_14px"
                >
                  Description
                </p>
                <h4
                  style={{
                    color:
                      formMode === "Edit" &&
                      formData?.Description?.trim() !==
                        initialFormData?.Description?.trim()
                        ? "var(--secondary_color)"
                        : "var(--text-black-87)",
                    marginTop: "0.3rem",
                  }}
                >
                  {formData?.Description}
                </h4>
                {formMode === "Edit" &&
                  !formData?.Description?.trim() &&
                  initialFormData?.Description?.trim() && (
                    <h4
                      style={{
                        color: "var(--warning_color)",
                        marginTop: "0.3rem",
                      }}
                    >
                      {initialFormData?.Description}
                    </h4>
                  )}
              </div>
            </div>
          </div>
        )}
      {formData?.Unit && formData?.Unit?.length > 0 && (
        <>
          <div className={styles.summaryDivData}>
            <div className={styles.summaryDataContent}>
              <p
                style={{ color: "var(--text-black-60)" }}
                className="p_tag_14px"
              >
                {formData?.Unit?.length > 1 ? "Units" : "Unit"}
              </p>
              <div
                style={{
                  color: "var(--text-black-87)",
                  display: "flex",
                  marginTop: "0.3rem",
                  gap: "0.3rem 2rem",
                  flexWrap: "wrap",
                }}
              >
                {formData?.Unit?.map((item: any, index: number) => (
                  <h4
                    style={{
                      color:
                        formMode === "Edit" &&
                        !initialFormData?.Unit?.find(
                          (unit: any) => unit?.name === item?.name
                        )?.name?.trim()
                          ? "var(--secondary_color)"
                          : "var(--text-black-87)",
                    }}
                  >
                    {item?.name}
                  </h4>
                ))}
                {deletedUnitArray?.map((item: any, index: number) => (
                  <h4
                    style={{
                      color: formMode === "Edit" ? "var(--warning_color)" : "",
                    }}
                  >
                    {item?.name}
                  </h4>
                ))}
              </div>
            </div>
          </div>
        </>
      )}
      {formData?.Brands &&
        formData?.Brands?.length > 0 &&
        formData?.Brands?.[0]?.brand?.name?.trim() && (
          <>
            <h4 style={{ margin: "0.6rem" }}>Brand</h4>
            {formData?.Brands?.map((brandItem: any, index: number) => (
              <div className={styles.summaryDivData}>
                <div
                  className={`${
                    brandItem?.brand?.name || brandItem?.Grade?.length
                      ? styles.summaryDataContent
                      : ""
                  }`}
                >
                  {brandItem?.brand?.name?.trim() && (
                    <>
                      <p
                        style={{ color: "var(--text-black-60)" }}
                        className="p_tag_14px"
                      >
                        Name
                      </p>
                      <h4
                        style={{
                          color:
                            formMode === "Edit" &&
                            formData?.Brands?.find(
                              (i: any) => i?._id === brandItem?._id
                            )?.brand?.name?.trim() !==
                              initialFormData?.Brands?.find(
                                (i: any) => i?._id === brandItem?._id
                              )?.brand?.name?.trim()
                              ? "var(--secondary_color)"
                              : "var(--text-black-87)",
                          marginTop: "0.3rem",
                        }}
                      >
                        {brandItem?.brand?.name}
                      </h4>{" "}
                    </>
                  )}
                  {brandItem?.Grade?.length > 0 && (
                    <p
                      style={{
                        color: "var(--text-black-60)",
                        marginTop: "0.3rem",
                      }}
                      className="p_tag_14px"
                    >
                      Grades
                    </p>
                  )}
                  <div
                    style={{
                      color: "var(--text-black-87)",
                      display: "flex",
                      marginTop: "0.3rem",
                      gap: "0.3rem 2rem",
                      flexWrap: "wrap",
                    }}
                  >
                    {brandItem?.Grade?.map((gradeItem: any, i: number) => (
                      <h4
                        style={{
                          color:
                            formMode === "Edit" &&
                            !initialFormData?.Brands?.find(
                              (brandItems: any) =>
                                brandItems?._id === brandItem?._id
                            )?.Grade?.includes(gradeItem)
                              ? "var(--secondary_color)"
                              : "var(--text-black-87)",
                        }}
                      >
                        {gradeItem}
                      </h4>
                    ))}
                    {deletedGradeData &&
                      deletedGradeData
                        ?.filter(
                          (gradeItem: any, i: any) => gradeItem[brandItem?._id!]
                        )?.[0]
                        ?.[brandItem?._id!]?.map((grade: any, i: any) => (
                          <h4
                            style={{
                              color: "var(--warning_color)",
                            }}
                          >
                            {!formData?.Brands?.find(
                              (i: any) => i?._id === brandItem?._id
                            ).Grade.includes(grade) && grade}
                          </h4>
                        ))}
                  </div>
                  {formData?.Brands?.[index]?.ConversionRates &&
                    formData?.Brands?.[index]?.ConversionRates?.length > 0 &&
                    formData?.Brands?.[index]?.ConversionRates?.[0] &&
                    brandItem?.ConversionRates?.map((item: any, i: number) => (
                      <div>
                        <p
                          style={{
                            color: "var(--text-black-60)",
                            marginTop: "0.3rem",
                          }}
                          className="p_tag_14px"
                        >
                          {formData?.Brands?.[index]?.ConversionRates?.length &&
                          formData?.Brands?.[index]?.ConversionRates?.length ==
                            1 &&
                          deletedUnitData?.length === 0
                            ? `Conversion Rate`
                            : `Conversion Rate ${i + 1}`}
                        </p>
                        <div
                          style={{
                            color: "var(--text-black-87)",
                            marginTop: "0.3rem",
                            gap: "0.3rem 2rem",
                          }}
                        >
                          <h4
                            style={{
                              color:
                                formMode === "Edit" &&
                                formData?.Brands?.find(
                                  (brand: any) => brand?._id === brandItem?._id
                                )?.ConversionRates?.find(
                                  (rate: any) => rate._id == item._id
                                )?.fromUnit !==
                                  initialFormData?.Brands?.find(
                                    (brand: any) =>
                                      brand?._id === brandItem?._id
                                  )?.ConversionRates?.find(
                                    (rate: any) => rate._id == item._id
                                  )?.fromUnit
                                  ? "var(--secondary_color)"
                                  : "var(--text-black-87)",
                            }}
                          >
                            {item?.fromUnit}
                          </h4>
                          <h4
                            style={{
                              color:
                                formMode === "Edit" &&
                                formData?.Brands?.find(
                                  (brand: any) => brand?._id === brandItem?._id
                                )?.ConversionRates?.find(
                                  (rate: any) => rate._id == item._id
                                )?.rate !==
                                  initialFormData?.Brands?.find(
                                    (brand: any) =>
                                      brand?._id === brandItem?._id
                                  )?.ConversionRates?.find(
                                    (rate: any) => rate._id == item._id
                                  )?.rate
                                  ? "var(--secondary_color)"
                                  : "var(--text-black-87)",
                            }}
                          >
                            {item?.rate}
                          </h4>
                          <h4
                            style={{
                              color:
                                formMode === "Edit" &&
                                formData?.Brands?.find(
                                  (brand: any) => brand?._id === brandItem?._id
                                )?.ConversionRates?.find(
                                  (rate: any) => rate._id == item._id
                                )?.toUnit !==
                                  initialFormData?.Brands?.find(
                                    (brand: any) =>
                                      brand?._id === brandItem?._id
                                  )?.ConversionRates?.find(
                                    (rate: any) => rate._id == item._id
                                  )?.toUnit
                                  ? "var(--secondary_color)"
                                  : "var(--text-black-87)",
                            }}
                          >
                            {item?.toUnit}
                          </h4>
                        </div>
                      </div>
                    ))}
                  {deletedUnitData?.map((entry: any, unitIndex: any) => {
                    // const brandId = Object.keys(entry)[0];
                    const deletedRates = entry[brandItem?._id];
                    return (
                      <>
                        {deletedRates &&
                          deletedRates?.map((rate: any, i: any) => (
                            <>
                              <p
                                style={{
                                  color: "var(--text-black-60)",
                                  marginTop: "0.3rem",
                                }}
                                className="p_tag_14px"
                              >
                                {deletedRates?.length &&
                                deletedRates?.length === 1 &&
                                formData?.Brands?.[index]?.ConversionRates
                                  ?.length === 0
                                  ? `Conversion Rate`
                                  : `Conversion Rate ${
                                      formData?.Brands?.[index]?.ConversionRates
                                        ?.length +
                                      i +
                                      1
                                    }`}
                              </p>
                              <div
                                style={{
                                  color: "var(--warning_color)",
                                  marginTop: "0.3rem",
                                  gap: "0.3rem 2rem",
                                }}
                              >
                                <h4
                                  style={{
                                    color: "var(--warning_color)",
                                  }}
                                >
                                  {rate?.fromUnit}
                                </h4>
                                <h4
                                  style={{
                                    color: "var(--warning_color)",
                                  }}
                                >
                                  {rate?.rate}
                                </h4>
                                <h4
                                  style={{
                                    color: "var(--warning_color)",
                                  }}
                                >
                                  {rate?.toUnit}
                                </h4>
                              </div>
                            </>
                          ))}
                      </>
                    );
                  })}
                </div>
              </div>
            ))}
            {deletedFormData?.map((item: any, ind: any) => (
              <div className={styles.summaryDivData}>
                <div className={styles.summaryDataContent}>
                  <p
                    style={{ color: "var(--text-black-60)" }}
                    className="p_tag_14px"
                  >
                    Name
                  </p>
                  <h4
                    style={{
                      color: "var(--warning_color)",
                      marginTop: "0.3rem",
                    }}
                  >
                    {item?.brand?.name}
                  </h4>
                  {formData?.Brands?.[0]?.Grade &&
                    formData?.Brands?.[0]?.Grade?.length > 0 &&
                    formData?.Brands?.[0]?.Grade?.[0]?.trim() && (
                      <>
                        {" "}
                        <p
                          style={{
                            color: "var(--text-black-60)",
                            marginTop: "0.3rem",
                          }}
                          className="p_tag_14px"
                        >
                          Grades
                        </p>
                        <div
                          style={{
                            display: "flex",
                            marginTop: "0.3rem",
                            gap: "0.3rem 2rem",
                            flexWrap: "wrap",
                          }}
                        >
                          {item?.Grade?.map((item: any, i: any) => (
                            <h4
                              style={{
                                color: "var(--warning_color)",
                              }}
                            >
                              {item}
                            </h4>
                          ))}
                        </div>
                      </>
                    )}
                  {deletedUnitData?.map((entry: any, index: any) => {
                    const deletedRates = entry[item?._id];
                    return (
                      <>
                        {deletedRates &&
                          deletedRates?.map((rate: any, i: any) => (
                            <>
                              <p
                                style={{
                                  color: "var(--text-black-60)",
                                  marginTop: "0.3rem",
                                }}
                                className="p_tag_14px"
                              >
                                {deletedRates?.length === 0
                                  ? `Conversion Rate`
                                  : `Conversion Rate ${i+1}`}
                              </p>
                              <div
                                style={{
                                  color: "var(--warning_color)",
                                  marginTop: "0.3rem",
                                  gap: "0.3rem 2rem",
                                }}
                              >
                                <h4
                                  style={{
                                    color: "var(--warning_color)",
                                  }}
                                >
                                  {rate?.fromUnit}
                                </h4>
                                <h4
                                  style={{
                                    color: "var(--warning_color)",
                                  }}
                                >
                                  {rate?.rate}
                                </h4>
                                <h4
                                  style={{
                                    color: "var(--warning_color)",
                                  }}
                                >
                                  {rate?.toUnit}
                                </h4>
                              </div>
                            </>
                          ))}
                      </>
                    );
                  })}
                </div>
              </div>
            ))}
          </>
        )}
    </div>
  );
};

export default MaterialSummary;
