import { isDeleted } from "./../../redux/Interfaces/Modules/Reuseable/Reuseable.d";
import PQueue from "p-queue";

import { AppDispatch } from "../../redux/store";
import { saveSyncData, saveSyncTime } from "../BackupFunctions/BackupFunctions";
import {
  getTime,
  initializeDatabase,
  projectFilterImages,
} from "../../functions/functions";

import { ProjectData } from "../../modules/Billing/Pages/ProjectPlanning/Projects/AddProjectForm/Interfaces/interface";

import { BillingMasterApi } from "../../redux/api/Modules/Billing/ProjectPlanningApi";
import { BillingApi } from "../../redux/api/Modules/Billing/Billingapi";
import {
  deleteSubtaskInPlanning,
  deleteTowerRoutes,
  setSubtaskBasicDetails,
  setSubtaskInPlanning,
} from "../../redux/features/Modules/Billing/ProjectPlanning/Slices/projectPlanningSlice";
import { addOrUpdateTowerRoutes } from "../../redux/features/Modules/Billing/ProjectPlanning/Slices/projectPlanningSlice";

export const project = async (
  dispatch: AppDispatch,
  categoryIds: string[]
): Promise<void> => {
  const date = await getTime("project");

  let time = "2025-06-04T06:03:20.030Z";
  if (date) {
    time = date.date;
  }

  console.log(time, "this is tiem after fetching from localdb");
  const result = await dispatch(
    BillingApi.endpoints.getAllProject.initiate(time as string, {
      forceRefetch: true,
    }) as any
  );
  console.log(result?.data?.data?.response, "project function called");

  if (result?.data?.data?.response?.projectDetails?.length > 0) {
    const getLocalDbData = async () => {
      try {
        const dbName = await initializeDatabase("projects");
        const fetchedData = await window.electron.allbulkGet({
          dbName,
        });

        return fetchedData?.docs;
      } catch (error) {
        console.error("Error fetching project data", error);
      }
    };

    const localDbData = await getLocalDbData();
    console.log(localDbData, "getLocalDbData");
    const newdata: ProjectData[] = result?.data?.data.response?.projectDetails;
    const FilterimagesResult = await projectFilterImages({
      newdata,
      localDbData,
      dispatch,
    });
    console.log(FilterimagesResult, "FilterimagesResult");

    saveSyncTime(result.data.data.date, "project");
    var datasaved = await saveSyncData(
      result?.data?.data?.response?.projectDetails.map((el: any) => {
        return {
          ...el,
          lowercase_name: el.name ? el.name.toLowerCase() : "",
        };
      }),
      time,
      "projects",
      false,
      dispatch
    );
    const counts = [
      {
        OngoingProjects: result?.data?.data?.response?.onGoingCount,
        _id: "OngoingProjects",
      },
      {
        completedProjects: result?.data?.data?.response?.completedCount,
        _id: "completedProjects",
      },
    ];

    console.log("project countsss>>", counts);
    for (const count of counts) {
      await saveSyncData(count, time, "counts");
    }
    console.log(datasaved, "datasaved");

    return result?.data.data;
  }
};

export const Towerlocations = async (
  dispatch: AppDispatch,
  towerlocationIds: string[],
  projectIds: string[]
): Promise<any[]> => {
  const TowerLocationqueue = new PQueue({ concurrency: 1 });
  const allResults: any[] = [];

  if (projectIds.length === 0) return [];

  for (const projectId of projectIds) {
    TowerLocationqueue.add(async () => {
      try {
        const { date: lastSyncTime = "2025-06-04T06:03:20.030Z" } =
          (await getTime("Towerlocations")) || {};
        const result = await dispatch(
          BillingApi.endpoints.getTowerlocationByProjectId.initiate(
            { projectId, time: lastSyncTime, category: "", isDeleted: false },
            { forceRefetch: true }
          )
        );
        console.log(result,"tower ka data backend se")

        console.log("resultttttt", result.data?.data.response);
        const towerDetails = result.data?.data?.response?.towerDetail || [];
        if (towerDetails.length > 0) {
          console.log("resultttttt123123");
          const syncTime = result.data?.data?.date;
          if (syncTime) saveSyncTime(syncTime, "Towerlocations");

          await saveSyncData(
            towerDetails.map((el) => ({
              ...el,
              lowercase_name: el.name?.toLowerCase() || "",
            })),
            "",
            "Towerlocations",
            false,
            dispatch
          );

          const countsTN = [
            {
              nonDeletedTowerCount:
                result.data.data.response.nonDeletedTowerCount,
              nonDeletedNonTowerCount:
                result.data.data.response.nonDeletedNonTowerCount,
              _id: projectId,
              projectId,
              timestamp: lastSyncTime,
            },
          ];

          for (const count of countsTN)
            await saveSyncData(count, lastSyncTime, "countsTN");

          allResults.push(...towerDetails);
        }
      } catch (error) {
        console.error("Error processing tower locations backup", error);
      }
    });
  }

  await TowerLocationqueue.onIdle();
  const TowerlocationsIds = allResults.map((item) => item._id);

  // await new Promise((resolve) => setTimeout(resolve, 5000));
  const TowerRoutesResult = await TowerRoutes(dispatch, TowerlocationsIds);

  const taskResults: any[] = [];
  const TasksQueue = new PQueue({ concurrency: 1 });
  for (const tower of TowerRoutesResult) {
    TasksQueue.add(async () => {
      const taskId = tower._id;
      const dbName = await initializeDatabase("TaskBasicDetails");
      const result = await dispatch(
        BillingMasterApi.endpoints.getTaskBasicDetailByTowerId.initiate(
          { locationId: tower.Tower_id, taskId },
          { forceRefetch: true }
        )
      );

      const taskData = {
        ...result.data?.data,
        _id: `${result.data?.data?.taskid}-${result.data?.data?._id}`,
      };

      await saveSyncData(taskData, "", "TaskBasicDetails", false, dispatch);
      await saveSyncData(
        { ...tower, progressloading: 25 },
        "",
        "TowerRoutes",
        false,
        dispatch
      );

      if (result.data) taskResults.push(result.data.data);
    });
  }
  await TasksQueue.onIdle();

  const SubTaskQueue = new PQueue({ concurrency: 1 });

  for (const task of TowerRoutesResult) {
    let date = await getTime(`SubTasksBasicDetails-${task._id}`);
    let time = "2025-06-04T06:03:20.030Z";
    if (date) {
      time = date.date;
    }
    SubTaskQueue.add(async () => {
      const result = await dispatch(
        BillingMasterApi.endpoints.getAllSubtaskForTaskRouteForPlanning.initiate(
          { locationId: task.Tower_id, taskId: task._id, time },
          { forceRefetch: true }
        )
      );
      console.log(result, "aman sir da daddddddd");
      const finalData = result?.data?.data?.response?.finalData;
      const deletedSubtasks = result?.data?.data?.response?.deletedSubasks;
      saveSyncTime(result?.data?.date, "project");
      console.log(result, "this is result from subtaskbasicdetails");
      saveSyncTime(
        result?.data?.data?.date,
        `SubTasksBasicDetails-${task._id}`
      );
      if (finalData?.length > 0) {
        await saveSyncData(
          finalData.map((e) => ({ ...e, _id: `${e?._id}` })),
          "",
          "SubTasksBasicDetails",
          false,
          dispatch
        );
        await saveSyncData(
          { ...task, progressloading: 50 },
          "",
          "TowerRoutes",
          false,
          dispatch
        );

        const SubtaskIds = finalData.map((item) => item._id);
        await SubtasklocDetail(dispatch, SubtaskIds, {
          ...task,
          progressloading: 50,
        });
      } else {
        await saveSyncData(
          { ...task, progressloading: 100 },
          "",
          "TowerRoutes",
          false,
          dispatch
        );
      }
      if (Array.isArray(deletedSubtasks) && deletedSubtasks.length > 0) {
        for (const delId of deletedSubtasks) {
          try {
            await window.electron.deletedocbyid({
              dbName: "SubtasklocDetail",
              idname: "_id",
              _id: delId,
            });
            await window.electron.deletedocbyid({
              dbName: "SubTasksBasicDetails",
              idname: "_id",
              _id: delId,
            });
            for (const delId of deletedSubtasks) {
              await updateMaterialTable(delId, dispatch);
            }

            console.log(
              `Deleted subtask with id: ${delId} and updated materialtable`
            );
          } catch (err) {
            console.error(`Error deleting subtask with id: ${delId}`, err);
          }
        }
      }
      dispatch(
        setSubtaskInPlanning({
          subtask: finalData.map((e: any) => ({
            ...e,
            uniqueId: `${task.Tower_id}-${task._id}`,
            _id: `${e._id}`,
          })),
          deletedSubtasks: deletedSubtasks || [],
        })
      );
    });
  }
  await SubTaskQueue.onIdle();

  return { Locations: allResults, TaskBasicDetails: taskResults };
};

export const TowerRoutes = async (
  dispatch: AppDispatch,
  TowerIDs: string[]
): Promise<any[]> => {
  const taskQueue = new PQueue({ concurrency: 1 });
  const allResults: any[] = [];

  if (TowerIDs.length === 0) return [];

  for (const towerId of TowerIDs) {
    taskQueue.add(async () => {
      try {
        const { date: lastSyncTime = "" } =
          (await getTime(`TowerRoutes-${towerId}`)) || {};

        const result = await dispatch(
          BillingMasterApi.endpoints.getTowerRoutes.initiate(
            { locationId: towerId, time: lastSyncTime },
            { forceRefetch: true }
          )
        );

        const dbName = await initializeDatabase("TowerRoutes");
        const towerRoutesFromLocalDb =
          await window.electron.getDocumentByParentId({
            categoryId: "Tower_id",
            dbName,
            catId: towerId,
            needSearching: true,
          });

        if (result?.data?.data?.date) {
          await saveSyncTime(result.data.data.date, `TowerRoutes-${towerId}`);
        }

        const routes = (result?.data?.data?.response.routes || []).map(
          (route, index) => {
            const existsInLocalDb = Array.isArray(towerRoutesFromLocalDb)
              ? towerRoutesFromLocalDb.some(
                  (localRoute: any) => localRoute._id === route._id
                )
              : false;

            return {
              ...route,
              _id: `${route._id}`,
              ...(existsInLocalDb ? {} : { progressloading: 0 }),
              orderIndex: index,
            };
          }
        );
        const deletedTasks = result?.data?.data?.response?.deletedTasks || [];
        if (Array.isArray(deletedTasks) && deletedTasks.length > 0) {
          for (const delId of deletedTasks) {
            try {
              await window.electron.deletedocbyid({
                dbName: "TowerRoutes",
                idname: "_id",
                _id: delId,
              });
              console.log(`Deleted task with id: ${delId} from local db`);
            } catch (err) {
              console.error(`Error deleting task with id: ${delId}`, err);
            }
          }
          dispatch(deleteTowerRoutes(deletedTasks));
        }

        await saveSyncData(routes, "", "TowerRoutes", false, dispatch);
        allResults.push(...routes);
      } catch (error) {
        console.error("Error processing tower routes backup", error);
      }
    });
  }

  await taskQueue.onIdle();
  return allResults;
};

export const TowerlocationsDetails = async (
  dispatch: AppDispatch,
  taskIds: string[],
  towerLocationIds: string[]
) => {
  let date = await getTime("towerLocationDetails");
  let time = "2025-06-04T06:03:20.030Z";
  if (date) {
    time = date.date;
  }
  console.log(`Timestamp for towerLocationDetails:`, time);

  const TowerRoutesResult = await TowerRoutes(dispatch, towerLocationIds);
  console.log(TowerRoutesResult, "TowerRoutesResult in towerlocatindetail");

  const TasksQueue = new PQueue({ concurrency: 1 });
  const taskResults: any[] = [];

  const firstTowerId = towerLocationIds[0];
  if (!firstTowerId) {
    console.warn("No towerLocationIds provided!");
    return;
  }

  for (const taskId of taskIds) {
    TasksQueue.add(async () => {
      try {
        const formatedTaskId = taskId.split("-")[0];
        const result = await dispatch(
          BillingMasterApi.endpoints.getTaskBasicDetailByTowerId.initiate(
            {
              locationId: firstTowerId,
              taskId: formatedTaskId,
              time,
            },
            { forceRefetch: true }
          )
        );

        const data = result?.data?.data;
        if (data) {
          const taskData = {
            ...data,
            ...(data?.taskid && { taskId: data.taskid }),
            _id: `${data?.taskid}-${data?._id}`,
          };

          const dbName = await initializeDatabase("TowerRoutes");
          const towerRoutesFromLocalDb =
            await window.electron.getDocumentByParentId({
              categoryId: "Tower_id",
              dbName,
              catId: firstTowerId,
              needSearching: true,
            });
          const matchedTaskRoute = towerRoutesFromLocalDb?.filter(
            (e: any) => e._id == taskData?.taskid
          );

          const newTask = {
            ...matchedTaskRoute[0],
            progressPercentage: taskData?.progressPercentage,
            progressloading: 25,
          };

          console.log(newTask, "this ios new task");
          await saveSyncData(newTask, "", "TowerRoutes", false, dispatch);
          dispatch(addOrUpdateTowerRoutes([newTask]));

          await saveSyncData(taskData, "", "TaskBasicDetails", false, dispatch);
          taskResults.push(data);
        }
      } catch (err) {
        console.error("Error fetching task basic detail", err);
      }
    });
  }

  await TasksQueue.onIdle();
  console.log(taskResults, "taskResultssssss");

  const SubTaskQueue = new PQueue({ concurrency: 1 });
  const SubtaskResults: any[] = [];

  for (const taskId of taskIds) {
    SubTaskQueue.add(async () => {
      try {
        const date = await getTime(`SubTasksBasicDetails-${taskId}`);
        time = "2025-06-04T06:03:20.030Z";
        if (date) {
          time = date.date;
        }

        const result = await dispatch(
          BillingMasterApi.endpoints.getAllSubtaskForTaskRouteForPlanning.initiate(
            {
              locationId: firstTowerId,
              taskId,
              time,
            },
            { forceRefetch: true }
          )
        );

        const finalData = result?.data?.data?.response?.finalData ?? [];
        const deletedSubtasks =
          result?.data?.data?.response?.deletedSubtasks ?? [];
        let formatted: any[] = [];

        const dbName = await initializeDatabase("TowerRoutes");
        const towerRoutesFromLocalDb =
          await window.electron.getDocumentByParentId({
            categoryId: "Tower_id",
            dbName,
            catId: firstTowerId,
            needSearching: true,
          });
        const currentTowerRoute = towerRoutesFromLocalDb?.find(
          (e: any) => e._id === taskId
        );

        if (finalData.length > 0) {
          formatted = finalData.map((e: any) => ({
            ...e,
            uniqueId: `${firstTowerId}-${taskId}`,
            _id: `${e._id}`,
          }));

          const subtaskDbName = await initializeDatabase(
            "SubTasksBasicDetails"
          );
          const localSubtasks = await window.electron.getDocumentByParentId({
            categoryId: "task_id",
            dbName: subtaskDbName,
            catId: taskId,
          });

          console.log(localSubtasks, "subtaks Fetching");
          const oldSubtasks = localSubtasks?.filter(
            (subtask) => subtask._id?.length === 13
          );

          if (oldSubtasks?.length > 0) {
            for (const oldSubtask of oldSubtasks) {
              try {
                await window.electron.deletedocbyid({
                  dbName: "SubtasklocDetail",
                  idname: "_id",
                  _id: oldSubtask._id,
                });
                await window.electron.deletedocbyid({
                  dbName: "SubTasksBasicDetails",
                  idname: "_id",
                  _id: oldSubtask._id,
                });
                dispatch(
                  deleteSubtaskInPlanning({
                    towerId: firstTowerId,
                    taskId: taskId,
                    subtaskId: oldSubtask._id,
                  })
                );

                console.log(`Deleted old subtask with id: ${oldSubtask._id}`);
              } catch (err) {
                console.error(
                  `Error deleting old subtask with id: ${oldSubtask._id}`,
                  err
                );
              }
            }
          }

          await saveSyncData(
            formatted,
            "",
            "SubTasksBasicDetails",
            false,
            dispatch
          );

          const updatedTowerRoute = {
            ...currentTowerRoute,
            progressloading: 50,
          };
          await saveSyncData(
            updatedTowerRoute,
            "",
            "TowerRoutes",
            false,
            dispatch
          );
          dispatch(addOrUpdateTowerRoutes([updatedTowerRoute]));

          console.log(formatted, "subtaskbasicdetailkadata");
          saveSyncTime(
            result?.data?.data?.date,
            `SubTasksBasicDetails-${taskId}`
          );
          SubtaskResults.push(...formatted);

          const SubtaskIds = finalData.map((item: any) => item._id);
          await SubtasklocDetail(dispatch, SubtaskIds, updatedTowerRoute);
        } else {
          const completedTowerRoute = {
            ...currentTowerRoute,
            progressloading: 100,
          };
          await saveSyncData(
            completedTowerRoute,
            "",
            "TowerRoutes",
            false,
            dispatch
          );
          dispatch(addOrUpdateTowerRoutes([completedTowerRoute]));
        }

        if (Array.isArray(deletedSubtasks) && deletedSubtasks.length > 0) {
          for (const delId of deletedSubtasks) {
            try {
              await window.electron.deletedocbyid({
                dbName: "SubtasklocDetail",
                idname: "_id",
                _id: delId,
              });
              await window.electron.deletedocbyid({
                dbName: "SubTasksBasicDetails",
                idname: "_id",
                _id: delId,
              });
              await updateMaterialTable(delId, dispatch);

              console.log(
                `Deleted subtask with id: ${delId} and updated materialtable`
              );
            } catch (err) {
              console.error(`Error deleting subtask with id: ${delId}`, err);
            }
          }
        }

        console.log(
          finalData,
          deletedSubtasks,
          "final data and deleted subtasks"
        );
        dispatch(
          setSubtaskBasicDetails({
            subtasks: finalData.map((e: any) => ({
              ...e,
              _id: `${e._id}`,
            })),
            deletedSubtasks: deletedSubtasks || [],
          })
        );
        console.log(result?.data?.data?.response, "this is deleting task");
      } catch (error) {
        console.log(error, "error in subtask queue");
      }
    });
  }

  await SubTaskQueue.onIdle();
  console.log(SubtaskResults, "SubtaskResults of task route");
};

export const SubtasklocDetail = async (
  dispatch: AppDispatch,
  SubtaskIds: string[],
  towertoSave?: any,
  taskId?: string
): Promise<any[]> => {
  const subtaskDetails: any[] = [];
  const materialCategoryMap: Record<
    string,
    {
      categoryId: string;
      categoryName: string;
      materials: any[];
      taskId: string;
      _id: string;
    }
  > = {};
  const taskQueue = new PQueue({ concurrency: 1 });

  if (SubtaskIds.length === 0) return [];

  let currentProgress = towertoSave?.progressloading ?? 50;
  const increment = SubtaskIds.length ? 50 / SubtaskIds.length : 0;

  let task_Id: string;

  for (const subtaskId of SubtaskIds) {
    taskQueue.add(async () => {
      try {
        const { date: lastSyncTime = "2025-06-04T06:03:20.030Z" } =
          (await getTime(`subtaskDetail_${subtaskId}`)) || {};
        const result = await dispatch(
          BillingMasterApi.endpoints.getSubTaskDetailById.initiate(
            { SubTaskId: subtaskId, time: lastSyncTime },
            { forceRefetch: true }
          )
        );
        console.log(result, "subtask planning data");
        let subtask = result?.data?.data?.response?.subTaskData;
        task_Id = result?.data?.data?.response?.taskId;
        if (Array.isArray(subtask)) subtask = subtask[0];

        await saveSyncData(subtask, "", "SubtasklocDetail", false, dispatch);

        const materials = subtask?.materialId || [];
        for (const material of materials) {
          const catId = material?.Mastermaterial_id?.materialCategoryId?._id;
          const catName =
            material?.Mastermaterial_id?.materialCategoryId?.name || "";
          const qty = Number(material.quantity) || 0;

          if (!catId) continue;

          if (!materialCategoryMap[catId]) {
            materialCategoryMap[catId] = {
              categoryId: catId,
              categoryName: catName,
              materials: [],
              taskId: task_Id,
              _id: `${catId}-${task_Id}`,
            };
          }

          const existingMaterial = materialCategoryMap[catId].materials.find(
            (existing) =>
              existing.Masterbrand_id === material?.Masterbrand_id?._id &&
              existing.Mastermaterial_id === material?.Mastermaterial_id?._id
          );

          if (existingMaterial) {
            existingMaterial.quantity += qty;

            existingMaterial.subtasks.push({
              name: subtask?.subtaskId?.name,
              id: subtask?._id,
              quantity: qty,
            });
          } else {
            materialCategoryMap[catId].materials.push({
              Masterbrand_id: material?.Masterbrand_id?._id || "",
              Brandname: material?.Masterbrand_id?.Brandname || "",
              Mastermaterial_id: material?.Mastermaterial_id?._id || "",
              name: material?.Mastermaterial_id?.name || "",
              unit: material?.Mastermaterial_id?.unit || [],
              quantity: qty,
              spec: material?.spec || "",
              subtasks: [
                {
                  name: subtask?.subtaskId?.name,
                  id: subtask?._id,
                  quantity: qty,
                },
              ],
              _id: material?._id || "",
            });
          }
        }

        currentProgress = Math.min(
          100,
          parseFloat((currentProgress + increment).toFixed(2))
        );
        await saveSyncData(
          { ...towertoSave, progressloading: currentProgress },
          "",
          "TowerRoutes",
          false,
          dispatch
        );
        dispatch(
          addOrUpdateTowerRoutes([
            {
              ...towertoSave,
              progressloading: currentProgress,
            },
          ])
        );
        subtaskDetails.push(subtask);

        if ((result.data?.data as any)?.date) {
          saveSyncTime(result.data.data.date, `subtaskDetail_${subtaskId}`);
        }
      } catch (error) {
        console.error("Error processing subtask detail backup", error);
      }
    });
  }

  await taskQueue.onIdle();

  const dbName = await initializeDatabase("materialtable");
  const existingDocs = await window.electron.allbulkGet({ dbName });
  console.log(existingDocs, "DUPLICATE DATA");

  const summaryArray = Object.values(materialCategoryMap).map((category) => {
    const existingDoc = existingDocs?.docs?.find(
      (doc: any) => doc._id === `${category.categoryId}-${category.taskId}`
    );

    if (existingDoc) {
      window.electron.deletedocbyid({
        dbName: "materialtable",
        idname: "_id",
        _id: existingDoc._id,
      });
    }

    return category;
  });

  console.log(summaryArray, "sahil Singh db dikhao");

  await saveSyncData(summaryArray, "", "materialtable", false, dispatch);

  return subtaskDetails;
};

export const updateMaterialTable = async (
  delId: string,
  dispatch: AppDispatch
): Promise<void> => {
  try {
    const dbName = await initializeDatabase("materialtable");
    const existingDocs = await window.electron.allbulkGet({ dbName });
    console.log(existingDocs, "sara Data");

    const updatedDocs = existingDocs.docs.map((doc: any) => {
      const updatedMaterials = doc.materials
        .map((material: any) => {
          const filteredSubtasks = material.subtasks.filter(
            (subtask: any) => subtask.id !== delId
          );

          if (filteredSubtasks.length === 0) {
            return null;
          }

          return { ...material, subtasks: filteredSubtasks };
        })
        .filter((material: any) => material !== null);

      if (updatedMaterials.length === 0) {
        return null;
      }

      return { ...doc, materials: updatedMaterials };
    });

    const finalDocs = updatedDocs.filter((doc: any) => doc !== null);

    await saveSyncData(finalDocs, "", "materialtable", false, dispatch);

    const deletedCategories = updatedDocs
      .filter((doc: any) => doc === null)
      .map((doc: any) => doc?._id);

    for (const categoryId of deletedCategories) {
      await window.electron.deletedocbyid({
        dbName: "materialtable",
        idname: "_id",
        _id: categoryId,
      });
      console.log(`Deleted category with id: ${categoryId} from local db`);
    }

    console.log(`Deleted subtask with id: ${delId} and updated materialtable`);
  } catch (err) {
    console.error(`Error deleting subtask with id: ${delId}`, err);
  }
};
