// AddToolTip.tsx
import React, { useState } from "react";
import styles from "./Styles/AddToolTip.module.css";

import ToolTriggerTip from "../ToolTriggerTip";
import { openPopup } from "../../../../redux/features/Modules/Reusble/popupSlice";
import {
  setTriggerFormData,
  setTriggerMode,
} from "../../../../redux/features/Modules/TaskMaster/Slices/TriggerEventSlice";
import { AddCategoryIcon, DeleteIcon } from "../../../../assets/icons";
import { useDispatch, useSelector } from "react-redux";
import { TriggerToolTipProps } from "../TaskMasterInterfaces/TaskMasterInterface";
import { setInputValue } from "../../../../redux/features/Modules/Reusble/floatinglabelslice";
import { RootState } from "../../../../redux/store";
import { isValidValue } from "../../../../functions/functions";


const TriggerTip: React.FC<TriggerToolTipProps> = ({
  label,
  className = "",
  className2 = "",
  onClick,
  handleDelete,
  isEdit = false,
  activeTip,
  data = [],
  icon,
  isPllaning = true,
  isActive = true,
}) => {
  const dispatch = useDispatch();

  const triggerData = useSelector(
    (state: RootState) => state.taskMaster.currentSubtaskData?.AutoId
  );
  

  
  const handlePopup = () => {
    dispatch(
      setTriggerFormData({
        TriggerAction: {
          ActionName: triggerData?.TriggerAction?.ActionName ?? null,
          ActionTime: Number(triggerData?.TriggerAction?.ActionTime),
        },
        TriggerResponse: triggerData?.TriggerResponse ?? null,
        ResponseTime: Number(triggerData?.ResponseTime),
      })
    )
    dispatch(setInputValue({trigger_event_add_start_after_time: String(triggerData?.TriggerAction?.ActionTime)}))
    dispatch(setInputValue({trigger_event_add_response_time: String(triggerData?.ResponseTime)}))
    dispatch(openPopup("TEForm"));
    dispatch(setTriggerMode("edit"));
  };

  console.log('editt in trigger tip',isEdit)
  return (
    <div className={`${styles.addtooltip_container} ${styles[className]}`}>
      <div className={`${styles.addtooltip_sub_container} ${styles[className2]}`}>
        <div className={styles.addtooltip_header}>
          <div style={{ display: "flex", gap: isPllaning ? "0.8rem" : "0" }}>
            <span>{icon && icon}</span>
            <h4 style={{ color: (isActive && !triggerData?.TriggerResponse?.isFirst) || activeTip === "Trigger" ? "black" : "gray" }}>{label}</h4>
          </div>

          {isPllaning ? (
            isActive &&
            !(data[0]?.name) && isEdit && !triggerData?.TriggerResponse?.isFirst && (
              <span className="cursor_pointer" onClick={onClick}>
                <AddCategoryIcon marginTop={0.25} />
              </span>
            )
          ) : (
            <circle
              nameclass="material_bubble"
              icon={
                <p
                  className="small_text_p"
                  style={{ color: "var(--main_background" }}
                >
                  33
                </p>
              }
            />
          )}
        </div>
        {data && data.length > 0 && isValidValue(data[0]?.name) && (
          <div className={`${styles.addtooltip_data_container} ${styles.cover_extra_space}`} style={{position: "relative"}}>
            {data.map((item, index) => (
              <>
              <ToolTriggerTip
                key={index}
                activeTip = {activeTip}
                isTimeInterval={activeTip === "Action"}
                handleClick={() =>isEdit && handlePopup()}
                content={(item as any)?.name}
              />
              { isEdit && handleDelete && (
                <div
                  className={styles.delete_icon_tooltip}
                  onClick={() => handleDelete(item)}
                >
                  <DeleteIcon />
                </div>
              )}
              </>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TriggerTip;
