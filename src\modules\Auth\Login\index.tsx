import { useEffect, useRef, useState } from "react";

import styles from "./Styles/LoginPage.module.css";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { useAuth } from "../../../AuthProvider";
import { useToast } from "../../../hooks/ToastHook";
import { HidePassword } from "../../../assets/icons";
import Button from "../../../components/Reusble/Global/Button";

const Login = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [showPassword, setShowPassword] = useState(false);
  const { login } = useAuth();
  const [inputs, setInputs] = useState({
    employeeId: "",
    password: "",
  });

  const [error, setErrors] = useState({
    employeeId: "",
    password: "",
  });

  const showToast = useToast();

  const handleValidation = () => {
    let valid = true;
    const { employeeId, password } = inputs;
    const errors = {
      employeeId: "",
      password: "",
    };
    if (!employeeId) {
      errors.employeeId = "Employee ID is required";
      valid = false;
    }
    if (!password) {
      errors.password = "Password is required";
      valid = false;
    }
    setErrors(errors);
    return valid;
  };

  const handleSubmission = async () => {
    if (handleValidation()) {
      try {
        await window.electron.deleteCookies();
        const data = (await login(
          inputs.employeeId,
          inputs.password
        )) as unknown as any;
        if (data.success) {
          data.success && navigate("/category");
          setInputs({
            employeeId: "",
            password: "",
          });
          return;
        }
        if (data.status == 404) {
          return showToast({
            messageContent: "Invalid Username or Password",
            type: "danger",
          });
        } else {
          return showToast({
            messageContent: "Something went wrong",
            type: "danger",
          });
        }
      } catch (error: any) {
        console.error("Login failed:", error);
      }
    } else {
      console.log("Validation failed");
    }
  };

  const handleInputs = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    setInputs({ ...inputs, [e.target.name]: e.target.value });
  };

  const handleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <>
      <div className={`${styles.login_headings}`}>
        <h2>Building Trust!</h2>
        <div>
          <h3>Take control of your projects and lead your</h3>
          <h3> team from anywhere</h3>
        </div>
      </div>
      <div className={`${styles.login_inputs_container}`}>
        <div>
          {error.employeeId && (
            <p className={`error_text_p ${styles.login_inputs_error}`}>
              {error.employeeId}
            </p>
          )}
          <div
            className={styles.login_inputs}
            style={
              error.employeeId !== ""
                ? {
                    border: "1px solid var(--warning_color)",
                  }
                : {}
            }
          >
            <input
              type="text"
              placeholder="Employee ID"
              name="employeeId"
              value={inputs.employeeId}
              onChange={handleInputs}
              className={styles.login_input}
              onKeyDown={(e) => (e.key === "Enter" ? handleSubmission() : null)}
            />
          </div>
        </div>
        <div>
          {error.password && (
            <p className={`error_text_p ${styles.login_inputs_error}`}>
              {error.password}
            </p>
          )}
          <div
            className={`${styles.login_inputs} , ${styles.login_password_field}`}
            style={
              error.password !== ""
                ? {
                    border: "1px solid var(--warning_color)",
                  }
                : {}
            }
          >
            <input
              type={showPassword ? "text" : "password"}
              className={styles.login_input}
              placeholder="Password"
              name="password"
              value={inputs.password}
              onChange={handleInputs}
              onKeyDown={(e) => (e.key === "Enter" ? handleSubmission() : null)}
            />
            <div
              onClick={handleShowPassword}
              className={`${styles.login_hidepassword}`}
            >
              <HidePassword />
            </div>
          </div>
          <div className={`${styles.login_utilties}`}>
            <p className="small_text_p">Forgot Password?</p>
            <p
              className={`small_text_p ${styles.login_with_OTP}`}
              onClick={() => navigate("/auth/otp-verification")}
            >
              Log in with OTP
            </p>
          </div>
        </div>
      </div>
      <div className={`${styles.login_footer}`}>
        <div className={`${styles.login_button}`}>
          <Button
            type="Accept"
            Content="Log in"
            width="65%"
            Callback={handleSubmission}
          />
        </div>
        <div className={`${styles.login_policies}`}>
          <p>
            By logging in, you agree to our <b>Terms & Conditions.</b>
          </p>
          <p>
            Learn how we process your data in our <b>Privacy Policy.</b>
          </p>
        </div>
      </div>
    </>
  );
};

export default Login;
