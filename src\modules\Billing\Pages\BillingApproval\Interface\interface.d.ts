// Base interfaces for reusable structures
interface BaseEntity {
  _id: string;
  name: string;
  is_selected: boolean;
  status?: string;
}

interface TowerInfo {
  id: string;
  name: string;
}

interface TaskDetails {
  description: string;
  startDate: string;
  endDate: string;
  area: string;
  duration: string;
  drawings: Drawing[];
  remarks: string;
  subtasks: {
    id: string;
    name: string;
    isSelected: boolean;
  }[];
}

interface TimestampFields {
  created_at?: string;
  updated_at?: string;
}

// Main interfaces
export interface TargetGenerationFormState {
  category: "Tower" | "Non-Tower" | "Miscellaneous";
  selectedTower: TowerInfo;
  isSubtaskForm: boolean;
  selectedTasks: {
    id: string;
    name: string;
    isSelected: boolean;
  }[];
  activeTaskDetails: string | null;
  taskDetails: {
    [taskId: string]: TaskDetails;
  };
  finalDetails: {
    selectedTaskIds: string[];
  };
  currentStep: "details" | "finalDetails";
  isFormValid: boolean;
  errors: {
    [key: string]: boolean;
  };
}

export interface MonthlyTargetData extends TimestampFields {
  id: string;
  projectId: string;
  location_type?: string;
  towerId: string;
  towerName: string;
  tasks: Task[];
  subtasks?: Subtask[];
  drawing?: Drawing[];
  description?: string;
  status?: string;
  is_deleted?: boolean;
  isApproved?: boolean;
  assigned_to?: string[];
  estimated_cost?: number;
}

export interface Task extends BaseEntity {
  task_name?: string;
  towerRouteId?: string;
  Tower_id?: string;
  floor_type?: string;
  details?: Partial<Omit<TaskDetails, 'drawings' | 'subtasks'>>;
  subtasks?: Subtask[];
}

export interface Subtask extends BaseEntity {
  parent_task_id: string;
}

export interface Drawing {
  name: string;
  path?: string;
  file?: File;
}

export interface TargetGenerationFormProps {
  onClose: () => void;
  projectId?: string;
  editData?: MonthlyTargetData | null;
  mode?: "create" | "edit";
  onSubmitSuccess?: () => void;
}