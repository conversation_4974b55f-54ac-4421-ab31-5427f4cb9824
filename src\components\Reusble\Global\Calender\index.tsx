import { useState, useRef, useEffect } from "react";
import styles from "./Styles/calender.module.css";
import { DropDownArrowUpIcon } from "../../../../assets/icons";

const DAYS = ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sat"];
const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

interface DropdownProps {
  options: (string | number)[];
  value: string | number;
  onChange: (value: string | number) => void;
  className?: string;
  dropdownId: string;
  openDropdownId: string | null;
  setOpenDropdownId: (id: string | null) => void;
}

function CustomDropdown({
  options,
  value,
  onChange,
  className,
  dropdownId,
  openDropdownId,
  setOpenDropdownId,
}: DropdownProps) {
  const dropdownRef = useRef<HTMLDivElement>(null);

  // useEffect(() => {
  //   const handleClickOutside = (event: MouseEvent) => {
  //     if (
  //       dropdownRef.current &&
  //       !dropdownRef.current.contains(event.target as Node)
  //     ) {
  //       setOpenDropdownId(null);
  //     }
  //   };
  //   document.addEventListener("mousedown", handleClickOutside);
  //   return () => document.removeEventListener("mousedown", handleClickOutside);
  // }, [setOpenDropdownId]);

  const handleSelect = (option: string | number) => {
    onChange(option);
    setOpenDropdownId(null);
  };

  // useEffect(() => {
  //   const handleClickOutside = (event: MouseEvent) => {
  //     if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
  //       setOpenDropdownId(null); 
  //     }
  //   };

  //   document.addEventListener("mousedown", handleClickOutside);
  //   return () => {
  //     document.removeEventListener("mousedown", handleClickOutside);
  //   };
  // }, [setOpenDropdownId]);

  return (
    <div ref={dropdownRef} className={`${styles.dropdown}`}>
      <button
        className={styles.dropdownToggle}
        onClick={() =>
          setOpenDropdownId(openDropdownId === dropdownId ? null : dropdownId)
        }
      >
        {value}{" "}
        <span
          className={
            openDropdownId === dropdownId ? styles.revarrow : styles.arrow
          }
        >
          <DropDownArrowUpIcon />
        </span>
      </button>
      {openDropdownId === dropdownId && (
        <div
          className={`${styles.dropdownmenuOuterContainer} ${className || ""}`}
        >
          <div className={styles.dropdownMenu}>
            {options.map((option) => (
              <div
                key={String(option)}
                className={`${styles.dropdownItem} ${
                  option === value ? styles.active : ""
                }`}
                onClick={() => handleSelect(option)}
              >
                {option}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

interface CalendarProps {
  initialDate?: string;
  handleselect: (selectedDate: string | null) => void;
}

export default function Calendar({ initialDate, handleselect }: CalendarProps) {
  const parsedInitialDate = initialDate ? new Date(initialDate) : new Date();
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Normalize today's date for comparisons

  const [currentDate, setCurrentDate] = useState(parsedInitialDate || new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(parsedInitialDate);
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null); // Track the open dropdown
  const month = currentDate.getMonth();
  const year = currentDate.getFullYear();

  // useEffect(() => {
  //   setCurrentDate(new Date(year, month, 1));
  //   setSelectedDate(null);
  // }, [month, year]);

  useEffect(() => {
    if (
      selectedDate &&
      (selectedDate.getMonth() !== month || selectedDate.getFullYear() !== year)
    ) {
      setSelectedDate(null);
    }
  }, [month, year]);

  const handleMonthChange = (monthName: string | number) => {
    const newMonthIndex =
      typeof monthName === "string" ? MONTHS.indexOf(monthName) : monthName;
    if (newMonthIndex >= 0) {
      setCurrentDate(new Date(year, newMonthIndex, 1));
    }
  };

  const handleYearChange = (newYear: string | number) => {
    const parsedYear =
      typeof newYear === "string" ? parseInt(newYear, 10) : newYear;
    if (!isNaN(parsedYear)) {
      setCurrentDate(new Date(parsedYear, month, 1));
    }
  };

 const handleDateClick = (date: Date) => {
  const normalizedDate = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
  setSelectedDate(normalizedDate);
  handleselect(normalizedDate.toISOString());
};


  const isSelectedDate = (date: Date) =>
    selectedDate &&
    date.getFullYear() === selectedDate.getFullYear() &&
    date.getMonth() === selectedDate.getMonth() &&
    date.getDate() === selectedDate.getDate();

  const generateCalendarDays = () => {
    const firstDayOfMonth = new Date(year, month, 1);
    const lastDayOfMonth = new Date(year, month + 1, 0);
    const daysInMonth = lastDayOfMonth.getDate();
    const startingDayOfWeek = firstDayOfMonth.getDay();

    const prevMonthLastDate = new Date(year, month, 0).getDate();
    const prevMonthDays: Date[] = [];
    for (let i = startingDayOfWeek - 1; i >= 0; i--) {
      prevMonthDays.push(new Date(year, month - 1, prevMonthLastDate - i));
    }

    const currentMonthDays: Date[] = [];
    for (let i = 1; i <= daysInMonth; i++) {
      currentMonthDays.push(new Date(year, month, i));
    }

    const totalShown = prevMonthDays.length + currentMonthDays.length;
    const remaining = 42 - totalShown;
    const nextMonthDays: Date[] = [];
    for (let i = 1; i <= remaining; i++) {
      nextMonthDays.push(new Date(year, month + 1, i));
    }

    return [...prevMonthDays, ...currentMonthDays, ...nextMonthDays];
  };

  const calendarDays = generateCalendarDays();
  const yearOptions = Array.from({ length: 21 }, (_, i) => year - 10 + i);

  return (
    <div className={styles.calendar}>
      <div className={styles.header}>
        <CustomDropdown
          options={MONTHS}
          value={MONTHS[month]}
          onChange={handleMonthChange}
          className={styles.popupDropdownMonth}
          dropdownId="month"
          openDropdownId={openDropdownId}
          setOpenDropdownId={setOpenDropdownId}
        />
        <CustomDropdown
          options={yearOptions}
          value={year}
          onChange={handleYearChange}
          className={styles.popupDropdownYear}
          dropdownId="year"
          openDropdownId={openDropdownId}
          setOpenDropdownId={setOpenDropdownId}
        />
      </div>
      <div className={styles.daysHeader}>
        {DAYS.map((day) => (
          <div key={day} className={styles.dayName}>
            {day}
          </div>
        ))}
      </div>
      <div className={styles.daysGrid}>
        {calendarDays.map((date, index) => {
          const isCurrentMonth = date.getMonth() === month;

          return (
            <div
              key={index}
              className={`${styles.day} 
                ${isCurrentMonth ? "" : styles.otherMonth}
                ${isSelectedDate(date) ? styles.selected : ""}
                ${
                  date.toDateString() === today.toDateString()
                    ? styles.current_date
                    : ""
                }
              `}
              onClick={() => isCurrentMonth && handleDateClick(date)}
              style={{ cursor: isCurrentMonth ? "pointer" : "default" }}
            >
              {date.getDate()}
            </div>
          );
        })}
      </div>
    </div>
  );
}
