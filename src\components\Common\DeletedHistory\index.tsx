/*  AUTHOR NAME: CHARVI */

import { CloseIcon, UndoArrow } from "../../../assets/icons";
import { pmphoto } from "../../../assets/images";
import { formatTime } from "../../../functions/functions";
import styles from "./Styles/DeletedHistory.module.css";

const deletedItems = [
  {
    name: "<PERSON><PERSON>",
    time: "13:00 ",
    userName: "Alex Rox",
    date: "Nov 20, 2024",
    photo: pmphoto,
  },
  {
    name: "<PERSON>",
    time: "15:30",
    userName: "<PERSON>",
    date: "Nov 19, 2024",
    photo: pmphoto,
  },
  {
    name: "<PERSON>",
    time: "00:00",
    userName: "<PERSON>",
    date: "Nov 19, 2024",
    photo: pmphoto,
  },
];

export function DeletedHistory() {
  return (
    <div className={styles.deletedhistory_container}>
      <div className={styles.deletedhistory_header}>
        <div></div>
        <p>Bin</p>
        <CloseIcon />
      </div>
      <div className={styles.deletedhistory_maincontent}>
        {deletedItems.map((item, index) => (
          <div
            key={index}
            className={styles.deletedhistory_deleteditemcontainer}
          >
            <div className={styles.deletedhistory_deleteditemleft}>
              <UndoArrow />
              <div>
                <p
                  className="small_text_p"
                  style={{ color: "var(--text-black-60)" }}
                >
                  {item.name}
                </p>
                <p
                  className="small_text_p"
                  style={{ color: "var(--text-black-28)" }}
                >
                  {formatTime(item.time)}
                </p>
              </div>
            </div>
            <div className={styles.deletedhistory_deleteditemright}>
              <div>
                <p
                  className="small_text_p"
                  style={{ color: "var(--text-black-60)" }}
                >
                  {item.userName}
                </p>
                <p
                  className="small_text_p"
                  style={{ color: "var(--text-black-28)" }}
                >
                  {item.date}
                </p>
              </div>
              <img src={item.photo} alt={item.userName} />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
