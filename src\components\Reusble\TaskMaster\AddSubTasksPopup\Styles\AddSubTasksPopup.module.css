@keyframes slideIn {
  from {
    transform: translate(100%, 0%);
  }

  to {
    transform: translate(0%, 0%);
  }
}

@keyframes slideOut {
  from {
    transform: translate(0%, 0%);
  }

  to {
    transform: translate(100%, 0%);
  }
}

.addtaskspopup_container.closing {
  animation: slideOut 0.5s ease-out;
}

.addtaskspopup_container {
  position: fixed;
  top: 7rem;
  right: 1.5rem;
  transform: translate(0%, 0%);
  background: var(--blur-background);
  /* background-color: var(--main_background); */
  padding: 1.25rem 0.9rem;
  box-shadow: 0px 4px 40px 0px #00000080;
  border-radius: 2.6rem;
  z-index: 9999;
  width: 34rem;
  /* min-height: 85vh; */
  height: calc(100% - 8.5rem);
  animation: slideIn 0.5s ease-out;
  backdrop-filter: blur(150px);
}

.addtaskspopup_header {
  color: var(--primary_color);
  display: flex;
  justify-content: center;
  padding: 0.6rem;
}

.closeButton {
  position: absolute;
  top: 1.5em;
  right: 0.625rem;
  padding: 1rem;
  background: transparent;
  border: none;

  cursor: pointer;
}

.addtaskspopup_btngrp {
  position: fixed;
  bottom: 0;
  /* background-color: var( --text-white-100); */
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  padding:1.5rem 1rem;
  display: flex;
  justify-content: center;
  gap: 1rem;
  border-radius: 2.6rem;
  /* backdrop-filter: blur(60px); */
}

.addtaskspopup_input1 {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  position: relative;
  margin-bottom: 1rem;
  padding: 0 0.6rem;
}
.addtaskspopup_description{
  padding: 0 0.6rem;
  width: 100%;
}

.summaryDivData {
  display: flex;
  align-items: center;
}

.summaryDataContent {
  display: flex;
  flex-direction: column;
  background: var(--main_background);
  border-radius: 0.75rem;
  width: 30.8rem;
  width: 100%;
  height: calc(100% - 1REM);

  /* max-width: 28.5rem; */
  min-height: 3rem;
  padding: 1rem;
  white-space: normal;
  margin: 0.6rem;
  max-width: 100%;
  /* gap: 0.2rem; */
  line-height: 1.363rem;
  text-align: justify;
}
.summary_subtask_main_content{
  height: calc(100% - 6.5rem);
  max-height: calc(100% - 6.5rem);
  overflow: auto; 
  /* background-color: red; */
}
.discard_summary_subtask_main_content{
  height: calc(100% - 8.5rem);
  max-height: calc(100% - 8.5rem);
  overflow: auto; 
}

.summaryDataContent_weightage {
  display: flex;

  background: var(--main_background);
  border-radius: 0.75rem;
  width: 30.8rem;
  /* max-width: 28.5rem; */
  min-height: 3rem;
  padding: 1rem;
  word-break: break-all;
  overflow-wrap: break-word;
  white-space: normal;
  margin: 0.6rem;

  line-height: 1.363rem;
  justify-content: space-between;
  align-items: center;
}

.flexContainer {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  max-width: 100%;
  /* gap: 1rem; */
  justify-content: space-between;
}

.flexContainer > .summaryDivData {
  flex: 1;
  max-width: calc(50% - 0rem);
  box-sizing: border-box;
}
